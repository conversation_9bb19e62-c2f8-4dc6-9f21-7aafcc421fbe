package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"
)

// 简化的API结构
type SimpleRequest struct {
	Prompt             string  `json:"prompt"`
	ImageSize          string  `json:"image_size,omitempty"`
	BatchSize          int     `json:"batch_size,omitempty"`
	NumInferenceSteps  int     `json:"num_inference_steps,omitempty"`
	GuidanceScale      float64 `json:"guidance_scale,omitempty"`
	NegativePrompt     string  `json:"negative_prompt,omitempty"`
	Model              string  `json:"model,omitempty"`
	Quality            string  `json:"quality,omitempty"`
}

type SimpleResponse struct {
	Success bool `json:"success"`
	Data    struct {
		ImageURL       string `json:"image_url,omitempty"`
		LocalURL       string `json:"local_url,omitempty"`
		Seed           int64  `json:"seed"`
		GenerationTime float64 `json:"generation_time"`
		Images         []struct {
			ImageURL string `json:"image_url"`
			LocalURL string `json:"local_url"`
			Index    int    `json:"index"`
		} `json:"images,omitempty"`
	} `json:"data"`
	Message string `json:"message,omitempty"`
}

const BaseURL = "https://www.diwangzhidao.com/api/wenshengtu/api.php"

func main() {
	fmt.Println("=== AI图片生成器 (命令行版本) ===")
	fmt.Println()

	// 获取用户输入
	var prompt string
	fmt.Print("请输入图片描述提示词: ")
	fmt.Scanln(&prompt)
	if prompt == "" {
		prompt = "杰作，最佳画质，超高分辨率，一位美丽的女性，精致五官，自然表情"
	}

	var negativePrompt string
	fmt.Print("请输入负面提示词 (可选，直接回车跳过): ")
	fmt.Scanln(&negativePrompt)

	var imageSize string
	fmt.Print("请选择图片尺寸 (1024x1024): ")
	fmt.Scanln(&imageSize)
	if imageSize == "" {
		imageSize = "1024x1024"
	}

	// 创建请求
	req := SimpleRequest{
		Prompt:            prompt,
		NegativePrompt:    negativePrompt,
		ImageSize:         imageSize,
		BatchSize:         1,
		NumInferenceSteps: 25,
		GuidanceScale:     8.0,
		Model:             "Kwai-Kolors/Kolors",
		Quality:           "standard",
	}

	fmt.Println("\n正在生成图片...")
	fmt.Printf("提示词: %s\n", prompt)
	if negativePrompt != "" {
		fmt.Printf("负面提示词: %s\n", negativePrompt)
	}
	fmt.Printf("图片尺寸: %s\n", imageSize)
	fmt.Println()

	// 调用API
	resp, err := callAPI(req)
	if err != nil {
		log.Fatalf("API调用失败: %v", err)
	}

	if !resp.Success {
		log.Fatalf("生成失败: %s", resp.Message)
	}

	// 显示结果
	fmt.Printf("✅ 生成成功！\n")
	fmt.Printf("生成时间: %.2f秒\n", resp.Data.GenerationTime)
	fmt.Printf("随机种子: %d\n", resp.Data.Seed)

	// 下载图片
	var imageURL string
	if resp.Data.LocalURL != "" {
		imageURL = resp.Data.LocalURL
	} else {
		imageURL = resp.Data.ImageURL
	}

	if imageURL != "" {
		fmt.Printf("图片URL: %s\n", imageURL)
		
		// 下载并保存图片
		if err := downloadAndSaveImage(imageURL); err != nil {
			log.Printf("保存图片失败: %v", err)
		} else {
			fmt.Println("✅ 图片已保存到当前目录")
		}
	}

	// 处理多张图片
	if len(resp.Data.Images) > 0 {
		fmt.Printf("共生成 %d 张图片:\n", len(resp.Data.Images))
		for i, img := range resp.Data.Images {
			url := img.LocalURL
			if url == "" {
				url = img.ImageURL
			}
			fmt.Printf("图片 %d: %s\n", i+1, url)
			
			if err := downloadAndSaveImageWithIndex(url, i+1); err != nil {
				log.Printf("保存图片 %d 失败: %v", i+1, err)
			}
		}
	}

	fmt.Println("\n程序执行完成！")
}

func callAPI(req SimpleRequest) (*SimpleResponse, error) {
	// 序列化请求
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	// 创建HTTP请求
	url := fmt.Sprintf("%s?action=generate", BaseURL)
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{Timeout: 120 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 解析响应
	var result SimpleResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &result, nil
}

func downloadAndSaveImage(url string) error {
	return downloadAndSaveImageWithIndex(url, 0)
}

func downloadAndSaveImageWithIndex(url string, index int) error {
	// 下载图片
	resp, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("下载图片失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载失败，状态码: %d", resp.StatusCode)
	}

	// 读取图片数据
	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取图片数据失败: %w", err)
	}

	// 生成文件名
	timestamp := time.Now().Format("20060102_150405")
	var filename string
	if index > 0 {
		filename = fmt.Sprintf("ai_image_%s_%d.png", timestamp, index)
	} else {
		filename = fmt.Sprintf("ai_image_%s.png", timestamp)
	}

	// 保存图片
	if err := os.WriteFile(filename, imageData, 0644); err != nil {
		return fmt.Errorf("保存图片失败: %w", err)
	}

	fmt.Printf("✅ 图片已保存: %s\n", filename)
	return nil
}
