package utils

import (
	"bytes"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"os"
	"path/filepath"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/storage"
)

// SaveImageToFile 保存图片到文件
func SaveImageToFile(imageData []byte, directory, filename string) (string, error) {
	// 确保目录存在
	if err := os.MkdirAll(directory, 0755); err != nil {
		return "", fmt.Errorf("创建目录失败: %w", err)
	}
	
	// 如果没有提供文件名，生成一个
	if filename == "" {
		timestamp := time.Now().Format("20060102_150405")
		filename = fmt.Sprintf("ai_image_%s.png", timestamp)
	}
	
	// 确保文件名有正确的扩展名
	if !strings.HasSuffix(strings.ToLower(filename), ".png") && 
	   !strings.HasSuffix(strings.ToLower(filename), ".jpg") && 
	   !strings.HasSuffix(strings.ToLower(filename), ".jpeg") {
		filename += ".png"
	}
	
	filePath := filepath.Join(directory, filename)
	
	// 如果文件已存在，添加序号
	originalPath := filePath
	counter := 1
	for {
		if _, err := os.Stat(filePath); os.IsNotExist(err) {
			break
		}
		
		ext := filepath.Ext(originalPath)
		nameWithoutExt := strings.TrimSuffix(originalPath, ext)
		filePath = fmt.Sprintf("%s_%d%s", nameWithoutExt, counter, ext)
		counter++
	}
	
	// 写入文件
	if err := os.WriteFile(filePath, imageData, 0644); err != nil {
		return "", fmt.Errorf("写入文件失败: %w", err)
	}
	
	return filePath, nil
}

// ConvertToFyneResource 将图片数据转换为Fyne资源
func ConvertToFyneResource(imageData []byte) (fyne.Resource, error) {
	// 检测图片格式
	_, format, err := image.DecodeConfig(bytes.NewReader(imageData))
	if err != nil {
		return nil, fmt.Errorf("检测图片格式失败: %w", err)
	}

	// 根据格式创建资源
	var resource fyne.Resource
	switch format {
	case "jpeg", "jpg":
		resource = fyne.NewStaticResource("image.jpg", imageData)
	case "png":
		resource = fyne.NewStaticResource("image.png", imageData)
	default:
		// 默认转换为PNG
		img, _, err := image.Decode(bytes.NewReader(imageData))
		if err != nil {
			return nil, fmt.Errorf("解码图片失败: %w", err)
		}

		var buf bytes.Buffer
		if err := png.Encode(&buf, img); err != nil {
			return nil, fmt.Errorf("编码PNG失败: %w", err)
		}

		resource = fyne.NewStaticResource("image.png", buf.Bytes())
	}

	return resource, nil
}

// ResizeImageData 调整图片大小（保持比例）
func ResizeImageData(imageData []byte, maxWidth, maxHeight int) ([]byte, error) {
	// 解码图片
	img, format, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return nil, fmt.Errorf("解码图片失败: %w", err)
	}
	
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()
	
	// 如果图片已经足够小，直接返回
	if width <= maxWidth && height <= maxHeight {
		return imageData, nil
	}
	
	// 计算缩放比例
	scaleX := float64(maxWidth) / float64(width)
	scaleY := float64(maxHeight) / float64(height)
	scale := scaleX
	if scaleY < scaleX {
		scale = scaleY
	}
	
	newWidth := int(float64(width) * scale)
	newHeight := int(float64(height) * scale)
	
	// 创建新图片（简单的最近邻缩放）
	newImg := image.NewRGBA(image.Rect(0, 0, newWidth, newHeight))
	
	for y := 0; y < newHeight; y++ {
		for x := 0; x < newWidth; x++ {
			srcX := int(float64(x) / scale)
			srcY := int(float64(y) / scale)
			if srcX >= width {
				srcX = width - 1
			}
			if srcY >= height {
				srcY = height - 1
			}
			newImg.Set(x, y, img.At(srcX, srcY))
		}
	}
	
	// 编码新图片
	var buf bytes.Buffer
	switch format {
	case "jpeg", "jpg":
		if err := jpeg.Encode(&buf, newImg, &jpeg.Options{Quality: 90}); err != nil {
			return nil, fmt.Errorf("编码JPEG失败: %w", err)
		}
	default:
		if err := png.Encode(&buf, newImg); err != nil {
			return nil, fmt.Errorf("编码PNG失败: %w", err)
		}
	}
	
	return buf.Bytes(), nil
}

// GetImageDimensions 获取图片尺寸
func GetImageDimensions(imageData []byte) (int, int, error) {
	config, _, err := image.DecodeConfig(bytes.NewReader(imageData))
	if err != nil {
		return 0, 0, fmt.Errorf("获取图片尺寸失败: %w", err)
	}
	
	return config.Width, config.Height, nil
}
