package api

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

const (
	BaseURL = "https://www.diwangzhidao.com/api/wenshengtu/api.php"
	Timeout = 120 * time.Second
)

// Client API客户端
type Client struct {
	httpClient *http.Client
	baseURL    string
}

// NewClient 创建新的API客户端
func NewClient() *Client {
	return &Client{
		httpClient: &http.Client{
			Timeout: Timeout,
		},
		baseURL: BaseURL,
	}
}

// GenerateImage 生成图片
func (c *Client) GenerateImage(req *GenerateRequest) (*GenerateResponse, error) {
	// 设置默认值
	if req.Model == "" {
		req.Model = "Kwai-Kolors/Kolors"
	}
	if req.ImageSize == "" {
		req.ImageSize = "1024x1024"
	}
	if req.BatchSize == 0 {
		req.BatchSize = 1
	}
	if req.NumInferenceSteps == 0 {
		req.NumInferenceSteps = 25
	}
	if req.GuidanceScale == 0 {
		req.GuidanceScale = 8.0
	}
	if req.Quality == "" {
		req.Quality = "standard"
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 创建HTTP请求
	url := fmt.Sprintf("%s?action=generate", c.baseURL)
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 解析响应
	var result GenerateResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &result, nil
}

// GetAPIInfo 获取API信息
func (c *Client) GetAPIInfo() (*InfoResponse, error) {
	url := fmt.Sprintf("%s?action=info", c.baseURL)
	
	resp, err := c.httpClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("获取API信息失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var result InfoResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &result, nil
}

// DownloadImage 下载图片
func (c *Client) DownloadImage(url string) ([]byte, error) {
	resp, err := c.httpClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("下载图片失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("下载图片失败，状态码: %d", resp.StatusCode)
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取图片数据失败: %w", err)
	}

	return data, nil
}
