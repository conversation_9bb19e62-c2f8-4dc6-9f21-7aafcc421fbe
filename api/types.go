package api

// GenerateRequest 生成图片的请求结构
type GenerateRequest struct {
	Model              string  `json:"model,omitempty"`
	Prompt             string  `json:"prompt"`
	ImageSize          string  `json:"image_size,omitempty"`
	BatchSize          int     `json:"batch_size,omitempty"`
	NumInferenceSteps  int     `json:"num_inference_steps,omitempty"`
	GuidanceScale      float64 `json:"guidance_scale,omitempty"`
	NegativePrompt     string  `json:"negative_prompt,omitempty"`
	Seed               int64   `json:"seed,omitempty"`
	Style              string  `json:"style,omitempty"`
	Quality            string  `json:"quality,omitempty"`
}

// GenerateResponse 生成图片的响应结构
type GenerateResponse struct {
	Success   bool                 `json:"success"`
	Data      *GenerateData        `json:"data,omitempty"`
	ErrorCode int                  `json:"error_code,omitempty"`
	Message   string               `json:"message,omitempty"`
	Details   map[string]interface{} `json:"details,omitempty"`
	Timestamp int64                `json:"timestamp"`
}

// GenerateData 生成数据
type GenerateData struct {
	Seed           int64        `json:"seed"`
	GenerationTime float64      `json:"generation_time"`
	Prompt         string       `json:"prompt"`
	Params         *RequestParams `json:"params"`
	Count          int          `json:"count"`
	ImageURL       string       `json:"image_url,omitempty"`
	LocalURL       string       `json:"local_url,omitempty"`
	Images         []ImageInfo  `json:"images,omitempty"`
}

// RequestParams 请求参数
type RequestParams struct {
	Model             string  `json:"model"`
	ImageSize         string  `json:"image_size"`
	BatchSize         int     `json:"batch_size"`
	NumInferenceSteps int     `json:"num_inference_steps"`
	GuidanceScale     float64 `json:"guidance_scale"`
	NegativePrompt    string  `json:"negative_prompt"`
	Style             string  `json:"style"`
	Quality           string  `json:"quality"`
}

// ImageInfo 图片信息
type ImageInfo struct {
	ImageURL string `json:"image_url"`
	LocalURL string `json:"local_url"`
	Index    int    `json:"index"`
}

// InfoResponse API信息响应
type InfoResponse struct {
	Success   bool      `json:"success"`
	Data      *InfoData `json:"data,omitempty"`
	Timestamp int64     `json:"timestamp"`
}

// InfoData API信息数据
type InfoData struct {
	APIName           string   `json:"api_name"`
	Version           string   `json:"version"`
	Model             string   `json:"model"`
	SupportedSizes    []string `json:"supported_sizes"`
	MaxPromptLength   int      `json:"max_prompt_length"`
	PricePerImage     string   `json:"price_per_image"`
	ServerTime        string   `json:"server_time"`
}

// 预定义的常量
var (
	// 支持的图片尺寸
	SupportedSizes = []string{
		"512x512",
		"768x768", 
		"1024x1024",
		"1024x768",
		"768x1024",
		"1152x896",
		"896x1152",
		"1216x832",
		"832x1216",
		"1344x768",
		"768x1344",
	}

	// 支持的模型
	SupportedModels = []string{
		"Kwai-Kolors/Kolors",
		"stabilityai/stable-diffusion-3-medium",
		"black-forest-labs/FLUX.1-schnell",
		"black-forest-labs/FLUX.1-dev",
	}

	// 支持的风格
	SupportedStyles = []string{
		"",
		"anime",
		"photographic",
		"digital-art",
		"comic-book",
		"fantasy-art",
		"line-art",
		"oil-painting",
		"watercolor",
	}

	// 支持的质量
	SupportedQualities = []string{
		"standard",
		"hd",
	}
)
