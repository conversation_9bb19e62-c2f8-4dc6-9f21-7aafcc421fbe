package config

import (
	"encoding/json"
	"os"
	"path/filepath"
)

// Config 应用配置
type Config struct {
	// 默认参数设置
	DefaultModel             string  `json:"default_model"`
	DefaultImageSize         string  `json:"default_image_size"`
	DefaultBatchSize         int     `json:"default_batch_size"`
	DefaultNumInferenceSteps int     `json:"default_num_inference_steps"`
	DefaultGuidanceScale     float64 `json:"default_guidance_scale"`
	DefaultStyle             string  `json:"default_style"`
	DefaultQuality           string  `json:"default_quality"`
	
	// 界面设置
	WindowWidth  int `json:"window_width"`
	WindowHeight int `json:"window_height"`
	
	// 保存路径
	SaveDirectory string `json:"save_directory"`
	
	// 历史记录
	PromptHistory         []string `json:"prompt_history"`
	NegativePromptHistory []string `json:"negative_prompt_history"`
	MaxHistorySize        int      `json:"max_history_size"`
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	homeDir, _ := os.UserHomeDir()
	defaultSaveDir := filepath.Join(homeDir, "AI_Generated_Images")
	
	return &Config{
		DefaultModel:             "Kwai-Kolors/Kolors",
		DefaultImageSize:         "1024x1024",
		DefaultBatchSize:         1,
		DefaultNumInferenceSteps: 25,
		DefaultGuidanceScale:     8.0,
		DefaultStyle:             "",
		DefaultQuality:           "standard",
		WindowWidth:              1200,
		WindowHeight:             800,
		SaveDirectory:            defaultSaveDir,
		PromptHistory:            []string{},
		NegativePromptHistory:    []string{},
		MaxHistorySize:           50,
	}
}

// getConfigPath 获取配置文件路径
func getConfigPath() (string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", err
	}
	
	configDir := filepath.Join(homeDir, ".ai-image-generator")
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return "", err
	}
	
	return filepath.Join(configDir, "config.json"), nil
}

// LoadConfig 加载配置
func LoadConfig() (*Config, error) {
	configPath, err := getConfigPath()
	if err != nil {
		return DefaultConfig(), nil
	}
	
	data, err := os.ReadFile(configPath)
	if err != nil {
		if os.IsNotExist(err) {
			return DefaultConfig(), nil
		}
		return nil, err
	}
	
	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return DefaultConfig(), nil
	}
	
	return &config, nil
}

// SaveConfig 保存配置
func (c *Config) Save() error {
	configPath, err := getConfigPath()
	if err != nil {
		return err
	}
	
	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return err
	}
	
	return os.WriteFile(configPath, data, 0644)
}

// AddPromptHistory 添加提示词历史
func (c *Config) AddPromptHistory(prompt string) {
	if prompt == "" {
		return
	}
	
	// 检查是否已存在
	for i, p := range c.PromptHistory {
		if p == prompt {
			// 移动到最前面
			c.PromptHistory = append([]string{prompt}, append(c.PromptHistory[:i], c.PromptHistory[i+1:]...)...)
			return
		}
	}
	
	// 添加到最前面
	c.PromptHistory = append([]string{prompt}, c.PromptHistory...)
	
	// 限制历史记录数量
	if len(c.PromptHistory) > c.MaxHistorySize {
		c.PromptHistory = c.PromptHistory[:c.MaxHistorySize]
	}
}

// AddNegativePromptHistory 添加负面提示词历史
func (c *Config) AddNegativePromptHistory(prompt string) {
	if prompt == "" {
		return
	}
	
	// 检查是否已存在
	for i, p := range c.NegativePromptHistory {
		if p == prompt {
			// 移动到最前面
			c.NegativePromptHistory = append([]string{prompt}, append(c.NegativePromptHistory[:i], c.NegativePromptHistory[i+1:]...)...)
			return
		}
	}
	
	// 添加到最前面
	c.NegativePromptHistory = append([]string{prompt}, c.NegativePromptHistory...)
	
	// 限制历史记录数量
	if len(c.NegativePromptHistory) > c.MaxHistorySize {
		c.NegativePromptHistory = c.NegativePromptHistory[:c.MaxHistorySize]
	}
}
