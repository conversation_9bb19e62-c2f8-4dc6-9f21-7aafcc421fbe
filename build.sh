#!/bin/bash

echo "正在构建AI图片生成器..."

# 设置Go环境变量
export CGO_ENABLED=1
export GO111MODULE=on

# 清理之前的构建
rm -f ai-image-generator
mkdir -p build

echo ""
echo "正在安装依赖..."
go mod tidy

echo ""
echo "正在编译当前平台版本..."
go build -ldflags "-s -w" -o build/ai-image-generator

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 当前平台版本编译成功！"
    echo "输出文件: build/ai-image-generator"
    chmod +x build/ai-image-generator
else
    echo ""
    echo "❌ 当前平台版本编译失败！"
    exit 1
fi

echo ""
echo "正在编译Windows版本..."
GOOS=windows GOARCH=amd64 go build -ldflags "-s -w" -o build/ai-image-generator-windows.exe

if [ $? -eq 0 ]; then
    echo "✅ Windows版本编译成功！"
    echo "输出文件: build/ai-image-generator-windows.exe"
else
    echo "❌ Windows版本编译失败！"
fi

echo ""
echo "正在编译Linux版本..."
GOOS=linux GOARCH=amd64 go build -ldflags "-s -w" -o build/ai-image-generator-linux

if [ $? -eq 0 ]; then
    echo "✅ Linux版本编译成功！"
    echo "输出文件: build/ai-image-generator-linux"
    chmod +x build/ai-image-generator-linux
else
    echo "❌ Linux版本编译失败！"
fi

echo ""
echo "正在编译macOS版本..."
GOOS=darwin GOARCH=amd64 go build -ldflags "-s -w" -o build/ai-image-generator-macos

if [ $? -eq 0 ]; then
    echo "✅ macOS版本编译成功！"
    echo "输出文件: build/ai-image-generator-macos"
    chmod +x build/ai-image-generator-macos
else
    echo "❌ macOS版本编译失败！"
fi

echo ""
echo "🎉 构建完成！"
echo ""
echo "生成的文件："
ls -la build/

echo ""
echo "使用说明："
echo "1. 当前平台运行: ./build/ai-image-generator"
echo "2. Windows用户运行: build/ai-image-generator-windows.exe"
echo "3. Linux用户运行: ./build/ai-image-generator-linux"
echo "4. macOS用户运行: ./build/ai-image-generator-macos"
echo ""
