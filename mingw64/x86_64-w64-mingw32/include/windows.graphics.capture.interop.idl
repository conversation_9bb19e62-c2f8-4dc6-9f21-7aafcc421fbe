/*
 * Copyright (C) 2023 Mo<PERSON>ad Al<PERSON>af
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "windows.ui.composition.idl";
import "windows.graphics.capture.idl";
import "sdkddkver.h";

[
    uuid(3628e81b-3cac-4c60-b7f4-23ce0e0c3356)
]
interface IGraphicsCaptureItemInterop : IUnknown
{
    HRESULT CreateForWindow([in] HWND window, [in] REFIID iid, [out, iid_is(iid)] void **result);
    HRESULT CreateForMonitor([in] HMONITOR monitor, [in] REFIID iid, [out, iid_is(iid)] void **result);
}
