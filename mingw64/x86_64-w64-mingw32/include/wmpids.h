/*
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

/* WMPCoreEvents */
#define DISPID_WMPCOREEVENT_OPENSTATECHANGE     5001
#define DISPID_WMPCOREEVENT_STATUSCHANGE        5002

#define DISPID_WMPCOREEVENT_PLAYSTATECHANGE     5101
#define DISPID_WMPCOREEVENT_AUDIOLANGUAGECHANGE 5102

#define DISPID_WMPCOREEVENT_ENDOFSTREAM         5201
#define DISPID_WMPCOREEVENT_POSITIONCHANGE      5202
#define DISPID_WMPCOREEVENT_MARKERHIT           5203
#define DISPID_WMPCOREEVENT_DURATIONUNITCHANGE  5204

#define DISPID_WMPCOREEVENT_SCRIPTCOMMAND       5301

#define DISPID_WMPCOREEVENT_DISCONNECT          5401
#define DISPID_WMPCOREEVENT_BUFFERING           5402
#define DISPID_WMPCOREEVENT_NEWSTREAM           5403

#define DISPID_WMPCOREEVENT_ERROR               5501

#define DISPID_WMPCOREEVENT_WARNING             5601

#define DISPID_WMPCOREEVENT_CDROMMEDIACHANGE    5701

#define DISPID_WMPCOREEVENT_PLAYLISTCHANGE                         5801
#define DISPID_WMPCOREEVENT_MEDIACHANGE                            5802
#define DISPID_WMPCOREEVENT_CURRENTMEDIAITEMAVAILABLE              5803
#define DISPID_WMPCOREEVENT_CURRENTPLAYLISTCHANGE                  5804
#define DISPID_WMPCOREEVENT_CURRENTPLAYLISTITEMAVAILABLE           5805
#define DISPID_WMPCOREEVENT_CURRENTITEMCHANGE                      5806
#define DISPID_WMPCOREEVENT_MEDIACOLLECTIONCHANGE                  5807
#define DISPID_WMPCOREEVENT_MEDIACOLLECTIONATTRIBUTESTRINGADDED    5808
#define DISPID_WMPCOREEVENT_MEDIACOLLECTIONATTRIBUTESTRINGREMOVED  5809
#define DISPID_WMPCOREEVENT_PLAYLISTCOLLECTIONCHANGE               5810
#define DISPID_WMPCOREEVENT_PLAYLISTCOLLECTIONPLAYLISTADDED        5811
#define DISPID_WMPCOREEVENT_PLAYLISTCOLLECTIONPLAYLISTREMOVED      5812
#define DISPID_WMPCOREEVENT_PLAYLISTCOLLECTIONPLAYLISTSETASDELETED 5818
#define DISPID_WMPCOREEVENT_MODECHANGE                             5819
#define DISPID_WMPCOREEVENT_MEDIACOLLECTIONATTRIBUTESTRINGCHANGED  5820
#define DISPID_WMPCOREEVENT_MEDIAERROR                             5821
#define DISPID_WMPCOREEVENT_DOMAINCHANGE                           5822
#define DISPID_WMPCOREEVENT_OPENPLAYLISTSWITCH                     5823

#define DISPID_WMPOCXEVENT_SWITCHEDTOPLAYERAPPLICATION 6501
#define DISPID_WMPOCXEVENT_SWITCHEDTOCONTROL           6502
#define DISPID_WMPOCXEVENT_PLAYERDOCKEDSTATECHANGE     6503
#define DISPID_WMPOCXEVENT_PLAYERRECONNECT             6504
#define DISPID_WMPOCXEVENT_CLICK                       6505
#define DISPID_WMPOCXEVENT_DOUBLECLICK                 6506
#define DISPID_WMPOCXEVENT_KEYDOWN                     6507
#define DISPID_WMPOCXEVENT_KEYPRESS                    6508
#define DISPID_WMPOCXEVENT_KEYUP                       6509
#define DISPID_WMPOCXEVENT_MOUSEDOWN                   6510
#define DISPID_WMPOCXEVENT_MOUSEMOVE                   6511
#define DISPID_WMPOCXEVENT_MOUSEUP                     6512
