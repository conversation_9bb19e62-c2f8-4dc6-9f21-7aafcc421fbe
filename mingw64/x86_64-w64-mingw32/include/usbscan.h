/*
 * usbscan.h
 *
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> within this package.
 */

#ifndef _USBSCAN_H_
#define _USBSCAN_H_

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION (WINAPI_PARTITION_DESKTOP)

#ifndef MAX_NUM_PIPES
#define MAX_NUM_PIPES 8
#endif

#define BULKIN_FLAG 0x80

#pragma pack(push, 8)
typedef struct _DRV_VERSION {
  unsigned major;
  unsigned minor;
  unsigned internal;
} DRV_VERSION,*PDRV_VERSION;

typedef struct _IO_BLOCK {
  unsigned uOffset;
  unsigned uLength;
  PUCHAR pbyData;
  unsigned uIndex;
} IO_BLOCK,*PIO_BLOCK;

typedef struct _IO_BLOCK_EX {
  unsigned uOffset;
  unsigned uLength;
  PUCHAR pbyData;
  unsigned uIndex;
  UCHAR bRequest;
  <PERSON><PERSON><PERSON> bmRequestType;
  UCHAR fTransferDirectionIn;
} IO_BLOCK_EX,*PIO_BLOCK_EX;

typedef struct _CHANNEL_INFO {
  unsigned EventChannelSize;
  unsigned uReadDataAlignment;
  unsigned uWriteDataAlignment;
} CHANNEL_INFO,*PCHANNEL_INFO;

typedef enum {
  EVENT_PIPE,
  READ_DATA_PIPE,
  WRITE_DATA_PIPE,
  ALL_PIPE
} PIPE_TYPE;

typedef struct _USBSCAN_GET_DESCRIPTOR {
  UCHAR DescriptorType;
  UCHAR Index;
  USHORT LanguageId;
} USBSCAN_GET_DESCRIPTOR,*PUSBSCAN_GET_DESCRIPTOR;

typedef struct _DEVICE_DESCRIPTOR {
  USHORT usVendorId;
  USHORT usProductId;
  USHORT usBcdDevice;
  USHORT usLanguageId;
} DEVICE_DESCRIPTOR,*PDEVICE_DESCRIPTOR;

typedef enum _RAW_PIPE_TYPE {
  USBSCAN_PIPE_CONTROL,
  USBSCAN_PIPE_ISOCHRONOUS,
  USBSCAN_PIPE_BULK,
  USBSCAN_PIPE_INTERRUPT
} RAW_PIPE_TYPE;

typedef struct _USBSCAN_PIPE_INFORMATION {
  USHORT MaximumPacketSize;
  UCHAR EndpointAddress;
  UCHAR Interval;
  RAW_PIPE_TYPE PipeType;
} USBSCAN_PIPE_INFORMATION,*PUSBSCAN_PIPE_INFORMATION;

typedef struct _USBSCAN_PIPE_CONFIGURATION {
  ULONG NumberOfPipes;
  USBSCAN_PIPE_INFORMATION PipeInfo[MAX_NUM_PIPES];
} USBSCAN_PIPE_CONFIGURATION,*PUSBSCAN_PIPE_CONFIGURATION;

typedef struct _USBSCAN_TIMEOUT {
  ULONG TimeoutRead;
  ULONG TimeoutWrite;
  ULONG TimeoutEvent;
} USBSCAN_TIMEOUT,*PUSBSCAN_TIMEOUT;
#pragma pack(pop)

#define FILE_DEVICE_USB_SCAN 0x8000
#define IOCTL_INDEX 0x0800
#define ALL ALL_PIPE
#define IOCTL_ABORT_PIPE IOCTL_CANCEL_IO

#define IOCTL_GET_VERSION CTL_CODE (FILE_DEVICE_USB_SCAN, IOCTL_INDEX, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_CANCEL_IO CTL_CODE (FILE_DEVICE_USB_SCAN, IOCTL_INDEX+1, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_WAIT_ON_DEVICE_EVENT CTL_CODE (FILE_DEVICE_USB_SCAN, IOCTL_INDEX+2, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_READ_REGISTERS CTL_CODE (FILE_DEVICE_USB_SCAN, IOCTL_INDEX+3, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_WRITE_REGISTERS CTL_CODE (FILE_DEVICE_USB_SCAN, IOCTL_INDEX+4, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_GET_CHANNEL_ALIGN_RQST CTL_CODE (FILE_DEVICE_USB_SCAN, IOCTL_INDEX+5, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_GET_DEVICE_DESCRIPTOR CTL_CODE (FILE_DEVICE_USB_SCAN, IOCTL_INDEX+6, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RESET_PIPE CTL_CODE (FILE_DEVICE_USB_SCAN, IOCTL_INDEX+7, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_GET_USB_DESCRIPTOR CTL_CODE (FILE_DEVICE_USB_SCAN, IOCTL_INDEX+8, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_SEND_USB_REQUEST CTL_CODE (FILE_DEVICE_USB_SCAN, IOCTL_INDEX+9, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_GET_PIPE_CONFIGURATION CTL_CODE (FILE_DEVICE_USB_SCAN, IOCTL_INDEX+10, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_SET_TIMEOUT CTL_CODE (FILE_DEVICE_USB_SCAN, IOCTL_INDEX+11, METHOD_BUFFERED, FILE_ANY_ACCESS)

#endif

#endif
