/*
 * Copyright 2021 Zebediah Figura for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "oaidl.idl";
import "ocidl.idl";

[
    object,
    local,
    uuid(d9b67d36-a9ad-4eb4-baef-db284ef5504c),
]
interface IWMAuthorizer : IUnknown
{
    HRESULT GetCertCount(DWORD *count);
    HRESULT GetCert(DWORD index, BYTE **data);
    HRESULT GetSharedData(DWORD index, const BYTE *data, BYTE *cert, BYTE **ret_data);
}

[
    object,
    local,
    uuid(2720598a-d0f2-4189-bd10-91c46ef0936f),
]
interface IWMSecureChannel : IWMAuthorizer
{
    HRESULT WMSC_AddCertificate(IWMAuthorizer *cert);
    HRESULT WMSC_AddSignature(BYTE *signature, DWORD size);
    HRESULT WMSC_Connect(IWMSecureChannel *peer);
    HRESULT WMSC_IsConnected(BOOL *connected);
    HRESULT WMSC_Disconnect();
    HRESULT WMSC_GetValidCertificate(BYTE **cert, DWORD *signature);
    HRESULT WMSC_Encrypt(BYTE *data, DWORD size);
    HRESULT WMSC_Decrypt(BYTE *data, DWORD size);
    HRESULT WMSC_Lock();
    HRESULT WMSC_Unlock();
    HRESULT WMSC_SetSharedData(DWORD index, BYTE *data);
}

[
    object,
    local,
    uuid(94bc0598-c3d2-11d3-bedf-00c04f612986),
]
interface IWMGetSecureChannel : IUnknown
{
    HRESULT GetPeerSecureChannelInterface(IWMSecureChannel **channel);
}
