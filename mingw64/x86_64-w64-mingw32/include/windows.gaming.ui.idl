/*
 * Copyright 2022 <PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

#ifndef DO_NO_IMPORTS
import "inspectable.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";
#endif

namespace Windows.Gaming.UI {
    runtimeclass GameBar;

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.Gaming.UI.GameBar),
        uuid(1db9a292-cc78-4173-be45-b61e67283ea7)
    ]
    interface IGameBarStatics : IInspectable
    {
        [eventadd] HRESULT VisibilityChanged([in] Windows.Foundation.EventHandler<IInspectable *> *handler, [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT VisibilityChanged([in] EventRegistrationToken token);
        [eventadd] HRESULT IsInputRedirectedChanged([in] Windows.Foundation.EventHandler<IInspectable *> *handler, [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT IsInputRedirectedChanged([in] EventRegistrationToken token);
        [propget] HRESULT Visible([out, retval] boolean *value);
        [propget] HRESULT IsInputRedirected([out, retval] boolean *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        static(Windows.Gaming.UI.IGameBarStatics, Windows.Foundation.UniversalApiContract, 2.0),
        threading(both)
    ]
    runtimeclass GameBar
    {
    }
}
