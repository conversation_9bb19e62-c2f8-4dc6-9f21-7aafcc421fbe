/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#ifndef WSCAPI_H
#define WSCAPI_H

#include <winapifamily.h>

#if WIN<PERSON>I_FAMILY_PARTITION(WIN<PERSON>I_PARTITION_DESKTOP)

#if _WIN32_WINNT >= 0x0600
typedef enum _WSC_SECURITY_PROVIDER {
    WSC_SECURITY_PROVIDER_FIREWALL = 0x1,
    WSC_SECURITY_PROVIDER_AUTOUPDATE_SETTINGS = 0x2,
    WSC_SECURITY_PROVIDER_ANTIVIRUS = 0x4,
    WSC_SECURITY_PROVIDER_ANTISPYWARE = 0x8,
    WSC_SECURITY_PROVIDER_INTERNET_SETTINGS = 0x10,
    WSC_SECURITY_PROVIDER_USER_ACCOUNT_CONTROL = 0x20,
    WSC_SECURITY_PROVIDER_SERVICE = 0x40,
    WSC_SECURITY_PROVIDER_NONE = 0,
    WSC_SECURITY_PROVIDER_ALL = WSC_SECURITY_PROVIDER_FIREWALL | WSC_SECURITY_PROVIDER_AUTOUPDATE_SETTINGS | WSC_SECURITY_PROVIDER_ANTIVIRUS | WSC_SECURITY_PROVIDER_ANTISPYWARE | WSC_SECURITY_PROVIDER_INTERNET_SETTINGS | WSC_SECURITY_PROVIDER_USER_ACCOUNT_CONTROL | WSC_SECURITY_PROVIDER_SERVICE
} WSC_SECURITY_PROVIDER, *PWSC_SECURITY_PROVIDER;

typedef enum _WSC_SECURITY_PROVIDER_HEALTH {
    WSC_SECURITY_PROVIDER_HEALTH_GOOD,
    WSC_SECURITY_PROVIDER_HEALTH_NOTMONITORED,
    WSC_SECURITY_PROVIDER_HEALTH_POOR,
    WSC_SECURITY_PROVIDER_HEALTH_SNOOZE
} WSC_SECURITY_PROVIDER_HEALTH, *PWSC_SECURITY_PROVIDER_HEALTH;

STDAPI WscRegisterForChanges(LPVOID Reserved, PHANDLE phCallbackRegistration, LPTHREAD_START_ROUTINE lpCallbackAddress, PVOID pContext);
STDAPI WscUnRegisterChanges(HANDLE hRegistrationHandle);
STDAPI WscRegisterForUserNotifications(void);
STDAPI WscGetSecurityProviderHealth(DWORD Providers, PWSC_SECURITY_PROVIDER_HEALTH pHealth);
HRESULT wscShowAMSCN(DWORD InputFlags, PDWORD pdwResultFlags);
HRESULT wscLaunchAdminMakeDefaultUI(PCWSTR pwszProductName);
STDAPI WscQueryAntiMalwareUri(void);
STDAPI WscGetAntiMalwareUri(LPWSTR *ppszUri);

#endif /* _WIN32_WINNT >= 0x0600 */

#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */

#endif /* WSCAPI_H */
