/*** Autogenerated by WIDL 10.8 from include/wia_lh.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __wia_lh_h__
#define __wia_lh_h__

/* Forward declarations */

#ifndef __IWiaDevMgr_FWD_DEFINED__
#define __IWiaDevMgr_FWD_DEFINED__
typedef interface IWiaDevMgr IWiaDevMgr;
#ifdef __cplusplus
interface IWiaDevMgr;
#endif /* __cplusplus */
#endif

#ifndef __IEnumWIA_DEV_INFO_FWD_DEFINED__
#define __IEnumWIA_DEV_INFO_FWD_DEFINED__
typedef interface IEnumWIA_DEV_INFO IEnumWIA_DEV_INFO;
#ifdef __cplusplus
interface IEnumWIA_DEV_INFO;
#endif /* __cplusplus */
#endif

#ifndef __IWiaPropertyStorage_FWD_DEFINED__
#define __IWiaPropertyStorage_FWD_DEFINED__
typedef interface IWiaPropertyStorage IWiaPropertyStorage;
#ifdef __cplusplus
interface IWiaPropertyStorage;
#endif /* __cplusplus */
#endif

#ifndef __IWiaItem_FWD_DEFINED__
#define __IWiaItem_FWD_DEFINED__
typedef interface IWiaItem IWiaItem;
#ifdef __cplusplus
interface IWiaItem;
#endif /* __cplusplus */
#endif

#ifndef __IWiaEventCallback_FWD_DEFINED__
#define __IWiaEventCallback_FWD_DEFINED__
typedef interface IWiaEventCallback IWiaEventCallback;
#ifdef __cplusplus
interface IWiaEventCallback;
#endif /* __cplusplus */
#endif

#ifndef __IEnumWIA_DEV_CAPS_FWD_DEFINED__
#define __IEnumWIA_DEV_CAPS_FWD_DEFINED__
typedef interface IEnumWIA_DEV_CAPS IEnumWIA_DEV_CAPS;
#ifdef __cplusplus
interface IEnumWIA_DEV_CAPS;
#endif /* __cplusplus */
#endif

#ifndef __IWiaTransferCallback_FWD_DEFINED__
#define __IWiaTransferCallback_FWD_DEFINED__
typedef interface IWiaTransferCallback IWiaTransferCallback;
#ifdef __cplusplus
interface IWiaTransferCallback;
#endif /* __cplusplus */
#endif

#ifndef __IWiaPreview_FWD_DEFINED__
#define __IWiaPreview_FWD_DEFINED__
typedef interface IWiaPreview IWiaPreview;
#ifdef __cplusplus
interface IWiaPreview;
#endif /* __cplusplus */
#endif

#ifndef __IEnumWiaItem2_FWD_DEFINED__
#define __IEnumWiaItem2_FWD_DEFINED__
typedef interface IEnumWiaItem2 IEnumWiaItem2;
#ifdef __cplusplus
interface IEnumWiaItem2;
#endif /* __cplusplus */
#endif

#ifndef __IWiaItem2_FWD_DEFINED__
#define __IWiaItem2_FWD_DEFINED__
typedef interface IWiaItem2 IWiaItem2;
#ifdef __cplusplus
interface IWiaItem2;
#endif /* __cplusplus */
#endif

#ifndef __IWiaDevMgr2_FWD_DEFINED__
#define __IWiaDevMgr2_FWD_DEFINED__
typedef interface IWiaDevMgr2 IWiaDevMgr2;
#ifdef __cplusplus
interface IWiaDevMgr2;
#endif /* __cplusplus */
#endif

#ifndef __WiaDevMgr2_FWD_DEFINED__
#define __WiaDevMgr2_FWD_DEFINED__
#ifdef __cplusplus
typedef class WiaDevMgr2 WiaDevMgr2;
#else
typedef struct WiaDevMgr2 WiaDevMgr2;
#endif /* defined __cplusplus */
#endif /* defined __WiaDevMgr2_FWD_DEFINED__ */

/* Headers for imported files */

#include <unknwn.h>
#include <oaidl.h>
#include <propidl.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef struct _WIA_DITHER_PATTERN_DATA {
    LONG lSize;
    BSTR bstrPatternName;
    LONG lPatternWidth;
    LONG lPatternLength;
    LONG cbPattern;
    BYTE *pbPattern;
} WIA_DITHER_PATTERN_DATA;
typedef struct _WIA_DITHER_PATTERN_DATA *PWIA_DITHER_PATTERN_DATA;
typedef struct _WIA_PROPID_TO_NAME {
    PROPID propid;
    LPOLESTR pszName;
} WIA_PROPID_TO_NAME;
typedef struct _WIA_PROPID_TO_NAME *PWIA_PROPID_TO_NAME;
typedef struct _WIA_FORMAT_INFO {
    GUID guidFormatID;
    LONG lTymed;
} WIA_FORMAT_INFO;
typedef struct _WIA_FORMAT_INFO *PWIA_FORMAT_INFO;
#include <wiadef.h>
#ifndef __IEnumWIA_DEV_INFO_FWD_DEFINED__
#define __IEnumWIA_DEV_INFO_FWD_DEFINED__
typedef interface IEnumWIA_DEV_INFO IEnumWIA_DEV_INFO;
#ifdef __cplusplus
interface IEnumWIA_DEV_INFO;
#endif /* __cplusplus */
#endif

#ifndef __IWiaPropertyStorage_FWD_DEFINED__
#define __IWiaPropertyStorage_FWD_DEFINED__
typedef interface IWiaPropertyStorage IWiaPropertyStorage;
#ifdef __cplusplus
interface IWiaPropertyStorage;
#endif /* __cplusplus */
#endif

#ifndef __IWiaItem_FWD_DEFINED__
#define __IWiaItem_FWD_DEFINED__
typedef interface IWiaItem IWiaItem;
#ifdef __cplusplus
interface IWiaItem;
#endif /* __cplusplus */
#endif

#ifndef __IWiaEventCallback_FWD_DEFINED__
#define __IWiaEventCallback_FWD_DEFINED__
typedef interface IWiaEventCallback IWiaEventCallback;
#ifdef __cplusplus
interface IWiaEventCallback;
#endif /* __cplusplus */
#endif

#ifndef __IEnumWIA_DEV_CAPS_FWD_DEFINED__
#define __IEnumWIA_DEV_CAPS_FWD_DEFINED__
typedef interface IEnumWIA_DEV_CAPS IEnumWIA_DEV_CAPS;
#ifdef __cplusplus
interface IEnumWIA_DEV_CAPS;
#endif /* __cplusplus */
#endif

#ifndef __IWiaTransferCallback_FWD_DEFINED__
#define __IWiaTransferCallback_FWD_DEFINED__
typedef interface IWiaTransferCallback IWiaTransferCallback;
#ifdef __cplusplus
interface IWiaTransferCallback;
#endif /* __cplusplus */
#endif

#ifndef __IWiaPreview_FWD_DEFINED__
#define __IWiaPreview_FWD_DEFINED__
typedef interface IWiaPreview IWiaPreview;
#ifdef __cplusplus
interface IWiaPreview;
#endif /* __cplusplus */
#endif

#ifndef __IEnumWiaItem2_FWD_DEFINED__
#define __IEnumWiaItem2_FWD_DEFINED__
typedef interface IEnumWiaItem2 IEnumWiaItem2;
#ifdef __cplusplus
interface IEnumWiaItem2;
#endif /* __cplusplus */
#endif

#ifndef __IWiaItem2_FWD_DEFINED__
#define __IWiaItem2_FWD_DEFINED__
typedef interface IWiaItem2 IWiaItem2;
#ifdef __cplusplus
interface IWiaItem2;
#endif /* __cplusplus */
#endif

#ifndef __IWiaDevMgr2_FWD_DEFINED__
#define __IWiaDevMgr2_FWD_DEFINED__
typedef interface IWiaDevMgr2 IWiaDevMgr2;
#ifdef __cplusplus
interface IWiaDevMgr2;
#endif /* __cplusplus */
#endif

DEFINE_GUID(CLSID_WiaDevMgr, 0xa1f4e726,0x8cf1,0x11d1,0xbf,0x92,0x00,0x60,0x08,0x1e,0xd8,0x11);
/*****************************************************************************
 * IWiaDevMgr interface
 */
#ifndef __IWiaDevMgr_INTERFACE_DEFINED__
#define __IWiaDevMgr_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWiaDevMgr, 0x5eb2502a, 0x8cf1, 0x11d1, 0xbf,0x92, 0x00,0x60,0x08,0x1e,0xd8,0x11);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5eb2502a-8cf1-11d1-bf92-0060081ed811")
IWiaDevMgr : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE EnumDeviceInfo(
        LONG lFlag,
        IEnumWIA_DEV_INFO **ppIEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDevice(
        BSTR bstrDeviceID,
        IWiaItem **ppWiaItemRoot) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectDeviceDlg(
        HWND hwndParent,
        LONG lDeviceType,
        LONG lFlags,
        BSTR *pbstrDeviceID,
        IWiaItem **ppItemRoot) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectDeviceDlgID(
        HWND hwndParent,
        LONG lDeviceType,
        LONG lFlags,
        BSTR *pbstrDeviceID) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetImageDlg(
        HWND hwndParent,
        LONG lDeviceType,
        LONG lFlags,
        LONG lIntent,
        IWiaItem *pItemRoot,
        BSTR bstrFilename,
        GUID *pguidFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterEventCallbackProgram(
        LONG lFlags,
        BSTR bstrDeviceID,
        const GUID *pEventGUID,
        BSTR bstrCommandline,
        BSTR bstrName,
        BSTR bstrDescription,
        BSTR bstrIcon) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterEventCallbackInterface(
        LONG lFlags,
        BSTR bstrDeviceID,
        const GUID *pEventGUID,
        IWiaEventCallback *pIWiaEventCallback,
        IUnknown **pEventObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterEventCallbackCLSID(
        LONG lFlags,
        BSTR bstrDeviceID,
        const GUID *pEventGUID,
        const GUID *pClsID,
        BSTR bstrName,
        BSTR bstrDescription,
        BSTR bstrIcon) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddDeviceDlg(
        HWND hwndParent,
        LONG lFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWiaDevMgr, 0x5eb2502a, 0x8cf1, 0x11d1, 0xbf,0x92, 0x00,0x60,0x08,0x1e,0xd8,0x11)
#endif
#else
typedef struct IWiaDevMgrVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWiaDevMgr *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWiaDevMgr *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWiaDevMgr *This);

    /*** IWiaDevMgr methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumDeviceInfo)(
        IWiaDevMgr *This,
        LONG lFlag,
        IEnumWIA_DEV_INFO **ppIEnum);

    HRESULT (STDMETHODCALLTYPE *CreateDevice)(
        IWiaDevMgr *This,
        BSTR bstrDeviceID,
        IWiaItem **ppWiaItemRoot);

    HRESULT (STDMETHODCALLTYPE *SelectDeviceDlg)(
        IWiaDevMgr *This,
        HWND hwndParent,
        LONG lDeviceType,
        LONG lFlags,
        BSTR *pbstrDeviceID,
        IWiaItem **ppItemRoot);

    HRESULT (STDMETHODCALLTYPE *SelectDeviceDlgID)(
        IWiaDevMgr *This,
        HWND hwndParent,
        LONG lDeviceType,
        LONG lFlags,
        BSTR *pbstrDeviceID);

    HRESULT (STDMETHODCALLTYPE *GetImageDlg)(
        IWiaDevMgr *This,
        HWND hwndParent,
        LONG lDeviceType,
        LONG lFlags,
        LONG lIntent,
        IWiaItem *pItemRoot,
        BSTR bstrFilename,
        GUID *pguidFormat);

    HRESULT (STDMETHODCALLTYPE *RegisterEventCallbackProgram)(
        IWiaDevMgr *This,
        LONG lFlags,
        BSTR bstrDeviceID,
        const GUID *pEventGUID,
        BSTR bstrCommandline,
        BSTR bstrName,
        BSTR bstrDescription,
        BSTR bstrIcon);

    HRESULT (STDMETHODCALLTYPE *RegisterEventCallbackInterface)(
        IWiaDevMgr *This,
        LONG lFlags,
        BSTR bstrDeviceID,
        const GUID *pEventGUID,
        IWiaEventCallback *pIWiaEventCallback,
        IUnknown **pEventObject);

    HRESULT (STDMETHODCALLTYPE *RegisterEventCallbackCLSID)(
        IWiaDevMgr *This,
        LONG lFlags,
        BSTR bstrDeviceID,
        const GUID *pEventGUID,
        const GUID *pClsID,
        BSTR bstrName,
        BSTR bstrDescription,
        BSTR bstrIcon);

    HRESULT (STDMETHODCALLTYPE *AddDeviceDlg)(
        IWiaDevMgr *This,
        HWND hwndParent,
        LONG lFlags);

    END_INTERFACE
} IWiaDevMgrVtbl;

interface IWiaDevMgr {
    CONST_VTBL IWiaDevMgrVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWiaDevMgr_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWiaDevMgr_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWiaDevMgr_Release(This) (This)->lpVtbl->Release(This)
/*** IWiaDevMgr methods ***/
#define IWiaDevMgr_EnumDeviceInfo(This,lFlag,ppIEnum) (This)->lpVtbl->EnumDeviceInfo(This,lFlag,ppIEnum)
#define IWiaDevMgr_CreateDevice(This,bstrDeviceID,ppWiaItemRoot) (This)->lpVtbl->CreateDevice(This,bstrDeviceID,ppWiaItemRoot)
#define IWiaDevMgr_SelectDeviceDlg(This,hwndParent,lDeviceType,lFlags,pbstrDeviceID,ppItemRoot) (This)->lpVtbl->SelectDeviceDlg(This,hwndParent,lDeviceType,lFlags,pbstrDeviceID,ppItemRoot)
#define IWiaDevMgr_SelectDeviceDlgID(This,hwndParent,lDeviceType,lFlags,pbstrDeviceID) (This)->lpVtbl->SelectDeviceDlgID(This,hwndParent,lDeviceType,lFlags,pbstrDeviceID)
#define IWiaDevMgr_GetImageDlg(This,hwndParent,lDeviceType,lFlags,lIntent,pItemRoot,bstrFilename,pguidFormat) (This)->lpVtbl->GetImageDlg(This,hwndParent,lDeviceType,lFlags,lIntent,pItemRoot,bstrFilename,pguidFormat)
#define IWiaDevMgr_RegisterEventCallbackProgram(This,lFlags,bstrDeviceID,pEventGUID,bstrCommandline,bstrName,bstrDescription,bstrIcon) (This)->lpVtbl->RegisterEventCallbackProgram(This,lFlags,bstrDeviceID,pEventGUID,bstrCommandline,bstrName,bstrDescription,bstrIcon)
#define IWiaDevMgr_RegisterEventCallbackInterface(This,lFlags,bstrDeviceID,pEventGUID,pIWiaEventCallback,pEventObject) (This)->lpVtbl->RegisterEventCallbackInterface(This,lFlags,bstrDeviceID,pEventGUID,pIWiaEventCallback,pEventObject)
#define IWiaDevMgr_RegisterEventCallbackCLSID(This,lFlags,bstrDeviceID,pEventGUID,pClsID,bstrName,bstrDescription,bstrIcon) (This)->lpVtbl->RegisterEventCallbackCLSID(This,lFlags,bstrDeviceID,pEventGUID,pClsID,bstrName,bstrDescription,bstrIcon)
#define IWiaDevMgr_AddDeviceDlg(This,hwndParent,lFlags) (This)->lpVtbl->AddDeviceDlg(This,hwndParent,lFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT IWiaDevMgr_QueryInterface(IWiaDevMgr* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWiaDevMgr_AddRef(IWiaDevMgr* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWiaDevMgr_Release(IWiaDevMgr* This) {
    return This->lpVtbl->Release(This);
}
/*** IWiaDevMgr methods ***/
static inline HRESULT IWiaDevMgr_EnumDeviceInfo(IWiaDevMgr* This,LONG lFlag,IEnumWIA_DEV_INFO **ppIEnum) {
    return This->lpVtbl->EnumDeviceInfo(This,lFlag,ppIEnum);
}
static inline HRESULT IWiaDevMgr_CreateDevice(IWiaDevMgr* This,BSTR bstrDeviceID,IWiaItem **ppWiaItemRoot) {
    return This->lpVtbl->CreateDevice(This,bstrDeviceID,ppWiaItemRoot);
}
static inline HRESULT IWiaDevMgr_SelectDeviceDlg(IWiaDevMgr* This,HWND hwndParent,LONG lDeviceType,LONG lFlags,BSTR *pbstrDeviceID,IWiaItem **ppItemRoot) {
    return This->lpVtbl->SelectDeviceDlg(This,hwndParent,lDeviceType,lFlags,pbstrDeviceID,ppItemRoot);
}
static inline HRESULT IWiaDevMgr_SelectDeviceDlgID(IWiaDevMgr* This,HWND hwndParent,LONG lDeviceType,LONG lFlags,BSTR *pbstrDeviceID) {
    return This->lpVtbl->SelectDeviceDlgID(This,hwndParent,lDeviceType,lFlags,pbstrDeviceID);
}
static inline HRESULT IWiaDevMgr_GetImageDlg(IWiaDevMgr* This,HWND hwndParent,LONG lDeviceType,LONG lFlags,LONG lIntent,IWiaItem *pItemRoot,BSTR bstrFilename,GUID *pguidFormat) {
    return This->lpVtbl->GetImageDlg(This,hwndParent,lDeviceType,lFlags,lIntent,pItemRoot,bstrFilename,pguidFormat);
}
static inline HRESULT IWiaDevMgr_RegisterEventCallbackProgram(IWiaDevMgr* This,LONG lFlags,BSTR bstrDeviceID,const GUID *pEventGUID,BSTR bstrCommandline,BSTR bstrName,BSTR bstrDescription,BSTR bstrIcon) {
    return This->lpVtbl->RegisterEventCallbackProgram(This,lFlags,bstrDeviceID,pEventGUID,bstrCommandline,bstrName,bstrDescription,bstrIcon);
}
static inline HRESULT IWiaDevMgr_RegisterEventCallbackInterface(IWiaDevMgr* This,LONG lFlags,BSTR bstrDeviceID,const GUID *pEventGUID,IWiaEventCallback *pIWiaEventCallback,IUnknown **pEventObject) {
    return This->lpVtbl->RegisterEventCallbackInterface(This,lFlags,bstrDeviceID,pEventGUID,pIWiaEventCallback,pEventObject);
}
static inline HRESULT IWiaDevMgr_RegisterEventCallbackCLSID(IWiaDevMgr* This,LONG lFlags,BSTR bstrDeviceID,const GUID *pEventGUID,const GUID *pClsID,BSTR bstrName,BSTR bstrDescription,BSTR bstrIcon) {
    return This->lpVtbl->RegisterEventCallbackCLSID(This,lFlags,bstrDeviceID,pEventGUID,pClsID,bstrName,bstrDescription,bstrIcon);
}
static inline HRESULT IWiaDevMgr_AddDeviceDlg(IWiaDevMgr* This,HWND hwndParent,LONG lFlags) {
    return This->lpVtbl->AddDeviceDlg(This,hwndParent,lFlags);
}
#endif
#endif

#endif


#endif  /* __IWiaDevMgr_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumWIA_DEV_INFO interface
 */
#ifndef __IEnumWIA_DEV_INFO_INTERFACE_DEFINED__
#define __IEnumWIA_DEV_INFO_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumWIA_DEV_INFO, 0x5e38b83c, 0x8cf1, 0x11d1, 0xbf,0x92, 0x00,0x60,0x08,0x1e,0xd8,0x11);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5e38b83c-8cf1-11d1-bf92-0060081ed811")
IEnumWIA_DEV_INFO : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        IWiaPropertyStorage **rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumWIA_DEV_INFO **ppIEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCount(
        ULONG *celt) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumWIA_DEV_INFO, 0x5e38b83c, 0x8cf1, 0x11d1, 0xbf,0x92, 0x00,0x60,0x08,0x1e,0xd8,0x11)
#endif
#else
typedef struct IEnumWIA_DEV_INFOVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumWIA_DEV_INFO *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumWIA_DEV_INFO *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumWIA_DEV_INFO *This);

    /*** IEnumWIA_DEV_INFO methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumWIA_DEV_INFO *This,
        ULONG celt,
        IWiaPropertyStorage **rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumWIA_DEV_INFO *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumWIA_DEV_INFO *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumWIA_DEV_INFO *This,
        IEnumWIA_DEV_INFO **ppIEnum);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IEnumWIA_DEV_INFO *This,
        ULONG *celt);

    END_INTERFACE
} IEnumWIA_DEV_INFOVtbl;

interface IEnumWIA_DEV_INFO {
    CONST_VTBL IEnumWIA_DEV_INFOVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumWIA_DEV_INFO_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumWIA_DEV_INFO_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumWIA_DEV_INFO_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumWIA_DEV_INFO methods ***/
#define IEnumWIA_DEV_INFO_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumWIA_DEV_INFO_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumWIA_DEV_INFO_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumWIA_DEV_INFO_Clone(This,ppIEnum) (This)->lpVtbl->Clone(This,ppIEnum)
#define IEnumWIA_DEV_INFO_GetCount(This,celt) (This)->lpVtbl->GetCount(This,celt)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumWIA_DEV_INFO_QueryInterface(IEnumWIA_DEV_INFO* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumWIA_DEV_INFO_AddRef(IEnumWIA_DEV_INFO* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumWIA_DEV_INFO_Release(IEnumWIA_DEV_INFO* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumWIA_DEV_INFO methods ***/
static inline HRESULT IEnumWIA_DEV_INFO_Next(IEnumWIA_DEV_INFO* This,ULONG celt,IWiaPropertyStorage **rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static inline HRESULT IEnumWIA_DEV_INFO_Skip(IEnumWIA_DEV_INFO* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumWIA_DEV_INFO_Reset(IEnumWIA_DEV_INFO* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumWIA_DEV_INFO_Clone(IEnumWIA_DEV_INFO* This,IEnumWIA_DEV_INFO **ppIEnum) {
    return This->lpVtbl->Clone(This,ppIEnum);
}
static inline HRESULT IEnumWIA_DEV_INFO_GetCount(IEnumWIA_DEV_INFO* This,ULONG *celt) {
    return This->lpVtbl->GetCount(This,celt);
}
#endif
#endif

#endif


#endif  /* __IEnumWIA_DEV_INFO_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWiaPropertyStorage interface
 */
#ifndef __IWiaPropertyStorage_INTERFACE_DEFINED__
#define __IWiaPropertyStorage_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWiaPropertyStorage, 0x98b5e8a0, 0x29cc, 0x491a, 0xaa,0xc0, 0xe6,0xdb,0x4f,0xdc,0xce,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("98b5e8a0-29cc-491a-aac0-e6db4fdcceb6")
IWiaPropertyStorage : public IUnknown
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWiaPropertyStorage, 0x98b5e8a0, 0x29cc, 0x491a, 0xaa,0xc0, 0xe6,0xdb,0x4f,0xdc,0xce,0xb6)
#endif
#else
typedef struct IWiaPropertyStorageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWiaPropertyStorage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWiaPropertyStorage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWiaPropertyStorage *This);

    END_INTERFACE
} IWiaPropertyStorageVtbl;

interface IWiaPropertyStorage {
    CONST_VTBL IWiaPropertyStorageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWiaPropertyStorage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWiaPropertyStorage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWiaPropertyStorage_Release(This) (This)->lpVtbl->Release(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWiaPropertyStorage_QueryInterface(IWiaPropertyStorage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWiaPropertyStorage_AddRef(IWiaPropertyStorage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWiaPropertyStorage_Release(IWiaPropertyStorage* This) {
    return This->lpVtbl->Release(This);
}
#endif
#endif

#endif


#endif  /* __IWiaPropertyStorage_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWiaItem interface
 */
#ifndef __IWiaItem_INTERFACE_DEFINED__
#define __IWiaItem_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWiaItem, 0x4db1ad10, 0x3391, 0x11d2, 0x9a,0x33, 0x00,0xc0,0x4f,0xa3,0x61,0x45);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4db1ad10-3391-11d2-9a33-00c04fa36145")
IWiaItem : public IUnknown
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWiaItem, 0x4db1ad10, 0x3391, 0x11d2, 0x9a,0x33, 0x00,0xc0,0x4f,0xa3,0x61,0x45)
#endif
#else
typedef struct IWiaItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWiaItem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWiaItem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWiaItem *This);

    END_INTERFACE
} IWiaItemVtbl;

interface IWiaItem {
    CONST_VTBL IWiaItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWiaItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWiaItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWiaItem_Release(This) (This)->lpVtbl->Release(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWiaItem_QueryInterface(IWiaItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWiaItem_AddRef(IWiaItem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWiaItem_Release(IWiaItem* This) {
    return This->lpVtbl->Release(This);
}
#endif
#endif

#endif


#endif  /* __IWiaItem_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWiaEventCallback interface
 */
#ifndef __IWiaEventCallback_INTERFACE_DEFINED__
#define __IWiaEventCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWiaEventCallback, 0xae6287b0, 0x0084, 0x11d2, 0x97,0x3b, 0x00,0xa0,0xc9,0x06,0x8f,0x2e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ae6287b0-0084-11d2-973b-00a0c9068f2e")
IWiaEventCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ImageEventCallback(
        const GUID *pEventGUID,
        BSTR bstrEventDescription,
        BSTR bstrDeviceID,
        BSTR bstrDeviceDescription,
        DWORD dwDeviceType,
        BSTR bstrFullItemName,
        ULONG *pulEventType,
        ULONG ulReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWiaEventCallback, 0xae6287b0, 0x0084, 0x11d2, 0x97,0x3b, 0x00,0xa0,0xc9,0x06,0x8f,0x2e)
#endif
#else
typedef struct IWiaEventCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWiaEventCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWiaEventCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWiaEventCallback *This);

    /*** IWiaEventCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *ImageEventCallback)(
        IWiaEventCallback *This,
        const GUID *pEventGUID,
        BSTR bstrEventDescription,
        BSTR bstrDeviceID,
        BSTR bstrDeviceDescription,
        DWORD dwDeviceType,
        BSTR bstrFullItemName,
        ULONG *pulEventType,
        ULONG ulReserved);

    END_INTERFACE
} IWiaEventCallbackVtbl;

interface IWiaEventCallback {
    CONST_VTBL IWiaEventCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWiaEventCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWiaEventCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWiaEventCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IWiaEventCallback methods ***/
#define IWiaEventCallback_ImageEventCallback(This,pEventGUID,bstrEventDescription,bstrDeviceID,bstrDeviceDescription,dwDeviceType,bstrFullItemName,pulEventType,ulReserved) (This)->lpVtbl->ImageEventCallback(This,pEventGUID,bstrEventDescription,bstrDeviceID,bstrDeviceDescription,dwDeviceType,bstrFullItemName,pulEventType,ulReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IWiaEventCallback_QueryInterface(IWiaEventCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWiaEventCallback_AddRef(IWiaEventCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWiaEventCallback_Release(IWiaEventCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IWiaEventCallback methods ***/
static inline HRESULT IWiaEventCallback_ImageEventCallback(IWiaEventCallback* This,const GUID *pEventGUID,BSTR bstrEventDescription,BSTR bstrDeviceID,BSTR bstrDeviceDescription,DWORD dwDeviceType,BSTR bstrFullItemName,ULONG *pulEventType,ULONG ulReserved) {
    return This->lpVtbl->ImageEventCallback(This,pEventGUID,bstrEventDescription,bstrDeviceID,bstrDeviceDescription,dwDeviceType,bstrFullItemName,pulEventType,ulReserved);
}
#endif
#endif

#endif


#endif  /* __IWiaEventCallback_INTERFACE_DEFINED__ */

typedef struct _WIA_DEV_CAP {
    GUID guid;
    ULONG ulFlags;
    BSTR bstrName;
    BSTR bstrDescription;
    BSTR bstrIcon;
    BSTR bstrCommandline;
} WIA_DEV_CAP;
typedef struct _WIA_DEV_CAP *PWIA_DEV_CAP;
typedef struct _WIA_DEV_CAP WIA_EVENT_HANDLER;
typedef struct _WIA_DEV_CAP *PWIA_EVENT_HANDLER;
/*****************************************************************************
 * IEnumWIA_DEV_CAPS interface
 */
#ifndef __IEnumWIA_DEV_CAPS_INTERFACE_DEFINED__
#define __IEnumWIA_DEV_CAPS_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumWIA_DEV_CAPS, 0x1fcc4287, 0xaca6, 0x11d2, 0xa0,0x93, 0x00,0xc0,0x4f,0x72,0xdc,0x3c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1fcc4287-aca6-11d2-a093-00c04f72dc3c")
IEnumWIA_DEV_CAPS : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        WIA_DEV_CAP *rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumWIA_DEV_CAPS **ppIEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCount(
        ULONG *pcelt) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumWIA_DEV_CAPS, 0x1fcc4287, 0xaca6, 0x11d2, 0xa0,0x93, 0x00,0xc0,0x4f,0x72,0xdc,0x3c)
#endif
#else
typedef struct IEnumWIA_DEV_CAPSVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumWIA_DEV_CAPS *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumWIA_DEV_CAPS *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumWIA_DEV_CAPS *This);

    /*** IEnumWIA_DEV_CAPS methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumWIA_DEV_CAPS *This,
        ULONG celt,
        WIA_DEV_CAP *rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumWIA_DEV_CAPS *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumWIA_DEV_CAPS *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumWIA_DEV_CAPS *This,
        IEnumWIA_DEV_CAPS **ppIEnum);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IEnumWIA_DEV_CAPS *This,
        ULONG *pcelt);

    END_INTERFACE
} IEnumWIA_DEV_CAPSVtbl;

interface IEnumWIA_DEV_CAPS {
    CONST_VTBL IEnumWIA_DEV_CAPSVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumWIA_DEV_CAPS_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumWIA_DEV_CAPS_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumWIA_DEV_CAPS_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumWIA_DEV_CAPS methods ***/
#define IEnumWIA_DEV_CAPS_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumWIA_DEV_CAPS_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumWIA_DEV_CAPS_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumWIA_DEV_CAPS_Clone(This,ppIEnum) (This)->lpVtbl->Clone(This,ppIEnum)
#define IEnumWIA_DEV_CAPS_GetCount(This,pcelt) (This)->lpVtbl->GetCount(This,pcelt)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumWIA_DEV_CAPS_QueryInterface(IEnumWIA_DEV_CAPS* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumWIA_DEV_CAPS_AddRef(IEnumWIA_DEV_CAPS* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumWIA_DEV_CAPS_Release(IEnumWIA_DEV_CAPS* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumWIA_DEV_CAPS methods ***/
static inline HRESULT IEnumWIA_DEV_CAPS_Next(IEnumWIA_DEV_CAPS* This,ULONG celt,WIA_DEV_CAP *rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static inline HRESULT IEnumWIA_DEV_CAPS_Skip(IEnumWIA_DEV_CAPS* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumWIA_DEV_CAPS_Reset(IEnumWIA_DEV_CAPS* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumWIA_DEV_CAPS_Clone(IEnumWIA_DEV_CAPS* This,IEnumWIA_DEV_CAPS **ppIEnum) {
    return This->lpVtbl->Clone(This,ppIEnum);
}
static inline HRESULT IEnumWIA_DEV_CAPS_GetCount(IEnumWIA_DEV_CAPS* This,ULONG *pcelt) {
    return This->lpVtbl->GetCount(This,pcelt);
}
#endif
#endif

#endif


#endif  /* __IEnumWIA_DEV_CAPS_INTERFACE_DEFINED__ */

typedef struct _WiaTransferParams {
    LONG lMessage;
    LONG lPercentComplete;
    ULONG64 ulTransferredBytes;
    HRESULT hrErrorStatus;
} WiaTransferParams;
/*****************************************************************************
 * IWiaTransferCallback interface
 */
#ifndef __IWiaTransferCallback_INTERFACE_DEFINED__
#define __IWiaTransferCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWiaTransferCallback, 0x27d4eaaf, 0x28a6, 0x4ca5, 0x9a,0xab, 0xe6,0x78,0x16,0x8b,0x95,0x27);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("27d4eaaf-28a6-4ca5-9aab-e678168b9527")
IWiaTransferCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE TransferCallback(
        LONG lFlags,
        WiaTransferParams *pWiaTransferParams) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNextStream(
        LONG lFlags,
        BSTR bstrItemName,
        BSTR bstrFullItemName,
        IStream **ppDestination) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWiaTransferCallback, 0x27d4eaaf, 0x28a6, 0x4ca5, 0x9a,0xab, 0xe6,0x78,0x16,0x8b,0x95,0x27)
#endif
#else
typedef struct IWiaTransferCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWiaTransferCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWiaTransferCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWiaTransferCallback *This);

    /*** IWiaTransferCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *TransferCallback)(
        IWiaTransferCallback *This,
        LONG lFlags,
        WiaTransferParams *pWiaTransferParams);

    HRESULT (STDMETHODCALLTYPE *GetNextStream)(
        IWiaTransferCallback *This,
        LONG lFlags,
        BSTR bstrItemName,
        BSTR bstrFullItemName,
        IStream **ppDestination);

    END_INTERFACE
} IWiaTransferCallbackVtbl;

interface IWiaTransferCallback {
    CONST_VTBL IWiaTransferCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWiaTransferCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWiaTransferCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWiaTransferCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IWiaTransferCallback methods ***/
#define IWiaTransferCallback_TransferCallback(This,lFlags,pWiaTransferParams) (This)->lpVtbl->TransferCallback(This,lFlags,pWiaTransferParams)
#define IWiaTransferCallback_GetNextStream(This,lFlags,bstrItemName,bstrFullItemName,ppDestination) (This)->lpVtbl->GetNextStream(This,lFlags,bstrItemName,bstrFullItemName,ppDestination)
#else
/*** IUnknown methods ***/
static inline HRESULT IWiaTransferCallback_QueryInterface(IWiaTransferCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWiaTransferCallback_AddRef(IWiaTransferCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWiaTransferCallback_Release(IWiaTransferCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IWiaTransferCallback methods ***/
static inline HRESULT IWiaTransferCallback_TransferCallback(IWiaTransferCallback* This,LONG lFlags,WiaTransferParams *pWiaTransferParams) {
    return This->lpVtbl->TransferCallback(This,lFlags,pWiaTransferParams);
}
static inline HRESULT IWiaTransferCallback_GetNextStream(IWiaTransferCallback* This,LONG lFlags,BSTR bstrItemName,BSTR bstrFullItemName,IStream **ppDestination) {
    return This->lpVtbl->GetNextStream(This,lFlags,bstrItemName,bstrFullItemName,ppDestination);
}
#endif
#endif

#endif


#endif  /* __IWiaTransferCallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWiaPreview interface
 */
#ifndef __IWiaPreview_INTERFACE_DEFINED__
#define __IWiaPreview_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWiaPreview, 0x95c2b4fd, 0x33f2, 0x4d86, 0xad,0x40, 0x94,0x31,0xf0,0xdf,0x08,0xf7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("95c2b4fd-33f2-4d86-ad40-9431f0df08f7")
IWiaPreview : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetNewPreview(
        LONG lFlags,
        IWiaItem2 *pWiaItem2,
        IWiaTransferCallback *pWiaTransferCallback) = 0;

    virtual HRESULT STDMETHODCALLTYPE UpdatePreview(
        LONG lFlags,
        IWiaItem2 *pChildWiaItem2,
        IWiaTransferCallback *pWiaTransferCallback) = 0;

    virtual HRESULT STDMETHODCALLTYPE DetectRegions(
        LONG lFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWiaPreview, 0x95c2b4fd, 0x33f2, 0x4d86, 0xad,0x40, 0x94,0x31,0xf0,0xdf,0x08,0xf7)
#endif
#else
typedef struct IWiaPreviewVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWiaPreview *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWiaPreview *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWiaPreview *This);

    /*** IWiaPreview methods ***/
    HRESULT (STDMETHODCALLTYPE *GetNewPreview)(
        IWiaPreview *This,
        LONG lFlags,
        IWiaItem2 *pWiaItem2,
        IWiaTransferCallback *pWiaTransferCallback);

    HRESULT (STDMETHODCALLTYPE *UpdatePreview)(
        IWiaPreview *This,
        LONG lFlags,
        IWiaItem2 *pChildWiaItem2,
        IWiaTransferCallback *pWiaTransferCallback);

    HRESULT (STDMETHODCALLTYPE *DetectRegions)(
        IWiaPreview *This,
        LONG lFlags);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IWiaPreview *This);

    END_INTERFACE
} IWiaPreviewVtbl;

interface IWiaPreview {
    CONST_VTBL IWiaPreviewVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWiaPreview_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWiaPreview_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWiaPreview_Release(This) (This)->lpVtbl->Release(This)
/*** IWiaPreview methods ***/
#define IWiaPreview_GetNewPreview(This,lFlags,pWiaItem2,pWiaTransferCallback) (This)->lpVtbl->GetNewPreview(This,lFlags,pWiaItem2,pWiaTransferCallback)
#define IWiaPreview_UpdatePreview(This,lFlags,pChildWiaItem2,pWiaTransferCallback) (This)->lpVtbl->UpdatePreview(This,lFlags,pChildWiaItem2,pWiaTransferCallback)
#define IWiaPreview_DetectRegions(This,lFlags) (This)->lpVtbl->DetectRegions(This,lFlags)
#define IWiaPreview_Clear(This) (This)->lpVtbl->Clear(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWiaPreview_QueryInterface(IWiaPreview* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWiaPreview_AddRef(IWiaPreview* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWiaPreview_Release(IWiaPreview* This) {
    return This->lpVtbl->Release(This);
}
/*** IWiaPreview methods ***/
static inline HRESULT IWiaPreview_GetNewPreview(IWiaPreview* This,LONG lFlags,IWiaItem2 *pWiaItem2,IWiaTransferCallback *pWiaTransferCallback) {
    return This->lpVtbl->GetNewPreview(This,lFlags,pWiaItem2,pWiaTransferCallback);
}
static inline HRESULT IWiaPreview_UpdatePreview(IWiaPreview* This,LONG lFlags,IWiaItem2 *pChildWiaItem2,IWiaTransferCallback *pWiaTransferCallback) {
    return This->lpVtbl->UpdatePreview(This,lFlags,pChildWiaItem2,pWiaTransferCallback);
}
static inline HRESULT IWiaPreview_DetectRegions(IWiaPreview* This,LONG lFlags) {
    return This->lpVtbl->DetectRegions(This,lFlags);
}
static inline HRESULT IWiaPreview_Clear(IWiaPreview* This) {
    return This->lpVtbl->Clear(This);
}
#endif
#endif

#endif


#endif  /* __IWiaPreview_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumWiaItem2 interface
 */
#ifndef __IEnumWiaItem2_INTERFACE_DEFINED__
#define __IEnumWiaItem2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumWiaItem2, 0x59970af4, 0xcd0d, 0x44d9, 0xab,0x24, 0x52,0x29,0x56,0x30,0xe5,0x82);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("59970af4-cd0d-44d9-ab24-52295630e582")
IEnumWiaItem2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG cElt,
        IWiaItem2 **ppIWiaItem2,
        ULONG *pcEltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG cElt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumWiaItem2 **ppIEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCount(
        ULONG *cElt) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumWiaItem2, 0x59970af4, 0xcd0d, 0x44d9, 0xab,0x24, 0x52,0x29,0x56,0x30,0xe5,0x82)
#endif
#else
typedef struct IEnumWiaItem2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumWiaItem2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumWiaItem2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumWiaItem2 *This);

    /*** IEnumWiaItem2 methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumWiaItem2 *This,
        ULONG cElt,
        IWiaItem2 **ppIWiaItem2,
        ULONG *pcEltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumWiaItem2 *This,
        ULONG cElt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumWiaItem2 *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumWiaItem2 *This,
        IEnumWiaItem2 **ppIEnum);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IEnumWiaItem2 *This,
        ULONG *cElt);

    END_INTERFACE
} IEnumWiaItem2Vtbl;

interface IEnumWiaItem2 {
    CONST_VTBL IEnumWiaItem2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumWiaItem2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumWiaItem2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumWiaItem2_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumWiaItem2 methods ***/
#define IEnumWiaItem2_Next(This,cElt,ppIWiaItem2,pcEltFetched) (This)->lpVtbl->Next(This,cElt,ppIWiaItem2,pcEltFetched)
#define IEnumWiaItem2_Skip(This,cElt) (This)->lpVtbl->Skip(This,cElt)
#define IEnumWiaItem2_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumWiaItem2_Clone(This,ppIEnum) (This)->lpVtbl->Clone(This,ppIEnum)
#define IEnumWiaItem2_GetCount(This,cElt) (This)->lpVtbl->GetCount(This,cElt)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumWiaItem2_QueryInterface(IEnumWiaItem2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumWiaItem2_AddRef(IEnumWiaItem2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumWiaItem2_Release(IEnumWiaItem2* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumWiaItem2 methods ***/
static inline HRESULT IEnumWiaItem2_Next(IEnumWiaItem2* This,ULONG cElt,IWiaItem2 **ppIWiaItem2,ULONG *pcEltFetched) {
    return This->lpVtbl->Next(This,cElt,ppIWiaItem2,pcEltFetched);
}
static inline HRESULT IEnumWiaItem2_Skip(IEnumWiaItem2* This,ULONG cElt) {
    return This->lpVtbl->Skip(This,cElt);
}
static inline HRESULT IEnumWiaItem2_Reset(IEnumWiaItem2* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumWiaItem2_Clone(IEnumWiaItem2* This,IEnumWiaItem2 **ppIEnum) {
    return This->lpVtbl->Clone(This,ppIEnum);
}
static inline HRESULT IEnumWiaItem2_GetCount(IEnumWiaItem2* This,ULONG *cElt) {
    return This->lpVtbl->GetCount(This,cElt);
}
#endif
#endif

#endif


#endif  /* __IEnumWiaItem2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWiaItem2 interface
 */
#ifndef __IWiaItem2_INTERFACE_DEFINED__
#define __IWiaItem2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWiaItem2, 0x6cba0075, 0x1287, 0x407d, 0x9b,0x77, 0xcf,0x0e,0x03,0x04,0x35,0xcc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6cba0075-1287-407d-9b77-cf0e030435cc")
IWiaItem2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateChildItem(
        LONG lItemFlags,
        LONG lCreationFlags,
        BSTR bstrItemName,
        IWiaItem2 **ppIWiaItem2) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteItem(
        LONG lFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumChildItems(
        const GUID *pCategoryGUID,
        IEnumWiaItem2 **ppIEnumWiaItem2) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindItemByName(
        LONG lFlags,
        BSTR bstrFullItemName,
        IWiaItem2 **ppIWiaItem2) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetItemCategory(
        GUID *pItemCategoryGUID) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetItemType(
        LONG *pItemType) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeviceDlg(
        LONG lFlags,
        HWND hwndParent,
        BSTR bstrFolderName,
        BSTR bstrFilename,
        LONG *plNumFiles,
        BSTR **ppbstrFilePaths,
        IWiaItem2 **ppItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeviceCommand(
        LONG lFlags,
        const GUID *pCmdGUID,
        IWiaItem2 **ppIWiaItem2) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumDeviceCapabilities(
        LONG lFlags,
        IEnumWIA_DEV_CAPS **ppIEnumWIA_DEV_CAPS) = 0;

    virtual HRESULT STDMETHODCALLTYPE CheckExtension(
        LONG lFlags,
        BSTR bstrName,
        REFIID riidExtensionInterface,
        WINBOOL *pbExtensionExists) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetExtension(
        LONG lFlags,
        BSTR bstrName,
        REFIID riidExtensionInterface,
        void **ppOut) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetParentItem(
        IWiaItem2 **ppIWiaItem2) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRootItem(
        IWiaItem2 **ppIWiaItem2) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPreviewComponent(
        LONG lFlags,
        IWiaPreview **ppWiaPreview) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumRegisterEventInfo(
        LONG lFlags,
        const GUID *pEventGUID,
        IEnumWIA_DEV_CAPS **ppIEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Diagnostic(
        ULONG ulSize,
        BYTE *pBuffer) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWiaItem2, 0x6cba0075, 0x1287, 0x407d, 0x9b,0x77, 0xcf,0x0e,0x03,0x04,0x35,0xcc)
#endif
#else
typedef struct IWiaItem2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWiaItem2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWiaItem2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWiaItem2 *This);

    /*** IWiaItem2 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateChildItem)(
        IWiaItem2 *This,
        LONG lItemFlags,
        LONG lCreationFlags,
        BSTR bstrItemName,
        IWiaItem2 **ppIWiaItem2);

    HRESULT (STDMETHODCALLTYPE *DeleteItem)(
        IWiaItem2 *This,
        LONG lFlags);

    HRESULT (STDMETHODCALLTYPE *EnumChildItems)(
        IWiaItem2 *This,
        const GUID *pCategoryGUID,
        IEnumWiaItem2 **ppIEnumWiaItem2);

    HRESULT (STDMETHODCALLTYPE *FindItemByName)(
        IWiaItem2 *This,
        LONG lFlags,
        BSTR bstrFullItemName,
        IWiaItem2 **ppIWiaItem2);

    HRESULT (STDMETHODCALLTYPE *GetItemCategory)(
        IWiaItem2 *This,
        GUID *pItemCategoryGUID);

    HRESULT (STDMETHODCALLTYPE *GetItemType)(
        IWiaItem2 *This,
        LONG *pItemType);

    HRESULT (STDMETHODCALLTYPE *DeviceDlg)(
        IWiaItem2 *This,
        LONG lFlags,
        HWND hwndParent,
        BSTR bstrFolderName,
        BSTR bstrFilename,
        LONG *plNumFiles,
        BSTR **ppbstrFilePaths,
        IWiaItem2 **ppItem);

    HRESULT (STDMETHODCALLTYPE *DeviceCommand)(
        IWiaItem2 *This,
        LONG lFlags,
        const GUID *pCmdGUID,
        IWiaItem2 **ppIWiaItem2);

    HRESULT (STDMETHODCALLTYPE *EnumDeviceCapabilities)(
        IWiaItem2 *This,
        LONG lFlags,
        IEnumWIA_DEV_CAPS **ppIEnumWIA_DEV_CAPS);

    HRESULT (STDMETHODCALLTYPE *CheckExtension)(
        IWiaItem2 *This,
        LONG lFlags,
        BSTR bstrName,
        REFIID riidExtensionInterface,
        WINBOOL *pbExtensionExists);

    HRESULT (STDMETHODCALLTYPE *GetExtension)(
        IWiaItem2 *This,
        LONG lFlags,
        BSTR bstrName,
        REFIID riidExtensionInterface,
        void **ppOut);

    HRESULT (STDMETHODCALLTYPE *GetParentItem)(
        IWiaItem2 *This,
        IWiaItem2 **ppIWiaItem2);

    HRESULT (STDMETHODCALLTYPE *GetRootItem)(
        IWiaItem2 *This,
        IWiaItem2 **ppIWiaItem2);

    HRESULT (STDMETHODCALLTYPE *GetPreviewComponent)(
        IWiaItem2 *This,
        LONG lFlags,
        IWiaPreview **ppWiaPreview);

    HRESULT (STDMETHODCALLTYPE *EnumRegisterEventInfo)(
        IWiaItem2 *This,
        LONG lFlags,
        const GUID *pEventGUID,
        IEnumWIA_DEV_CAPS **ppIEnum);

    HRESULT (STDMETHODCALLTYPE *Diagnostic)(
        IWiaItem2 *This,
        ULONG ulSize,
        BYTE *pBuffer);

    END_INTERFACE
} IWiaItem2Vtbl;

interface IWiaItem2 {
    CONST_VTBL IWiaItem2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWiaItem2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWiaItem2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWiaItem2_Release(This) (This)->lpVtbl->Release(This)
/*** IWiaItem2 methods ***/
#define IWiaItem2_CreateChildItem(This,lItemFlags,lCreationFlags,bstrItemName,ppIWiaItem2) (This)->lpVtbl->CreateChildItem(This,lItemFlags,lCreationFlags,bstrItemName,ppIWiaItem2)
#define IWiaItem2_DeleteItem(This,lFlags) (This)->lpVtbl->DeleteItem(This,lFlags)
#define IWiaItem2_EnumChildItems(This,pCategoryGUID,ppIEnumWiaItem2) (This)->lpVtbl->EnumChildItems(This,pCategoryGUID,ppIEnumWiaItem2)
#define IWiaItem2_FindItemByName(This,lFlags,bstrFullItemName,ppIWiaItem2) (This)->lpVtbl->FindItemByName(This,lFlags,bstrFullItemName,ppIWiaItem2)
#define IWiaItem2_GetItemCategory(This,pItemCategoryGUID) (This)->lpVtbl->GetItemCategory(This,pItemCategoryGUID)
#define IWiaItem2_GetItemType(This,pItemType) (This)->lpVtbl->GetItemType(This,pItemType)
#define IWiaItem2_DeviceDlg(This,lFlags,hwndParent,bstrFolderName,bstrFilename,plNumFiles,ppbstrFilePaths,ppItem) (This)->lpVtbl->DeviceDlg(This,lFlags,hwndParent,bstrFolderName,bstrFilename,plNumFiles,ppbstrFilePaths,ppItem)
#define IWiaItem2_DeviceCommand(This,lFlags,pCmdGUID,ppIWiaItem2) (This)->lpVtbl->DeviceCommand(This,lFlags,pCmdGUID,ppIWiaItem2)
#define IWiaItem2_EnumDeviceCapabilities(This,lFlags,ppIEnumWIA_DEV_CAPS) (This)->lpVtbl->EnumDeviceCapabilities(This,lFlags,ppIEnumWIA_DEV_CAPS)
#define IWiaItem2_CheckExtension(This,lFlags,bstrName,riidExtensionInterface,pbExtensionExists) (This)->lpVtbl->CheckExtension(This,lFlags,bstrName,riidExtensionInterface,pbExtensionExists)
#define IWiaItem2_GetExtension(This,lFlags,bstrName,riidExtensionInterface,ppOut) (This)->lpVtbl->GetExtension(This,lFlags,bstrName,riidExtensionInterface,ppOut)
#define IWiaItem2_GetParentItem(This,ppIWiaItem2) (This)->lpVtbl->GetParentItem(This,ppIWiaItem2)
#define IWiaItem2_GetRootItem(This,ppIWiaItem2) (This)->lpVtbl->GetRootItem(This,ppIWiaItem2)
#define IWiaItem2_GetPreviewComponent(This,lFlags,ppWiaPreview) (This)->lpVtbl->GetPreviewComponent(This,lFlags,ppWiaPreview)
#define IWiaItem2_EnumRegisterEventInfo(This,lFlags,pEventGUID,ppIEnum) (This)->lpVtbl->EnumRegisterEventInfo(This,lFlags,pEventGUID,ppIEnum)
#define IWiaItem2_Diagnostic(This,ulSize,pBuffer) (This)->lpVtbl->Diagnostic(This,ulSize,pBuffer)
#else
/*** IUnknown methods ***/
static inline HRESULT IWiaItem2_QueryInterface(IWiaItem2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWiaItem2_AddRef(IWiaItem2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWiaItem2_Release(IWiaItem2* This) {
    return This->lpVtbl->Release(This);
}
/*** IWiaItem2 methods ***/
static inline HRESULT IWiaItem2_CreateChildItem(IWiaItem2* This,LONG lItemFlags,LONG lCreationFlags,BSTR bstrItemName,IWiaItem2 **ppIWiaItem2) {
    return This->lpVtbl->CreateChildItem(This,lItemFlags,lCreationFlags,bstrItemName,ppIWiaItem2);
}
static inline HRESULT IWiaItem2_DeleteItem(IWiaItem2* This,LONG lFlags) {
    return This->lpVtbl->DeleteItem(This,lFlags);
}
static inline HRESULT IWiaItem2_EnumChildItems(IWiaItem2* This,const GUID *pCategoryGUID,IEnumWiaItem2 **ppIEnumWiaItem2) {
    return This->lpVtbl->EnumChildItems(This,pCategoryGUID,ppIEnumWiaItem2);
}
static inline HRESULT IWiaItem2_FindItemByName(IWiaItem2* This,LONG lFlags,BSTR bstrFullItemName,IWiaItem2 **ppIWiaItem2) {
    return This->lpVtbl->FindItemByName(This,lFlags,bstrFullItemName,ppIWiaItem2);
}
static inline HRESULT IWiaItem2_GetItemCategory(IWiaItem2* This,GUID *pItemCategoryGUID) {
    return This->lpVtbl->GetItemCategory(This,pItemCategoryGUID);
}
static inline HRESULT IWiaItem2_GetItemType(IWiaItem2* This,LONG *pItemType) {
    return This->lpVtbl->GetItemType(This,pItemType);
}
static inline HRESULT IWiaItem2_DeviceDlg(IWiaItem2* This,LONG lFlags,HWND hwndParent,BSTR bstrFolderName,BSTR bstrFilename,LONG *plNumFiles,BSTR **ppbstrFilePaths,IWiaItem2 **ppItem) {
    return This->lpVtbl->DeviceDlg(This,lFlags,hwndParent,bstrFolderName,bstrFilename,plNumFiles,ppbstrFilePaths,ppItem);
}
static inline HRESULT IWiaItem2_DeviceCommand(IWiaItem2* This,LONG lFlags,const GUID *pCmdGUID,IWiaItem2 **ppIWiaItem2) {
    return This->lpVtbl->DeviceCommand(This,lFlags,pCmdGUID,ppIWiaItem2);
}
static inline HRESULT IWiaItem2_EnumDeviceCapabilities(IWiaItem2* This,LONG lFlags,IEnumWIA_DEV_CAPS **ppIEnumWIA_DEV_CAPS) {
    return This->lpVtbl->EnumDeviceCapabilities(This,lFlags,ppIEnumWIA_DEV_CAPS);
}
static inline HRESULT IWiaItem2_CheckExtension(IWiaItem2* This,LONG lFlags,BSTR bstrName,REFIID riidExtensionInterface,WINBOOL *pbExtensionExists) {
    return This->lpVtbl->CheckExtension(This,lFlags,bstrName,riidExtensionInterface,pbExtensionExists);
}
static inline HRESULT IWiaItem2_GetExtension(IWiaItem2* This,LONG lFlags,BSTR bstrName,REFIID riidExtensionInterface,void **ppOut) {
    return This->lpVtbl->GetExtension(This,lFlags,bstrName,riidExtensionInterface,ppOut);
}
static inline HRESULT IWiaItem2_GetParentItem(IWiaItem2* This,IWiaItem2 **ppIWiaItem2) {
    return This->lpVtbl->GetParentItem(This,ppIWiaItem2);
}
static inline HRESULT IWiaItem2_GetRootItem(IWiaItem2* This,IWiaItem2 **ppIWiaItem2) {
    return This->lpVtbl->GetRootItem(This,ppIWiaItem2);
}
static inline HRESULT IWiaItem2_GetPreviewComponent(IWiaItem2* This,LONG lFlags,IWiaPreview **ppWiaPreview) {
    return This->lpVtbl->GetPreviewComponent(This,lFlags,ppWiaPreview);
}
static inline HRESULT IWiaItem2_EnumRegisterEventInfo(IWiaItem2* This,LONG lFlags,const GUID *pEventGUID,IEnumWIA_DEV_CAPS **ppIEnum) {
    return This->lpVtbl->EnumRegisterEventInfo(This,lFlags,pEventGUID,ppIEnum);
}
static inline HRESULT IWiaItem2_Diagnostic(IWiaItem2* This,ULONG ulSize,BYTE *pBuffer) {
    return This->lpVtbl->Diagnostic(This,ulSize,pBuffer);
}
#endif
#endif

#endif


#endif  /* __IWiaItem2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWiaDevMgr2 interface
 */
#ifndef __IWiaDevMgr2_INTERFACE_DEFINED__
#define __IWiaDevMgr2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWiaDevMgr2, 0x79c07cf1, 0xcbdd, 0x41ee, 0x8e,0xc3, 0xf0,0x00,0x80,0xca,0xda,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79c07cf1-cbdd-41ee-8ec3-f00080cada7a")
IWiaDevMgr2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE EnumDeviceInfo(
        LONG lFlags,
        IEnumWIA_DEV_INFO **ppIEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDevice(
        LONG lFlags,
        BSTR bstrDeviceID,
        IWiaItem2 **ppWiaItem2Root) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectDeviceDlg(
        HWND hwndParent,
        LONG lDeviceType,
        LONG lFlags,
        BSTR *pbstrDeviceID,
        IWiaItem2 **ppItemRoot) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectDeviceDlgID(
        HWND hwndParent,
        LONG lDeviceType,
        LONG lFlags,
        BSTR *pbstrDeviceID) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterEventCallbackInterface(
        LONG lFlags,
        BSTR bstrDeviceID,
        const GUID *pEventGUID,
        IWiaEventCallback *pIWiaEventCallback,
        IUnknown **pEventObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterEventCallbackProgram(
        LONG lFlags,
        BSTR bstrDeviceID,
        const GUID *pEventGUID,
        BSTR bstrFullAppName,
        BSTR bstrCommandLineArg,
        BSTR bstrName,
        BSTR bstrDescription,
        BSTR bstrIcon) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterEventCallbackCLSID(
        LONG lFlags,
        BSTR bstrDeviceID,
        const GUID *pEventGUID,
        const GUID *pClsID,
        BSTR bstrName,
        BSTR bstrDescription,
        BSTR bstrIcon) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetImageDlg(
        LONG lFlags,
        BSTR bstrDeviceID,
        HWND hwndParent,
        BSTR bstrFolderName,
        BSTR bstrFilename,
        LONG *plNumFiles,
        BSTR **ppbstrFilePaths,
        IWiaItem2 **ppItem) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWiaDevMgr2, 0x79c07cf1, 0xcbdd, 0x41ee, 0x8e,0xc3, 0xf0,0x00,0x80,0xca,0xda,0x7a)
#endif
#else
typedef struct IWiaDevMgr2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWiaDevMgr2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWiaDevMgr2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWiaDevMgr2 *This);

    /*** IWiaDevMgr2 methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumDeviceInfo)(
        IWiaDevMgr2 *This,
        LONG lFlags,
        IEnumWIA_DEV_INFO **ppIEnum);

    HRESULT (STDMETHODCALLTYPE *CreateDevice)(
        IWiaDevMgr2 *This,
        LONG lFlags,
        BSTR bstrDeviceID,
        IWiaItem2 **ppWiaItem2Root);

    HRESULT (STDMETHODCALLTYPE *SelectDeviceDlg)(
        IWiaDevMgr2 *This,
        HWND hwndParent,
        LONG lDeviceType,
        LONG lFlags,
        BSTR *pbstrDeviceID,
        IWiaItem2 **ppItemRoot);

    HRESULT (STDMETHODCALLTYPE *SelectDeviceDlgID)(
        IWiaDevMgr2 *This,
        HWND hwndParent,
        LONG lDeviceType,
        LONG lFlags,
        BSTR *pbstrDeviceID);

    HRESULT (STDMETHODCALLTYPE *RegisterEventCallbackInterface)(
        IWiaDevMgr2 *This,
        LONG lFlags,
        BSTR bstrDeviceID,
        const GUID *pEventGUID,
        IWiaEventCallback *pIWiaEventCallback,
        IUnknown **pEventObject);

    HRESULT (STDMETHODCALLTYPE *RegisterEventCallbackProgram)(
        IWiaDevMgr2 *This,
        LONG lFlags,
        BSTR bstrDeviceID,
        const GUID *pEventGUID,
        BSTR bstrFullAppName,
        BSTR bstrCommandLineArg,
        BSTR bstrName,
        BSTR bstrDescription,
        BSTR bstrIcon);

    HRESULT (STDMETHODCALLTYPE *RegisterEventCallbackCLSID)(
        IWiaDevMgr2 *This,
        LONG lFlags,
        BSTR bstrDeviceID,
        const GUID *pEventGUID,
        const GUID *pClsID,
        BSTR bstrName,
        BSTR bstrDescription,
        BSTR bstrIcon);

    HRESULT (STDMETHODCALLTYPE *GetImageDlg)(
        IWiaDevMgr2 *This,
        LONG lFlags,
        BSTR bstrDeviceID,
        HWND hwndParent,
        BSTR bstrFolderName,
        BSTR bstrFilename,
        LONG *plNumFiles,
        BSTR **ppbstrFilePaths,
        IWiaItem2 **ppItem);

    END_INTERFACE
} IWiaDevMgr2Vtbl;

interface IWiaDevMgr2 {
    CONST_VTBL IWiaDevMgr2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWiaDevMgr2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWiaDevMgr2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWiaDevMgr2_Release(This) (This)->lpVtbl->Release(This)
/*** IWiaDevMgr2 methods ***/
#define IWiaDevMgr2_EnumDeviceInfo(This,lFlags,ppIEnum) (This)->lpVtbl->EnumDeviceInfo(This,lFlags,ppIEnum)
#define IWiaDevMgr2_CreateDevice(This,lFlags,bstrDeviceID,ppWiaItem2Root) (This)->lpVtbl->CreateDevice(This,lFlags,bstrDeviceID,ppWiaItem2Root)
#define IWiaDevMgr2_SelectDeviceDlg(This,hwndParent,lDeviceType,lFlags,pbstrDeviceID,ppItemRoot) (This)->lpVtbl->SelectDeviceDlg(This,hwndParent,lDeviceType,lFlags,pbstrDeviceID,ppItemRoot)
#define IWiaDevMgr2_SelectDeviceDlgID(This,hwndParent,lDeviceType,lFlags,pbstrDeviceID) (This)->lpVtbl->SelectDeviceDlgID(This,hwndParent,lDeviceType,lFlags,pbstrDeviceID)
#define IWiaDevMgr2_RegisterEventCallbackInterface(This,lFlags,bstrDeviceID,pEventGUID,pIWiaEventCallback,pEventObject) (This)->lpVtbl->RegisterEventCallbackInterface(This,lFlags,bstrDeviceID,pEventGUID,pIWiaEventCallback,pEventObject)
#define IWiaDevMgr2_RegisterEventCallbackProgram(This,lFlags,bstrDeviceID,pEventGUID,bstrFullAppName,bstrCommandLineArg,bstrName,bstrDescription,bstrIcon) (This)->lpVtbl->RegisterEventCallbackProgram(This,lFlags,bstrDeviceID,pEventGUID,bstrFullAppName,bstrCommandLineArg,bstrName,bstrDescription,bstrIcon)
#define IWiaDevMgr2_RegisterEventCallbackCLSID(This,lFlags,bstrDeviceID,pEventGUID,pClsID,bstrName,bstrDescription,bstrIcon) (This)->lpVtbl->RegisterEventCallbackCLSID(This,lFlags,bstrDeviceID,pEventGUID,pClsID,bstrName,bstrDescription,bstrIcon)
#define IWiaDevMgr2_GetImageDlg(This,lFlags,bstrDeviceID,hwndParent,bstrFolderName,bstrFilename,plNumFiles,ppbstrFilePaths,ppItem) (This)->lpVtbl->GetImageDlg(This,lFlags,bstrDeviceID,hwndParent,bstrFolderName,bstrFilename,plNumFiles,ppbstrFilePaths,ppItem)
#else
/*** IUnknown methods ***/
static inline HRESULT IWiaDevMgr2_QueryInterface(IWiaDevMgr2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWiaDevMgr2_AddRef(IWiaDevMgr2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWiaDevMgr2_Release(IWiaDevMgr2* This) {
    return This->lpVtbl->Release(This);
}
/*** IWiaDevMgr2 methods ***/
static inline HRESULT IWiaDevMgr2_EnumDeviceInfo(IWiaDevMgr2* This,LONG lFlags,IEnumWIA_DEV_INFO **ppIEnum) {
    return This->lpVtbl->EnumDeviceInfo(This,lFlags,ppIEnum);
}
static inline HRESULT IWiaDevMgr2_CreateDevice(IWiaDevMgr2* This,LONG lFlags,BSTR bstrDeviceID,IWiaItem2 **ppWiaItem2Root) {
    return This->lpVtbl->CreateDevice(This,lFlags,bstrDeviceID,ppWiaItem2Root);
}
static inline HRESULT IWiaDevMgr2_SelectDeviceDlg(IWiaDevMgr2* This,HWND hwndParent,LONG lDeviceType,LONG lFlags,BSTR *pbstrDeviceID,IWiaItem2 **ppItemRoot) {
    return This->lpVtbl->SelectDeviceDlg(This,hwndParent,lDeviceType,lFlags,pbstrDeviceID,ppItemRoot);
}
static inline HRESULT IWiaDevMgr2_SelectDeviceDlgID(IWiaDevMgr2* This,HWND hwndParent,LONG lDeviceType,LONG lFlags,BSTR *pbstrDeviceID) {
    return This->lpVtbl->SelectDeviceDlgID(This,hwndParent,lDeviceType,lFlags,pbstrDeviceID);
}
static inline HRESULT IWiaDevMgr2_RegisterEventCallbackInterface(IWiaDevMgr2* This,LONG lFlags,BSTR bstrDeviceID,const GUID *pEventGUID,IWiaEventCallback *pIWiaEventCallback,IUnknown **pEventObject) {
    return This->lpVtbl->RegisterEventCallbackInterface(This,lFlags,bstrDeviceID,pEventGUID,pIWiaEventCallback,pEventObject);
}
static inline HRESULT IWiaDevMgr2_RegisterEventCallbackProgram(IWiaDevMgr2* This,LONG lFlags,BSTR bstrDeviceID,const GUID *pEventGUID,BSTR bstrFullAppName,BSTR bstrCommandLineArg,BSTR bstrName,BSTR bstrDescription,BSTR bstrIcon) {
    return This->lpVtbl->RegisterEventCallbackProgram(This,lFlags,bstrDeviceID,pEventGUID,bstrFullAppName,bstrCommandLineArg,bstrName,bstrDescription,bstrIcon);
}
static inline HRESULT IWiaDevMgr2_RegisterEventCallbackCLSID(IWiaDevMgr2* This,LONG lFlags,BSTR bstrDeviceID,const GUID *pEventGUID,const GUID *pClsID,BSTR bstrName,BSTR bstrDescription,BSTR bstrIcon) {
    return This->lpVtbl->RegisterEventCallbackCLSID(This,lFlags,bstrDeviceID,pEventGUID,pClsID,bstrName,bstrDescription,bstrIcon);
}
static inline HRESULT IWiaDevMgr2_GetImageDlg(IWiaDevMgr2* This,LONG lFlags,BSTR bstrDeviceID,HWND hwndParent,BSTR bstrFolderName,BSTR bstrFilename,LONG *plNumFiles,BSTR **ppbstrFilePaths,IWiaItem2 **ppItem) {
    return This->lpVtbl->GetImageDlg(This,lFlags,bstrDeviceID,hwndParent,bstrFolderName,bstrFilename,plNumFiles,ppbstrFilePaths,ppItem);
}
#endif
#endif

#endif


#endif  /* __IWiaDevMgr2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * WiaDevMgr2 coclass
 */

DEFINE_GUID(CLSID_WiaDevMgr2, 0xb6c292bc, 0x7c88, 0x41ee, 0x8b,0x54, 0x8e,0xc9,0x26,0x17,0xe5,0x99);

#ifdef __cplusplus
class DECLSPEC_UUID("b6c292bc-7c88-41ee-8b54-8ec92617e599") WiaDevMgr2;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WiaDevMgr2, 0xb6c292bc, 0x7c88, 0x41ee, 0x8b,0x54, 0x8e,0xc9,0x26,0x17,0xe5,0x99)
#endif
#endif

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER HWND_UserSize     (ULONG *, ULONG, HWND *);
unsigned char * __RPC_USER HWND_UserMarshal  (ULONG *, unsigned char *, HWND *);
unsigned char * __RPC_USER HWND_UserUnmarshal(ULONG *, unsigned char *, HWND *);
void            __RPC_USER HWND_UserFree     (ULONG *, HWND *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __wia_lh_h__ */
