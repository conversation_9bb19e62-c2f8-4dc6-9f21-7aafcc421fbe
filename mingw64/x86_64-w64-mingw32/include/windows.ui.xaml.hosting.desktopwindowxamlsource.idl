/*
 * Copyright (C) 2023 Biswap<PERSON>yo <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "oaidl.idl";

[
    object,
    uuid(3cbcf1bf-2f76-4e9c-96ab-e84b37972554),
    local,
    pointer_default(unique)
]
interface IDesktopWindowXamlSourceNative : IUnknown
{
    HRESULT AttachToWindow([in] HWND parent_wnd);
    [propget] HRESULT WindowHandle([out, retval] HWND *wnd);
}

[
    object,
    uuid(e3dcd8c7-3057-4692-99c3-7b7720afda31),
    local,
    pointer_default(unique)
]
interface IDesktopWindowXamlSourceNative2 : IDesktopWindowXamlSourceNative
{
    HRESULT PreTranslateMessage([in] const MSG *message, [out, retval] BOOL *result);
}
