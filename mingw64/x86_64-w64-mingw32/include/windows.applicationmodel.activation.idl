/*
 * Copyright (C) 2024 <PERSON><PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

#ifndef DO_NO_IMPORTS
import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";
import "windows.applicationmodel.background.idl";
import "windows.applicationmodel.core.idl";
import "windows.storage.idl";
import "windows.system.idl";
#endif

namespace Windows.ApplicationModel.Background {
    interface IBackgroundTaskInstance;

    runtimeclass BackgroundTaskRegistrationGroup;
}

namespace Windows.ApplicationModel.Core {
    runtimeclass CoreApplicationView;
}

namespace Windows.ApplicationModel.Activation {
    typedef enum ActivationKind ActivationKind;
    typedef enum ApplicationExecutionState ApplicationExecutionState;

    interface IActivatedEventArgs;
    interface IBackgroundActivatedEventArgs;
    interface ISplashScreen;

    runtimeclass BackgroundActivatedEventArgs;
    runtimeclass SplashScreen;

    declare {
        interface Windows.Foundation.EventHandler<Windows.ApplicationModel.Activation.BackgroundActivatedEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.ApplicationModel.Activation.SplashScreen *, IInspectable *>;
        interface Windows.Foundation.TypedEventHandler<Windows.ApplicationModel.Background.BackgroundTaskRegistrationGroup *, Windows.ApplicationModel.Activation.BackgroundActivatedEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.ApplicationModel.Core.CoreApplicationView *, Windows.ApplicationModel.Activation.IActivatedEventArgs *>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum ActivationKind
    {
        Launch                              = 0,
        Search                              = 1,
        ShareTarget                         = 2,
        File                                = 3,
        Protocol                            = 4,
        FileOpenPicker                      = 5,
        FileSavePicker                      = 6,
        CachedFileUpdater                   = 7,
        ContactPicker                       = 8,
        Device                              = 9,
        PrintTaskSettings                   = 10,
        CameraSettings                      = 11,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        RestrictedLaunch                    = 12,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        AppointmentsProvider                = 13,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        Contact                             = 14,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        LockScreenCall                      = 15,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        VoiceCommand                        = 16,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        LockScreen                          = 17,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        PickerReturned                      = 1000,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        WalletAction                        = 1001,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        PickFileContinuation                = 1002,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        PickSaveFileContinuation            = 1003,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        PickFolderContinuation              = 1004,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        WebAuthenticationBrokerContinuation = 1005,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        WebAccountProvider                  = 1006,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        ComponentUI                         = 1007,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        ProtocolForResults                  = 1009,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        ToastNotification                   = 1010,
        [contract(Windows.Foundation.UniversalApiContract, 2.0)]
        Print3DWorkflow                     = 1011,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        DialReceiver                        = 1012,
        [contract(Windows.Foundation.UniversalApiContract, 2.0)]
        DevicePairing                       = 1013,
        [contract(Windows.Foundation.UniversalApiContract, 3.0)]
        UserDataAccountsProvider            = 1014,
        [contract(Windows.Foundation.UniversalApiContract, 3.0)]
        FilePickerExperience                = 1015,
        [contract(Windows.Foundation.UniversalApiContract, 4.0)]
        LockScreenComponent                 = 1016,
        [contract(Windows.Foundation.UniversalApiContract, 4.0)]
        ContactPanel                        = 1017,
        [contract(Windows.Foundation.UniversalApiContract, 5.0)]
        PrintWorkflowForegroundTask         = 1018,
        [contract(Windows.Foundation.UniversalApiContract, 5.0)]
        GameUIProvider                      = 1019,
        [contract(Windows.Foundation.UniversalApiContract, 5.0)]
        StartupTask                         = 1020,
        [contract(Windows.Foundation.UniversalApiContract, 5.0)]
        CommandLineLaunch                   = 1021,
        [contract(Windows.Foundation.UniversalApiContract, 6.0)]
        BarcodeScannerProvider              = 1022
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum ApplicationExecutionState
    {
        NotRunning   = 0,
        Running      = 1,
        Suspended    = 2,
        Terminated   = 3,
        ClosedByUser = 4
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        uuid(cf651713-cd08-4fd8-b697-a281b6544e2e)
    ]
    interface IActivatedEventArgs : IInspectable
    {
        [propget] HRESULT Kind([out, retval] Windows.ApplicationModel.Activation.ActivationKind *value);
        [propget] HRESULT PreviousExecutionState([out, retval] Windows.ApplicationModel.Activation.ApplicationExecutionState *value);
        [propget] HRESULT SplashScreen([out, retval] Windows.ApplicationModel.Activation.SplashScreen **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        uuid(ab14bee0-e760-440e-a91c-44796de3a92d)
    ]
    interface IBackgroundActivatedEventArgs : IInspectable
    {
        [propget] HRESULT TaskInstance([out, retval] Windows.ApplicationModel.Background.IBackgroundTaskInstance **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.ApplicationModel.Activation.SplashScreen),
        uuid(ca4d975c-d4d6-43f0-97c0-0833c6391c24)
    ]
    interface ISplashScreen : IInspectable
    {
        [propget] HRESULT ImageLocation([out, retval] Windows.Foundation.Rect *value);
        [eventadd] HRESULT Dismissed([in] Windows.Foundation.TypedEventHandler<Windows.ApplicationModel.Activation.SplashScreen *, IInspectable *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT Dismissed([in] EventRegistrationToken cookie);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        marshaling_behavior(agile)
    ]
    runtimeclass BackgroundActivatedEventArgs
    {
        [default] interface Windows.ApplicationModel.Activation.IBackgroundActivatedEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    runtimeclass SplashScreen
    {
        [default] interface Windows.ApplicationModel.Activation.ISplashScreen;
    }
}
