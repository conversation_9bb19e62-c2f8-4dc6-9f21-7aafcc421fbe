/*
 * Copyright (C) 2023 Mo<PERSON>ad Al-Jaf
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";
import "windows.devices.geolocation.idl";
/* import "windows.storage.idl"; */
import "windows.storage.streams.idl";

namespace Windows.Storage.FileProperties {
    interface IBasicProperties;

    runtimeclass BasicProperties;

    declare {
        interface Windows.Foundation.AsyncOperationCompletedHandler<Windows.Storage.FileProperties.BasicProperties *>;
        interface Windows.Foundation.IAsyncOperation<Windows.Storage.FileProperties.BasicProperties *>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Storage.FileProperties.BasicProperties),
        uuid(d05d55db-785e-4a66-be02-9beec58aea81)
    ]
    interface IBasicProperties : IInspectable
    {
        [propget] HRESULT Size([out, retval] UINT64 *value);
        [propget] HRESULT DateModified([out, retval] Windows.Foundation.DateTime *value);
        [propget] HRESULT ItemDate([out, retval] Windows.Foundation.DateTime *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    runtimeclass BasicProperties
    {
        [default] interface Windows.Storage.FileProperties.IBasicProperties;
        interface Windows.Storage.FileProperties.IStorageItemExtraProperties;
    }
}
