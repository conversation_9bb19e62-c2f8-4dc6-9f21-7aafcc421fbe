cpp_quote("/**")
cpp_quote(" * This file is part of the mingw-w64 runtime package.")
cpp_quote(" * No warranty is given; refer to the file DISCLAIMER within this package.")
cpp_quote(" */")
cpp_quote("")

import "dispex.idl";

cpp_quote("#include <winapifamily.h>")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

[uuid (565783c6-CB41-11d1-8b02-00600806d9b6), lcid (0x00), version (1.2)]
library WbemScripting {
  importlib ("stdole2.tlb");

  interface ISWbemDateTime;
  interface ISWbemEventSource;
  interface ISWbemLastError;
  interface ISWbemLocator;
  interface ISWbemMethod;
  interface ISWbemMethodSet;
  interface ISWbemNamedValue;
  interface ISWbemNamedValueSet;
  interface ISWbemObject;
  interface ISWbemObjectEx;
  interface ISWbemObjectPath;
  interface ISWbemObjectSet;
  interface ISWbemQualifier;
  interface ISWbemQualifierSet;
  interface ISWbemPrivilege;
  interface ISWbemPrivilegeSet;
  interface ISWbemProperty;
  interface ISWbemPropertySet;
  interface ISWbemRefresher;
  interface ISWbemRefreshableItem;
  interface ISWbemSecurity;
  interface ISWbemServices;
  interface ISWbemServicesEx;
  interface ISWbemSink;
  interface ISWbemSinkEvents;

  typedef [v1_enum, uuid (4a249b72-FC9A-11d1-8b1e-00600806d9b6)]
  enum WbemChangeFlagEnum {
    wbemChangeFlagCreateOrUpdate = 0x0,
    wbemChangeFlagUpdateOnly = 0x1,
    wbemChangeFlagCreateOnly = 0x2,
    wbemChangeFlagUpdateCompatible = 0x0,
    wbemChangeFlagUpdateSafeMode = 0x20,
    wbemChangeFlagUpdateForceMode = 0x40,
    wbemChangeFlagStrongValidation = 0x80,
    wbemChangeFlagAdvisory = 0x000010000
  } WbemChangeFlagEnum;

  typedef [v1_enum, uuid (4a249b73-FC9A-11d1-8b1e-00600806d9b6)]
  enum WbemFlagEnum {
    wbemFlagReturnImmediately = 0x10,
    wbemFlagReturnWhenComplete = 0,
    wbemFlagBidirectional = 0,
    wbemFlagForwardOnly = 0x20,
    wbemFlagNoErrorObject = 0x40,
    wbemFlagReturnErrorObject = 0,
    wbemFlagSendStatus = 0x80,
    wbemFlagDontSendStatus = 0,
    wbemFlagEnsureLocatable = 0x100,
    wbemFlagDirectRead = 0x200,
    wbemFlagSendOnlySelected = 0,
    wbemFlagUseAmendedQualifiers = 0x20000,
    wbemFlagGetDefault = 0x0,
    wbemFlagSpawnInstance = 0x1,
    wbemFlagUseCurrentTime = 0x1
  } WbemFlagEnum;

  typedef [v1_enum, uuid (4a249b76-FC9A-11d1-8b1e-00600806d9b6)]
  enum WbemQueryFlagEnum {
    wbemQueryFlagDeep = 0,
    wbemQueryFlagShallow = 1,
    wbemQueryFlagPrototype = 2
  } WbemQueryFlagEnum;

  typedef [v1_enum, uuid (4a249b78-FC9A-11d1-8b1e-00600806d9b6)]
  enum WbemTextFlagEnum {
    wbemTextFlagNoFlavors = 0x1
  } WbemTextFlagEnum;

  typedef [v1_enum, uuid (BF078C2A-07d9-11d2-8b21-00600806d9b6)]
  enum WbemTimeout {
    wbemTimeoutInfinite = 0xffffffff
  } WbemTimeout;

  typedef [v1_enum, uuid (4a249b79-FC9A-11d1-8b1e-00600806d9b6)]
  enum WbemComparisonFlagEnum {
    wbemComparisonFlagIncludeAll = 0,
    wbemComparisonFlagIgnoreQualifiers = 0x1,
    wbemComparisonFlagIgnoreObjectSource = 0x2,
    wbemComparisonFlagIgnoreDefaultValues = 0x4,
    wbemComparisonFlagIgnoreClass = 0x8,
    wbemComparisonFlagIgnoreCase = 0x10,
    wbemComparisonFlagIgnoreFlavor = 0x20
  } WbemComparisonFlagEnum;

  typedef [v1_enum, uuid (4a249b7b-FC9A-11d1-8b1e-00600806d9b6)]
  enum WbemCimtypeEnum {
    wbemCimtypeSint16 = 2,
    wbemCimtypeSint32 = 3,
    wbemCimtypeReal32 = 4,
    wbemCimtypeReal64 = 5,
    wbemCimtypeString = 8,
    wbemCimtypeBoolean = 11,
    wbemCimtypeObject = 13,
    wbemCimtypeSint8 = 16,
    wbemCimtypeUint8 = 17,
    wbemCimtypeUint16 = 18,
    wbemCimtypeUint32 = 19,
    wbemCimtypeSint64 = 20,
    wbemCimtypeUint64 = 21,
    wbemCimtypeDatetime = 101,
    wbemCimtypeReference = 102,
    wbemCimtypeChar16 = 103
  } WbemCimtypeEnum;

  typedef [v1_enum, uuid (4a249b7c-FC9A-11d1-8b1e-00600806d9b6)]
  enum WbemErrorEnum {
    wbemNoErr = 0,
    wbemErrFailed = 0x80041001,
    wbemErrNotFound = 0x80041002,
    wbemErrAccessDenied = 0x80041003,
    wbemErrProviderFailure = 0x80041004,
    wbemErrTypeMismatch = 0x80041005,
    wbemErrOutOfMemory = 0x80041006,
    wbemErrInvalidContext = 0x80041007,
    wbemErrInvalidParameter = 0x80041008,
    wbemErrNotAvailable = 0x80041009,
    wbemErrCriticalError = 0x8004100a,
    wbemErrInvalidStream = 0x8004100b,
    wbemErrNotSupported = 0x8004100c,
    wbemErrInvalidSuperclass = 0x8004100d,
    wbemErrInvalidNamespace = 0x8004100e,
    wbemErrInvalidObject = 0x8004100f,
    wbemErrInvalidClass = 0x80041010,
    wbemErrProviderNotFound = 0x80041011,
    wbemErrInvalidProviderRegistration = 0x80041012,
    wbemErrProviderLoadFailure = 0x80041013,
    wbemErrInitializationFailure = 0x80041014,
    wbemErrTransportFailure = 0x80041015,
    wbemErrInvalidOperation = 0x80041016,
    wbemErrInvalidQuery = 0x80041017,
    wbemErrInvalidQueryType = 0x80041018,
    wbemErrAlreadyExists = 0x80041019,
    wbemErrOverrideNotAllowed = 0x8004101a,
    wbemErrPropagatedQualifier = 0x8004101b,
    wbemErrPropagatedProperty = 0x8004101c,
    wbemErrUnexpected = 0x8004101d,
    wbemErrIllegalOperation = 0x8004101e,
    wbemErrCannotBeKey = 0x8004101f,
    wbemErrIncompleteClass = 0x80041020,
    wbemErrInvalidSyntax = 0x80041021,
    wbemErrNondecoratedObject = 0x80041022,
    wbemErrReadOnly = 0x80041023,
    wbemErrProviderNotCapable = 0x80041024,
    wbemErrClassHasChildren = 0x80041025,
    wbemErrClassHasInstances = 0x80041026,
    wbemErrQueryNotImplemented = 0x80041027,
    wbemErrIllegalNull = 0x80041028,
    wbemErrInvalidQualifierType = 0x80041029,
    wbemErrInvalidPropertyType = 0x8004102a,
    wbemErrValueOutOfRange = 0x8004102b,
    wbemErrCannotBeSingleton = 0x8004102c,
    wbemErrInvalidCimType = 0x8004102d,
    wbemErrInvalidMethod = 0x8004102e,
    wbemErrInvalidMethodParameters = 0x8004102f,
    wbemErrSystemProperty = 0x80041030,
    wbemErrInvalidProperty = 0x80041031,
    wbemErrCallCancelled = 0x80041032,
    wbemErrShuttingDown = 0x80041033,
    wbemErrPropagatedMethod = 0x80041034,
    wbemErrUnsupportedParameter = 0x80041035,
    wbemErrMissingParameter = 0x80041036,
    wbemErrInvalidParameterId = 0x80041037,
    wbemErrNonConsecutiveParameterIds = 0x80041038,
    wbemErrParameterIdOnRetval = 0x80041039,
    wbemErrInvalidObjectPath = 0x8004103a,
    wbemErrOutOfDiskSpace = 0x8004103b,
    wbemErrBufferTooSmall = 0x8004103c,
    wbemErrUnsupportedPutExtension = 0x8004103d,
    wbemErrUnknownObjectType = 0x8004103e,
    wbemErrUnknownPacketType = 0x8004103f,
    wbemErrMarshalVersionMismatch = 0x80041040,
    wbemErrMarshalInvalidSignature = 0x80041041,
    wbemErrInvalidQualifier = 0x80041042,
    wbemErrInvalidDuplicateParameter = 0x80041043,
    wbemErrTooMuchData = 0x80041044,
    wbemErrServerTooBusy = 0x80041045,
    wbemErrInvalidFlavor = 0x80041046,
    wbemErrCircularReference = 0x80041047,
    wbemErrUnsupportedClassUpdate = 0x80041048,
    wbemErrCannotChangeKeyInheritance = 0x80041049,
    wbemErrCannotChangeIndexInheritance = 0x80041050,
    wbemErrTooManyProperties = 0x80041051,
    wbemErrUpdateTypeMismatch = 0x80041052,
    wbemErrUpdateOverrideNotAllowed = 0x80041053,
    wbemErrUpdatePropagatedMethod = 0x80041054,
    wbemErrMethodNotImplemented = 0x80041055,
    wbemErrMethodDisabled = 0x80041056,
    wbemErrRefresherBusy = 0x80041057,
    wbemErrUnparsableQuery = 0x80041058,
    wbemErrNotEventClass = 0x80041059,
    wbemErrMissingGroupWithin = 0x8004105a,
    wbemErrMissingAggregationList = 0x8004105b,
    wbemErrPropertyNotAnObject = 0x8004105c,
    wbemErrAggregatingByObject = 0x8004105d,
    wbemErrUninterpretableProviderQuery = 0x8004105f,
    wbemErrBackupRestoreWinmgmtRunning = 0x80041060,
    wbemErrQueueOverflow = 0x80041061,
    wbemErrPrivilegeNotHeld = 0x80041062,
    wbemErrInvalidOperator = 0x80041063,
    wbemErrLocalCredentials = 0x80041064,
    wbemErrCannotBeAbstract = 0x80041065,
    wbemErrAmendedObject = 0x80041066,
    wbemErrClientTooSlow = 0x80041067,
    wbemErrNullSecurityDescriptor = 0x80041068,
    wbemErrTimeout = 0x80041069,
    wbemErrInvalidAssociation = 0x8004106a,
    wbemErrAmbiguousOperation = 0x8004106b,
    wbemErrQuotaViolation = 0x8004106c,
    wbemErrTransactionConflict = 0x8004106d,
    wbemErrForcedRollback = 0x8004106e,
    wbemErrUnsupportedLocale = 0x8004106f,
    wbemErrHandleOutOfDate = 0x80041070,
    wbemErrConnectionFailed = 0x80041071,
    wbemErrInvalidHandleRequest = 0x80041072,
    wbemErrPropertyNameTooWide = 0x80041073,
    wbemErrClassNameTooWide = 0x80041074,
    wbemErrMethodNameTooWide = 0x80041075,
    wbemErrQualifierNameTooWide = 0x80041076,
    wbemErrRerunCommand = 0x80041077,
    wbemErrDatabaseVerMismatch = 0x80041078,
    wbemErrVetoPut = 0x80041079,
    wbemErrVetoDelete = 0x8004107a,

    wbemErrInvalidLocale = 0x80041080,
    wbemErrProviderSuspended = 0x80041081,
    wbemErrSynchronizationRequired = 0x80041082,
    wbemErrNoSchema = 0x80041083,
    wbemErrProviderAlreadyRegistered = 0x80041084,
    wbemErrProviderNotRegistered = 0x80041085,
    wbemErrFatalTransportError = 0x80041086,
    wbemErrEncryptedConnectionRequired = 0x80041087,
    wbemErrRegistrationTooBroad = 0x80042001,
    wbemErrRegistrationTooPrecise = 0x80042002,
    wbemErrTimedout = 0x80043001,
    wbemErrResetToDefault = 0x80043002
  } WbemErrorEnum;

  typedef [v1_enum, uuid (B54D66E7-2287-11d2-8b33-00600806d9b6)]
  enum WbemAuthenticationLevelEnum {
    wbemAuthenticationLevelDefault = 0,
    wbemAuthenticationLevelNone = 1,
    wbemAuthenticationLevelConnect = 2,
    wbemAuthenticationLevelCall = 3,
    wbemAuthenticationLevelPkt = 4,
    wbemAuthenticationLevelPktIntegrity = 5,
    wbemAuthenticationLevelPktPrivacy = 6
  } WbemAuthenticationLevelEnum;

  typedef [v1_enum, uuid (B54D66E8-2287-11d2-8b33-00600806d9b6)]
  enum WbemImpersonationLevelEnum {
    wbemImpersonationLevelAnonymous = 1,
    wbemImpersonationLevelIdentify = 2,
    wbemImpersonationLevelImpersonate = 3,
    wbemImpersonationLevelDelegate = 4
  } WbemImpersonationLevelEnum;

  typedef [v1_enum, uuid (176d2f70-5af3-11d2-8b4a-00600806d9b6)]
  enum WbemPrivilegeEnum {
    wbemPrivilegeCreateToken = 1,
    wbemPrivilegePrimaryToken = 2,
    wbemPrivilegeLockMemory = 3,
    wbemPrivilegeIncreaseQuota = 4,
    wbemPrivilegeMachineAccount = 5,
    wbemPrivilegeTcb = 6,
    wbemPrivilegeSecurity = 7,
    wbemPrivilegeTakeOwnership = 8,
    wbemPrivilegeLoadDriver = 9,
    wbemPrivilegeSystemProfile = 10,
    wbemPrivilegeSystemtime = 11,
    wbemPrivilegeProfileSingleProcess = 12,
    wbemPrivilegeIncreaseBasePriority = 13,
    wbemPrivilegeCreatePagefile = 14,
    wbemPrivilegeCreatePermanent = 15,
    wbemPrivilegeBackup = 16,
    wbemPrivilegeRestore = 17,
    wbemPrivilegeShutdown = 18,
    wbemPrivilegeDebug = 19,
    wbemPrivilegeAudit = 20,
    wbemPrivilegeSystemEnvironment = 21,
    wbemPrivilegeChangeNotify = 22,
    wbemPrivilegeRemoteShutdown = 23,
    wbemPrivilegeUndock = 24,
    wbemPrivilegeSyncAgent = 25,
    wbemPrivilegeEnableDelegation = 26,
    wbemPrivilegeManageVolume = 27
  } WbemPrivilegeEnum;

  typedef [v1_enum, uuid (09ff1992-EA0E-11d3-B391-00105a1f473a)]
  enum WbemObjectTextFormatEnum {
    wbemObjectTextFormatCIMDTD20 = 1,
    wbemObjectTextFormatWMIDTD20 = 2
  } WbemObjectTextFormatEnum;

  typedef [v1_enum] enum WbemConnectOptionsEnum {
    wbemConnectFlagUseMaxWait = 0x80
  } WbemConnectOptionsEnum;

const ULONG WBEMS_DISPID_OBJECT_READY = 1;
const ULONG WBEMS_DISPID_COMPLETED = 2;
const ULONG WBEMS_DISPID_PROGRESS = 3;
const ULONG WBEMS_DISPID_OBJECT_PUT = 4;
const ULONG WBEMS_DISPID_CONNECTION_READY = 5;

const ULONG WBEMS_DISPID_DERIVATION = 23;

  [uuid (76a64158-CB41-11d1-8b02-00600806d9b6)]
  coclass SWbemLocator {
    interface ISWbemLocator;
  };

  [uuid (9aed384e-CE8B-11d1-8b05-00600806d9b6)]
  coclass SWbemNamedValueSet {
    interface ISWbemNamedValueSet;
  };

  [uuid (5791bc26-CE9C-11d1-97bf-0000f81e849c)]
  coclass SWbemObjectPath {
    interface ISWbemObjectPath;
  };

  [uuid (C2FEEEAC-CFCD-11d1-8b05-00600806d9b6)]
  coclass SWbemLastError {
    interface ISWbemLastError;
  };

  [uuid (75718c9a-F029-11d1-A1AC-00c04fb6c223)]
  coclass SWbemSink {
    interface ISWbemSink;
    [default, source] dispinterface ISWbemSinkEvents;
  };

  [uuid (47dfbe54-CF76-11d3-B38F-00105a1f473a)]
  coclass SWbemDateTime {
    interface ISWbemDateTime;
  };

  [uuid (D269BF5C-D9C1-11d3-B38F-00105a1f473a)]
  coclass SWbemRefresher {
    interface ISWbemRefresher;
  };

  [uuid (04b83d63-21ae-11d2-8b33-00600806d9b6), noncreatable]
  coclass SWbemServices {
    interface ISWbemServices;
  };

  [uuid (62e522dc-8cf3-40a8-8b2e-37d595651e40), noncreatable]
  coclass SWbemServicesEx {
    interface ISWbemServicesEx;
  };

  [uuid (04b83d62-21ae-11d2-8b33-00600806d9b6), noncreatable]
  coclass SWbemObject {
    interface ISWbemObject;
  };

  [uuid (D6BDAFB2-9435-491f-BB87-6aa0f0bc31a2), noncreatable]
  coclass SWbemObjectEx {
    interface ISWbemObjectEx;
  };

  [uuid (04b83d61-21ae-11d2-8b33-00600806d9b6), noncreatable]
  coclass SWbemObjectSet {
    interface ISWbemObjectSet;
  };

  [uuid (04b83d60-21ae-11d2-8b33-00600806d9b6), noncreatable]
  coclass SWbemNamedValue {
    interface ISWbemNamedValue;
  };

  [uuid (04b83d5f-21ae-11d2-8b33-00600806d9b6), noncreatable]
  coclass SWbemQualifier {
    interface ISWbemQualifier;
  }

  [uuid (04b83d5e-21ae-11d2-8b33-00600806d9b6), noncreatable]
  coclass SWbemQualifierSet {
    interface ISWbemQualifierSet;
  };

  [uuid (04b83d5d-21ae-11d2-8b33-00600806d9b6), noncreatable]
  coclass SWbemProperty {
    interface ISWbemProperty;
  };

  [uuid (04b83d5c-21ae-11d2-8b33-00600806d9b6), noncreatable]
  coclass SWbemPropertySet {
    interface ISWbemPropertySet;
  };

  [uuid (04b83d5b-21ae-11d2-8b33-00600806d9b6), noncreatable]
  coclass SWbemMethod {
    interface ISWbemMethod;
  };

  [uuid (04b83d5a-21ae-11d2-8b33-00600806d9b6), noncreatable]
  coclass SWbemMethodSet {
    interface ISWbemMethodSet;
  }

  [uuid (04b83d58-21ae-11d2-8b33-00600806d9b6), noncreatable]
  coclass SWbemEventSource {
    interface ISWbemEventSource;
  };

  [uuid (B54D66E9-2287-11d2-8b33-00600806d9b6), noncreatable]
  coclass SWbemSecurity {
    interface ISWbemSecurity;
  }

  [uuid (26ee67bc-5804-11d2-8b4a-00600806d9b6), noncreatable]
  coclass SWbemPrivilege {
    interface ISWbemPrivilege;
  }

  [uuid (26ee67be-5804-11d2-8b4a-00600806d9b6), noncreatable]
  coclass SWbemPrivilegeSet {
    interface ISWbemPrivilegeSet;
  }

  [uuid (8c6854bc-DE4B-11d3-B390-00105a1f473a), noncreatable]
  coclass SWbemRefreshableItem {
    interface ISWbemRefreshableItem;
  }
};

[object, local, uuid (76a6415b-CB41-11d1-8b02-00600806d9b6), dual, hidden, oleautomation, pointer_default (unique)]
interface ISWbemLocator : IDispatch {
  [id (1)] HRESULT ConnectServer ([in, defaultvalue (L".")] BSTR strServer,[in, defaultvalue (L"")] BSTR strNamespace,[in, defaultvalue (L"")] BSTR strUser,[in, defaultvalue (L"")] BSTR strPassword,[in, defaultvalue (L"")] BSTR strLocale,[in, defaultvalue (L"")] BSTR strAuthority,[in, defaultvalue (0)] long iSecurityFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemServices **objWbemServices);
  [id (2), propget] HRESULT Security_ ([out, retval] ISWbemSecurity **objWbemSecurity);
};

[local, object, uuid (76a6415c-CB41-11d1-8b02-00600806d9b6), oleautomation, dual, pointer_default (unique), hidden]
interface ISWbemServices : IDispatch {
  [id (1)] HRESULT Get ([in, defaultvalue (L"")] BSTR strObjectPath,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemObject **objWbemObject);
  [id (2)] HRESULT GetAsync ([in] IDispatch *objWbemSink,[in, defaultvalue (L"")] BSTR strObjectPath,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[in, defaultvalue (0)] IDispatch *objWbemAsyncContext);
  [id (3)] HRESULT Delete ([in] BSTR strObjectPath,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet);
  [id (4)] HRESULT DeleteAsync ([in] IDispatch *objWbemSink,[in] BSTR strObjectPath,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[in, defaultvalue (0)] IDispatch *objWbemAsyncContext);
  [id (5)] HRESULT InstancesOf ([in] BSTR strClass,[in, defaultvalue (wbemFlagReturnImmediately)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemObjectSet **objWbemObjectSet);
  [id (6)] HRESULT InstancesOfAsync ([in] IDispatch *objWbemSink,[in] BSTR strClass,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[in, defaultvalue (0)] IDispatch *objWbemAsyncContext);
  [id (7)] HRESULT SubclassesOf ([in, defaultvalue (L"")] BSTR strSuperclass,[in, defaultvalue (wbemFlagReturnImmediately|wbemQueryFlagDeep)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemObjectSet **objWbemObjectSet);
  [id (8)] HRESULT SubclassesOfAsync ([in] IDispatch *objWbemSink,[in, defaultvalue (L"")] BSTR strSuperclass,[in, defaultvalue (wbemQueryFlagDeep)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[in, defaultvalue (0)] IDispatch *objWbemAsyncContext);
  [id (9)] HRESULT ExecQuery ([in] BSTR strQuery,[in, defaultvalue (L"WQL")] BSTR strQueryLanguage,[in, defaultvalue (wbemFlagReturnImmediately)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemObjectSet **objWbemObjectSet);
  [id (10)] HRESULT ExecQueryAsync ([in] IDispatch *objWbemSink,[in] BSTR strQuery,[in, defaultvalue (L"WQL")] BSTR strQueryLanguage,[in, defaultvalue (0)] long lFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[in, defaultvalue (0)] IDispatch *objWbemAsyncContext);
  [id (11)] HRESULT AssociatorsOf ([in] BSTR strObjectPath,[in, defaultvalue (L"")] BSTR strAssocClass,[in, defaultvalue (L"")] BSTR strResultClass,[in, defaultvalue (L"")] BSTR strResultRole,[in, defaultvalue (L"")] BSTR strRole,[in, defaultvalue (FALSE)] VARIANT_BOOL bClassesOnly,[in, defaultvalue (FALSE)] VARIANT_BOOL bSchemaOnly,[in, defaultvalue (L"")] BSTR strRequiredAssocQualifier,[in, defaultvalue (L"")] BSTR strRequiredQualifier,[in, defaultvalue (wbemFlagReturnImmediately)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemObjectSet **objWbemObjectSet);
  [id (12)] HRESULT AssociatorsOfAsync ([in] IDispatch *objWbemSink,[in] BSTR strObjectPath,[in, defaultvalue (L"")] BSTR strAssocClass,[in, defaultvalue (L"")] BSTR strResultClass,[in, defaultvalue (L"")] BSTR strResultRole,[in, defaultvalue (L"")] BSTR strRole,[in, defaultvalue (FALSE)] VARIANT_BOOL bClassesOnly,[in, defaultvalue (FALSE)] VARIANT_BOOL bSchemaOnly,[in, defaultvalue (L"")] BSTR strRequiredAssocQualifier,[in, defaultvalue (L"")] BSTR strRequiredQualifier,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[in, defaultvalue (0)] IDispatch *objWbemAsyncContext);
  [id (13)] HRESULT ReferencesTo ([in] BSTR strObjectPath,[in, defaultvalue (L"")] BSTR strResultClass,[in, defaultvalue (L"")] BSTR strRole,[in, defaultvalue (FALSE)] VARIANT_BOOL bClassesOnly,[in, defaultvalue (FALSE)] VARIANT_BOOL bSchemaOnly,[in, defaultvalue (L"")] BSTR strRequiredQualifier,[in, defaultvalue (wbemFlagReturnImmediately)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemObjectSet **objWbemObjectSet);
  [id (14)] HRESULT ReferencesToAsync ([in] IDispatch *objWbemSink,[in] BSTR strObjectPath,[in, defaultvalue (L"")] BSTR strResultClass,[in, defaultvalue (L"")] BSTR strRole,[in, defaultvalue (FALSE)] VARIANT_BOOL bClassesOnly,[in, defaultvalue (FALSE)] VARIANT_BOOL bSchemaOnly,[in, defaultvalue (L"")] BSTR strRequiredQualifier,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[in, defaultvalue (0)] IDispatch *objWbemAsyncContext);
  [id (15)] HRESULT ExecNotificationQuery ([in] BSTR strQuery,[in, defaultvalue (L"WQL")] BSTR strQueryLanguage,[in, defaultvalue (wbemFlagReturnImmediately|wbemFlagForwardOnly)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemEventSource **objWbemEventSource);
  [id (16)] HRESULT ExecNotificationQueryAsync ([in] IDispatch *objWbemSink,[in] BSTR strQuery,[in, defaultvalue (L"WQL")] BSTR strQueryLanguage,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[in, defaultvalue (0)] IDispatch *objWbemAsyncContext);
  [id (17)] HRESULT ExecMethod ([in] BSTR strObjectPath,[in] BSTR strMethodName,[in, defaultvalue (0)] IDispatch *objWbemInParameters,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemObject **objWbemOutParameters);
  [id (18)] HRESULT ExecMethodAsync ([in] IDispatch *objWbemSink,[in] BSTR strObjectPath,[in] BSTR strMethodName,[in, defaultvalue (0)] IDispatch *objWbemInParameters,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[in, defaultvalue (0)] IDispatch *objWbemAsyncContext);
  [id (19), propget] HRESULT Security_ ([out, retval] ISWbemSecurity **objWbemSecurity);
};

[local, object, uuid (d2f68443-85dc-427e-91d8-366554cc754c), oleautomation, dual, nonextensible, pointer_default (unique), hidden]
interface ISWbemServicesEx : ISWbemServices {
  [id (20)] HRESULT Put ([in] ISWbemObjectEx *objWbemObject,[in, defaultvalue (wbemChangeFlagCreateOrUpdate)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemObjectPath **objWbemObjectPath);
  [id (21)] HRESULT PutAsync ([in] ISWbemSink *objWbemSink,[in] ISWbemObjectEx *objWbemObject,[in, defaultvalue (wbemChangeFlagCreateOrUpdate)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[in, defaultvalue (0)] IDispatch *objWbemAsyncContext);
};

[local, object, uuid (76a6415a-cb41-11d1-8b02-00600806d9b6), dual, oleautomation, hidden]
interface ISWbemObject : IDispatch {
  [id (1)] HRESULT Put_ ([in, defaultvalue (wbemChangeFlagCreateOrUpdate)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemObjectPath **objWbemObjectPath);
  [id (2)] HRESULT PutAsync_ ([in] IDispatch *objWbemSink,[in, defaultvalue (wbemChangeFlagCreateOrUpdate)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[in, defaultvalue (0)] IDispatch *objWbemAsyncContext);
  [id (3)] HRESULT Delete_ ([in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet);
  [id (4)] HRESULT DeleteAsync_ ([in] IDispatch *objWbemSink,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[in, defaultvalue (0)] IDispatch *objWbemAsyncContext);
  [id (5)] HRESULT Instances_ ([in, defaultvalue (wbemFlagReturnImmediately)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemObjectSet **objWbemObjectSet);
  [id (6)] HRESULT InstancesAsync_ ([in] IDispatch *objWbemSink,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[in, defaultvalue (0)] IDispatch *objWbemAsyncContext);
  [id (7)] HRESULT Subclasses_ ([in, defaultvalue (wbemFlagReturnImmediately|wbemQueryFlagDeep)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemObjectSet **objWbemObjectSet);
  [id (8)] HRESULT SubclassesAsync_ ([in] IDispatch *objWbemSink,[in, defaultvalue (wbemQueryFlagDeep)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[in, defaultvalue (0)] IDispatch *objWbemAsyncContext);
  [id (9)] HRESULT Associators_ ([in, defaultvalue (L"")] BSTR strAssocClass,[in, defaultvalue (L"")] BSTR strResultClass,[in, defaultvalue (L"")] BSTR strResultRole,[in, defaultvalue (L"")] BSTR strRole,[in, defaultvalue (FALSE)] VARIANT_BOOL bClassesOnly,[in, defaultvalue (FALSE)] VARIANT_BOOL bSchemaOnly,[in, defaultvalue (L"")] BSTR strRequiredAssocQualifier,[in, defaultvalue (L"")] BSTR strRequiredQualifier,[in, defaultvalue (wbemFlagReturnImmediately)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemObjectSet **objWbemObjectSet);
  [id (10)] HRESULT AssociatorsAsync_ ([in] IDispatch *objWbemSink,[in, defaultvalue (L"")] BSTR strAssocClass,[in, defaultvalue (L"")] BSTR strResultClass,[in, defaultvalue (L"")] BSTR strResultRole,[in, defaultvalue (L"")] BSTR strRole,[in, defaultvalue (FALSE)] VARIANT_BOOL bClassesOnly,[in, defaultvalue (FALSE)] VARIANT_BOOL bSchemaOnly,[in, defaultvalue (L"")] BSTR strRequiredAssocQualifier,[in, defaultvalue (L"")] BSTR strRequiredQualifier,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[in, defaultvalue (0)] IDispatch *objWbemAsyncContext);
  [id (11)] HRESULT References_ ([in, defaultvalue (L"")] BSTR strResultClass,[in, defaultvalue (L"")] BSTR strRole,[in, defaultvalue (FALSE)] VARIANT_BOOL bClassesOnly,[in, defaultvalue (FALSE)] VARIANT_BOOL bSchemaOnly,[in, defaultvalue (L"")] BSTR strRequiredQualifier,[in, defaultvalue (wbemFlagReturnImmediately)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemObjectSet **objWbemObjectSet);
  [id (12)] HRESULT ReferencesAsync_ ([in] IDispatch *objWbemSink,[in, defaultvalue (L"")] BSTR strResultClass,[in, defaultvalue (L"")] BSTR strRole,[in, defaultvalue (FALSE)] VARIANT_BOOL bClassesOnly,[in, defaultvalue (FALSE)] VARIANT_BOOL bSchemaOnly,[in, defaultvalue (L"")] BSTR strRequiredQualifier,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[in, defaultvalue (0)] IDispatch *objWbemAsyncContext);
  [id (13)] HRESULT ExecMethod_ ([in] BSTR strMethodName,[in, defaultvalue (0)] IDispatch *objWbemInParameters,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemObject **objWbemOutParameters);
  [id (14)] HRESULT ExecMethodAsync_ ([in] IDispatch *objWbemSink,[in] BSTR strMethodName,[in, defaultvalue (0)] IDispatch *objWbemInParameters,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[in, defaultvalue (0)] IDispatch *objWbemAsyncContext);
  [id (15)] HRESULT Clone_ ([out, retval] ISWbemObject **objWbemObject);
  [id (16)] HRESULT GetObjectText_ ([in, defaultvalue (0)] long iFlags,[defaultvalue (0), out, retval] BSTR *strObjectText);
  [id (17)] HRESULT SpawnDerivedClass_ ([in, defaultvalue (0)] long iFlags,[defaultvalue (0), out, retval] ISWbemObject **objWbemObject);
  [id (18)] HRESULT SpawnInstance_ ([in, defaultvalue (0)] long iFlags,[defaultvalue (0), out, retval] ISWbemObject **objWbemObject);
  [id (19)] HRESULT CompareTo_ ([in] IDispatch *objWbemObject,[in, defaultvalue (wbemComparisonFlagIncludeAll)] long iFlags,[defaultvalue (0), out, retval] VARIANT_BOOL *bResult);
  [id (20), propget] HRESULT Qualifiers_ ([out, retval] ISWbemQualifierSet **objWbemQualifierSet);
  [id (21), propget] HRESULT Properties_ ([out, retval] ISWbemPropertySet **objWbemPropertySet);
  [id (22), propget] HRESULT Methods_ ([out, retval] ISWbemMethodSet **objWbemMethodSet);
  [id (23), propget] HRESULT Derivation_ ([out, retval] VARIANT *strClassNameArray);
  [id (24), propget] HRESULT Path_ ([out, retval] ISWbemObjectPath **objWbemObjectPath);
  [id (25), propget] HRESULT Security_ ([out, retval] ISWbemSecurity **objWbemSecurity);
};

[local, object, uuid (269ad56a-8a67-4129-bc8c-0506dcfe9880), dual, oleautomation, hidden]
interface ISWbemObjectEx : ISWbemObject {
  [id (26)] HRESULT Refresh_ ([in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet);
  [id (27), propget] HRESULT SystemProperties_ ([out, retval] ISWbemPropertySet **objWbemPropertySet);
  [id (28)] HRESULT GetText_ ([in] WbemObjectTextFormatEnum iObjectTextFormat,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] BSTR *bsText);
  [id (29)] HRESULT SetFromText_ ([in] BSTR bsText,[in] WbemObjectTextFormatEnum iObjectTextFormat,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet);
};

[local, object, uuid (d962db84-d4bb-11d1-8b09-00600806d9b6), dual, oleautomation, hidden]
interface ISWbemLastError : ISWbemObject {
};

[local, object, uuid (76a6415f-cb41-11d1-8b02-00600806d9b6), oleautomation, dual, hidden, nonextensible]
interface ISWbemObjectSet : IDispatch {
  [id (DISPID_NEWENUM), propget, restricted] HRESULT _NewEnum ([out, retval] IUnknown **pUnk);
  [id (DISPID_VALUE)] HRESULT Item ([in] BSTR strObjectPath,[in, defaultvalue (0)] long iFlags,[defaultvalue (0), out, retval] ISWbemObject **objWbemObject);
  [id (1), propget] HRESULT Count ([out, retval] long *iCount);
  [id (4), propget] HRESULT Security_ ([out, retval] ISWbemSecurity **objWbemSecurity);
  [id (5)] HRESULT ItemIndex ([in] long lIndex,[out, retval] ISWbemObject **objWbemObject);
};

[local, object, uuid (cf2376ea-ce8c-11d1-8b05-00600806d9b6), oleautomation, dual, hidden]
interface ISWbemNamedValueSet : IDispatch {
  [id (DISPID_NEWENUM), propget, restricted] HRESULT _NewEnum ([out, retval] IUnknown **pUnk);
  [id (DISPID_VALUE)] HRESULT Item ([in] BSTR strName,[in, defaultvalue (0)] long iFlags,[defaultvalue (0), out, retval] ISWbemNamedValue **objWbemNamedValue);
  [id (1), propget] HRESULT Count ([out, retval] long *iCount);
  [id (2)] HRESULT Add ([in] BSTR strName,[in] VARIANT *varValue,[in, defaultvalue (0)] long iFlags,[defaultvalue (0), out, retval] ISWbemNamedValue **objWbemNamedValue);
  [id (3)] HRESULT Remove ([in] BSTR strName,[in, defaultvalue (0)] long iFlags);
  [id (4)] HRESULT Clone ([out, retval] ISWbemNamedValueSet **objWbemNamedValueSet);
  [id (5)] HRESULT DeleteAll ();
};

[local, object, uuid (76a64164-CB41-11d1-8b02-00600806d9b6), dual, oleautomation, hidden]
interface ISWbemNamedValue : IDispatch {
  [id (DISPID_VALUE), propget] HRESULT Value ([out, retval] VARIANT *varValue);
  [id (DISPID_VALUE), propput] HRESULT Value ([in] VARIANT *varValue);
  [id (2), propget] HRESULT Name ([out, retval] BSTR *strName);
};

[object, local, uuid (5791bc27-CE9C-11d1-97bf-0000f81e849c), dual, oleautomation, hidden, pointer_default (unique)]
interface ISWbemObjectPath : IDispatch {
  [id (DISPID_VALUE), propget] HRESULT Path ([out, retval] BSTR *strPath);
  [id (DISPID_VALUE), propput] HRESULT Path ([in] BSTR strPath);
  [id (1), propget] HRESULT RelPath ([out, retval] BSTR *strRelPath);
  [id (1), propput] HRESULT RelPath ([in] BSTR strRelPath);
  [id (2), propget] HRESULT Server ([out, retval] BSTR *strServer);
  [id (2), propput] HRESULT Server ([in] BSTR strServer);
  [id (3), propget] HRESULT Namespace ([out, retval] BSTR *strNamespace);
  [id (3), propput] HRESULT Namespace ([in] BSTR strNamespace);
  [id (4), propget] HRESULT ParentNamespace ([out, retval] BSTR *strParentNamespace);
  [id (5), propget] HRESULT DisplayName ([out, retval] BSTR *strDisplayName);
  [id (5), propput] HRESULT DisplayName ([in] BSTR strDisplayName);
  [id (6), propget] HRESULT Class ([out, retval] BSTR *strClass);
  [id (6), propput] HRESULT Class ([in] BSTR strClass);
  [id (7), propget] HRESULT IsClass ([out, retval] VARIANT_BOOL *bIsClass);
  [id (8)] HRESULT SetAsClass ();
  [id (9), propget] HRESULT IsSingleton ([out, retval] VARIANT_BOOL *bIsSingleton);
  [id (10)] HRESULT SetAsSingleton ();
  [id (11), propget] HRESULT Keys ([out, retval] ISWbemNamedValueSet **objWbemNamedValueSet);
  [id (12), propget] HRESULT Security_ ([out, retval] ISWbemSecurity **objWbemSecurity);
  [id (13), propget] HRESULT Locale ([out, retval] BSTR *strLocale);
  [id (13), propput] HRESULT Locale ([in] BSTR strLocale);
  [id (14), propget] HRESULT Authority ([out, retval] BSTR *strAuthority);
  [id (14), propput] HRESULT Authority ([in] BSTR strAuthority);
};

[object, local, uuid (1a388f98-D4BA-11d1-8b09-00600806d9b6), dual, oleautomation, hidden, pointer_default (unique)]
interface ISWbemProperty : IDispatch {
  [id (DISPID_VALUE), propget] HRESULT Value ([out, retval] VARIANT *varValue);
  [id (DISPID_VALUE), propput] HRESULT Value ([in] VARIANT *varValue);
  [id (1), propget] HRESULT Name ([out, retval] BSTR *strName);
  [id (2), propget] HRESULT IsLocal ([out, retval] VARIANT_BOOL *bIsLocal);
  [id (3), propget] HRESULT Origin ([out, retval] BSTR *strOrigin);
  [id (4), propget] HRESULT CIMType ([out, retval] WbemCimtypeEnum *iCimType);
  [id (5), propget] HRESULT Qualifiers_ ([out, retval] ISWbemQualifierSet **objWbemQualifierSet);
  [id (6), propget] HRESULT IsArray ([out, retval] VARIANT_BOOL *bIsArray);
};

[object, local, uuid (dea0a7B2-d4ba-11d1-8b09-00600806d9b6), oleautomation, dual, hidden]
interface ISWbemPropertySet : IDispatch {
  [id (DISPID_NEWENUM), propget, restricted] HRESULT _NewEnum ([out, retval] IUnknown **pUnk);
  [id (DISPID_VALUE)] HRESULT Item ([in] BSTR strName,[in, defaultvalue (0)] long iFlags,[defaultvalue (0), out, retval] ISWbemProperty **objWbemProperty);
  [id (1), propget] HRESULT Count ([out, retval] long *iCount);
  [id (2)] HRESULT Add ([in] BSTR strName,[in] WbemCimtypeEnum iCIMType,[in, defaultvalue (FALSE)] VARIANT_BOOL bIsArray,[in, defaultvalue (0)] long iFlags,[defaultvalue (0), out, retval] ISWbemProperty **objWbemProperty);
  [id (3)] HRESULT Remove ([in] BSTR strName,[in, defaultvalue (0)] long iFlags);
};

[object, local, uuid (79b05932-D3B7-11d1-8b06-00600806d9b6), dual, oleautomation, hidden, nonextensible, pointer_default (unique)]
interface ISWbemQualifier : IDispatch {
  [id (DISPID_VALUE), propget] HRESULT Value ([out, retval] VARIANT *varValue);
  [id (DISPID_VALUE), propput] HRESULT Value ([in] VARIANT *varValue);
  [id (1), propget] HRESULT Name ([out, retval] BSTR *strName);
  [id (2), propget] HRESULT IsLocal ([out, retval] VARIANT_BOOL *bIsLocal);
  [id (3), propget] HRESULT PropagatesToSubclass ([out, retval] VARIANT_BOOL *bPropagatesToSubclass);
  [id (3), propput] HRESULT PropagatesToSubclass ([in] VARIANT_BOOL bPropagatesToSubclass);
  [id (4), propget] HRESULT PropagatesToInstance ([out, retval] VARIANT_BOOL *bPropagatesToInstance);
  [id (4), propput] HRESULT PropagatesToInstance ([in] VARIANT_BOOL bPropagatesToInstance);
  [id (5), propget] HRESULT IsOverridable ([out, retval] VARIANT_BOOL *bIsOverridable);
  [id (5), propput] HRESULT IsOverridable ([in] VARIANT_BOOL bIsOverridable);
  [id (6), propget] HRESULT IsAmended ([out, retval] VARIANT_BOOL *bIsAmended);
};

[object, local, uuid (9b16ed16-d3df-11d1-8b08-00600806d9b6), oleautomation, dual, hidden, nonextensible]
interface ISWbemQualifierSet : IDispatch {
  [id (DISPID_NEWENUM), propget, restricted] HRESULT _NewEnum ([out, retval] IUnknown **pUnk);
  [id (DISPID_VALUE)] HRESULT Item ([in] BSTR name,[in, defaultvalue (0)] long iFlags,[defaultvalue (0), out, retval] ISWbemQualifier **objWbemQualifier);
  [id (1), propget] HRESULT Count ([out, retval] long *iCount);
  [id (2)] HRESULT Add ([in] BSTR strName,[in] VARIANT *varVal,[in, defaultvalue (TRUE)] VARIANT_BOOL bPropagatesToSubclass,[in, defaultvalue (TRUE)] VARIANT_BOOL bPropagatesToInstance,[in, defaultvalue (TRUE)] VARIANT_BOOL bIsOverridable,[in, defaultvalue (0)] long iFlags,[defaultvalue (0), out, retval] ISWbemQualifier **objWbemQualifier);
  [id (3)] HRESULT Remove ([in] BSTR strName,[in, defaultvalue (0)] long iFlags);
};

[object, local, uuid (422e8e90-D955-11d1-8b09-00600806d9b6), dual, oleautomation, pointer_default (unique), nonextensible, hidden]
interface ISWbemMethod : IDispatch {
  [id (1), propget] HRESULT Name ([out, retval] BSTR *strName);
  [id (2), propget] HRESULT Origin ([out, retval] BSTR *strOrigin);
  [id (3), propget] HRESULT InParameters ([out, retval] ISWbemObject **objWbemInParameters);
  [id (4), propget] HRESULT OutParameters ([out, retval] ISWbemObject **objWbemOutParameters);
  [id (5), propget] HRESULT Qualifiers_ ([out, retval] ISWbemQualifierSet **objWbemQualifierSet);
};

[local, object, uuid (c93ba292-d955-11d1-8b09-00600806d9b6), oleautomation, dual, nonextensible, hidden]
interface ISWbemMethodSet : IDispatch {
  [id (DISPID_NEWENUM), propget, restricted] HRESULT _NewEnum ([out, retval] IUnknown **pUnk);
  [id (DISPID_VALUE)] HRESULT Item ([in] BSTR strName,[in, defaultvalue (0)] long iFlags,[defaultvalue (0), out, retval] ISWbemMethod **objWbemMethod);
  [id (1), propget] HRESULT Count ([out, retval] long *iCount);
};

[object, uuid (75718c9f-f029-11d1-a1ac-00c04fb6c223), oleautomation, dual, hidden, nonextensible]
interface ISWbemSink : IDispatch {
  [id (1)] HRESULT Cancel ();
};

[uuid (75718ca0-f029-11d1-a1ac-00c04fb6c223), hidden] /* , nonextensible */
dispinterface ISWbemSinkEvents {
  properties:
  methods:
  [id (WBEMS_DISPID_OBJECT_READY)] void OnObjectReady (ISWbemObject *objWbemObject, ISWbemNamedValueSet *objWbemAsyncContext);
  [id (WBEMS_DISPID_COMPLETED)] void OnCompleted (WbemErrorEnum iHResult, ISWbemObject *objWbemErrorObject, ISWbemNamedValueSet *objWbemAsyncContext);
  [id (WBEMS_DISPID_PROGRESS)] void OnProgress (long iUpperBound, long iCurrent, BSTR strMessage, ISWbemNamedValueSet *objWbemAsyncContext);
  [id (WBEMS_DISPID_OBJECT_PUT)] void OnObjectPut (ISWbemObjectPath *objWbemObjectPath, ISWbemNamedValueSet *objWbemAsyncContext);
};

[object, local, uuid (27d54d92-0ebe-11d2-8b22-00600806d9b6), oleautomation, dual, nonextensible, hidden]
interface ISWbemEventSource : IDispatch {
  [id (1)] HRESULT NextEvent ([in, defaultvalue (wbemTimeoutInfinite)] long iTimeoutMs,[defaultvalue (0), out, retval] ISWbemObject **objWbemObject);
  [id (2), propget] HRESULT Security_ ([out, retval] ISWbemSecurity **objWbemSecurity);
};

[object, local, uuid (b54d66e6-2287-11d2-8b33-00600806d9b6), oleautomation, dual, nonextensible, hidden]
interface ISWbemSecurity : IDispatch {
  [id (1), propget] HRESULT ImpersonationLevel ([out, retval] WbemImpersonationLevelEnum *iImpersonationLevel);
  [id (1), propput] HRESULT ImpersonationLevel ([in] WbemImpersonationLevelEnum iImpersonationLevel);
  [id (2), propget] HRESULT AuthenticationLevel ([out, retval] WbemAuthenticationLevelEnum *iAuthenticationLevel);
  [id (2), propput] HRESULT AuthenticationLevel ([in] WbemAuthenticationLevelEnum iAuthenticationLevel);
  [id (3), propget] HRESULT Privileges ([out, retval] ISWbemPrivilegeSet **objWbemPrivilegeSet);
};

[object, local, uuid (26ee67bd-5804-11d2-8b4a-00600806d9b6), oleautomation, dual, nonextensible, hidden]
interface ISWbemPrivilege : IDispatch {
  [id (DISPID_VALUE), propget] HRESULT IsEnabled ([out, retval] VARIANT_BOOL *bIsEnabled);
  [id (DISPID_VALUE), propput] HRESULT IsEnabled ([in] VARIANT_BOOL bIsEnabled);
  [id (1), propget] HRESULT Name ([out, retval] BSTR *strDisplayName);
  [id (2), propget] HRESULT DisplayName ([out, retval] BSTR *strDisplayName);
  [id (3), propget] HRESULT Identifier ([out, retval] WbemPrivilegeEnum *iPrivilege);
};

[object, local, uuid (26ee67bf-5804-11d2-8b4a-00600806d9b6), oleautomation, dual, hidden, nonextensible]
interface ISWbemPrivilegeSet : IDispatch {
  [id (DISPID_NEWENUM), propget, restricted] HRESULT _NewEnum ([out, retval] IUnknown **pUnk);
  [id (DISPID_VALUE)] HRESULT Item ([in] WbemPrivilegeEnum iPrivilege,[out, retval] ISWbemPrivilege **objWbemPrivilege);
  [id (1), propget] HRESULT Count ([out, retval] long *iCount);
  [id (2)] HRESULT Add ([in] WbemPrivilegeEnum iPrivilege,[in, defaultvalue (TRUE)] VARIANT_BOOL bIsEnabled,[defaultvalue (0), out, retval] ISWbemPrivilege **objWbemPrivilege);
  [id (3)] HRESULT Remove ([in] WbemPrivilegeEnum iPrivilege);
  [id (4)] HRESULT DeleteAll ();
  [id (5)] HRESULT AddAsString ([in] BSTR strPrivilege,[in, defaultvalue (TRUE)] VARIANT_BOOL bIsEnabled,[defaultvalue (0), out, retval] ISWbemPrivilege **objWbemPrivilege);
};

[object, local, uuid (5e97458a-cf77-11d3-B38F-00105a1f473a), oleautomation, dual, nonextensible]
interface ISWbemDateTime : IDispatch {
  [id (DISPID_VALUE), propget] HRESULT Value ([out, retval] BSTR *strValue);
  [id (DISPID_VALUE), propput] HRESULT Value ([in] BSTR strValue);
  [id (1), propget] HRESULT Year ([out, retval] long *iYear);
  [id (1), propput] HRESULT Year ([in] long iYear);
  [id (2), propget] HRESULT YearSpecified ([out, retval] VARIANT_BOOL *bYearSpecified);
  [id (2), propput] HRESULT YearSpecified ([in] VARIANT_BOOL bYearSpecified);
  [id (3), propget] HRESULT Month ([out, retval] long *iMonth);
  [id (3), propput] HRESULT Month ([in] long iMonth);
  [id (4), propget] HRESULT MonthSpecified ([out, retval] VARIANT_BOOL *bMonthSpecified);
  [id (4), propput] HRESULT MonthSpecified ([in] VARIANT_BOOL bMonthSpecified);
  [id (5), propget] HRESULT Day ([out, retval] long *iDay);
  [id (5), propput] HRESULT Day ([in] long iDay);
  [id (6), propget] HRESULT DaySpecified ([out, retval] VARIANT_BOOL *bDaySpecified);
  [id (6), propput] HRESULT DaySpecified ([in] VARIANT_BOOL bDaySpecified);
  [id (7), propget] HRESULT Hours ([out, retval] long *iHours);
  [id (7), propput] HRESULT Hours ([in] long iHours);
  [id (8), propget] HRESULT HoursSpecified ([out, retval] VARIANT_BOOL *bHoursSpecified);
  [id (8), propput] HRESULT HoursSpecified ([in] VARIANT_BOOL bHoursSpecified);
  [id (9), propget] HRESULT Minutes ([out, retval] long *iMinutes);
  [id (9), propput] HRESULT Minutes ([in] long iMinutes);
  [id (10), propget] HRESULT MinutesSpecified ([out, retval] VARIANT_BOOL *bMinutesSpecified);
  [id (10), propput] HRESULT MinutesSpecified ([in] VARIANT_BOOL bMinutesSpecified);
  [id (11), propget] HRESULT Seconds ([out, retval] long *iSeconds);
  [id (11), propput] HRESULT Seconds ([in] long iSeconds);
  [id (12), propget] HRESULT SecondsSpecified ([out, retval] VARIANT_BOOL *bSecondsSpecified);
  [id (12), propput] HRESULT SecondsSpecified ([in] VARIANT_BOOL bSecondsSpecified);
  [id (13), propget] HRESULT Microseconds ([out, retval] long *iMicroseconds);
  [id (13), propput] HRESULT Microseconds ([in] long iMicroseconds);
  [id (14), propget] HRESULT MicrosecondsSpecified ([out, retval] VARIANT_BOOL *bMicrosecondsSpecified);
  [id (14), propput] HRESULT MicrosecondsSpecified ([in] VARIANT_BOOL bMicrosecondsSpecified);
  [id (15), propget] HRESULT UTC ([out, retval] long *iUTC);
  [id (15), propput] HRESULT UTC ([in] long iUTC);
  [id (16), propget] HRESULT UTCSpecified ([out, retval] VARIANT_BOOL *bUTCSpecified);
  [id (16), propput] HRESULT UTCSpecified ([in] VARIANT_BOOL bUTCSpecified);
  [id (17), propget] HRESULT IsInterval ([out, retval] VARIANT_BOOL *bIsInterval);
  [id (17), propput] HRESULT IsInterval ([in] VARIANT_BOOL bIsInterval);
  [id (18)] HRESULT GetVarDate ([in, defaultvalue (TRUE)] VARIANT_BOOL bIsLocal,[defaultvalue (0), out, retval] DATE *dVarDate);
  [id (19)] HRESULT SetVarDate ([in] DATE dVarDate,[in, defaultvalue (TRUE)] VARIANT_BOOL bIsLocal);
  [id (20)] HRESULT GetFileTime ([in, defaultvalue (TRUE)] VARIANT_BOOL bIsLocal,[defaultvalue (0), out, retval] BSTR *strFileTime);
  [id (21)] HRESULT SetFileTime ([in] BSTR strFileTime,[in, defaultvalue (TRUE)] VARIANT_BOOL bIsLocal);
};

[object, local, uuid (5ad4bf92-daab-11d3-b38f-00105a1f473a), oleautomation, dual, nonextensible]
interface ISWbemRefreshableItem : IDispatch {
  [id (1), propget] HRESULT Index ([out, retval] long *iIndex);
  [id (2), propget] HRESULT Refresher ([out, retval] ISWbemRefresher **objWbemRefresher);
  [id (3), propget] HRESULT IsSet ([out, retval] VARIANT_BOOL *bIsSet);
  [id (4), propget] HRESULT Object ([out, retval] ISWbemObjectEx **objWbemObject);
  [id (5), propget] HRESULT ObjectSet ([out, retval] ISWbemObjectSet **objWbemObjectSet);
  [id (6)] HRESULT Remove ([in, defaultvalue (0)] long iFlags);
};

[object, local, uuid (14d8250e-d9c2-11d3-b38f-00105a1f473a), oleautomation, dual, nonextensible]
interface ISWbemRefresher : IDispatch {
  [id (DISPID_NEWENUM), propget, restricted] HRESULT _NewEnum ([out, retval] IUnknown **pUnk);
  [id (DISPID_VALUE)] HRESULT Item ([in] long iIndex,[out, retval] ISWbemRefreshableItem **objWbemRefreshableItem);
  [id (1), propget] HRESULT Count ([out, retval] long *iCount);
  [id (2)] HRESULT Add ([in] ISWbemServicesEx *objWbemServices,[in] BSTR bsInstancePath,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemRefreshableItem **objWbemRefreshableItem);
  [id (3)] HRESULT AddEnum ([in] ISWbemServicesEx *objWbemServices,[in] BSTR bsClassName,[in, defaultvalue (0)] long iFlags,[in, defaultvalue (0)] IDispatch *objWbemNamedValueSet,[defaultvalue (0), out, retval] ISWbemRefreshableItem **objWbemRefreshableItem);
  [id (4)] HRESULT Remove ([in] long iIndex,[in, defaultvalue (0)] long iFlags);
  [id (5)] HRESULT Refresh ([in, defaultvalue (0)] long iFlags);
  [id (6), propget] HRESULT AutoReconnect ([out, retval] VARIANT_BOOL *bCount);
  [id (6), propput] HRESULT AutoReconnect ([in] VARIANT_BOOL bCount);
  [id (7)] HRESULT DeleteAll ();
};

cpp_quote("#endif")
