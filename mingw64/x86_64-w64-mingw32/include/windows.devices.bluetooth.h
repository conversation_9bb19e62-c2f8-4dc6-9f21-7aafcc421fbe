/*** Autogenerated by WIDL 10.8 from include/windows.devices.bluetooth.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_devices_bluetooth_h__
#define __windows_devices_bluetooth_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter ABI::Windows::Devices::Bluetooth::IBluetoothAdapter
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothAdapter;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics ABI::Windows::Devices::Bluetooth::IBluetoothAdapterStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothAdapterStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothAdapter_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothAdapter_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                class BluetoothAdapter;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothAdapter __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothAdapter;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothAdapter_FWD_DEFINED__ */

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.devices.enumeration.h>
#include <windows.devices.radios.h>
#include <windows.storage.streams.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter ABI::Windows::Devices::Bluetooth::IBluetoothAdapter
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothAdapter;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter2_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter2 __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter2 ABI::Windows::Devices::Bluetooth::IBluetoothAdapter2
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothAdapter2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter3_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter3 __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter3 ABI::Windows::Devices::Bluetooth::IBluetoothAdapter3
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothAdapter3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics ABI::Windows::Devices::Bluetooth::IBluetoothAdapterStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothAdapterStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IBluetoothAdapter interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter, 0x7974f04c, 0x5f7a, 0x4a34, 0x92,0x25, 0xa8,0x55,0xf8,0x4b,0x1a,0x8b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                MIDL_INTERFACE("7974f04c-5f7a-4a34-9225-a855f84b1a8b")
                IBluetoothAdapter : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_DeviceId(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_BluetoothAddress(
                        UINT64 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsClassicSupported(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsLowEnergySupported(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsPeripheralRoleSupported(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsCentralRoleSupported(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsAdvertisementOffloadSupported(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetRadioAsync(
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Radios::Radio* > **operation) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter, 0x7974f04c, 0x5f7a, 0x4a34, 0x92,0x25, 0xa8,0x55,0xf8,0x4b,0x1a,0x8b)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        TrustLevel *trustLevel);

    /*** IBluetoothAdapter methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DeviceId)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_BluetoothAddress)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        UINT64 *value);

    HRESULT (STDMETHODCALLTYPE *get_IsClassicSupported)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsLowEnergySupported)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsPeripheralRoleSupported)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsCentralRoleSupported)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsAdvertisementOffloadSupported)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetRadioAsync)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio **operation);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterVtbl;

interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter {
    CONST_VTBL __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBluetoothAdapter methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_DeviceId(This,value) (This)->lpVtbl->get_DeviceId(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_BluetoothAddress(This,value) (This)->lpVtbl->get_BluetoothAddress(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsClassicSupported(This,value) (This)->lpVtbl->get_IsClassicSupported(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsLowEnergySupported(This,value) (This)->lpVtbl->get_IsLowEnergySupported(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsPeripheralRoleSupported(This,value) (This)->lpVtbl->get_IsPeripheralRoleSupported(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsCentralRoleSupported(This,value) (This)->lpVtbl->get_IsCentralRoleSupported(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsAdvertisementOffloadSupported(This,value) (This)->lpVtbl->get_IsAdvertisementOffloadSupported(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetRadioAsync(This,operation) (This)->lpVtbl->GetRadioAsync(This,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_QueryInterface(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_AddRef(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_Release(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetIids(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetTrustLevel(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBluetoothAdapter methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_DeviceId(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,HSTRING *value) {
    return This->lpVtbl->get_DeviceId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_BluetoothAddress(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,UINT64 *value) {
    return This->lpVtbl->get_BluetoothAddress(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsClassicSupported(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,boolean *value) {
    return This->lpVtbl->get_IsClassicSupported(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsLowEnergySupported(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,boolean *value) {
    return This->lpVtbl->get_IsLowEnergySupported(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsPeripheralRoleSupported(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,boolean *value) {
    return This->lpVtbl->get_IsPeripheralRoleSupported(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsCentralRoleSupported(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,boolean *value) {
    return This->lpVtbl->get_IsCentralRoleSupported(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsAdvertisementOffloadSupported(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,boolean *value) {
    return This->lpVtbl->get_IsAdvertisementOffloadSupported(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetRadioAsync(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,__FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio **operation) {
    return This->lpVtbl->GetRadioAsync(This,operation);
}
#endif
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define IID_IBluetoothAdapter IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter
#define IBluetoothAdapterVtbl __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterVtbl
#define IBluetoothAdapter __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter
#define IBluetoothAdapter_QueryInterface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_QueryInterface
#define IBluetoothAdapter_AddRef __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_AddRef
#define IBluetoothAdapter_Release __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_Release
#define IBluetoothAdapter_GetIids __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetIids
#define IBluetoothAdapter_GetRuntimeClassName __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetRuntimeClassName
#define IBluetoothAdapter_GetTrustLevel __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetTrustLevel
#define IBluetoothAdapter_get_DeviceId __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_DeviceId
#define IBluetoothAdapter_get_BluetoothAddress __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_BluetoothAddress
#define IBluetoothAdapter_get_IsClassicSupported __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsClassicSupported
#define IBluetoothAdapter_get_IsLowEnergySupported __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsLowEnergySupported
#define IBluetoothAdapter_get_IsPeripheralRoleSupported __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsPeripheralRoleSupported
#define IBluetoothAdapter_get_IsCentralRoleSupported __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsCentralRoleSupported
#define IBluetoothAdapter_get_IsAdvertisementOffloadSupported __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsAdvertisementOffloadSupported
#define IBluetoothAdapter_GetRadioAsync __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetRadioAsync
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IBluetoothAdapterStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics, 0x8b02fb6a, 0xac4c, 0x4741, 0x86,0x61, 0x8e,0xab,0x7d,0x17,0xea,0x9f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                MIDL_INTERFACE("8b02fb6a-ac4c-4741-8661-8eab7d17ea9f")
                IBluetoothAdapterStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetDeviceSelector(
                        HSTRING *result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FromIdAsync(
                        HSTRING id,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetDefaultAsync(
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > **operation) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics, 0x8b02fb6a, 0xac4c, 0x4741, 0x86,0x61, 0x8e,0xab,0x7d,0x17,0xea,0x9f)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This,
        TrustLevel *trustLevel);

    /*** IBluetoothAdapterStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDeviceSelector)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *FromIdAsync)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This,
        HSTRING id,
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter **operation);

    HRESULT (STDMETHODCALLTYPE *GetDefaultAsync)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This,
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter **operation);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStaticsVtbl;

interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics {
    CONST_VTBL __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBluetoothAdapterStatics methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetDeviceSelector(This,result) (This)->lpVtbl->GetDeviceSelector(This,result)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_FromIdAsync(This,id,operation) (This)->lpVtbl->FromIdAsync(This,id,operation)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetDefaultAsync(This,operation) (This)->lpVtbl->GetDefaultAsync(This,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_QueryInterface(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_AddRef(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_Release(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetIids(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetTrustLevel(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBluetoothAdapterStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetDeviceSelector(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This,HSTRING *result) {
    return This->lpVtbl->GetDeviceSelector(This,result);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_FromIdAsync(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This,HSTRING id,__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter **operation) {
    return This->lpVtbl->FromIdAsync(This,id,operation);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetDefaultAsync(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This,__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter **operation) {
    return This->lpVtbl->GetDefaultAsync(This,operation);
}
#endif
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define IID_IBluetoothAdapterStatics IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics
#define IBluetoothAdapterStaticsVtbl __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStaticsVtbl
#define IBluetoothAdapterStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics
#define IBluetoothAdapterStatics_QueryInterface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_QueryInterface
#define IBluetoothAdapterStatics_AddRef __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_AddRef
#define IBluetoothAdapterStatics_Release __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_Release
#define IBluetoothAdapterStatics_GetIids __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetIids
#define IBluetoothAdapterStatics_GetRuntimeClassName __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetRuntimeClassName
#define IBluetoothAdapterStatics_GetTrustLevel __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetTrustLevel
#define IBluetoothAdapterStatics_GetDeviceSelector __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetDeviceSelector
#define IBluetoothAdapterStatics_FromIdAsync __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_FromIdAsync
#define IBluetoothAdapterStatics_GetDefaultAsync __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetDefaultAsync
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*
 * Class Windows.Devices.Bluetooth.BluetoothAdapter
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothAdapter_DEFINED
#define RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothAdapter_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothAdapter[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','B','l','u','e','t','o','o','t','h','.','B','l','u','e','t','o','o','t','h','A','d','a','p','t','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothAdapter[] = L"Windows.Devices.Bluetooth.BluetoothAdapter";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothAdapter[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','B','l','u','e','t','o','o','t','h','.','B','l','u','e','t','o','o','t','h','A','d','a','p','t','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothAdapter_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter, 0x10a10a88, 0x90e0, 0x511a, 0x9a,0x08, 0xd7,0x5f,0xeb,0x52,0xa1,0x9f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("10a10a88-90e0-511a-9a08-d75feb52a19f")
            IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Bluetooth::BluetoothAdapter*, ABI::Windows::Devices::Bluetooth::IBluetoothAdapter* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter, 0x10a10a88, 0x90e0, 0x511a, 0x9a,0x08, 0xd7,0x5f,0xeb,0x52,0xa1,0x9f)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapterVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Release(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_BluetoothAdapter IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter
#define IAsyncOperationCompletedHandler_BluetoothAdapterVtbl __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapterVtbl
#define IAsyncOperationCompletedHandler_BluetoothAdapter __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter
#define IAsyncOperationCompletedHandler_BluetoothAdapter_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_QueryInterface
#define IAsyncOperationCompletedHandler_BluetoothAdapter_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_AddRef
#define IAsyncOperationCompletedHandler_BluetoothAdapter_Release __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Release
#define IAsyncOperationCompletedHandler_BluetoothAdapter_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter, 0x46fce70c, 0x6c07, 0x5a3a, 0xb7,0x75, 0x26,0xf9,0x94,0x02,0x55,0x3f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("46fce70c-6c07-5a3a-b775-26f99402553f")
            IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Bluetooth::BluetoothAdapter*, ABI::Windows::Devices::Bluetooth::IBluetoothAdapter* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter, 0x46fce70c, 0x6c07, 0x5a3a, 0xb7,0x75, 0x26,0xf9,0x94,0x02,0x55,0x3f)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapterVtbl;

interface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter {
    CONST_VTBL __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_QueryInterface(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_AddRef(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Release(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetIids(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetTrustLevel(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_put_Completed(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_get_Completed(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetResults(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_BluetoothAdapter IID___FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter
#define IAsyncOperation_BluetoothAdapterVtbl __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapterVtbl
#define IAsyncOperation_BluetoothAdapter __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter
#define IAsyncOperation_BluetoothAdapter_QueryInterface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_QueryInterface
#define IAsyncOperation_BluetoothAdapter_AddRef __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_AddRef
#define IAsyncOperation_BluetoothAdapter_Release __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Release
#define IAsyncOperation_BluetoothAdapter_GetIids __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetIids
#define IAsyncOperation_BluetoothAdapter_GetRuntimeClassName __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetRuntimeClassName
#define IAsyncOperation_BluetoothAdapter_GetTrustLevel __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetTrustLevel
#define IAsyncOperation_BluetoothAdapter_put_Completed __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_put_Completed
#define IAsyncOperation_BluetoothAdapter_get_Completed __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_get_Completed
#define IAsyncOperation_BluetoothAdapter_GetResults __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_devices_bluetooth_h__ */
