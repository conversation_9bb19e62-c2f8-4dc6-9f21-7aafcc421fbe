/*
 * Copyright 2011 <PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "oaidl.idl";
import "ocidl.idl";

[
    uuid(92ca9dcd-5622-4bba-a805-5e9f541bd8c9),
    object,
    pointer_default(unique)
]
interface IObjectArray : IUnknown
{
    HRESULT GetCount([out] UINT *pcObjects);
    HRESULT GetAt(
        [in] UINT uiIndex,
        [in] REFIID riid,
        [out, iid_is(riid)] void **ppv);
}

[
    uuid(5632b1a4-e38a-400a-928a-d4cd63230295),
    object,
    pointer_default(unique)
]
interface IObjectCollection : IObjectArray
{
    HRESULT AddObject([in] IUnknown *punk);
    HRESULT AddFromArray([in] IObjectArray *poaSource);
    HRESULT RemoveObjectAt([in] UINT uiIndex);
    HRESULT Clear();
}
