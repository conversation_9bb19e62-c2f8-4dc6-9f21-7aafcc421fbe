/*
 * Copyright 2021 <PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

#ifndef DO_NO_IMPORTS
import "inspectable.idl";
import "eventtoken.idl";
import "windows.foundation.idl";
#endif

namespace Windows.Security.ExchangeActiveSyncProvisioning {
    interface IEasClientDeviceInformation;
    runtimeclass EasClientDeviceInformation;

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Security.ExchangeActiveSyncProvisioning.EasClientDeviceInformation),
        uuid(54dfd981-1968-4ca3-b958-e595d16505eb)
    ]
    interface IEasClientDeviceInformation : IInspectable
    {
        [propget] HRESULT Id([out, retval] GUID *value);
        [propget] HRESULT OperatingSystem([out, retval] HSTRING *value);
        [propget] HRESULT FriendlyName([out, retval] HSTRING *value);
        [propget] HRESULT SystemManufacturer([out, retval] HSTRING *value);
        [propget] HRESULT SystemProductName([out, retval] HSTRING *value);
        [propget] HRESULT SystemSku([out, retval] HSTRING *value);
    }

    [
        activatable(Windows.Foundation.UniversalApiContract, 1.0),
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        threading(both)
    ]
    runtimeclass EasClientDeviceInformation
    {
        [default] interface Windows.Security.ExchangeActiveSyncProvisioning.IEasClientDeviceInformation;
    }
}
