/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#ifndef _L2CMN_H
#define _L2CMN_H

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WIN<PERSON>I_PARTITION_DESKTOP)

#ifdef __cplusplus
extern "C" {
#endif

#define L2_PROFILE_MAX_NAME_LENGTH     256

#define L2_NOTIFICATION_SOURCE_NONE                0
#define L2_NOTIFICATION_SOURCE_DOT3_AUTO_CONFIG    0x00000001
#define L2_NOTIFICATION_SOURCE_SECURITY            0x00000002
#define L2_NOTIFICATION_SOURCE_ONEX                0x00000004
#define L2_NOTIFICATION_SOURCE_WLAN_ACM            0x00000008
#define L2_NOTIFICATION_SOURCE_WLAN_MSM            0x00000010
#define L2_NOTIFICATION_SOURCE_WLAN_SECURITY       0x00000020
#define L2_NOTIFICATION_SOURCE_WLAN_IHV            0x00000040
#define L2_NOTIFICATION_SOURCE_WLAN_HNWK           0x00000080
#define L2_NOTIFICATION_SOURCE_WCM                 0x00000100
#define L2_NOTIFICATION_SOURCE_WCM_CSP             0x00000200
#define L2_NOTIFICATION_SOURCE_WFD                 0x00000400
#define L2_NOTIFICATION_SOURCE_WLAN_DEVICE_SERVICE 0x00000800
#define L2_NOTIFICATION_SOURCE_ALL                 0x0000ffff

#define L2_NOTIFICATION_CODE_PUBLIC_BEGIN          0x00000000
#define L2_NOTIFICATION_CODE_GROUP_SIZE            0x00001000
#define L2_NOTIFICATION_CODE_V2_BEGIN              (L2_NOTIFICATION_CODE_PUBLIC_BEGIN+L2_NOTIFICATION_CODE_GROUP_SIZE)

#define L2_REASON_CODE_GROUP_SIZE             0x10000
#define L2_REASON_CODE_GEN_BASE               0x10000
#define L2_REASON_CODE_DOT11_AC_BASE          (L2_REASON_CODE_GEN_BASE+L2_REASON_CODE_GROUP_SIZE)
#define L2_REASON_CODE_DOT11_MSM_BASE         (L2_REASON_CODE_DOT11_AC_BASE+L2_REASON_CODE_GROUP_SIZE)
#define L2_REASON_CODE_DOT11_SECURITY_BASE    (L2_REASON_CODE_DOT11_MSM_BASE+L2_REASON_CODE_GROUP_SIZE)
#define L2_REASON_CODE_ONEX_BASE              (L2_REASON_CODE_DOT11_SECURITY_BASE+L2_REASON_CODE_GROUP_SIZE)
#define L2_REASON_CODE_DOT3_AC_BASE           (L2_REASON_CODE_ONEX_BASE+L2_REASON_CODE_GROUP_SIZE)
#define L2_REASON_CODE_DOT3_MSM_BASE          (L2_REASON_CODE_DOT3_AC_BASE+L2_REASON_CODE_GROUP_SIZE)
#define L2_REASON_CODE_PROFILE_BASE           (L2_REASON_CODE_DOT3_MSM_BASE+L2_REASON_CODE_GROUP_SIZE)
#define L2_REASON_CODE_IHV_BASE               (L2_REASON_CODE_PROFILE_BASE+L2_REASON_CODE_GROUP_SIZE)
#define L2_REASON_CODE_WIMAX_BASE             (L2_REASON_CODE_IHV_BASE+L2_REASON_CODE_GROUP_SIZE)
#define L2_REASON_CODE_RESERVED_BASE          (L2_REASON_CODE_WIMAX_BASE+L2_REASON_CODE_GROUP_SIZE)
#define L2_REASON_CODE_SUCCESS                0
#define L2_REASON_CODE_UNKNOWN                (L2_REASON_CODE_GEN_BASE+1)
#define L2_REASON_CODE_PROFILE_MISSING        0x00000001

typedef struct _L2_NOTIFICATION_DATA {
    DWORD NotificationSource;
    DWORD NotificationCode;
    GUID InterfaceGuid;
    DWORD dwDataSize;
    PVOID pData;
} L2_NOTIFICATION_DATA, *PL2_NOTIFICATION_DATA;

#ifdef __cplusplus
}
#endif

#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */

#endif /* _L2CMN_H */
