/*
 * Copyright 2021 R<PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "oaidl.idl";
import "ocidl.idl";

cpp_quote("#define WMPGC_FLAGS_SUPPRESS_DIALOGS 0x00000002")

[
    odl,
    uuid(bfb377e5-c594-4369-a970-de896d5ece74),
    oleautomation
]
interface IWMPGraphCreation : IUnknown
{
    HRESULT GraphCreationPreRender([in] IUnknown *filter_graph, [in] IUnknown *reserved);
    HRESULT GraphCreationPostRender([in] IUnknown *filter_graph);
    HRESULT GetGraphCreationFlags([out, retval] DWORD *flags);
}
