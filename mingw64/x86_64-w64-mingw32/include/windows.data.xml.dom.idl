/*
 * Copyright (C) 2024 Biswa<PERSON><PERSON>yo <PERSON>h
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";
import "windows.storage.idl";
import "windows.storage.streams.idl";

namespace Windows.Data.Xml.Dom {
    typedef enum NodeType NodeType;

    interface IDtdEntity;
    interface IDtdNotation;
    interface IXmlAttribute;
    interface IXmlCDataSection;
    interface IXmlCharacterData;
    interface IXmlComment;
    interface IXmlDocument;
    interface IXmlDocumentFragment;
    interface IXmlDocumentIO;
    interface IXmlDocumentIO2;
    interface IXmlDocumentStatics;
    interface IXmlDocumentType;
    interface IXmlDomImplementation;
    interface IXmlElement;
    interface IXmlEntityReference;
    interface IXmlLoadSettings;
    interface IXmlNamedNodeMap;
    interface IXmlNode;
    interface IXmlNodeList;
    interface IXmlNodeSelector;
    interface IXmlNodeSerializer;
    interface IXmlProcessingInstruction;
    interface IXmlText;

    runtimeclass DtdEntity;
    runtimeclass DtdNotation;
    runtimeclass XmlAttribute;
    runtimeclass XmlCDataSection;
    runtimeclass XmlComment;
    runtimeclass XmlDocument;
    runtimeclass XmlDocumentFragment;
    runtimeclass XmlDocumentType;
    runtimeclass XmlDomImplementation;
    runtimeclass XmlElement;
    runtimeclass XmlEntityReference;
    runtimeclass XmlLoadSettings;
    runtimeclass XmlNamedNodeMap;
    runtimeclass XmlNodeList;
    runtimeclass XmlProcessingInstruction;
    runtimeclass XmlText;

    declare {
        interface Windows.Foundation.Collections.IIterable<Windows.Data.Xml.Dom.IXmlNode *>;
        interface Windows.Foundation.Collections.IIterator<Windows.Data.Xml.Dom.IXmlNode *>;
        interface Windows.Foundation.Collections.IVectorView<Windows.Data.Xml.Dom.IXmlNode *>;
        interface Windows.Foundation.IAsyncOperation<Windows.Data.Xml.Dom.XmlDocument *>;
        interface Windows.Foundation.AsyncOperationCompletedHandler<Windows.Data.Xml.Dom.XmlDocument *>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum NodeType
    {
        Invalid                   = 0,
        ElementNode               = 1,
        AttributeNode             = 2,
        TextNode                  = 3,
        DataSectionNode           = 4,
        EntityReferenceNode       = 5,
        EntityNode                = 6,
        ProcessingInstructionNode = 7,
        CommentNode               = 8,
        DocumentNode              = 9,
        DocumentTypeNode          = 10,
        DocumentFragmentNode      = 11,
        NotationNode              = 12,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.DtdEntity),
        uuid(6a0b5ffc-63b4-480f-9e6a-8a92816aade4)
    ]
    interface IDtdEntity : IInspectable
        requires Windows.Data.Xml.Dom.IXmlNode,
                 Windows.Data.Xml.Dom.IXmlNodeSelector,
                 Windows.Data.Xml.Dom.IXmlNodeSerializer
    {
        [propget] HRESULT PublicId([out, retval] IInspectable **value);
        [propget] HRESULT SystemId([out, retval] IInspectable **value);
        [propget] HRESULT NotationName([out, retval] IInspectable **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.DtdNotation),
        uuid(8cb4e04d-6d46-4edb-ab73-df83c51ad397)
    ]
    interface IDtdNotation : IInspectable
        requires Windows.Data.Xml.Dom.IXmlNode,
                 Windows.Data.Xml.Dom.IXmlNodeSelector,
                 Windows.Data.Xml.Dom.IXmlNodeSerializer
    {
        [propget] HRESULT PublicId([out, retval] IInspectable **value);
        [propget] HRESULT SystemId([out, retval] IInspectable **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.XmlAttribute),
        uuid(ac144aa4-b4f1-4db6-b206-8a22c308db0a)
    ]
    interface IXmlAttribute : IInspectable
        requires Windows.Data.Xml.Dom.IXmlNode,
                 Windows.Data.Xml.Dom.IXmlNodeSelector,
                 Windows.Data.Xml.Dom.IXmlNodeSerializer
    {
        [propget] HRESULT Name([out, retval] HSTRING *value);
        [propget] HRESULT Specified([out, retval] boolean *value);
        [propget] HRESULT Value([out, retval] HSTRING *value);
        [propput] HRESULT Value([in] HSTRING value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.XmlCDataSection),
        uuid(4d04b46f-c8bd-45b4-8899-0400d7c2c60f)
    ]
    interface IXmlCDataSection : IInspectable
        requires Windows.Data.Xml.Dom.IXmlText,
                 Windows.Data.Xml.Dom.IXmlCharacterData,
                 Windows.Data.Xml.Dom.IXmlNode,
                 Windows.Data.Xml.Dom.IXmlNodeSelector,
                 Windows.Data.Xml.Dom.IXmlNodeSerializer
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        uuid(132e42ab-4e36-4df6-b1c8-0ce62fd88b26)
    ]
    interface IXmlCharacterData : IInspectable
        requires Windows.Data.Xml.Dom.IXmlNode,
                 Windows.Data.Xml.Dom.IXmlNodeSelector,
                 Windows.Data.Xml.Dom.IXmlNodeSerializer
    {
        [propget] HRESULT Data([out, retval] HSTRING *value);
        [propput] HRESULT Data([in] HSTRING value);
        [propget] HRESULT Length([out, retval] UINT32 *value);
        HRESULT SubstringData([in] UINT32 offset, [in] UINT32 count, [out, retval] HSTRING *data);
        HRESULT AppendData([in] HSTRING data);
        HRESULT InsertData([in] UINT32 offset, [in] HSTRING data);
        HRESULT DeleteData([in] UINT32 offset, [in] UINT32 count);
        HRESULT ReplaceData([in] UINT32 offset, [in] UINT32 count, [in] HSTRING data);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.XmlComment),
        uuid(bca474d5-b61f-4611-9cac-2e92e3476d47)
    ]
    interface IXmlComment : IInspectable
        requires Windows.Data.Xml.Dom.IXmlCharacterData,
                 Windows.Data.Xml.Dom.IXmlNode,
                 Windows.Data.Xml.Dom.IXmlNodeSelector,
                 Windows.Data.Xml.Dom.IXmlNodeSerializer
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.XmlDocument),
        uuid(f7f3a506-1e87-42d6-bcfb-b8c809fa5494)
    ]
    interface IXmlDocument : IInspectable
        requires Windows.Data.Xml.Dom.IXmlNode,
                 Windows.Data.Xml.Dom.IXmlNodeSelector,
                 Windows.Data.Xml.Dom.IXmlNodeSerializer
    {
        [propget] HRESULT Doctype([out, retval] Windows.Data.Xml.Dom.XmlDocumentType **value);
        [propget] HRESULT Implementation([out, retval] Windows.Data.Xml.Dom.XmlDomImplementation **value);
        [propget] HRESULT DocumentElement([out, retval] Windows.Data.Xml.Dom.XmlElement **value);
        HRESULT CreateElement([in] HSTRING tag_name, [out, retval] Windows.Data.Xml.Dom.XmlElement **new_element);
        HRESULT CreateDocumentFragment([out, retval] Windows.Data.Xml.Dom.XmlDocumentFragment **new_document_fragment);
        HRESULT CreateTextNode([in] HSTRING data, [out, retval] Windows.Data.Xml.Dom.XmlText **new_text_node);
        HRESULT CreateComment([in] HSTRING data, [out, retval] Windows.Data.Xml.Dom.XmlComment **new_comment);
        HRESULT CreateProcessingInstruction([in] HSTRING target, [in] HSTRING data, [out, retval] Windows.Data.Xml.Dom.XmlProcessingInstruction **new_processing_instruction);
        HRESULT CreateAttribute([in] HSTRING name, [out, retval] Windows.Data.Xml.Dom.XmlAttribute **new_attribute);
        HRESULT CreateEntityReference([in] HSTRING name, [out, retval] Windows.Data.Xml.Dom.XmlEntityReference **new_entity_reference);
        HRESULT GetElementsByTagName([in] HSTRING tag_name, [out, retval] Windows.Data.Xml.Dom.XmlNodeList **elements);
        HRESULT CreateCDataSection([in] HSTRING data, [out, retval] Windows.Data.Xml.Dom.XmlCDataSection **new_cdata_section);
        [propget] HRESULT DocumentUri([out, retval] HSTRING *value);
        HRESULT CreateAttributeNS([in] IInspectable *namespace_uri, [in] HSTRING qualified_name, [out, retval] Windows.Data.Xml.Dom.XmlAttribute **new_attribute);
        HRESULT CreateElementNS([in] IInspectable *namespace_uri, [in] HSTRING qualified_name, [out, retval] Windows.Data.Xml.Dom.XmlElement **new_element);
        HRESULT GetElementById([in] HSTRING element_id, [out, retval] Windows.Data.Xml.Dom.XmlElement **element);
        HRESULT ImportNode([in] Windows.Data.Xml.Dom.IXmlNode *node, [in] boolean deep, [out, retval] Windows.Data.Xml.Dom.IXmlNode **new_node);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.XmlDocumentFragment),
        uuid(e2ea6a96-0c21-44a5-8bc9-9e4a262708ec)
    ]
    interface IXmlDocumentFragment : IInspectable
        requires Windows.Data.Xml.Dom.IXmlNode,
                 Windows.Data.Xml.Dom.IXmlNodeSelector,
                 Windows.Data.Xml.Dom.IXmlNodeSerializer
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.XmlDocument),
        uuid(6cd0e74e-ee65-4489-9ebf-ca43e87ba637)
    ]
    interface IXmlDocumentIO : IInspectable
    {
        [overload("LoadXml")] HRESULT LoadXml([in] HSTRING xml);
        [overload("LoadXml")] HRESULT LoadXmlWithSettings([in] HSTRING xml, [in] Windows.Data.Xml.Dom.XmlLoadSettings *load_settings);
        HRESULT SaveToFileAsync([in] Windows.Storage.IStorageFile *file, [out, retval] Windows.Foundation.IAsyncAction **async_info);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.XmlDocument),
        uuid(5d034661-7bd8-4ad5-9ebf-81e6347263b1)
    ]
    interface IXmlDocumentIO2 : IInspectable
    {
        [overload("LoadXmlFromBuffer")] HRESULT LoadXmlFromBuffer(
            [in] Windows.Storage.Streams.IBuffer *buffer);
        [overload("LoadXmlFromBuffer")] HRESULT LoadXmlFromBufferWithSettings(
            [in] Windows.Storage.Streams.IBuffer *buffer,
            [in] Windows.Data.Xml.Dom.XmlLoadSettings *load_settings);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.XmlDocument),
        uuid(5543d254-d757-4b79-9539-232b18f50bf1)
    ]
    interface IXmlDocumentStatics : IInspectable
    {
        [overload("LoadFromUriAsync")] HRESULT LoadFromUriAsync(
            [in] Windows.Foundation.Uri *uri,
            [out, retval] Windows.Foundation.IAsyncOperation<Windows.Data.Xml.Dom.XmlDocument *> **async_info);
        [overload("LoadFromUriAsync")] HRESULT LoadFromUriWithSettingsAsync(
            [in] Windows.Foundation.Uri *uri,
            [in] Windows.Data.Xml.Dom.XmlLoadSettings *load_settings,
            [out, retval] Windows.Foundation.IAsyncOperation<Windows.Data.Xml.Dom.XmlDocument *> **async_info);
        [overload("LoadFromFileAsync")] HRESULT LoadFromFileAsync(
            [in] Windows.Storage.IStorageFile *file,
            [out, retval] Windows.Foundation.IAsyncOperation<Windows.Data.Xml.Dom.XmlDocument *> **async_info);
        [overload("LoadFromFileAsync")] HRESULT LoadFromFileWithSettingsAsync(
            [in] Windows.Storage.IStorageFile *file,
            [in] Windows.Data.Xml.Dom.XmlLoadSettings *load_settings,
            [out, retval] Windows.Foundation.IAsyncOperation<Windows.Data.Xml.Dom.XmlDocument *> **async_info);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.XmlDocumentType),
        uuid(f7342425-9781-4964-8e94-9b1c6dfc9bc7)
    ]
    interface IXmlDocumentType : IInspectable
        requires Windows.Data.Xml.Dom.IXmlNode,
                 Windows.Data.Xml.Dom.IXmlNodeSelector,
                 Windows.Data.Xml.Dom.IXmlNodeSerializer
    {
        [propget] HRESULT Name([out, retval] HSTRING *value);
        [propget] HRESULT Entities([out, retval] Windows.Data.Xml.Dom.XmlNamedNodeMap **value);
        [propget] HRESULT Notations([out, retval] Windows.Data.Xml.Dom.XmlNamedNodeMap **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.XmlDomImplementation),
        uuid(6de58132-f11d-4fbb-8cc6-583cba93112f)
    ]
    interface IXmlDomImplementation : IInspectable
    {
        HRESULT HasFeature([in] HSTRING feature, [in] IInspectable *version, [out, retval] boolean *feature_supported);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.XmlElement),
        uuid(2dfb8a1f-6b10-4ef8-9f83-efcce8faec37)
    ]
    interface IXmlElement : IInspectable
        requires Windows.Data.Xml.Dom.IXmlNode,
                 Windows.Data.Xml.Dom.IXmlNodeSelector,
                 Windows.Data.Xml.Dom.IXmlNodeSerializer
    {
        [propget] HRESULT TagName([out, retval] HSTRING *value);
        HRESULT GetAttribute([in] HSTRING attribute_name, [out, retval] HSTRING *attribute_value);
        HRESULT SetAttribute([in] HSTRING attribute_name, [in] HSTRING attribute_value);
        HRESULT RemoveAttribute([in] HSTRING attribute_name);
        HRESULT GetAttributeNode([in] HSTRING attribute_name, [out, retval] Windows.Data.Xml.Dom.XmlAttribute **attribute_node);
        HRESULT SetAttributeNode([in] Windows.Data.Xml.Dom.XmlAttribute *new_attribute, [out, retval] Windows.Data.Xml.Dom.XmlAttribute **previous_attribute);
        HRESULT RemoveAttributeNode([in] Windows.Data.Xml.Dom.XmlAttribute *attribute_node, [out, retval] Windows.Data.Xml.Dom.XmlAttribute **removed_attribute);
        HRESULT GetElementsByTagName([in] HSTRING tag_name, [out, retval] Windows.Data.Xml.Dom.XmlNodeList **elements);
        HRESULT SetAttributeNS([in] IInspectable *namespace_uri, [in] HSTRING qualified_name, [in] HSTRING value);
        HRESULT GetAttributeNS([in] IInspectable *namespace_uri, [in] HSTRING local_name, [out, retval] HSTRING *value);
        HRESULT RemoveAttributeNS([in] IInspectable *namespace_uri, [in] HSTRING local_name);
        HRESULT SetAttributeNodeNS([in] Windows.Data.Xml.Dom.XmlAttribute *new_attribute, [out, retval] Windows.Data.Xml.Dom.XmlAttribute **previous_attribute);
        HRESULT GetAttributeNodeNS([in] IInspectable *namespace_uri, [in] HSTRING local_name, [out, retval] Windows.Data.Xml.Dom.XmlAttribute **previous_attribute);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.XmlEntityReference),
        uuid(2e2f47bc-c3d0-4ccf-bb86-0ab8c36a61cf)
    ]
    interface IXmlEntityReference : IInspectable
        requires Windows.Data.Xml.Dom.IXmlNode,
                 Windows.Data.Xml.Dom.IXmlNodeSelector,
                 Windows.Data.Xml.Dom.IXmlNodeSerializer
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.XmlLoadSettings),
        uuid(58aa07a8-fed6-46f7-b4c5-fb1ba72108d6)
    ]
    interface IXmlLoadSettings : IInspectable
    {
        [propget] HRESULT MaxElementDepth([out, retval] UINT32 *value);
        [propput] HRESULT MaxElementDepth([in] UINT32 value);
        [propget] HRESULT ProhibitDtd([out, retval] boolean *value);
        [propput] HRESULT ProhibitDtd([in] boolean value);
        [propget] HRESULT ResolveExternals([out, retval] boolean *value);
        [propput] HRESULT ResolveExternals([in] boolean value);
        [propget] HRESULT ValidateOnParse([out, retval] boolean *value);
        [propput] HRESULT ValidateOnParse([in] boolean value);
        [propget] HRESULT ElementContentWhiteSpace([out, retval] boolean *value);
        [propput] HRESULT ElementContentWhiteSpace([in] boolean value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.XmlNamedNodeMap),
        uuid(b3a69eb0-aab0-4b82-a6fa-b1453f7c021b)
    ]
    interface IXmlNamedNodeMap : IInspectable
        requires Windows.Foundation.Collections.IVectorView<Windows.Data.Xml.Dom.IXmlNode *>,
                 Windows.Foundation.Collections.IIterable<Windows.Data.Xml.Dom.IXmlNode *>
    {
        [propget] HRESULT Length([out, retval] UINT32 *value);
        HRESULT Item([in] UINT32 index, [out, retval] Windows.Data.Xml.Dom.IXmlNode **node);
        HRESULT GetNamedItem([in] HSTRING name, [out, retval] Windows.Data.Xml.Dom.IXmlNode **node);
        HRESULT SetNamedItem([in] Windows.Data.Xml.Dom.IXmlNode *node, [out, retval] Windows.Data.Xml.Dom.IXmlNode **previous_node);
        HRESULT RemoveNamedItem([in] HSTRING name, [out, retval] Windows.Data.Xml.Dom.IXmlNode **previous_node);
        HRESULT GetNamedItemNS([in] IInspectable *namespace_uri, [in] HSTRING name, [out, retval] Windows.Data.Xml.Dom.IXmlNode **node);
        HRESULT RemoveNamedItemNS([in] IInspectable *namespace_uri, [in] HSTRING name, [out, retval] Windows.Data.Xml.Dom.IXmlNode **previous_node);
        HRESULT SetNamedItemNS([in] Windows.Data.Xml.Dom.IXmlNode *node, [out, retval] Windows.Data.Xml.Dom.IXmlNode **previous_node);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        uuid(1c741d59-2122-47d5-a856-83f3d4214875)
    ]
    interface IXmlNode : IInspectable
        requires Windows.Data.Xml.Dom.IXmlNodeSelector,
                 Windows.Data.Xml.Dom.IXmlNodeSerializer
    {
        [propget] HRESULT NodeValue([out, retval] IInspectable **value);
        [propput] HRESULT NodeValue([in] IInspectable *value);
        [propget] HRESULT NodeType([out, retval] Windows.Data.Xml.Dom.NodeType *value);
        [propget] HRESULT NodeName([out, retval] HSTRING *value);
        [propget] HRESULT ParentNode([out, retval] Windows.Data.Xml.Dom.IXmlNode **value);
        [propget] HRESULT ChildNodes([out, retval] Windows.Data.Xml.Dom.XmlNodeList **value);
        [propget] HRESULT FirstChild([out, retval] Windows.Data.Xml.Dom.IXmlNode **value);
        [propget] HRESULT LastChild([out, retval] Windows.Data.Xml.Dom.IXmlNode **value);
        [propget] HRESULT PreviousSibling([out, retval] Windows.Data.Xml.Dom.IXmlNode **value);
        [propget] HRESULT NextSibling([out, retval] Windows.Data.Xml.Dom.IXmlNode **value);
        [propget] HRESULT Attributes([out, retval] Windows.Data.Xml.Dom.XmlNamedNodeMap **value);
        HRESULT HasChildNodes([out, retval] boolean *value);
        [propget] HRESULT OwnerDocument([out, retval] Windows.Data.Xml.Dom.XmlDocument **value);
        HRESULT InsertBefore([in] Windows.Data.Xml.Dom.IXmlNode *new_child, [in] Windows.Data.Xml.Dom.IXmlNode *reference_child, [out, retval] Windows.Data.Xml.Dom.IXmlNode **inserted_child);
        HRESULT ReplaceChild([in] Windows.Data.Xml.Dom.IXmlNode *new_child, [in] Windows.Data.Xml.Dom.IXmlNode *reference_child, [out, retval] Windows.Data.Xml.Dom.IXmlNode **previous_child);
        HRESULT RemoveChild([in] Windows.Data.Xml.Dom.IXmlNode *child_node, [out, retval] Windows.Data.Xml.Dom.IXmlNode **removed_child);
        HRESULT AppendChild([in] Windows.Data.Xml.Dom.IXmlNode *new_child, [out, retval] Windows.Data.Xml.Dom.IXmlNode **appended_child);
        HRESULT CloneNode([in] boolean deep, [out, retval] Windows.Data.Xml.Dom.IXmlNode **new_node);
        [propget] HRESULT NamespaceUri([out, retval] IInspectable **value);
        [propget] HRESULT LocalName([out, retval] IInspectable **value);
        [propget] HRESULT Prefix([out, retval] IInspectable **value);
        HRESULT Normalize();
        [propput] HRESULT Prefix([in] IInspectable *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.XmlNodeList),
        uuid(8c60ad77-83a4-4ec1-9c54-7ba429e13da6)
    ]
    interface IXmlNodeList : IInspectable
        requires Windows.Foundation.Collections.IVectorView<Windows.Data.Xml.Dom.IXmlNode *>,
                 Windows.Foundation.Collections.IIterable<Windows.Data.Xml.Dom.IXmlNode *>
    {
        [propget] HRESULT Length([out, retval] UINT32 *value);
        HRESULT Item([in] UINT32 index, [out, retval] Windows.Data.Xml.Dom.IXmlNode **node);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        uuid(63dbba8b-d0db-4fe1-b745-f9433afdc25b)
    ]
    interface IXmlNodeSelector : IInspectable
    {
        HRESULT SelectSingleNode([in] HSTRING xpath, [out, retval] Windows.Data.Xml.Dom.IXmlNode **node);
        HRESULT SelectNodes([in] HSTRING xpath, [out, retval] Windows.Data.Xml.Dom.XmlNodeList **node_list);
        HRESULT SelectSingleNodeNS([in] HSTRING xpath, [in] IInspectable *namespaces, [out, retval] Windows.Data.Xml.Dom.IXmlNode **node);
        HRESULT SelectNodesNS([in] HSTRING xpath, [in] IInspectable *namespaces, [out, retval] Windows.Data.Xml.Dom.XmlNodeList **node_list);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        uuid(5cc5b382-e6dd-4991-abef-06d8d2e7bd0c)
    ]
    interface IXmlNodeSerializer : IInspectable
    {
        HRESULT GetXml([out, retval] HSTRING *outer_xml);
        [propget] HRESULT InnerText([out, retval] HSTRING *value);
        [propput] HRESULT InnerText([in] HSTRING value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Data.Xml.Dom.XmlProcessingInstruction),
        uuid(2707fd1e-1e92-4ece-b6f4-26f069078ddc)
    ]
    interface IXmlProcessingInstruction : IInspectable
        requires Windows.Data.Xml.Dom.IXmlNode,
                 Windows.Data.Xml.Dom.IXmlNodeSelector,
                 Windows.Data.Xml.Dom.IXmlNodeSerializer
    {
        [propget] HRESULT Target([out, retval] HSTRING *value);
        [propget] HRESULT Data([out, retval] HSTRING *value);
        [propput] HRESULT Data([in] HSTRING value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        uuid(f931a4cb-308d-4760-a1d5-43b67450ac7e)
    ]
    interface IXmlText : IInspectable
        requires Windows.Data.Xml.Dom.IXmlCharacterData,
                 Windows.Data.Xml.Dom.IXmlNode,
                 Windows.Data.Xml.Dom.IXmlNodeSelector,
                 Windows.Data.Xml.Dom.IXmlNodeSerializer
    {
        HRESULT SplitText([in] UINT32 offset, [out, retval] Windows.Data.Xml.Dom.IXmlText **second_part);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass DtdEntity
    {
        [default] interface Windows.Data.Xml.Dom.IDtdEntity;
        interface Windows.Data.Xml.Dom.IXmlNode;
        interface Windows.Data.Xml.Dom.IXmlNodeSerializer;
        interface Windows.Data.Xml.Dom.IXmlNodeSelector;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass DtdNotation
    {
        [default] interface Windows.Data.Xml.Dom.IDtdNotation;
        interface Windows.Data.Xml.Dom.IXmlNode;
        interface Windows.Data.Xml.Dom.IXmlNodeSerializer;
        interface Windows.Data.Xml.Dom.IXmlNodeSelector;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass XmlAttribute
    {
        [default] interface Windows.Data.Xml.Dom.IXmlAttribute;
        interface Windows.Data.Xml.Dom.IXmlNode;
        interface Windows.Data.Xml.Dom.IXmlNodeSerializer;
        interface Windows.Data.Xml.Dom.IXmlNodeSelector;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass XmlCDataSection
    {
        [default] interface Windows.Data.Xml.Dom.IXmlCDataSection;
        interface Windows.Data.Xml.Dom.IXmlText;
        interface Windows.Data.Xml.Dom.IXmlCharacterData;
        interface Windows.Data.Xml.Dom.IXmlNode;
        interface Windows.Data.Xml.Dom.IXmlNodeSerializer;
        interface Windows.Data.Xml.Dom.IXmlNodeSelector;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass XmlComment
    {
        [default] interface Windows.Data.Xml.Dom.IXmlComment;
        interface Windows.Data.Xml.Dom.IXmlCharacterData;
        interface Windows.Data.Xml.Dom.IXmlNode;
        interface Windows.Data.Xml.Dom.IXmlNodeSerializer;
        interface Windows.Data.Xml.Dom.IXmlNodeSelector;
    }

    [
        activatable(Windows.Foundation.UniversalApiContract, 1.0),
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        static(Windows.Data.Xml.Dom.IXmlDocumentStatics, Windows.Foundation.UniversalApiContract, 1.0),
        threading(both)
    ]
    runtimeclass XmlDocument
    {
        [default] interface Windows.Data.Xml.Dom.IXmlDocument;
        interface Windows.Data.Xml.Dom.IXmlNode;
        interface Windows.Data.Xml.Dom.IXmlNodeSerializer;
        interface Windows.Data.Xml.Dom.IXmlNodeSelector;
        interface Windows.Data.Xml.Dom.IXmlDocumentIO;
        [contract(Windows.Foundation.UniversalApiContract, 1.0)] interface Windows.Data.Xml.Dom.IXmlDocumentIO2;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass XmlDocumentFragment
    {
        [default] interface Windows.Data.Xml.Dom.IXmlDocumentFragment;
        interface Windows.Data.Xml.Dom.IXmlNode;
        interface Windows.Data.Xml.Dom.IXmlNodeSerializer;
        interface Windows.Data.Xml.Dom.IXmlNodeSelector;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass XmlDocumentType
    {
        [default] interface Windows.Data.Xml.Dom.IXmlDocumentType;
        interface Windows.Data.Xml.Dom.IXmlNode;
        interface Windows.Data.Xml.Dom.IXmlNodeSerializer;
        interface Windows.Data.Xml.Dom.IXmlNodeSelector;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass XmlDomImplementation
    {
        [default] interface Windows.Data.Xml.Dom.IXmlDomImplementation;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass XmlElement
    {
        [default] interface Windows.Data.Xml.Dom.IXmlElement;
        interface Windows.Data.Xml.Dom.IXmlNode;
        interface Windows.Data.Xml.Dom.IXmlNodeSerializer;
        interface Windows.Data.Xml.Dom.IXmlNodeSelector;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass XmlEntityReference
    {
        [default] interface Windows.Data.Xml.Dom.IXmlEntityReference;
        interface Windows.Data.Xml.Dom.IXmlNode;
        interface Windows.Data.Xml.Dom.IXmlNodeSerializer;
        interface Windows.Data.Xml.Dom.IXmlNodeSelector;
    }

    [
        activatable(Windows.Foundation.UniversalApiContract, 1.0),
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass XmlLoadSettings
    {
        [default] interface Windows.Data.Xml.Dom.IXmlLoadSettings;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass XmlNamedNodeMap
    {
        [default] interface Windows.Data.Xml.Dom.IXmlNamedNodeMap;
        interface Windows.Foundation.Collections.IVectorView<Windows.Data.Xml.Dom.IXmlNode *>;
        interface Windows.Foundation.Collections.IIterable<Windows.Data.Xml.Dom.IXmlNode *>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass XmlNodeList
    {
        [default] interface Windows.Data.Xml.Dom.IXmlNodeList;
        interface Windows.Foundation.Collections.IVectorView<Windows.Data.Xml.Dom.IXmlNode *>;
        interface Windows.Foundation.Collections.IIterable<Windows.Data.Xml.Dom.IXmlNode *>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass XmlProcessingInstruction
    {
        [default] interface Windows.Data.Xml.Dom.IXmlProcessingInstruction;
        interface Windows.Data.Xml.Dom.IXmlNode;
        interface Windows.Data.Xml.Dom.IXmlNodeSerializer;
        interface Windows.Data.Xml.Dom.IXmlNodeSelector;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass XmlText
    {
        [default] interface Windows.Data.Xml.Dom.IXmlText;
        interface Windows.Data.Xml.Dom.IXmlCharacterData;
        interface Windows.Data.Xml.Dom.IXmlNode;
        interface Windows.Data.Xml.Dom.IXmlNodeSerializer;
        interface Windows.Data.Xml.Dom.IXmlNodeSelector;
    }
}
