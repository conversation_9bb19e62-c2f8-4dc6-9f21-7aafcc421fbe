/*** Autogenerated by WIDL 10.8 from include/vsmgmt.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __vsmgmt_h__
#define __vsmgmt_h__

/* Forward declarations */

#ifndef __IVssSnapshotMgmt_FWD_DEFINED__
#define __IVssSnapshotMgmt_FWD_DEFINED__
typedef interface IVssSnapshotMgmt IVssSnapshotMgmt;
#ifdef __cplusplus
interface IVssSnapshotMgmt;
#endif /* __cplusplus */
#endif

#ifndef __IVssSnapshotMgmt2_FWD_DEFINED__
#define __IVssSnapshotMgmt2_FWD_DEFINED__
typedef interface IVssSnapshotMgmt2 IVssSnapshotMgmt2;
#ifdef __cplusplus
interface IVssSnapshotMgmt2;
#endif /* __cplusplus */
#endif

#ifndef __IVssDifferentialSoftwareSnapshotMgmt_FWD_DEFINED__
#define __IVssDifferentialSoftwareSnapshotMgmt_FWD_DEFINED__
typedef interface IVssDifferentialSoftwareSnapshotMgmt IVssDifferentialSoftwareSnapshotMgmt;
#ifdef __cplusplus
interface IVssDifferentialSoftwareSnapshotMgmt;
#endif /* __cplusplus */
#endif

#ifndef __IVssDifferentialSoftwareSnapshotMgmt2_FWD_DEFINED__
#define __IVssDifferentialSoftwareSnapshotMgmt2_FWD_DEFINED__
typedef interface IVssDifferentialSoftwareSnapshotMgmt2 IVssDifferentialSoftwareSnapshotMgmt2;
#ifdef __cplusplus
interface IVssDifferentialSoftwareSnapshotMgmt2;
#endif /* __cplusplus */
#endif

#ifndef __IVssDifferentialSoftwareSnapshotMgmt3_FWD_DEFINED__
#define __IVssDifferentialSoftwareSnapshotMgmt3_FWD_DEFINED__
typedef interface IVssDifferentialSoftwareSnapshotMgmt3 IVssDifferentialSoftwareSnapshotMgmt3;
#ifdef __cplusplus
interface IVssDifferentialSoftwareSnapshotMgmt3;
#endif /* __cplusplus */
#endif

#ifndef __IVssEnumMgmtObject_FWD_DEFINED__
#define __IVssEnumMgmtObject_FWD_DEFINED__
typedef interface IVssEnumMgmtObject IVssEnumMgmtObject;
#ifdef __cplusplus
interface IVssEnumMgmtObject;
#endif /* __cplusplus */
#endif

#ifndef __VssSnapshotMgmt_FWD_DEFINED__
#define __VssSnapshotMgmt_FWD_DEFINED__
#ifdef __cplusplus
typedef class VssSnapshotMgmt VssSnapshotMgmt;
#else
typedef struct VssSnapshotMgmt VssSnapshotMgmt;
#endif /* defined __cplusplus */
#endif /* defined __VssSnapshotMgmt_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <vss.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
typedef enum _VSS_MGMT_OBJECT_TYPE {
    VSS_MGMT_OBJECT_UNKNOWN = 0,
    VSS_MGMT_OBJECT_VOLUME = 1,
    VSS_MGMT_OBJECT_DIFF_VOLUME = 2,
    VSS_MGMT_OBJECT_DIFF_AREA = 3
} VSS_MGMT_OBJECT_TYPE;
typedef enum _VSS_MGMT_OBJECT_TYPE *PVSS_MGMT_OBJECT_TYPE;
#define VSS_ASSOC_NO_MAX_SPACE (-1)

#define VSS_ASSOC_REMOVE (0)

typedef struct _VSS_VOLUME_PROP {
    VSS_PWSZ m_pwszVolumeName;
    VSS_PWSZ m_pwszVolumeDisplayName;
} VSS_VOLUME_PROP;
typedef struct _VSS_VOLUME_PROP *PVSS_VOLUME_PROP;
typedef struct _VSS_DIFF_VOLUME_PROP {
    VSS_PWSZ m_pwszVolumeName;
    VSS_PWSZ m_pwszVolumeDisplayName;
    LONGLONG m_llVolumeFreeSpace;
    LONGLONG m_llVolumeTotalSpace;
} VSS_DIFF_VOLUME_PROP;
typedef struct _VSS_DIFF_VOLUME_PROP *PVSS_DIFF_VOLUME_PROP;
typedef struct _VSS_DIFF_AREA_PROP {
    VSS_PWSZ m_pwszVolumeName;
    VSS_PWSZ m_pwszDiffAreaVolumeName;
    LONGLONG m_llMaximumDiffSpace;
    LONGLONG m_llAllocatedDiffSpace;
    LONGLONG m_llUsedDiffSpace;
} VSS_DIFF_AREA_PROP;
typedef struct _VSS_DIFF_AREA_PROP *PVSS_DIFF_AREA_PROP;
typedef union __WIDL_vsmgmt_generated_name_00000021 {
    VSS_VOLUME_PROP Vol;
    VSS_DIFF_VOLUME_PROP DiffVol;
    VSS_DIFF_AREA_PROP DiffArea;
} VSS_MGMT_OBJECT_UNION;
typedef union __WIDL_vsmgmt_generated_name_00000021 *PVSS_MGMT_OBJECT_UNION;
typedef struct _VSS_MGMT_OBJECT_PROP {
    VSS_MGMT_OBJECT_TYPE Type;
    VSS_MGMT_OBJECT_UNION Obj;
} VSS_MGMT_OBJECT_PROP;
typedef struct _VSS_MGMT_OBJECT_PROP *PVSS_MGMT_OBJECT_PROP;
typedef enum _VSS_PROTECTION_LEVEL {
    VSS_PROTECTION_LEVEL_ORIGINAL_VOLUME = 0,
    VSS_PROTECTION_LEVEL_SNAPSHOT = 1
} VSS_PROTECTION_LEVEL;
typedef enum _VSS_PROTECTION_LEVEL *PVSS_PROTECTION_LEVEL;
typedef enum _VSS_PROTECTION_FAULT {
    VSS_PROTECTION_FAULT_NONE = 0,
    VSS_PROTECTION_FAULT_DIFF_AREA_MISSING = 1,
    VSS_PROTECTION_FAULT_IO_FAILURE_DURING_ONLINE = 2,
    VSS_PROTECTION_FAULT_META_DATA_CORRUPTION = 3,
    VSS_PROTECTION_FAULT_MEMORY_ALLOCATION_FAILURE = 4,
    VSS_PROTECTION_FAULT_MAPPED_MEMORY_FAILURE = 5,
    VSS_PROTECTION_FAULT_COW_READ_FAILURE = 6,
    VSS_PROTECTION_FAULT_COW_WRITE_FAILURE = 7,
    VSS_PROTECTION_FAULT_DIFF_AREA_FULL = 8,
    VSS_PROTECTION_FAULT_GROW_TOO_SLOW = 9,
    VSS_PROTECTION_FAULT_GROW_FAILED = 10,
    VSS_PROTECTION_FAULT_DESTROY_ALL_SNAPSHOTS = 11,
    VSS_PROTECTION_FAULT_FILE_SYSTEM_FAILURE = 12,
    VSS_PROTECTION_FAULT_IO_FAILURE = 13,
    VSS_PROTECTION_FAULT_DIFF_AREA_REMOVED = 14,
    VSS_PROTECTION_FAULT_EXTERNAL_WRITER_TO_DIFF_AREA = 15,
    VSS_PROTECTION_FAULT_MOUNT_DURING_CLUSTER_OFFLINE = 16
} VSS_PROTECTION_FAULT;
typedef enum _VSS_PROTECTION_FAULT *PVSS_PROTECTION_FAULT;
typedef struct _VSS_VOLUME_PROTECTION_INFO {
    VSS_PROTECTION_LEVEL m_protectionLevel;
    WINBOOL m_volumeIsOfflineForProtection;
    VSS_PROTECTION_FAULT m_protectionFault;
    LONG m_failureStatus;
    WINBOOL m_volumeHasUnusedDiffArea;
    DWORD m_reserved;
} VSS_VOLUME_PROTECTION_INFO;
typedef struct _VSS_VOLUME_PROTECTION_INFO *PVSS_VOLUME_PROTECTION_INFO;
#ifndef __IVssSnapshotMgmt_FWD_DEFINED__
#define __IVssSnapshotMgmt_FWD_DEFINED__
typedef interface IVssSnapshotMgmt IVssSnapshotMgmt;
#ifdef __cplusplus
interface IVssSnapshotMgmt;
#endif /* __cplusplus */
#endif

#ifndef __IVssDifferentialSoftwareSnapshotMgmt_FWD_DEFINED__
#define __IVssDifferentialSoftwareSnapshotMgmt_FWD_DEFINED__
typedef interface IVssDifferentialSoftwareSnapshotMgmt IVssDifferentialSoftwareSnapshotMgmt;
#ifdef __cplusplus
interface IVssDifferentialSoftwareSnapshotMgmt;
#endif /* __cplusplus */
#endif

#ifndef __IVssEnumMgmtObject_FWD_DEFINED__
#define __IVssEnumMgmtObject_FWD_DEFINED__
typedef interface IVssEnumMgmtObject IVssEnumMgmtObject;
#ifdef __cplusplus
interface IVssEnumMgmtObject;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IVssSnapshotMgmt interface
 */
#ifndef __IVssSnapshotMgmt_INTERFACE_DEFINED__
#define __IVssSnapshotMgmt_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssSnapshotMgmt, 0xfa7df749, 0x66e7, 0x4986, 0xa2,0x7f, 0xe2,0xf0,0x4a,0xe5,0x37,0x72);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fa7df749-66e7-4986-a27f-e2f04ae53772")
IVssSnapshotMgmt : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetProviderMgmtInterface(
        VSS_ID ProviderId,
        REFIID InterfaceId,
        IUnknown **ppItf) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryVolumesSupportedForSnapshots(
        VSS_ID ProviderId,
        LONG lContext,
        IVssEnumMgmtObject **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE QuerySnapshotsByVolume(
        VSS_PWSZ pwszVolumeName,
        VSS_ID ProviderId,
        IVssEnumObject **ppEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssSnapshotMgmt, 0xfa7df749, 0x66e7, 0x4986, 0xa2,0x7f, 0xe2,0xf0,0x4a,0xe5,0x37,0x72)
#endif
#else
typedef struct IVssSnapshotMgmtVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssSnapshotMgmt *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssSnapshotMgmt *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssSnapshotMgmt *This);

    /*** IVssSnapshotMgmt methods ***/
    HRESULT (STDMETHODCALLTYPE *GetProviderMgmtInterface)(
        IVssSnapshotMgmt *This,
        VSS_ID ProviderId,
        REFIID InterfaceId,
        IUnknown **ppItf);

    HRESULT (STDMETHODCALLTYPE *QueryVolumesSupportedForSnapshots)(
        IVssSnapshotMgmt *This,
        VSS_ID ProviderId,
        LONG lContext,
        IVssEnumMgmtObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *QuerySnapshotsByVolume)(
        IVssSnapshotMgmt *This,
        VSS_PWSZ pwszVolumeName,
        VSS_ID ProviderId,
        IVssEnumObject **ppEnum);

    END_INTERFACE
} IVssSnapshotMgmtVtbl;

interface IVssSnapshotMgmt {
    CONST_VTBL IVssSnapshotMgmtVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssSnapshotMgmt_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssSnapshotMgmt_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssSnapshotMgmt_Release(This) (This)->lpVtbl->Release(This)
/*** IVssSnapshotMgmt methods ***/
#define IVssSnapshotMgmt_GetProviderMgmtInterface(This,ProviderId,InterfaceId,ppItf) (This)->lpVtbl->GetProviderMgmtInterface(This,ProviderId,InterfaceId,ppItf)
#define IVssSnapshotMgmt_QueryVolumesSupportedForSnapshots(This,ProviderId,lContext,ppEnum) (This)->lpVtbl->QueryVolumesSupportedForSnapshots(This,ProviderId,lContext,ppEnum)
#define IVssSnapshotMgmt_QuerySnapshotsByVolume(This,pwszVolumeName,ProviderId,ppEnum) (This)->lpVtbl->QuerySnapshotsByVolume(This,pwszVolumeName,ProviderId,ppEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssSnapshotMgmt_QueryInterface(IVssSnapshotMgmt* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssSnapshotMgmt_AddRef(IVssSnapshotMgmt* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssSnapshotMgmt_Release(IVssSnapshotMgmt* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssSnapshotMgmt methods ***/
static inline HRESULT IVssSnapshotMgmt_GetProviderMgmtInterface(IVssSnapshotMgmt* This,VSS_ID ProviderId,REFIID InterfaceId,IUnknown **ppItf) {
    return This->lpVtbl->GetProviderMgmtInterface(This,ProviderId,InterfaceId,ppItf);
}
static inline HRESULT IVssSnapshotMgmt_QueryVolumesSupportedForSnapshots(IVssSnapshotMgmt* This,VSS_ID ProviderId,LONG lContext,IVssEnumMgmtObject **ppEnum) {
    return This->lpVtbl->QueryVolumesSupportedForSnapshots(This,ProviderId,lContext,ppEnum);
}
static inline HRESULT IVssSnapshotMgmt_QuerySnapshotsByVolume(IVssSnapshotMgmt* This,VSS_PWSZ pwszVolumeName,VSS_ID ProviderId,IVssEnumObject **ppEnum) {
    return This->lpVtbl->QuerySnapshotsByVolume(This,pwszVolumeName,ProviderId,ppEnum);
}
#endif
#endif

#endif


#endif  /* __IVssSnapshotMgmt_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssSnapshotMgmt2 interface
 */
#ifndef __IVssSnapshotMgmt2_INTERFACE_DEFINED__
#define __IVssSnapshotMgmt2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssSnapshotMgmt2, 0x0f61ec39, 0xfe82, 0x45f2, 0xa3,0xf0, 0x76,0x8b,0x5d,0x42,0x71,0x02);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0f61ec39-fe82-45f2-a3f0-768b5d427102")
IVssSnapshotMgmt2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetMinDiffAreaSize(
        LONGLONG *pllMinDiffAreaSize) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssSnapshotMgmt2, 0x0f61ec39, 0xfe82, 0x45f2, 0xa3,0xf0, 0x76,0x8b,0x5d,0x42,0x71,0x02)
#endif
#else
typedef struct IVssSnapshotMgmt2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssSnapshotMgmt2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssSnapshotMgmt2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssSnapshotMgmt2 *This);

    /*** IVssSnapshotMgmt2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMinDiffAreaSize)(
        IVssSnapshotMgmt2 *This,
        LONGLONG *pllMinDiffAreaSize);

    END_INTERFACE
} IVssSnapshotMgmt2Vtbl;

interface IVssSnapshotMgmt2 {
    CONST_VTBL IVssSnapshotMgmt2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssSnapshotMgmt2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssSnapshotMgmt2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssSnapshotMgmt2_Release(This) (This)->lpVtbl->Release(This)
/*** IVssSnapshotMgmt2 methods ***/
#define IVssSnapshotMgmt2_GetMinDiffAreaSize(This,pllMinDiffAreaSize) (This)->lpVtbl->GetMinDiffAreaSize(This,pllMinDiffAreaSize)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssSnapshotMgmt2_QueryInterface(IVssSnapshotMgmt2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssSnapshotMgmt2_AddRef(IVssSnapshotMgmt2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssSnapshotMgmt2_Release(IVssSnapshotMgmt2* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssSnapshotMgmt2 methods ***/
static inline HRESULT IVssSnapshotMgmt2_GetMinDiffAreaSize(IVssSnapshotMgmt2* This,LONGLONG *pllMinDiffAreaSize) {
    return This->lpVtbl->GetMinDiffAreaSize(This,pllMinDiffAreaSize);
}
#endif
#endif

#endif


#endif  /* __IVssSnapshotMgmt2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssDifferentialSoftwareSnapshotMgmt interface
 */
#ifndef __IVssDifferentialSoftwareSnapshotMgmt_INTERFACE_DEFINED__
#define __IVssDifferentialSoftwareSnapshotMgmt_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssDifferentialSoftwareSnapshotMgmt, 0x214a0f28, 0xb737, 0x4026, 0xb8,0x47, 0x4f,0x9e,0x37,0xd7,0x95,0x29);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("214a0f28-b737-4026-b847-4f9e37d79529")
IVssDifferentialSoftwareSnapshotMgmt : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddDiffArea(
        VSS_PWSZ pwszVolumeName,
        VSS_PWSZ pwszDiffAreaVolumeName,
        LONGLONG llMaximumDiffSpace) = 0;

    virtual HRESULT STDMETHODCALLTYPE ChangeDiffAreaMaximumSize(
        VSS_PWSZ pwszVolumeName,
        VSS_PWSZ pwszDiffAreaVolumeName,
        LONGLONG llMaximumDiffSpace) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryVolumesSupportedForDiffAreas(
        VSS_PWSZ pwszOriginalVolumeName,
        IVssEnumMgmtObject **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryDiffAreasForVolume(
        VSS_PWSZ pwszVolumeName,
        IVssEnumMgmtObject **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryDiffAreasOnVolume(
        VSS_PWSZ pwszVolumeName,
        IVssEnumMgmtObject **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryDiffAreasForSnapshot(
        VSS_ID SnapshotId,
        IVssEnumMgmtObject **ppEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssDifferentialSoftwareSnapshotMgmt, 0x214a0f28, 0xb737, 0x4026, 0xb8,0x47, 0x4f,0x9e,0x37,0xd7,0x95,0x29)
#endif
#else
typedef struct IVssDifferentialSoftwareSnapshotMgmtVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssDifferentialSoftwareSnapshotMgmt *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssDifferentialSoftwareSnapshotMgmt *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssDifferentialSoftwareSnapshotMgmt *This);

    /*** IVssDifferentialSoftwareSnapshotMgmt methods ***/
    HRESULT (STDMETHODCALLTYPE *AddDiffArea)(
        IVssDifferentialSoftwareSnapshotMgmt *This,
        VSS_PWSZ pwszVolumeName,
        VSS_PWSZ pwszDiffAreaVolumeName,
        LONGLONG llMaximumDiffSpace);

    HRESULT (STDMETHODCALLTYPE *ChangeDiffAreaMaximumSize)(
        IVssDifferentialSoftwareSnapshotMgmt *This,
        VSS_PWSZ pwszVolumeName,
        VSS_PWSZ pwszDiffAreaVolumeName,
        LONGLONG llMaximumDiffSpace);

    HRESULT (STDMETHODCALLTYPE *QueryVolumesSupportedForDiffAreas)(
        IVssDifferentialSoftwareSnapshotMgmt *This,
        VSS_PWSZ pwszOriginalVolumeName,
        IVssEnumMgmtObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *QueryDiffAreasForVolume)(
        IVssDifferentialSoftwareSnapshotMgmt *This,
        VSS_PWSZ pwszVolumeName,
        IVssEnumMgmtObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *QueryDiffAreasOnVolume)(
        IVssDifferentialSoftwareSnapshotMgmt *This,
        VSS_PWSZ pwszVolumeName,
        IVssEnumMgmtObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *QueryDiffAreasForSnapshot)(
        IVssDifferentialSoftwareSnapshotMgmt *This,
        VSS_ID SnapshotId,
        IVssEnumMgmtObject **ppEnum);

    END_INTERFACE
} IVssDifferentialSoftwareSnapshotMgmtVtbl;

interface IVssDifferentialSoftwareSnapshotMgmt {
    CONST_VTBL IVssDifferentialSoftwareSnapshotMgmtVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssDifferentialSoftwareSnapshotMgmt_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssDifferentialSoftwareSnapshotMgmt_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssDifferentialSoftwareSnapshotMgmt_Release(This) (This)->lpVtbl->Release(This)
/*** IVssDifferentialSoftwareSnapshotMgmt methods ***/
#define IVssDifferentialSoftwareSnapshotMgmt_AddDiffArea(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace) (This)->lpVtbl->AddDiffArea(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace)
#define IVssDifferentialSoftwareSnapshotMgmt_ChangeDiffAreaMaximumSize(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace) (This)->lpVtbl->ChangeDiffAreaMaximumSize(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace)
#define IVssDifferentialSoftwareSnapshotMgmt_QueryVolumesSupportedForDiffAreas(This,pwszOriginalVolumeName,ppEnum) (This)->lpVtbl->QueryVolumesSupportedForDiffAreas(This,pwszOriginalVolumeName,ppEnum)
#define IVssDifferentialSoftwareSnapshotMgmt_QueryDiffAreasForVolume(This,pwszVolumeName,ppEnum) (This)->lpVtbl->QueryDiffAreasForVolume(This,pwszVolumeName,ppEnum)
#define IVssDifferentialSoftwareSnapshotMgmt_QueryDiffAreasOnVolume(This,pwszVolumeName,ppEnum) (This)->lpVtbl->QueryDiffAreasOnVolume(This,pwszVolumeName,ppEnum)
#define IVssDifferentialSoftwareSnapshotMgmt_QueryDiffAreasForSnapshot(This,SnapshotId,ppEnum) (This)->lpVtbl->QueryDiffAreasForSnapshot(This,SnapshotId,ppEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt_QueryInterface(IVssDifferentialSoftwareSnapshotMgmt* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssDifferentialSoftwareSnapshotMgmt_AddRef(IVssDifferentialSoftwareSnapshotMgmt* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssDifferentialSoftwareSnapshotMgmt_Release(IVssDifferentialSoftwareSnapshotMgmt* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssDifferentialSoftwareSnapshotMgmt methods ***/
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt_AddDiffArea(IVssDifferentialSoftwareSnapshotMgmt* This,VSS_PWSZ pwszVolumeName,VSS_PWSZ pwszDiffAreaVolumeName,LONGLONG llMaximumDiffSpace) {
    return This->lpVtbl->AddDiffArea(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt_ChangeDiffAreaMaximumSize(IVssDifferentialSoftwareSnapshotMgmt* This,VSS_PWSZ pwszVolumeName,VSS_PWSZ pwszDiffAreaVolumeName,LONGLONG llMaximumDiffSpace) {
    return This->lpVtbl->ChangeDiffAreaMaximumSize(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt_QueryVolumesSupportedForDiffAreas(IVssDifferentialSoftwareSnapshotMgmt* This,VSS_PWSZ pwszOriginalVolumeName,IVssEnumMgmtObject **ppEnum) {
    return This->lpVtbl->QueryVolumesSupportedForDiffAreas(This,pwszOriginalVolumeName,ppEnum);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt_QueryDiffAreasForVolume(IVssDifferentialSoftwareSnapshotMgmt* This,VSS_PWSZ pwszVolumeName,IVssEnumMgmtObject **ppEnum) {
    return This->lpVtbl->QueryDiffAreasForVolume(This,pwszVolumeName,ppEnum);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt_QueryDiffAreasOnVolume(IVssDifferentialSoftwareSnapshotMgmt* This,VSS_PWSZ pwszVolumeName,IVssEnumMgmtObject **ppEnum) {
    return This->lpVtbl->QueryDiffAreasOnVolume(This,pwszVolumeName,ppEnum);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt_QueryDiffAreasForSnapshot(IVssDifferentialSoftwareSnapshotMgmt* This,VSS_ID SnapshotId,IVssEnumMgmtObject **ppEnum) {
    return This->lpVtbl->QueryDiffAreasForSnapshot(This,SnapshotId,ppEnum);
}
#endif
#endif

#endif


#endif  /* __IVssDifferentialSoftwareSnapshotMgmt_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssDifferentialSoftwareSnapshotMgmt2 interface
 */
#ifndef __IVssDifferentialSoftwareSnapshotMgmt2_INTERFACE_DEFINED__
#define __IVssDifferentialSoftwareSnapshotMgmt2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssDifferentialSoftwareSnapshotMgmt2, 0x949d7353, 0x675f, 0x4275, 0x89,0x69, 0xf0,0x44,0xc6,0x27,0x78,0x15);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("949d7353-675f-4275-8969-f044c6277815")
IVssDifferentialSoftwareSnapshotMgmt2 : public IVssDifferentialSoftwareSnapshotMgmt
{
    virtual HRESULT STDMETHODCALLTYPE ChangeDiffAreaMaximumSizeEx(
        VSS_PWSZ pwszVolumeName,
        VSS_PWSZ pwszDiffAreaVolumeName,
        LONGLONG llMaximumDiffSpace,
        WINBOOL bVolatile) = 0;

    virtual HRESULT STDMETHODCALLTYPE MigrateDiffAreas(
        VSS_PWSZ pwszVolumeName,
        VSS_PWSZ pwszDiffAreaVolumeName,
        VSS_PWSZ pwszNewDiffAreaVolumeName) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryMigrationStatus(
        VSS_PWSZ pwszVolumeName,
        VSS_PWSZ pwszDiffAreaVolumeName,
        IVssAsync **ppAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSnapshotPriority(
        VSS_ID idSnapshot,
        BYTE priority) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssDifferentialSoftwareSnapshotMgmt2, 0x949d7353, 0x675f, 0x4275, 0x89,0x69, 0xf0,0x44,0xc6,0x27,0x78,0x15)
#endif
#else
typedef struct IVssDifferentialSoftwareSnapshotMgmt2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssDifferentialSoftwareSnapshotMgmt2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssDifferentialSoftwareSnapshotMgmt2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssDifferentialSoftwareSnapshotMgmt2 *This);

    /*** IVssDifferentialSoftwareSnapshotMgmt methods ***/
    HRESULT (STDMETHODCALLTYPE *AddDiffArea)(
        IVssDifferentialSoftwareSnapshotMgmt2 *This,
        VSS_PWSZ pwszVolumeName,
        VSS_PWSZ pwszDiffAreaVolumeName,
        LONGLONG llMaximumDiffSpace);

    HRESULT (STDMETHODCALLTYPE *ChangeDiffAreaMaximumSize)(
        IVssDifferentialSoftwareSnapshotMgmt2 *This,
        VSS_PWSZ pwszVolumeName,
        VSS_PWSZ pwszDiffAreaVolumeName,
        LONGLONG llMaximumDiffSpace);

    HRESULT (STDMETHODCALLTYPE *QueryVolumesSupportedForDiffAreas)(
        IVssDifferentialSoftwareSnapshotMgmt2 *This,
        VSS_PWSZ pwszOriginalVolumeName,
        IVssEnumMgmtObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *QueryDiffAreasForVolume)(
        IVssDifferentialSoftwareSnapshotMgmt2 *This,
        VSS_PWSZ pwszVolumeName,
        IVssEnumMgmtObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *QueryDiffAreasOnVolume)(
        IVssDifferentialSoftwareSnapshotMgmt2 *This,
        VSS_PWSZ pwszVolumeName,
        IVssEnumMgmtObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *QueryDiffAreasForSnapshot)(
        IVssDifferentialSoftwareSnapshotMgmt2 *This,
        VSS_ID SnapshotId,
        IVssEnumMgmtObject **ppEnum);

    /*** IVssDifferentialSoftwareSnapshotMgmt2 methods ***/
    HRESULT (STDMETHODCALLTYPE *ChangeDiffAreaMaximumSizeEx)(
        IVssDifferentialSoftwareSnapshotMgmt2 *This,
        VSS_PWSZ pwszVolumeName,
        VSS_PWSZ pwszDiffAreaVolumeName,
        LONGLONG llMaximumDiffSpace,
        WINBOOL bVolatile);

    HRESULT (STDMETHODCALLTYPE *MigrateDiffAreas)(
        IVssDifferentialSoftwareSnapshotMgmt2 *This,
        VSS_PWSZ pwszVolumeName,
        VSS_PWSZ pwszDiffAreaVolumeName,
        VSS_PWSZ pwszNewDiffAreaVolumeName);

    HRESULT (STDMETHODCALLTYPE *QueryMigrationStatus)(
        IVssDifferentialSoftwareSnapshotMgmt2 *This,
        VSS_PWSZ pwszVolumeName,
        VSS_PWSZ pwszDiffAreaVolumeName,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *SetSnapshotPriority)(
        IVssDifferentialSoftwareSnapshotMgmt2 *This,
        VSS_ID idSnapshot,
        BYTE priority);

    END_INTERFACE
} IVssDifferentialSoftwareSnapshotMgmt2Vtbl;

interface IVssDifferentialSoftwareSnapshotMgmt2 {
    CONST_VTBL IVssDifferentialSoftwareSnapshotMgmt2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssDifferentialSoftwareSnapshotMgmt2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssDifferentialSoftwareSnapshotMgmt2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssDifferentialSoftwareSnapshotMgmt2_Release(This) (This)->lpVtbl->Release(This)
/*** IVssDifferentialSoftwareSnapshotMgmt methods ***/
#define IVssDifferentialSoftwareSnapshotMgmt2_AddDiffArea(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace) (This)->lpVtbl->AddDiffArea(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace)
#define IVssDifferentialSoftwareSnapshotMgmt2_ChangeDiffAreaMaximumSize(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace) (This)->lpVtbl->ChangeDiffAreaMaximumSize(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace)
#define IVssDifferentialSoftwareSnapshotMgmt2_QueryVolumesSupportedForDiffAreas(This,pwszOriginalVolumeName,ppEnum) (This)->lpVtbl->QueryVolumesSupportedForDiffAreas(This,pwszOriginalVolumeName,ppEnum)
#define IVssDifferentialSoftwareSnapshotMgmt2_QueryDiffAreasForVolume(This,pwszVolumeName,ppEnum) (This)->lpVtbl->QueryDiffAreasForVolume(This,pwszVolumeName,ppEnum)
#define IVssDifferentialSoftwareSnapshotMgmt2_QueryDiffAreasOnVolume(This,pwszVolumeName,ppEnum) (This)->lpVtbl->QueryDiffAreasOnVolume(This,pwszVolumeName,ppEnum)
#define IVssDifferentialSoftwareSnapshotMgmt2_QueryDiffAreasForSnapshot(This,SnapshotId,ppEnum) (This)->lpVtbl->QueryDiffAreasForSnapshot(This,SnapshotId,ppEnum)
/*** IVssDifferentialSoftwareSnapshotMgmt2 methods ***/
#define IVssDifferentialSoftwareSnapshotMgmt2_ChangeDiffAreaMaximumSizeEx(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace,bVolatile) (This)->lpVtbl->ChangeDiffAreaMaximumSizeEx(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace,bVolatile)
#define IVssDifferentialSoftwareSnapshotMgmt2_MigrateDiffAreas(This,pwszVolumeName,pwszDiffAreaVolumeName,pwszNewDiffAreaVolumeName) (This)->lpVtbl->MigrateDiffAreas(This,pwszVolumeName,pwszDiffAreaVolumeName,pwszNewDiffAreaVolumeName)
#define IVssDifferentialSoftwareSnapshotMgmt2_QueryMigrationStatus(This,pwszVolumeName,pwszDiffAreaVolumeName,ppAsync) (This)->lpVtbl->QueryMigrationStatus(This,pwszVolumeName,pwszDiffAreaVolumeName,ppAsync)
#define IVssDifferentialSoftwareSnapshotMgmt2_SetSnapshotPriority(This,idSnapshot,priority) (This)->lpVtbl->SetSnapshotPriority(This,idSnapshot,priority)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt2_QueryInterface(IVssDifferentialSoftwareSnapshotMgmt2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssDifferentialSoftwareSnapshotMgmt2_AddRef(IVssDifferentialSoftwareSnapshotMgmt2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssDifferentialSoftwareSnapshotMgmt2_Release(IVssDifferentialSoftwareSnapshotMgmt2* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssDifferentialSoftwareSnapshotMgmt methods ***/
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt2_AddDiffArea(IVssDifferentialSoftwareSnapshotMgmt2* This,VSS_PWSZ pwszVolumeName,VSS_PWSZ pwszDiffAreaVolumeName,LONGLONG llMaximumDiffSpace) {
    return This->lpVtbl->AddDiffArea(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt2_ChangeDiffAreaMaximumSize(IVssDifferentialSoftwareSnapshotMgmt2* This,VSS_PWSZ pwszVolumeName,VSS_PWSZ pwszDiffAreaVolumeName,LONGLONG llMaximumDiffSpace) {
    return This->lpVtbl->ChangeDiffAreaMaximumSize(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt2_QueryVolumesSupportedForDiffAreas(IVssDifferentialSoftwareSnapshotMgmt2* This,VSS_PWSZ pwszOriginalVolumeName,IVssEnumMgmtObject **ppEnum) {
    return This->lpVtbl->QueryVolumesSupportedForDiffAreas(This,pwszOriginalVolumeName,ppEnum);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt2_QueryDiffAreasForVolume(IVssDifferentialSoftwareSnapshotMgmt2* This,VSS_PWSZ pwszVolumeName,IVssEnumMgmtObject **ppEnum) {
    return This->lpVtbl->QueryDiffAreasForVolume(This,pwszVolumeName,ppEnum);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt2_QueryDiffAreasOnVolume(IVssDifferentialSoftwareSnapshotMgmt2* This,VSS_PWSZ pwszVolumeName,IVssEnumMgmtObject **ppEnum) {
    return This->lpVtbl->QueryDiffAreasOnVolume(This,pwszVolumeName,ppEnum);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt2_QueryDiffAreasForSnapshot(IVssDifferentialSoftwareSnapshotMgmt2* This,VSS_ID SnapshotId,IVssEnumMgmtObject **ppEnum) {
    return This->lpVtbl->QueryDiffAreasForSnapshot(This,SnapshotId,ppEnum);
}
/*** IVssDifferentialSoftwareSnapshotMgmt2 methods ***/
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt2_ChangeDiffAreaMaximumSizeEx(IVssDifferentialSoftwareSnapshotMgmt2* This,VSS_PWSZ pwszVolumeName,VSS_PWSZ pwszDiffAreaVolumeName,LONGLONG llMaximumDiffSpace,WINBOOL bVolatile) {
    return This->lpVtbl->ChangeDiffAreaMaximumSizeEx(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace,bVolatile);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt2_MigrateDiffAreas(IVssDifferentialSoftwareSnapshotMgmt2* This,VSS_PWSZ pwszVolumeName,VSS_PWSZ pwszDiffAreaVolumeName,VSS_PWSZ pwszNewDiffAreaVolumeName) {
    return This->lpVtbl->MigrateDiffAreas(This,pwszVolumeName,pwszDiffAreaVolumeName,pwszNewDiffAreaVolumeName);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt2_QueryMigrationStatus(IVssDifferentialSoftwareSnapshotMgmt2* This,VSS_PWSZ pwszVolumeName,VSS_PWSZ pwszDiffAreaVolumeName,IVssAsync **ppAsync) {
    return This->lpVtbl->QueryMigrationStatus(This,pwszVolumeName,pwszDiffAreaVolumeName,ppAsync);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt2_SetSnapshotPriority(IVssDifferentialSoftwareSnapshotMgmt2* This,VSS_ID idSnapshot,BYTE priority) {
    return This->lpVtbl->SetSnapshotPriority(This,idSnapshot,priority);
}
#endif
#endif

#endif


#endif  /* __IVssDifferentialSoftwareSnapshotMgmt2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssDifferentialSoftwareSnapshotMgmt3 interface
 */
#ifndef __IVssDifferentialSoftwareSnapshotMgmt3_INTERFACE_DEFINED__
#define __IVssDifferentialSoftwareSnapshotMgmt3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssDifferentialSoftwareSnapshotMgmt3, 0x383f7e71, 0xa4c5, 0x401f, 0xb2,0x7f, 0xf8,0x26,0x28,0x9f,0x84,0x58);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("383f7e71-a4c5-401f-b27f-f826289f8458")
IVssDifferentialSoftwareSnapshotMgmt3 : public IVssDifferentialSoftwareSnapshotMgmt2
{
    virtual HRESULT STDMETHODCALLTYPE SetVolumeProtectLevel(
        VSS_PWSZ pwszVolumeName,
        VSS_PROTECTION_LEVEL protectionLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVolumeProtectLevel(
        VSS_PWSZ pwszVolumeName,
        VSS_VOLUME_PROTECTION_INFO *protectionLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClearVolumeProtectFault(
        VSS_PWSZ pwszVolumeName) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteUnusedDiffAreas(
        VSS_PWSZ pwszDiffAreaVolumeName) = 0;

    virtual HRESULT STDMETHODCALLTYPE QuerySnapshotDeltaBitmap(
        VSS_ID idSnapshotOlder,
        VSS_ID idSnapshotYounger,
        ULONG *pcBlockSizePerBit,
        ULONG *pcBitmapLength,
        BYTE **ppbBitmap) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssDifferentialSoftwareSnapshotMgmt3, 0x383f7e71, 0xa4c5, 0x401f, 0xb2,0x7f, 0xf8,0x26,0x28,0x9f,0x84,0x58)
#endif
#else
typedef struct IVssDifferentialSoftwareSnapshotMgmt3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This);

    /*** IVssDifferentialSoftwareSnapshotMgmt methods ***/
    HRESULT (STDMETHODCALLTYPE *AddDiffArea)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This,
        VSS_PWSZ pwszVolumeName,
        VSS_PWSZ pwszDiffAreaVolumeName,
        LONGLONG llMaximumDiffSpace);

    HRESULT (STDMETHODCALLTYPE *ChangeDiffAreaMaximumSize)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This,
        VSS_PWSZ pwszVolumeName,
        VSS_PWSZ pwszDiffAreaVolumeName,
        LONGLONG llMaximumDiffSpace);

    HRESULT (STDMETHODCALLTYPE *QueryVolumesSupportedForDiffAreas)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This,
        VSS_PWSZ pwszOriginalVolumeName,
        IVssEnumMgmtObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *QueryDiffAreasForVolume)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This,
        VSS_PWSZ pwszVolumeName,
        IVssEnumMgmtObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *QueryDiffAreasOnVolume)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This,
        VSS_PWSZ pwszVolumeName,
        IVssEnumMgmtObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *QueryDiffAreasForSnapshot)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This,
        VSS_ID SnapshotId,
        IVssEnumMgmtObject **ppEnum);

    /*** IVssDifferentialSoftwareSnapshotMgmt2 methods ***/
    HRESULT (STDMETHODCALLTYPE *ChangeDiffAreaMaximumSizeEx)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This,
        VSS_PWSZ pwszVolumeName,
        VSS_PWSZ pwszDiffAreaVolumeName,
        LONGLONG llMaximumDiffSpace,
        WINBOOL bVolatile);

    HRESULT (STDMETHODCALLTYPE *MigrateDiffAreas)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This,
        VSS_PWSZ pwszVolumeName,
        VSS_PWSZ pwszDiffAreaVolumeName,
        VSS_PWSZ pwszNewDiffAreaVolumeName);

    HRESULT (STDMETHODCALLTYPE *QueryMigrationStatus)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This,
        VSS_PWSZ pwszVolumeName,
        VSS_PWSZ pwszDiffAreaVolumeName,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *SetSnapshotPriority)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This,
        VSS_ID idSnapshot,
        BYTE priority);

    /*** IVssDifferentialSoftwareSnapshotMgmt3 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetVolumeProtectLevel)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This,
        VSS_PWSZ pwszVolumeName,
        VSS_PROTECTION_LEVEL protectionLevel);

    HRESULT (STDMETHODCALLTYPE *GetVolumeProtectLevel)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This,
        VSS_PWSZ pwszVolumeName,
        VSS_VOLUME_PROTECTION_INFO *protectionLevel);

    HRESULT (STDMETHODCALLTYPE *ClearVolumeProtectFault)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This,
        VSS_PWSZ pwszVolumeName);

    HRESULT (STDMETHODCALLTYPE *DeleteUnusedDiffAreas)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This,
        VSS_PWSZ pwszDiffAreaVolumeName);

    HRESULT (STDMETHODCALLTYPE *QuerySnapshotDeltaBitmap)(
        IVssDifferentialSoftwareSnapshotMgmt3 *This,
        VSS_ID idSnapshotOlder,
        VSS_ID idSnapshotYounger,
        ULONG *pcBlockSizePerBit,
        ULONG *pcBitmapLength,
        BYTE **ppbBitmap);

    END_INTERFACE
} IVssDifferentialSoftwareSnapshotMgmt3Vtbl;

interface IVssDifferentialSoftwareSnapshotMgmt3 {
    CONST_VTBL IVssDifferentialSoftwareSnapshotMgmt3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssDifferentialSoftwareSnapshotMgmt3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssDifferentialSoftwareSnapshotMgmt3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssDifferentialSoftwareSnapshotMgmt3_Release(This) (This)->lpVtbl->Release(This)
/*** IVssDifferentialSoftwareSnapshotMgmt methods ***/
#define IVssDifferentialSoftwareSnapshotMgmt3_AddDiffArea(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace) (This)->lpVtbl->AddDiffArea(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace)
#define IVssDifferentialSoftwareSnapshotMgmt3_ChangeDiffAreaMaximumSize(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace) (This)->lpVtbl->ChangeDiffAreaMaximumSize(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace)
#define IVssDifferentialSoftwareSnapshotMgmt3_QueryVolumesSupportedForDiffAreas(This,pwszOriginalVolumeName,ppEnum) (This)->lpVtbl->QueryVolumesSupportedForDiffAreas(This,pwszOriginalVolumeName,ppEnum)
#define IVssDifferentialSoftwareSnapshotMgmt3_QueryDiffAreasForVolume(This,pwszVolumeName,ppEnum) (This)->lpVtbl->QueryDiffAreasForVolume(This,pwszVolumeName,ppEnum)
#define IVssDifferentialSoftwareSnapshotMgmt3_QueryDiffAreasOnVolume(This,pwszVolumeName,ppEnum) (This)->lpVtbl->QueryDiffAreasOnVolume(This,pwszVolumeName,ppEnum)
#define IVssDifferentialSoftwareSnapshotMgmt3_QueryDiffAreasForSnapshot(This,SnapshotId,ppEnum) (This)->lpVtbl->QueryDiffAreasForSnapshot(This,SnapshotId,ppEnum)
/*** IVssDifferentialSoftwareSnapshotMgmt2 methods ***/
#define IVssDifferentialSoftwareSnapshotMgmt3_ChangeDiffAreaMaximumSizeEx(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace,bVolatile) (This)->lpVtbl->ChangeDiffAreaMaximumSizeEx(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace,bVolatile)
#define IVssDifferentialSoftwareSnapshotMgmt3_MigrateDiffAreas(This,pwszVolumeName,pwszDiffAreaVolumeName,pwszNewDiffAreaVolumeName) (This)->lpVtbl->MigrateDiffAreas(This,pwszVolumeName,pwszDiffAreaVolumeName,pwszNewDiffAreaVolumeName)
#define IVssDifferentialSoftwareSnapshotMgmt3_QueryMigrationStatus(This,pwszVolumeName,pwszDiffAreaVolumeName,ppAsync) (This)->lpVtbl->QueryMigrationStatus(This,pwszVolumeName,pwszDiffAreaVolumeName,ppAsync)
#define IVssDifferentialSoftwareSnapshotMgmt3_SetSnapshotPriority(This,idSnapshot,priority) (This)->lpVtbl->SetSnapshotPriority(This,idSnapshot,priority)
/*** IVssDifferentialSoftwareSnapshotMgmt3 methods ***/
#define IVssDifferentialSoftwareSnapshotMgmt3_SetVolumeProtectLevel(This,pwszVolumeName,protectionLevel) (This)->lpVtbl->SetVolumeProtectLevel(This,pwszVolumeName,protectionLevel)
#define IVssDifferentialSoftwareSnapshotMgmt3_GetVolumeProtectLevel(This,pwszVolumeName,protectionLevel) (This)->lpVtbl->GetVolumeProtectLevel(This,pwszVolumeName,protectionLevel)
#define IVssDifferentialSoftwareSnapshotMgmt3_ClearVolumeProtectFault(This,pwszVolumeName) (This)->lpVtbl->ClearVolumeProtectFault(This,pwszVolumeName)
#define IVssDifferentialSoftwareSnapshotMgmt3_DeleteUnusedDiffAreas(This,pwszDiffAreaVolumeName) (This)->lpVtbl->DeleteUnusedDiffAreas(This,pwszDiffAreaVolumeName)
#define IVssDifferentialSoftwareSnapshotMgmt3_QuerySnapshotDeltaBitmap(This,idSnapshotOlder,idSnapshotYounger,pcBlockSizePerBit,pcBitmapLength,ppbBitmap) (This)->lpVtbl->QuerySnapshotDeltaBitmap(This,idSnapshotOlder,idSnapshotYounger,pcBlockSizePerBit,pcBitmapLength,ppbBitmap)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt3_QueryInterface(IVssDifferentialSoftwareSnapshotMgmt3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssDifferentialSoftwareSnapshotMgmt3_AddRef(IVssDifferentialSoftwareSnapshotMgmt3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssDifferentialSoftwareSnapshotMgmt3_Release(IVssDifferentialSoftwareSnapshotMgmt3* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssDifferentialSoftwareSnapshotMgmt methods ***/
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt3_AddDiffArea(IVssDifferentialSoftwareSnapshotMgmt3* This,VSS_PWSZ pwszVolumeName,VSS_PWSZ pwszDiffAreaVolumeName,LONGLONG llMaximumDiffSpace) {
    return This->lpVtbl->AddDiffArea(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt3_ChangeDiffAreaMaximumSize(IVssDifferentialSoftwareSnapshotMgmt3* This,VSS_PWSZ pwszVolumeName,VSS_PWSZ pwszDiffAreaVolumeName,LONGLONG llMaximumDiffSpace) {
    return This->lpVtbl->ChangeDiffAreaMaximumSize(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt3_QueryVolumesSupportedForDiffAreas(IVssDifferentialSoftwareSnapshotMgmt3* This,VSS_PWSZ pwszOriginalVolumeName,IVssEnumMgmtObject **ppEnum) {
    return This->lpVtbl->QueryVolumesSupportedForDiffAreas(This,pwszOriginalVolumeName,ppEnum);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt3_QueryDiffAreasForVolume(IVssDifferentialSoftwareSnapshotMgmt3* This,VSS_PWSZ pwszVolumeName,IVssEnumMgmtObject **ppEnum) {
    return This->lpVtbl->QueryDiffAreasForVolume(This,pwszVolumeName,ppEnum);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt3_QueryDiffAreasOnVolume(IVssDifferentialSoftwareSnapshotMgmt3* This,VSS_PWSZ pwszVolumeName,IVssEnumMgmtObject **ppEnum) {
    return This->lpVtbl->QueryDiffAreasOnVolume(This,pwszVolumeName,ppEnum);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt3_QueryDiffAreasForSnapshot(IVssDifferentialSoftwareSnapshotMgmt3* This,VSS_ID SnapshotId,IVssEnumMgmtObject **ppEnum) {
    return This->lpVtbl->QueryDiffAreasForSnapshot(This,SnapshotId,ppEnum);
}
/*** IVssDifferentialSoftwareSnapshotMgmt2 methods ***/
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt3_ChangeDiffAreaMaximumSizeEx(IVssDifferentialSoftwareSnapshotMgmt3* This,VSS_PWSZ pwszVolumeName,VSS_PWSZ pwszDiffAreaVolumeName,LONGLONG llMaximumDiffSpace,WINBOOL bVolatile) {
    return This->lpVtbl->ChangeDiffAreaMaximumSizeEx(This,pwszVolumeName,pwszDiffAreaVolumeName,llMaximumDiffSpace,bVolatile);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt3_MigrateDiffAreas(IVssDifferentialSoftwareSnapshotMgmt3* This,VSS_PWSZ pwszVolumeName,VSS_PWSZ pwszDiffAreaVolumeName,VSS_PWSZ pwszNewDiffAreaVolumeName) {
    return This->lpVtbl->MigrateDiffAreas(This,pwszVolumeName,pwszDiffAreaVolumeName,pwszNewDiffAreaVolumeName);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt3_QueryMigrationStatus(IVssDifferentialSoftwareSnapshotMgmt3* This,VSS_PWSZ pwszVolumeName,VSS_PWSZ pwszDiffAreaVolumeName,IVssAsync **ppAsync) {
    return This->lpVtbl->QueryMigrationStatus(This,pwszVolumeName,pwszDiffAreaVolumeName,ppAsync);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt3_SetSnapshotPriority(IVssDifferentialSoftwareSnapshotMgmt3* This,VSS_ID idSnapshot,BYTE priority) {
    return This->lpVtbl->SetSnapshotPriority(This,idSnapshot,priority);
}
/*** IVssDifferentialSoftwareSnapshotMgmt3 methods ***/
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt3_SetVolumeProtectLevel(IVssDifferentialSoftwareSnapshotMgmt3* This,VSS_PWSZ pwszVolumeName,VSS_PROTECTION_LEVEL protectionLevel) {
    return This->lpVtbl->SetVolumeProtectLevel(This,pwszVolumeName,protectionLevel);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt3_GetVolumeProtectLevel(IVssDifferentialSoftwareSnapshotMgmt3* This,VSS_PWSZ pwszVolumeName,VSS_VOLUME_PROTECTION_INFO *protectionLevel) {
    return This->lpVtbl->GetVolumeProtectLevel(This,pwszVolumeName,protectionLevel);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt3_ClearVolumeProtectFault(IVssDifferentialSoftwareSnapshotMgmt3* This,VSS_PWSZ pwszVolumeName) {
    return This->lpVtbl->ClearVolumeProtectFault(This,pwszVolumeName);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt3_DeleteUnusedDiffAreas(IVssDifferentialSoftwareSnapshotMgmt3* This,VSS_PWSZ pwszDiffAreaVolumeName) {
    return This->lpVtbl->DeleteUnusedDiffAreas(This,pwszDiffAreaVolumeName);
}
static inline HRESULT IVssDifferentialSoftwareSnapshotMgmt3_QuerySnapshotDeltaBitmap(IVssDifferentialSoftwareSnapshotMgmt3* This,VSS_ID idSnapshotOlder,VSS_ID idSnapshotYounger,ULONG *pcBlockSizePerBit,ULONG *pcBitmapLength,BYTE **ppbBitmap) {
    return This->lpVtbl->QuerySnapshotDeltaBitmap(This,idSnapshotOlder,idSnapshotYounger,pcBlockSizePerBit,pcBitmapLength,ppbBitmap);
}
#endif
#endif

#endif


#endif  /* __IVssDifferentialSoftwareSnapshotMgmt3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssEnumMgmtObject interface
 */
#ifndef __IVssEnumMgmtObject_INTERFACE_DEFINED__
#define __IVssEnumMgmtObject_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssEnumMgmtObject, 0x01954e6b, 0x9254, 0x4e6e, 0x80,0x8c, 0xc9,0xe0,0x5d,0x00,0x76,0x96);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("01954e6b-9254-4e6e-808c-c9e05d007696")
IVssEnumMgmtObject : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        VSS_MGMT_OBJECT_PROP *rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IVssEnumMgmtObject **ppenum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssEnumMgmtObject, 0x01954e6b, 0x9254, 0x4e6e, 0x80,0x8c, 0xc9,0xe0,0x5d,0x00,0x76,0x96)
#endif
#else
typedef struct IVssEnumMgmtObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssEnumMgmtObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssEnumMgmtObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssEnumMgmtObject *This);

    /*** IVssEnumMgmtObject methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IVssEnumMgmtObject *This,
        ULONG celt,
        VSS_MGMT_OBJECT_PROP *rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IVssEnumMgmtObject *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IVssEnumMgmtObject *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IVssEnumMgmtObject *This,
        IVssEnumMgmtObject **ppenum);

    END_INTERFACE
} IVssEnumMgmtObjectVtbl;

interface IVssEnumMgmtObject {
    CONST_VTBL IVssEnumMgmtObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssEnumMgmtObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssEnumMgmtObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssEnumMgmtObject_Release(This) (This)->lpVtbl->Release(This)
/*** IVssEnumMgmtObject methods ***/
#define IVssEnumMgmtObject_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IVssEnumMgmtObject_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IVssEnumMgmtObject_Reset(This) (This)->lpVtbl->Reset(This)
#define IVssEnumMgmtObject_Clone(This,ppenum) (This)->lpVtbl->Clone(This,ppenum)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssEnumMgmtObject_QueryInterface(IVssEnumMgmtObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssEnumMgmtObject_AddRef(IVssEnumMgmtObject* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssEnumMgmtObject_Release(IVssEnumMgmtObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssEnumMgmtObject methods ***/
static inline HRESULT IVssEnumMgmtObject_Next(IVssEnumMgmtObject* This,ULONG celt,VSS_MGMT_OBJECT_PROP *rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static inline HRESULT IVssEnumMgmtObject_Skip(IVssEnumMgmtObject* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IVssEnumMgmtObject_Reset(IVssEnumMgmtObject* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IVssEnumMgmtObject_Clone(IVssEnumMgmtObject* This,IVssEnumMgmtObject **ppenum) {
    return This->lpVtbl->Clone(This,ppenum);
}
#endif
#endif

#endif


#endif  /* __IVssEnumMgmtObject_INTERFACE_DEFINED__ */

#ifndef __VSMGMT_LIBRARY_DEFINED__
#define __VSMGMT_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_VSMGMT, 0x84015c41, 0x291d, 0x49e6, 0xbf,0x7f, 0xdd,0x40,0xae,0x93,0x63,0x2b);

/*****************************************************************************
 * VssSnapshotMgmt coclass
 */

DEFINE_GUID(CLSID_VssSnapshotMgmt, 0x0b5a2c52, 0x3eb9, 0x470a, 0x96,0xe2, 0x6c,0x6d,0x45,0x70,0xe4,0x0f);

#ifdef __cplusplus
class DECLSPEC_UUID("0b5a2c52-3eb9-470a-96e2-6c6d4570e40f") VssSnapshotMgmt;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(VssSnapshotMgmt, 0x0b5a2c52, 0x3eb9, 0x470a, 0x96,0xe2, 0x6c,0x6d,0x45,0x70,0xe4,0x0f)
#endif
#endif

#endif /* __VSMGMT_LIBRARY_DEFINED__ */
#endif /* WINAPI_PARTITION_DESKTOP */
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __vsmgmt_h__ */
