/*** Autogenerated by WIDL 10.8 from include/mfobjects.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __mfobjects_h__
#define __mfobjects_h__

/* Forward declarations */

#ifndef __IMFAttributes_FWD_DEFINED__
#define __IMFAttributes_FWD_DEFINED__
typedef interface IMFAttributes IMFAttributes;
#ifdef __cplusplus
interface IMFAttributes;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaBuffer_FWD_DEFINED__
#define __IMFMediaBuffer_FWD_DEFINED__
typedef interface IMFMediaBuffer IMFMediaBuffer;
#ifdef __cplusplus
interface IMFMediaBuffer;
#endif /* __cplusplus */
#endif

#ifndef __IMFSample_FWD_DEFINED__
#define __IMFSample_FWD_DEFINED__
typedef interface IMFSample IMFSample;
#ifdef __cplusplus
interface IMFSample;
#endif /* __cplusplus */
#endif

#ifndef __IMF2DBuffer_FWD_DEFINED__
#define __IMF2DBuffer_FWD_DEFINED__
typedef interface IMF2DBuffer IMF2DBuffer;
#ifdef __cplusplus
interface IMF2DBuffer;
#endif /* __cplusplus */
#endif

#ifndef __IMF2DBuffer2_FWD_DEFINED__
#define __IMF2DBuffer2_FWD_DEFINED__
typedef interface IMF2DBuffer2 IMF2DBuffer2;
#ifdef __cplusplus
interface IMF2DBuffer2;
#endif /* __cplusplus */
#endif

#ifndef __IMFDXGIBuffer_FWD_DEFINED__
#define __IMFDXGIBuffer_FWD_DEFINED__
typedef interface IMFDXGIBuffer IMFDXGIBuffer;
#ifdef __cplusplus
interface IMFDXGIBuffer;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaType_FWD_DEFINED__
#define __IMFMediaType_FWD_DEFINED__
typedef interface IMFMediaType IMFMediaType;
#ifdef __cplusplus
interface IMFMediaType;
#endif /* __cplusplus */
#endif

#ifndef __IMFAudioMediaType_FWD_DEFINED__
#define __IMFAudioMediaType_FWD_DEFINED__
typedef interface IMFAudioMediaType IMFAudioMediaType;
#ifdef __cplusplus
interface IMFAudioMediaType;
#endif /* __cplusplus */
#endif

#ifndef __IMFVideoMediaType_FWD_DEFINED__
#define __IMFVideoMediaType_FWD_DEFINED__
typedef interface IMFVideoMediaType IMFVideoMediaType;
#ifdef __cplusplus
interface IMFVideoMediaType;
#endif /* __cplusplus */
#endif

#ifndef __IMFAsyncResult_FWD_DEFINED__
#define __IMFAsyncResult_FWD_DEFINED__
typedef interface IMFAsyncResult IMFAsyncResult;
#ifdef __cplusplus
interface IMFAsyncResult;
#endif /* __cplusplus */
#endif

#ifndef __IMFAsyncCallback_FWD_DEFINED__
#define __IMFAsyncCallback_FWD_DEFINED__
typedef interface IMFAsyncCallback IMFAsyncCallback;
#ifdef __cplusplus
interface IMFAsyncCallback;
#endif /* __cplusplus */
#endif

#ifndef __IMFAsyncCallbackLogging_FWD_DEFINED__
#define __IMFAsyncCallbackLogging_FWD_DEFINED__
typedef interface IMFAsyncCallbackLogging IMFAsyncCallbackLogging;
#ifdef __cplusplus
interface IMFAsyncCallbackLogging;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaEvent_FWD_DEFINED__
#define __IMFMediaEvent_FWD_DEFINED__
typedef interface IMFMediaEvent IMFMediaEvent;
#ifdef __cplusplus
interface IMFMediaEvent;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaEventGenerator_FWD_DEFINED__
#define __IMFMediaEventGenerator_FWD_DEFINED__
typedef interface IMFMediaEventGenerator IMFMediaEventGenerator;
#ifdef __cplusplus
interface IMFMediaEventGenerator;
#endif /* __cplusplus */
#endif

#ifndef __IMFRemoteAsyncCallback_FWD_DEFINED__
#define __IMFRemoteAsyncCallback_FWD_DEFINED__
typedef interface IMFRemoteAsyncCallback IMFRemoteAsyncCallback;
#ifdef __cplusplus
interface IMFRemoteAsyncCallback;
#endif /* __cplusplus */
#endif

#ifndef __IMFByteStream_FWD_DEFINED__
#define __IMFByteStream_FWD_DEFINED__
typedef interface IMFByteStream IMFByteStream;
#ifdef __cplusplus
interface IMFByteStream;
#endif /* __cplusplus */
#endif

#ifndef __IMFByteStreamProxyClassFactory_FWD_DEFINED__
#define __IMFByteStreamProxyClassFactory_FWD_DEFINED__
typedef interface IMFByteStreamProxyClassFactory IMFByteStreamProxyClassFactory;
#ifdef __cplusplus
interface IMFByteStreamProxyClassFactory;
#endif /* __cplusplus */
#endif

#ifndef __IMFSampleOutputStream_FWD_DEFINED__
#define __IMFSampleOutputStream_FWD_DEFINED__
typedef interface IMFSampleOutputStream IMFSampleOutputStream;
#ifdef __cplusplus
interface IMFSampleOutputStream;
#endif /* __cplusplus */
#endif

#ifndef __IMFCollection_FWD_DEFINED__
#define __IMFCollection_FWD_DEFINED__
typedef interface IMFCollection IMFCollection;
#ifdef __cplusplus
interface IMFCollection;
#endif /* __cplusplus */
#endif

#ifndef __IMFMediaEventQueue_FWD_DEFINED__
#define __IMFMediaEventQueue_FWD_DEFINED__
typedef interface IMFMediaEventQueue IMFMediaEventQueue;
#ifdef __cplusplus
interface IMFMediaEventQueue;
#endif /* __cplusplus */
#endif

#ifndef __IMFActivate_FWD_DEFINED__
#define __IMFActivate_FWD_DEFINED__
typedef interface IMFActivate IMFActivate;
#ifdef __cplusplus
interface IMFActivate;
#endif /* __cplusplus */
#endif

#ifndef __IMFPluginControl_FWD_DEFINED__
#define __IMFPluginControl_FWD_DEFINED__
typedef interface IMFPluginControl IMFPluginControl;
#ifdef __cplusplus
interface IMFPluginControl;
#endif /* __cplusplus */
#endif

#ifndef __IMFPluginControl2_FWD_DEFINED__
#define __IMFPluginControl2_FWD_DEFINED__
typedef interface IMFPluginControl2 IMFPluginControl2;
#ifdef __cplusplus
interface IMFPluginControl2;
#endif /* __cplusplus */
#endif

#ifndef __IMFDXGIDeviceManager_FWD_DEFINED__
#define __IMFDXGIDeviceManager_FWD_DEFINED__
typedef interface IMFDXGIDeviceManager IMFDXGIDeviceManager;
#ifdef __cplusplus
interface IMFDXGIDeviceManager;
#endif /* __cplusplus */
#endif

#ifndef __IMFMuxStreamAttributesManager_FWD_DEFINED__
#define __IMFMuxStreamAttributesManager_FWD_DEFINED__
typedef interface IMFMuxStreamAttributesManager IMFMuxStreamAttributesManager;
#ifdef __cplusplus
interface IMFMuxStreamAttributesManager;
#endif /* __cplusplus */
#endif

#ifndef __IMFMuxStreamMediaTypeManager_FWD_DEFINED__
#define __IMFMuxStreamMediaTypeManager_FWD_DEFINED__
typedef interface IMFMuxStreamMediaTypeManager IMFMuxStreamMediaTypeManager;
#ifdef __cplusplus
interface IMFMuxStreamMediaTypeManager;
#endif /* __cplusplus */
#endif

#ifndef __IMFMuxStreamSampleManager_FWD_DEFINED__
#define __IMFMuxStreamSampleManager_FWD_DEFINED__
typedef interface IMFMuxStreamSampleManager IMFMuxStreamSampleManager;
#ifdef __cplusplus
interface IMFMuxStreamSampleManager;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <unknwn.h>
#include <propsys.h>
#include <mediaobj.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#include <winapifamily.h>


typedef ULONGLONG QWORD;

#include <mmreg.h>
#if 0
typedef struct tWAVEFORMATEX {
    WORD wFormatTag;
    WORD nChannels;
    DWORD nSamplesPerSec;
    DWORD nAvgBytesPerSec;
    WORD nBlockAlign;
    WORD wBitsPerSample;
    WORD cbSize;
    BYTE pExtraBytes[1];
} WAVEFORMATEX;
typedef struct tWAVEFORMATEX *PWAVEFORMATEX;
typedef struct tWAVEFORMATEX *NPWAVEFORMATEX;
typedef struct tWAVEFORMATEX *LPWAVEFORMATEX;
typedef struct __WIDL_mfobjects_generated_name_00000020 {
    WORD wFormatTag;
    WORD nChannels;
    DWORD nSamplesPerSec;
    DWORD nAvgBytesPerSec;
    WORD nBlockAlign;
    WORD wBitsPerSample;
    WORD cbSize;
    WORD wValidBitsPerSample;
    DWORD dwChannelMask;
    GUID SubFormat;
} WAVEFORMATEXTENSIBLE;
typedef struct __WIDL_mfobjects_generated_name_00000020 *PWAVEFORMATEXTENSIBLE;
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
typedef enum _MF_ATTRIBUTE_TYPE {
    MF_ATTRIBUTE_UINT32 = VT_UI4,
    MF_ATTRIBUTE_UINT64 = VT_UI8,
    MF_ATTRIBUTE_DOUBLE = VT_R8,
    MF_ATTRIBUTE_GUID = VT_CLSID,
    MF_ATTRIBUTE_STRING = VT_LPWSTR,
    MF_ATTRIBUTE_BLOB = VT_VECTOR | VT_UI1,
    MF_ATTRIBUTE_IUNKNOWN = VT_UNKNOWN
} MF_ATTRIBUTE_TYPE;

typedef enum _MF_ATTRIBUTES_MATCH_TYPE {
    MF_ATTRIBUTES_MATCH_OUR_ITEMS = 0,
    MF_ATTRIBUTES_MATCH_THEIR_ITEMS = 1,
    MF_ATTRIBUTES_MATCH_ALL_ITEMS = 2,
    MF_ATTRIBUTES_MATCH_INTERSECTION = 3,
    MF_ATTRIBUTES_MATCH_SMALLER = 4
} MF_ATTRIBUTES_MATCH_TYPE;

/*****************************************************************************
 * IMFAttributes interface
 */
#ifndef __IMFAttributes_INTERFACE_DEFINED__
#define __IMFAttributes_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFAttributes, 0x2cd2d921, 0xc447, 0x44a7, 0xa1,0x3c, 0x4a,0xda,0xbf,0xc2,0x47,0xe3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2cd2d921-c447-44a7-a13c-4adabfc247e3")
IMFAttributes : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetItem(
        REFGUID guidKey,
        PROPVARIANT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetItemType(
        REFGUID guidKey,
        MF_ATTRIBUTE_TYPE *pType) = 0;

    virtual HRESULT STDMETHODCALLTYPE CompareItem(
        REFGUID guidKey,
        REFPROPVARIANT Value,
        WINBOOL *pbResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE Compare(
        IMFAttributes *pTheirs,
        MF_ATTRIBUTES_MATCH_TYPE MatchType,
        WINBOOL *pbResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUINT32(
        REFGUID guidKey,
        UINT32 *punValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUINT64(
        REFGUID guidKey,
        UINT64 *punValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDouble(
        REFGUID guidKey,
        double *pfValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGUID(
        REFGUID guidKey,
        GUID *pguidValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStringLength(
        REFGUID guidKey,
        UINT32 *pcchLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetString(
        REFGUID guidKey,
        LPWSTR pwszValue,
        UINT32 cchBufSize,
        UINT32 *pcchLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllocatedString(
        REFGUID guidKey,
        LPWSTR *ppwszValue,
        UINT32 *pcchLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBlobSize(
        REFGUID guidKey,
        UINT32 *pcbBlobSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBlob(
        REFGUID guidKey,
        UINT8 *pBuf,
        UINT32 cbBufSize,
        UINT32 *pcbBlobSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllocatedBlob(
        REFGUID guidKey,
        UINT8 **ppBuf,
        UINT32 *pcbSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUnknown(
        REFGUID guidKey,
        REFIID riid,
        LPVOID *ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetItem(
        REFGUID guidKey,
        REFPROPVARIANT Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteItem(
        REFGUID guidKey) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteAllItems(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUINT32(
        REFGUID guidKey,
        UINT32 unValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUINT64(
        REFGUID guidKey,
        UINT64 unValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDouble(
        REFGUID guidKey,
        double fValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGUID(
        REFGUID guidKey,
        REFGUID guidValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetString(
        REFGUID guidKey,
        LPCWSTR wszValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBlob(
        REFGUID guidKey,
        const UINT8 *pBuf,
        UINT32 cbBufSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUnknown(
        REFGUID guidKey,
        IUnknown *pUnknown) = 0;

    virtual HRESULT STDMETHODCALLTYPE LockStore(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnlockStore(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *pcItems) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetItemByIndex(
        UINT32 unIndex,
        GUID *pguidKey,
        PROPVARIANT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyAllItems(
        IMFAttributes *pDest) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFAttributes, 0x2cd2d921, 0xc447, 0x44a7, 0xa1,0x3c, 0x4a,0xda,0xbf,0xc2,0x47,0xe3)
#endif
#else
typedef struct IMFAttributesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFAttributes *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFAttributes *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFAttributes *This);

    /*** IMFAttributes methods ***/
    HRESULT (STDMETHODCALLTYPE *GetItem)(
        IMFAttributes *This,
        REFGUID guidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetItemType)(
        IMFAttributes *This,
        REFGUID guidKey,
        MF_ATTRIBUTE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *CompareItem)(
        IMFAttributes *This,
        REFGUID guidKey,
        REFPROPVARIANT Value,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        IMFAttributes *This,
        IMFAttributes *pTheirs,
        MF_ATTRIBUTES_MATCH_TYPE MatchType,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *GetUINT32)(
        IMFAttributes *This,
        REFGUID guidKey,
        UINT32 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetUINT64)(
        IMFAttributes *This,
        REFGUID guidKey,
        UINT64 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetDouble)(
        IMFAttributes *This,
        REFGUID guidKey,
        double *pfValue);

    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IMFAttributes *This,
        REFGUID guidKey,
        GUID *pguidValue);

    HRESULT (STDMETHODCALLTYPE *GetStringLength)(
        IMFAttributes *This,
        REFGUID guidKey,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetString)(
        IMFAttributes *This,
        REFGUID guidKey,
        LPWSTR pwszValue,
        UINT32 cchBufSize,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedString)(
        IMFAttributes *This,
        REFGUID guidKey,
        LPWSTR *ppwszValue,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetBlobSize)(
        IMFAttributes *This,
        REFGUID guidKey,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetBlob)(
        IMFAttributes *This,
        REFGUID guidKey,
        UINT8 *pBuf,
        UINT32 cbBufSize,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedBlob)(
        IMFAttributes *This,
        REFGUID guidKey,
        UINT8 **ppBuf,
        UINT32 *pcbSize);

    HRESULT (STDMETHODCALLTYPE *GetUnknown)(
        IMFAttributes *This,
        REFGUID guidKey,
        REFIID riid,
        LPVOID *ppv);

    HRESULT (STDMETHODCALLTYPE *SetItem)(
        IMFAttributes *This,
        REFGUID guidKey,
        REFPROPVARIANT Value);

    HRESULT (STDMETHODCALLTYPE *DeleteItem)(
        IMFAttributes *This,
        REFGUID guidKey);

    HRESULT (STDMETHODCALLTYPE *DeleteAllItems)(
        IMFAttributes *This);

    HRESULT (STDMETHODCALLTYPE *SetUINT32)(
        IMFAttributes *This,
        REFGUID guidKey,
        UINT32 unValue);

    HRESULT (STDMETHODCALLTYPE *SetUINT64)(
        IMFAttributes *This,
        REFGUID guidKey,
        UINT64 unValue);

    HRESULT (STDMETHODCALLTYPE *SetDouble)(
        IMFAttributes *This,
        REFGUID guidKey,
        double fValue);

    HRESULT (STDMETHODCALLTYPE *SetGUID)(
        IMFAttributes *This,
        REFGUID guidKey,
        REFGUID guidValue);

    HRESULT (STDMETHODCALLTYPE *SetString)(
        IMFAttributes *This,
        REFGUID guidKey,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SetBlob)(
        IMFAttributes *This,
        REFGUID guidKey,
        const UINT8 *pBuf,
        UINT32 cbBufSize);

    HRESULT (STDMETHODCALLTYPE *SetUnknown)(
        IMFAttributes *This,
        REFGUID guidKey,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *LockStore)(
        IMFAttributes *This);

    HRESULT (STDMETHODCALLTYPE *UnlockStore)(
        IMFAttributes *This);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IMFAttributes *This,
        UINT32 *pcItems);

    HRESULT (STDMETHODCALLTYPE *GetItemByIndex)(
        IMFAttributes *This,
        UINT32 unIndex,
        GUID *pguidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *CopyAllItems)(
        IMFAttributes *This,
        IMFAttributes *pDest);

    END_INTERFACE
} IMFAttributesVtbl;

interface IMFAttributes {
    CONST_VTBL IMFAttributesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFAttributes_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFAttributes_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFAttributes_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAttributes methods ***/
#define IMFAttributes_GetItem(This,guidKey,pValue) (This)->lpVtbl->GetItem(This,guidKey,pValue)
#define IMFAttributes_GetItemType(This,guidKey,pType) (This)->lpVtbl->GetItemType(This,guidKey,pType)
#define IMFAttributes_CompareItem(This,guidKey,Value,pbResult) (This)->lpVtbl->CompareItem(This,guidKey,Value,pbResult)
#define IMFAttributes_Compare(This,pTheirs,MatchType,pbResult) (This)->lpVtbl->Compare(This,pTheirs,MatchType,pbResult)
#define IMFAttributes_GetUINT32(This,guidKey,punValue) (This)->lpVtbl->GetUINT32(This,guidKey,punValue)
#define IMFAttributes_GetUINT64(This,guidKey,punValue) (This)->lpVtbl->GetUINT64(This,guidKey,punValue)
#define IMFAttributes_GetDouble(This,guidKey,pfValue) (This)->lpVtbl->GetDouble(This,guidKey,pfValue)
#define IMFAttributes_GetGUID(This,guidKey,pguidValue) (This)->lpVtbl->GetGUID(This,guidKey,pguidValue)
#define IMFAttributes_GetStringLength(This,guidKey,pcchLength) (This)->lpVtbl->GetStringLength(This,guidKey,pcchLength)
#define IMFAttributes_GetString(This,guidKey,pwszValue,cchBufSize,pcchLength) (This)->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength)
#define IMFAttributes_GetAllocatedString(This,guidKey,ppwszValue,pcchLength) (This)->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength)
#define IMFAttributes_GetBlobSize(This,guidKey,pcbBlobSize) (This)->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize)
#define IMFAttributes_GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize) (This)->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize)
#define IMFAttributes_GetAllocatedBlob(This,guidKey,ppBuf,pcbSize) (This)->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize)
#define IMFAttributes_GetUnknown(This,guidKey,riid,ppv) (This)->lpVtbl->GetUnknown(This,guidKey,riid,ppv)
#define IMFAttributes_SetItem(This,guidKey,Value) (This)->lpVtbl->SetItem(This,guidKey,Value)
#define IMFAttributes_DeleteItem(This,guidKey) (This)->lpVtbl->DeleteItem(This,guidKey)
#define IMFAttributes_DeleteAllItems(This) (This)->lpVtbl->DeleteAllItems(This)
#define IMFAttributes_SetUINT32(This,guidKey,unValue) (This)->lpVtbl->SetUINT32(This,guidKey,unValue)
#define IMFAttributes_SetUINT64(This,guidKey,unValue) (This)->lpVtbl->SetUINT64(This,guidKey,unValue)
#define IMFAttributes_SetDouble(This,guidKey,fValue) (This)->lpVtbl->SetDouble(This,guidKey,fValue)
#define IMFAttributes_SetGUID(This,guidKey,guidValue) (This)->lpVtbl->SetGUID(This,guidKey,guidValue)
#define IMFAttributes_SetString(This,guidKey,wszValue) (This)->lpVtbl->SetString(This,guidKey,wszValue)
#define IMFAttributes_SetBlob(This,guidKey,pBuf,cbBufSize) (This)->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize)
#define IMFAttributes_SetUnknown(This,guidKey,pUnknown) (This)->lpVtbl->SetUnknown(This,guidKey,pUnknown)
#define IMFAttributes_LockStore(This) (This)->lpVtbl->LockStore(This)
#define IMFAttributes_UnlockStore(This) (This)->lpVtbl->UnlockStore(This)
#define IMFAttributes_GetCount(This,pcItems) (This)->lpVtbl->GetCount(This,pcItems)
#define IMFAttributes_GetItemByIndex(This,unIndex,pguidKey,pValue) (This)->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue)
#define IMFAttributes_CopyAllItems(This,pDest) (This)->lpVtbl->CopyAllItems(This,pDest)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFAttributes_QueryInterface(IMFAttributes* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFAttributes_AddRef(IMFAttributes* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFAttributes_Release(IMFAttributes* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAttributes methods ***/
static inline HRESULT IMFAttributes_GetItem(IMFAttributes* This,REFGUID guidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItem(This,guidKey,pValue);
}
static inline HRESULT IMFAttributes_GetItemType(IMFAttributes* This,REFGUID guidKey,MF_ATTRIBUTE_TYPE *pType) {
    return This->lpVtbl->GetItemType(This,guidKey,pType);
}
static inline HRESULT IMFAttributes_CompareItem(IMFAttributes* This,REFGUID guidKey,REFPROPVARIANT Value,WINBOOL *pbResult) {
    return This->lpVtbl->CompareItem(This,guidKey,Value,pbResult);
}
static inline HRESULT IMFAttributes_Compare(IMFAttributes* This,IMFAttributes *pTheirs,MF_ATTRIBUTES_MATCH_TYPE MatchType,WINBOOL *pbResult) {
    return This->lpVtbl->Compare(This,pTheirs,MatchType,pbResult);
}
static inline HRESULT IMFAttributes_GetUINT32(IMFAttributes* This,REFGUID guidKey,UINT32 *punValue) {
    return This->lpVtbl->GetUINT32(This,guidKey,punValue);
}
static inline HRESULT IMFAttributes_GetUINT64(IMFAttributes* This,REFGUID guidKey,UINT64 *punValue) {
    return This->lpVtbl->GetUINT64(This,guidKey,punValue);
}
static inline HRESULT IMFAttributes_GetDouble(IMFAttributes* This,REFGUID guidKey,double *pfValue) {
    return This->lpVtbl->GetDouble(This,guidKey,pfValue);
}
static inline HRESULT IMFAttributes_GetGUID(IMFAttributes* This,REFGUID guidKey,GUID *pguidValue) {
    return This->lpVtbl->GetGUID(This,guidKey,pguidValue);
}
static inline HRESULT IMFAttributes_GetStringLength(IMFAttributes* This,REFGUID guidKey,UINT32 *pcchLength) {
    return This->lpVtbl->GetStringLength(This,guidKey,pcchLength);
}
static inline HRESULT IMFAttributes_GetString(IMFAttributes* This,REFGUID guidKey,LPWSTR pwszValue,UINT32 cchBufSize,UINT32 *pcchLength) {
    return This->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength);
}
static inline HRESULT IMFAttributes_GetAllocatedString(IMFAttributes* This,REFGUID guidKey,LPWSTR *ppwszValue,UINT32 *pcchLength) {
    return This->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength);
}
static inline HRESULT IMFAttributes_GetBlobSize(IMFAttributes* This,REFGUID guidKey,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize);
}
static inline HRESULT IMFAttributes_GetBlob(IMFAttributes* This,REFGUID guidKey,UINT8 *pBuf,UINT32 cbBufSize,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize);
}
static inline HRESULT IMFAttributes_GetAllocatedBlob(IMFAttributes* This,REFGUID guidKey,UINT8 **ppBuf,UINT32 *pcbSize) {
    return This->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize);
}
static inline HRESULT IMFAttributes_GetUnknown(IMFAttributes* This,REFGUID guidKey,REFIID riid,LPVOID *ppv) {
    return This->lpVtbl->GetUnknown(This,guidKey,riid,ppv);
}
static inline HRESULT IMFAttributes_SetItem(IMFAttributes* This,REFGUID guidKey,REFPROPVARIANT Value) {
    return This->lpVtbl->SetItem(This,guidKey,Value);
}
static inline HRESULT IMFAttributes_DeleteItem(IMFAttributes* This,REFGUID guidKey) {
    return This->lpVtbl->DeleteItem(This,guidKey);
}
static inline HRESULT IMFAttributes_DeleteAllItems(IMFAttributes* This) {
    return This->lpVtbl->DeleteAllItems(This);
}
static inline HRESULT IMFAttributes_SetUINT32(IMFAttributes* This,REFGUID guidKey,UINT32 unValue) {
    return This->lpVtbl->SetUINT32(This,guidKey,unValue);
}
static inline HRESULT IMFAttributes_SetUINT64(IMFAttributes* This,REFGUID guidKey,UINT64 unValue) {
    return This->lpVtbl->SetUINT64(This,guidKey,unValue);
}
static inline HRESULT IMFAttributes_SetDouble(IMFAttributes* This,REFGUID guidKey,double fValue) {
    return This->lpVtbl->SetDouble(This,guidKey,fValue);
}
static inline HRESULT IMFAttributes_SetGUID(IMFAttributes* This,REFGUID guidKey,REFGUID guidValue) {
    return This->lpVtbl->SetGUID(This,guidKey,guidValue);
}
static inline HRESULT IMFAttributes_SetString(IMFAttributes* This,REFGUID guidKey,LPCWSTR wszValue) {
    return This->lpVtbl->SetString(This,guidKey,wszValue);
}
static inline HRESULT IMFAttributes_SetBlob(IMFAttributes* This,REFGUID guidKey,const UINT8 *pBuf,UINT32 cbBufSize) {
    return This->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize);
}
static inline HRESULT IMFAttributes_SetUnknown(IMFAttributes* This,REFGUID guidKey,IUnknown *pUnknown) {
    return This->lpVtbl->SetUnknown(This,guidKey,pUnknown);
}
static inline HRESULT IMFAttributes_LockStore(IMFAttributes* This) {
    return This->lpVtbl->LockStore(This);
}
static inline HRESULT IMFAttributes_UnlockStore(IMFAttributes* This) {
    return This->lpVtbl->UnlockStore(This);
}
static inline HRESULT IMFAttributes_GetCount(IMFAttributes* This,UINT32 *pcItems) {
    return This->lpVtbl->GetCount(This,pcItems);
}
static inline HRESULT IMFAttributes_GetItemByIndex(IMFAttributes* This,UINT32 unIndex,GUID *pguidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue);
}
static inline HRESULT IMFAttributes_CopyAllItems(IMFAttributes* This,IMFAttributes *pDest) {
    return This->lpVtbl->CopyAllItems(This,pDest);
}
#endif
#endif

#endif


#endif  /* __IMFAttributes_INTERFACE_DEFINED__ */


enum MF_ATTRIBUTE_SERIALIZE_OPTIONS {
    MF_ATTRIBUTE_SERIALIZE_UNKNOWN_BYREF = 0x1
};

STDAPI MFSerializeAttributesToStream(IMFAttributes *pAttr, DWORD dwOptions, IStream *pStm);
STDAPI MFDeserializeAttributesFromStream(IMFAttributes *pAttr, DWORD dwOptions, IStream *pStm);

/*****************************************************************************
 * IMFMediaBuffer interface
 */
#ifndef __IMFMediaBuffer_INTERFACE_DEFINED__
#define __IMFMediaBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaBuffer, 0x045fa593, 0x8799, 0x42b8, 0xbc,0x8d, 0x89,0x68,0xc6,0x45,0x35,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("045fa593-8799-42b8-bc8d-8968c6453507")
IMFMediaBuffer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Lock(
        BYTE **ppbBuffer,
        DWORD *pcbMaxLength,
        DWORD *pcbCurrentLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unlock(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentLength(
        DWORD *pcbCurrentLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCurrentLength(
        DWORD cbCurrentLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMaxLength(
        DWORD *pcbMaxLength) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaBuffer, 0x045fa593, 0x8799, 0x42b8, 0xbc,0x8d, 0x89,0x68,0xc6,0x45,0x35,0x07)
#endif
#else
typedef struct IMFMediaBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaBuffer *This);

    /*** IMFMediaBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *Lock)(
        IMFMediaBuffer *This,
        BYTE **ppbBuffer,
        DWORD *pcbMaxLength,
        DWORD *pcbCurrentLength);

    HRESULT (STDMETHODCALLTYPE *Unlock)(
        IMFMediaBuffer *This);

    HRESULT (STDMETHODCALLTYPE *GetCurrentLength)(
        IMFMediaBuffer *This,
        DWORD *pcbCurrentLength);

    HRESULT (STDMETHODCALLTYPE *SetCurrentLength)(
        IMFMediaBuffer *This,
        DWORD cbCurrentLength);

    HRESULT (STDMETHODCALLTYPE *GetMaxLength)(
        IMFMediaBuffer *This,
        DWORD *pcbMaxLength);

    END_INTERFACE
} IMFMediaBufferVtbl;

interface IMFMediaBuffer {
    CONST_VTBL IMFMediaBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaBuffer methods ***/
#define IMFMediaBuffer_Lock(This,ppbBuffer,pcbMaxLength,pcbCurrentLength) (This)->lpVtbl->Lock(This,ppbBuffer,pcbMaxLength,pcbCurrentLength)
#define IMFMediaBuffer_Unlock(This) (This)->lpVtbl->Unlock(This)
#define IMFMediaBuffer_GetCurrentLength(This,pcbCurrentLength) (This)->lpVtbl->GetCurrentLength(This,pcbCurrentLength)
#define IMFMediaBuffer_SetCurrentLength(This,cbCurrentLength) (This)->lpVtbl->SetCurrentLength(This,cbCurrentLength)
#define IMFMediaBuffer_GetMaxLength(This,pcbMaxLength) (This)->lpVtbl->GetMaxLength(This,pcbMaxLength)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaBuffer_QueryInterface(IMFMediaBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaBuffer_AddRef(IMFMediaBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaBuffer_Release(IMFMediaBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaBuffer methods ***/
static inline HRESULT IMFMediaBuffer_Lock(IMFMediaBuffer* This,BYTE **ppbBuffer,DWORD *pcbMaxLength,DWORD *pcbCurrentLength) {
    return This->lpVtbl->Lock(This,ppbBuffer,pcbMaxLength,pcbCurrentLength);
}
static inline HRESULT IMFMediaBuffer_Unlock(IMFMediaBuffer* This) {
    return This->lpVtbl->Unlock(This);
}
static inline HRESULT IMFMediaBuffer_GetCurrentLength(IMFMediaBuffer* This,DWORD *pcbCurrentLength) {
    return This->lpVtbl->GetCurrentLength(This,pcbCurrentLength);
}
static inline HRESULT IMFMediaBuffer_SetCurrentLength(IMFMediaBuffer* This,DWORD cbCurrentLength) {
    return This->lpVtbl->SetCurrentLength(This,cbCurrentLength);
}
static inline HRESULT IMFMediaBuffer_GetMaxLength(IMFMediaBuffer* This,DWORD *pcbMaxLength) {
    return This->lpVtbl->GetMaxLength(This,pcbMaxLength);
}
#endif
#endif

#endif


#endif  /* __IMFMediaBuffer_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFSample interface
 */
#ifndef __IMFSample_INTERFACE_DEFINED__
#define __IMFSample_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFSample, 0xc40a00f2, 0xb93a, 0x4d80, 0xae,0x8c, 0x5a,0x1c,0x63,0x4f,0x58,0xe4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c40a00f2-b93a-4d80-ae8c-5a1c634f58e4")
IMFSample : public IMFAttributes
{
    virtual HRESULT STDMETHODCALLTYPE GetSampleFlags(
        DWORD *pdwSampleFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSampleFlags(
        DWORD dwSampleFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSampleTime(
        LONGLONG *phnsSampleTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSampleTime(
        LONGLONG hnsSampleTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSampleDuration(
        LONGLONG *phnsSampleDuration) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSampleDuration(
        LONGLONG hnsSampleDuration) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBufferCount(
        DWORD *pdwBufferCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBufferByIndex(
        DWORD dwIndex,
        IMFMediaBuffer **ppBuffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE ConvertToContiguousBuffer(
        IMFMediaBuffer **ppBuffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddBuffer(
        IMFMediaBuffer *pBuffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveBufferByIndex(
        DWORD dwIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAllBuffers(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTotalLength(
        DWORD *pcbTotalLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyToBuffer(
        IMFMediaBuffer *pBuffer) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFSample, 0xc40a00f2, 0xb93a, 0x4d80, 0xae,0x8c, 0x5a,0x1c,0x63,0x4f,0x58,0xe4)
#endif
#else
typedef struct IMFSampleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFSample *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFSample *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFSample *This);

    /*** IMFAttributes methods ***/
    HRESULT (STDMETHODCALLTYPE *GetItem)(
        IMFSample *This,
        REFGUID guidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetItemType)(
        IMFSample *This,
        REFGUID guidKey,
        MF_ATTRIBUTE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *CompareItem)(
        IMFSample *This,
        REFGUID guidKey,
        REFPROPVARIANT Value,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        IMFSample *This,
        IMFAttributes *pTheirs,
        MF_ATTRIBUTES_MATCH_TYPE MatchType,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *GetUINT32)(
        IMFSample *This,
        REFGUID guidKey,
        UINT32 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetUINT64)(
        IMFSample *This,
        REFGUID guidKey,
        UINT64 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetDouble)(
        IMFSample *This,
        REFGUID guidKey,
        double *pfValue);

    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IMFSample *This,
        REFGUID guidKey,
        GUID *pguidValue);

    HRESULT (STDMETHODCALLTYPE *GetStringLength)(
        IMFSample *This,
        REFGUID guidKey,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetString)(
        IMFSample *This,
        REFGUID guidKey,
        LPWSTR pwszValue,
        UINT32 cchBufSize,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedString)(
        IMFSample *This,
        REFGUID guidKey,
        LPWSTR *ppwszValue,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetBlobSize)(
        IMFSample *This,
        REFGUID guidKey,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetBlob)(
        IMFSample *This,
        REFGUID guidKey,
        UINT8 *pBuf,
        UINT32 cbBufSize,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedBlob)(
        IMFSample *This,
        REFGUID guidKey,
        UINT8 **ppBuf,
        UINT32 *pcbSize);

    HRESULT (STDMETHODCALLTYPE *GetUnknown)(
        IMFSample *This,
        REFGUID guidKey,
        REFIID riid,
        LPVOID *ppv);

    HRESULT (STDMETHODCALLTYPE *SetItem)(
        IMFSample *This,
        REFGUID guidKey,
        REFPROPVARIANT Value);

    HRESULT (STDMETHODCALLTYPE *DeleteItem)(
        IMFSample *This,
        REFGUID guidKey);

    HRESULT (STDMETHODCALLTYPE *DeleteAllItems)(
        IMFSample *This);

    HRESULT (STDMETHODCALLTYPE *SetUINT32)(
        IMFSample *This,
        REFGUID guidKey,
        UINT32 unValue);

    HRESULT (STDMETHODCALLTYPE *SetUINT64)(
        IMFSample *This,
        REFGUID guidKey,
        UINT64 unValue);

    HRESULT (STDMETHODCALLTYPE *SetDouble)(
        IMFSample *This,
        REFGUID guidKey,
        double fValue);

    HRESULT (STDMETHODCALLTYPE *SetGUID)(
        IMFSample *This,
        REFGUID guidKey,
        REFGUID guidValue);

    HRESULT (STDMETHODCALLTYPE *SetString)(
        IMFSample *This,
        REFGUID guidKey,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SetBlob)(
        IMFSample *This,
        REFGUID guidKey,
        const UINT8 *pBuf,
        UINT32 cbBufSize);

    HRESULT (STDMETHODCALLTYPE *SetUnknown)(
        IMFSample *This,
        REFGUID guidKey,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *LockStore)(
        IMFSample *This);

    HRESULT (STDMETHODCALLTYPE *UnlockStore)(
        IMFSample *This);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IMFSample *This,
        UINT32 *pcItems);

    HRESULT (STDMETHODCALLTYPE *GetItemByIndex)(
        IMFSample *This,
        UINT32 unIndex,
        GUID *pguidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *CopyAllItems)(
        IMFSample *This,
        IMFAttributes *pDest);

    /*** IMFSample methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSampleFlags)(
        IMFSample *This,
        DWORD *pdwSampleFlags);

    HRESULT (STDMETHODCALLTYPE *SetSampleFlags)(
        IMFSample *This,
        DWORD dwSampleFlags);

    HRESULT (STDMETHODCALLTYPE *GetSampleTime)(
        IMFSample *This,
        LONGLONG *phnsSampleTime);

    HRESULT (STDMETHODCALLTYPE *SetSampleTime)(
        IMFSample *This,
        LONGLONG hnsSampleTime);

    HRESULT (STDMETHODCALLTYPE *GetSampleDuration)(
        IMFSample *This,
        LONGLONG *phnsSampleDuration);

    HRESULT (STDMETHODCALLTYPE *SetSampleDuration)(
        IMFSample *This,
        LONGLONG hnsSampleDuration);

    HRESULT (STDMETHODCALLTYPE *GetBufferCount)(
        IMFSample *This,
        DWORD *pdwBufferCount);

    HRESULT (STDMETHODCALLTYPE *GetBufferByIndex)(
        IMFSample *This,
        DWORD dwIndex,
        IMFMediaBuffer **ppBuffer);

    HRESULT (STDMETHODCALLTYPE *ConvertToContiguousBuffer)(
        IMFSample *This,
        IMFMediaBuffer **ppBuffer);

    HRESULT (STDMETHODCALLTYPE *AddBuffer)(
        IMFSample *This,
        IMFMediaBuffer *pBuffer);

    HRESULT (STDMETHODCALLTYPE *RemoveBufferByIndex)(
        IMFSample *This,
        DWORD dwIndex);

    HRESULT (STDMETHODCALLTYPE *RemoveAllBuffers)(
        IMFSample *This);

    HRESULT (STDMETHODCALLTYPE *GetTotalLength)(
        IMFSample *This,
        DWORD *pcbTotalLength);

    HRESULT (STDMETHODCALLTYPE *CopyToBuffer)(
        IMFSample *This,
        IMFMediaBuffer *pBuffer);

    END_INTERFACE
} IMFSampleVtbl;

interface IMFSample {
    CONST_VTBL IMFSampleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFSample_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFSample_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFSample_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAttributes methods ***/
#define IMFSample_GetItem(This,guidKey,pValue) (This)->lpVtbl->GetItem(This,guidKey,pValue)
#define IMFSample_GetItemType(This,guidKey,pType) (This)->lpVtbl->GetItemType(This,guidKey,pType)
#define IMFSample_CompareItem(This,guidKey,Value,pbResult) (This)->lpVtbl->CompareItem(This,guidKey,Value,pbResult)
#define IMFSample_Compare(This,pTheirs,MatchType,pbResult) (This)->lpVtbl->Compare(This,pTheirs,MatchType,pbResult)
#define IMFSample_GetUINT32(This,guidKey,punValue) (This)->lpVtbl->GetUINT32(This,guidKey,punValue)
#define IMFSample_GetUINT64(This,guidKey,punValue) (This)->lpVtbl->GetUINT64(This,guidKey,punValue)
#define IMFSample_GetDouble(This,guidKey,pfValue) (This)->lpVtbl->GetDouble(This,guidKey,pfValue)
#define IMFSample_GetGUID(This,guidKey,pguidValue) (This)->lpVtbl->GetGUID(This,guidKey,pguidValue)
#define IMFSample_GetStringLength(This,guidKey,pcchLength) (This)->lpVtbl->GetStringLength(This,guidKey,pcchLength)
#define IMFSample_GetString(This,guidKey,pwszValue,cchBufSize,pcchLength) (This)->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength)
#define IMFSample_GetAllocatedString(This,guidKey,ppwszValue,pcchLength) (This)->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength)
#define IMFSample_GetBlobSize(This,guidKey,pcbBlobSize) (This)->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize)
#define IMFSample_GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize) (This)->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize)
#define IMFSample_GetAllocatedBlob(This,guidKey,ppBuf,pcbSize) (This)->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize)
#define IMFSample_GetUnknown(This,guidKey,riid,ppv) (This)->lpVtbl->GetUnknown(This,guidKey,riid,ppv)
#define IMFSample_SetItem(This,guidKey,Value) (This)->lpVtbl->SetItem(This,guidKey,Value)
#define IMFSample_DeleteItem(This,guidKey) (This)->lpVtbl->DeleteItem(This,guidKey)
#define IMFSample_DeleteAllItems(This) (This)->lpVtbl->DeleteAllItems(This)
#define IMFSample_SetUINT32(This,guidKey,unValue) (This)->lpVtbl->SetUINT32(This,guidKey,unValue)
#define IMFSample_SetUINT64(This,guidKey,unValue) (This)->lpVtbl->SetUINT64(This,guidKey,unValue)
#define IMFSample_SetDouble(This,guidKey,fValue) (This)->lpVtbl->SetDouble(This,guidKey,fValue)
#define IMFSample_SetGUID(This,guidKey,guidValue) (This)->lpVtbl->SetGUID(This,guidKey,guidValue)
#define IMFSample_SetString(This,guidKey,wszValue) (This)->lpVtbl->SetString(This,guidKey,wszValue)
#define IMFSample_SetBlob(This,guidKey,pBuf,cbBufSize) (This)->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize)
#define IMFSample_SetUnknown(This,guidKey,pUnknown) (This)->lpVtbl->SetUnknown(This,guidKey,pUnknown)
#define IMFSample_LockStore(This) (This)->lpVtbl->LockStore(This)
#define IMFSample_UnlockStore(This) (This)->lpVtbl->UnlockStore(This)
#define IMFSample_GetCount(This,pcItems) (This)->lpVtbl->GetCount(This,pcItems)
#define IMFSample_GetItemByIndex(This,unIndex,pguidKey,pValue) (This)->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue)
#define IMFSample_CopyAllItems(This,pDest) (This)->lpVtbl->CopyAllItems(This,pDest)
/*** IMFSample methods ***/
#define IMFSample_GetSampleFlags(This,pdwSampleFlags) (This)->lpVtbl->GetSampleFlags(This,pdwSampleFlags)
#define IMFSample_SetSampleFlags(This,dwSampleFlags) (This)->lpVtbl->SetSampleFlags(This,dwSampleFlags)
#define IMFSample_GetSampleTime(This,phnsSampleTime) (This)->lpVtbl->GetSampleTime(This,phnsSampleTime)
#define IMFSample_SetSampleTime(This,hnsSampleTime) (This)->lpVtbl->SetSampleTime(This,hnsSampleTime)
#define IMFSample_GetSampleDuration(This,phnsSampleDuration) (This)->lpVtbl->GetSampleDuration(This,phnsSampleDuration)
#define IMFSample_SetSampleDuration(This,hnsSampleDuration) (This)->lpVtbl->SetSampleDuration(This,hnsSampleDuration)
#define IMFSample_GetBufferCount(This,pdwBufferCount) (This)->lpVtbl->GetBufferCount(This,pdwBufferCount)
#define IMFSample_GetBufferByIndex(This,dwIndex,ppBuffer) (This)->lpVtbl->GetBufferByIndex(This,dwIndex,ppBuffer)
#define IMFSample_ConvertToContiguousBuffer(This,ppBuffer) (This)->lpVtbl->ConvertToContiguousBuffer(This,ppBuffer)
#define IMFSample_AddBuffer(This,pBuffer) (This)->lpVtbl->AddBuffer(This,pBuffer)
#define IMFSample_RemoveBufferByIndex(This,dwIndex) (This)->lpVtbl->RemoveBufferByIndex(This,dwIndex)
#define IMFSample_RemoveAllBuffers(This) (This)->lpVtbl->RemoveAllBuffers(This)
#define IMFSample_GetTotalLength(This,pcbTotalLength) (This)->lpVtbl->GetTotalLength(This,pcbTotalLength)
#define IMFSample_CopyToBuffer(This,pBuffer) (This)->lpVtbl->CopyToBuffer(This,pBuffer)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFSample_QueryInterface(IMFSample* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFSample_AddRef(IMFSample* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFSample_Release(IMFSample* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAttributes methods ***/
static inline HRESULT IMFSample_GetItem(IMFSample* This,REFGUID guidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItem(This,guidKey,pValue);
}
static inline HRESULT IMFSample_GetItemType(IMFSample* This,REFGUID guidKey,MF_ATTRIBUTE_TYPE *pType) {
    return This->lpVtbl->GetItemType(This,guidKey,pType);
}
static inline HRESULT IMFSample_CompareItem(IMFSample* This,REFGUID guidKey,REFPROPVARIANT Value,WINBOOL *pbResult) {
    return This->lpVtbl->CompareItem(This,guidKey,Value,pbResult);
}
static inline HRESULT IMFSample_Compare(IMFSample* This,IMFAttributes *pTheirs,MF_ATTRIBUTES_MATCH_TYPE MatchType,WINBOOL *pbResult) {
    return This->lpVtbl->Compare(This,pTheirs,MatchType,pbResult);
}
static inline HRESULT IMFSample_GetUINT32(IMFSample* This,REFGUID guidKey,UINT32 *punValue) {
    return This->lpVtbl->GetUINT32(This,guidKey,punValue);
}
static inline HRESULT IMFSample_GetUINT64(IMFSample* This,REFGUID guidKey,UINT64 *punValue) {
    return This->lpVtbl->GetUINT64(This,guidKey,punValue);
}
static inline HRESULT IMFSample_GetDouble(IMFSample* This,REFGUID guidKey,double *pfValue) {
    return This->lpVtbl->GetDouble(This,guidKey,pfValue);
}
static inline HRESULT IMFSample_GetGUID(IMFSample* This,REFGUID guidKey,GUID *pguidValue) {
    return This->lpVtbl->GetGUID(This,guidKey,pguidValue);
}
static inline HRESULT IMFSample_GetStringLength(IMFSample* This,REFGUID guidKey,UINT32 *pcchLength) {
    return This->lpVtbl->GetStringLength(This,guidKey,pcchLength);
}
static inline HRESULT IMFSample_GetString(IMFSample* This,REFGUID guidKey,LPWSTR pwszValue,UINT32 cchBufSize,UINT32 *pcchLength) {
    return This->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength);
}
static inline HRESULT IMFSample_GetAllocatedString(IMFSample* This,REFGUID guidKey,LPWSTR *ppwszValue,UINT32 *pcchLength) {
    return This->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength);
}
static inline HRESULT IMFSample_GetBlobSize(IMFSample* This,REFGUID guidKey,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize);
}
static inline HRESULT IMFSample_GetBlob(IMFSample* This,REFGUID guidKey,UINT8 *pBuf,UINT32 cbBufSize,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize);
}
static inline HRESULT IMFSample_GetAllocatedBlob(IMFSample* This,REFGUID guidKey,UINT8 **ppBuf,UINT32 *pcbSize) {
    return This->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize);
}
static inline HRESULT IMFSample_GetUnknown(IMFSample* This,REFGUID guidKey,REFIID riid,LPVOID *ppv) {
    return This->lpVtbl->GetUnknown(This,guidKey,riid,ppv);
}
static inline HRESULT IMFSample_SetItem(IMFSample* This,REFGUID guidKey,REFPROPVARIANT Value) {
    return This->lpVtbl->SetItem(This,guidKey,Value);
}
static inline HRESULT IMFSample_DeleteItem(IMFSample* This,REFGUID guidKey) {
    return This->lpVtbl->DeleteItem(This,guidKey);
}
static inline HRESULT IMFSample_DeleteAllItems(IMFSample* This) {
    return This->lpVtbl->DeleteAllItems(This);
}
static inline HRESULT IMFSample_SetUINT32(IMFSample* This,REFGUID guidKey,UINT32 unValue) {
    return This->lpVtbl->SetUINT32(This,guidKey,unValue);
}
static inline HRESULT IMFSample_SetUINT64(IMFSample* This,REFGUID guidKey,UINT64 unValue) {
    return This->lpVtbl->SetUINT64(This,guidKey,unValue);
}
static inline HRESULT IMFSample_SetDouble(IMFSample* This,REFGUID guidKey,double fValue) {
    return This->lpVtbl->SetDouble(This,guidKey,fValue);
}
static inline HRESULT IMFSample_SetGUID(IMFSample* This,REFGUID guidKey,REFGUID guidValue) {
    return This->lpVtbl->SetGUID(This,guidKey,guidValue);
}
static inline HRESULT IMFSample_SetString(IMFSample* This,REFGUID guidKey,LPCWSTR wszValue) {
    return This->lpVtbl->SetString(This,guidKey,wszValue);
}
static inline HRESULT IMFSample_SetBlob(IMFSample* This,REFGUID guidKey,const UINT8 *pBuf,UINT32 cbBufSize) {
    return This->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize);
}
static inline HRESULT IMFSample_SetUnknown(IMFSample* This,REFGUID guidKey,IUnknown *pUnknown) {
    return This->lpVtbl->SetUnknown(This,guidKey,pUnknown);
}
static inline HRESULT IMFSample_LockStore(IMFSample* This) {
    return This->lpVtbl->LockStore(This);
}
static inline HRESULT IMFSample_UnlockStore(IMFSample* This) {
    return This->lpVtbl->UnlockStore(This);
}
static inline HRESULT IMFSample_GetCount(IMFSample* This,UINT32 *pcItems) {
    return This->lpVtbl->GetCount(This,pcItems);
}
static inline HRESULT IMFSample_GetItemByIndex(IMFSample* This,UINT32 unIndex,GUID *pguidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue);
}
static inline HRESULT IMFSample_CopyAllItems(IMFSample* This,IMFAttributes *pDest) {
    return This->lpVtbl->CopyAllItems(This,pDest);
}
/*** IMFSample methods ***/
static inline HRESULT IMFSample_GetSampleFlags(IMFSample* This,DWORD *pdwSampleFlags) {
    return This->lpVtbl->GetSampleFlags(This,pdwSampleFlags);
}
static inline HRESULT IMFSample_SetSampleFlags(IMFSample* This,DWORD dwSampleFlags) {
    return This->lpVtbl->SetSampleFlags(This,dwSampleFlags);
}
static inline HRESULT IMFSample_GetSampleTime(IMFSample* This,LONGLONG *phnsSampleTime) {
    return This->lpVtbl->GetSampleTime(This,phnsSampleTime);
}
static inline HRESULT IMFSample_SetSampleTime(IMFSample* This,LONGLONG hnsSampleTime) {
    return This->lpVtbl->SetSampleTime(This,hnsSampleTime);
}
static inline HRESULT IMFSample_GetSampleDuration(IMFSample* This,LONGLONG *phnsSampleDuration) {
    return This->lpVtbl->GetSampleDuration(This,phnsSampleDuration);
}
static inline HRESULT IMFSample_SetSampleDuration(IMFSample* This,LONGLONG hnsSampleDuration) {
    return This->lpVtbl->SetSampleDuration(This,hnsSampleDuration);
}
static inline HRESULT IMFSample_GetBufferCount(IMFSample* This,DWORD *pdwBufferCount) {
    return This->lpVtbl->GetBufferCount(This,pdwBufferCount);
}
static inline HRESULT IMFSample_GetBufferByIndex(IMFSample* This,DWORD dwIndex,IMFMediaBuffer **ppBuffer) {
    return This->lpVtbl->GetBufferByIndex(This,dwIndex,ppBuffer);
}
static inline HRESULT IMFSample_ConvertToContiguousBuffer(IMFSample* This,IMFMediaBuffer **ppBuffer) {
    return This->lpVtbl->ConvertToContiguousBuffer(This,ppBuffer);
}
static inline HRESULT IMFSample_AddBuffer(IMFSample* This,IMFMediaBuffer *pBuffer) {
    return This->lpVtbl->AddBuffer(This,pBuffer);
}
static inline HRESULT IMFSample_RemoveBufferByIndex(IMFSample* This,DWORD dwIndex) {
    return This->lpVtbl->RemoveBufferByIndex(This,dwIndex);
}
static inline HRESULT IMFSample_RemoveAllBuffers(IMFSample* This) {
    return This->lpVtbl->RemoveAllBuffers(This);
}
static inline HRESULT IMFSample_GetTotalLength(IMFSample* This,DWORD *pcbTotalLength) {
    return This->lpVtbl->GetTotalLength(This,pcbTotalLength);
}
static inline HRESULT IMFSample_CopyToBuffer(IMFSample* This,IMFMediaBuffer *pBuffer) {
    return This->lpVtbl->CopyToBuffer(This,pBuffer);
}
#endif
#endif

#endif


#endif  /* __IMFSample_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMF2DBuffer interface
 */
#ifndef __IMF2DBuffer_INTERFACE_DEFINED__
#define __IMF2DBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMF2DBuffer, 0x7dc9d5f9, 0x9ed9, 0x44ec, 0x9b,0xbf, 0x06,0x00,0xbb,0x58,0x9f,0xbb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7dc9d5f9-9ed9-44ec-9bbf-0600bb589fbb")
IMF2DBuffer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Lock2D(
        BYTE **ppbScanline0,
        LONG *plPitch) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unlock2D(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScanline0AndPitch(
        BYTE **pbScanline0,
        LONG *plPitch) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsContiguousFormat(
        WINBOOL *pfIsContiguous) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContiguousLength(
        DWORD *pcbLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE ContiguousCopyTo(
        BYTE *pbDestBuffer,
        DWORD cbDestBuffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE ContiguousCopyFrom(
        const BYTE *pbSrcBuffer,
        DWORD cbSrcBuffer) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMF2DBuffer, 0x7dc9d5f9, 0x9ed9, 0x44ec, 0x9b,0xbf, 0x06,0x00,0xbb,0x58,0x9f,0xbb)
#endif
#else
typedef struct IMF2DBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMF2DBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMF2DBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMF2DBuffer *This);

    /*** IMF2DBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *Lock2D)(
        IMF2DBuffer *This,
        BYTE **ppbScanline0,
        LONG *plPitch);

    HRESULT (STDMETHODCALLTYPE *Unlock2D)(
        IMF2DBuffer *This);

    HRESULT (STDMETHODCALLTYPE *GetScanline0AndPitch)(
        IMF2DBuffer *This,
        BYTE **pbScanline0,
        LONG *plPitch);

    HRESULT (STDMETHODCALLTYPE *IsContiguousFormat)(
        IMF2DBuffer *This,
        WINBOOL *pfIsContiguous);

    HRESULT (STDMETHODCALLTYPE *GetContiguousLength)(
        IMF2DBuffer *This,
        DWORD *pcbLength);

    HRESULT (STDMETHODCALLTYPE *ContiguousCopyTo)(
        IMF2DBuffer *This,
        BYTE *pbDestBuffer,
        DWORD cbDestBuffer);

    HRESULT (STDMETHODCALLTYPE *ContiguousCopyFrom)(
        IMF2DBuffer *This,
        const BYTE *pbSrcBuffer,
        DWORD cbSrcBuffer);

    END_INTERFACE
} IMF2DBufferVtbl;

interface IMF2DBuffer {
    CONST_VTBL IMF2DBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMF2DBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMF2DBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMF2DBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IMF2DBuffer methods ***/
#define IMF2DBuffer_Lock2D(This,ppbScanline0,plPitch) (This)->lpVtbl->Lock2D(This,ppbScanline0,plPitch)
#define IMF2DBuffer_Unlock2D(This) (This)->lpVtbl->Unlock2D(This)
#define IMF2DBuffer_GetScanline0AndPitch(This,pbScanline0,plPitch) (This)->lpVtbl->GetScanline0AndPitch(This,pbScanline0,plPitch)
#define IMF2DBuffer_IsContiguousFormat(This,pfIsContiguous) (This)->lpVtbl->IsContiguousFormat(This,pfIsContiguous)
#define IMF2DBuffer_GetContiguousLength(This,pcbLength) (This)->lpVtbl->GetContiguousLength(This,pcbLength)
#define IMF2DBuffer_ContiguousCopyTo(This,pbDestBuffer,cbDestBuffer) (This)->lpVtbl->ContiguousCopyTo(This,pbDestBuffer,cbDestBuffer)
#define IMF2DBuffer_ContiguousCopyFrom(This,pbSrcBuffer,cbSrcBuffer) (This)->lpVtbl->ContiguousCopyFrom(This,pbSrcBuffer,cbSrcBuffer)
#else
/*** IUnknown methods ***/
static inline HRESULT IMF2DBuffer_QueryInterface(IMF2DBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMF2DBuffer_AddRef(IMF2DBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMF2DBuffer_Release(IMF2DBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IMF2DBuffer methods ***/
static inline HRESULT IMF2DBuffer_Lock2D(IMF2DBuffer* This,BYTE **ppbScanline0,LONG *plPitch) {
    return This->lpVtbl->Lock2D(This,ppbScanline0,plPitch);
}
static inline HRESULT IMF2DBuffer_Unlock2D(IMF2DBuffer* This) {
    return This->lpVtbl->Unlock2D(This);
}
static inline HRESULT IMF2DBuffer_GetScanline0AndPitch(IMF2DBuffer* This,BYTE **pbScanline0,LONG *plPitch) {
    return This->lpVtbl->GetScanline0AndPitch(This,pbScanline0,plPitch);
}
static inline HRESULT IMF2DBuffer_IsContiguousFormat(IMF2DBuffer* This,WINBOOL *pfIsContiguous) {
    return This->lpVtbl->IsContiguousFormat(This,pfIsContiguous);
}
static inline HRESULT IMF2DBuffer_GetContiguousLength(IMF2DBuffer* This,DWORD *pcbLength) {
    return This->lpVtbl->GetContiguousLength(This,pcbLength);
}
static inline HRESULT IMF2DBuffer_ContiguousCopyTo(IMF2DBuffer* This,BYTE *pbDestBuffer,DWORD cbDestBuffer) {
    return This->lpVtbl->ContiguousCopyTo(This,pbDestBuffer,cbDestBuffer);
}
static inline HRESULT IMF2DBuffer_ContiguousCopyFrom(IMF2DBuffer* This,const BYTE *pbSrcBuffer,DWORD cbSrcBuffer) {
    return This->lpVtbl->ContiguousCopyFrom(This,pbSrcBuffer,cbSrcBuffer);
}
#endif
#endif

#endif


#endif  /* __IMF2DBuffer_INTERFACE_DEFINED__ */


typedef enum _MF2DBuffer_LockFlags {
    MF2DBuffer_LockFlags_LockTypeMask = (0x1 | 0x2) | 0x3,
    MF2DBuffer_LockFlags_Read = 0x1,
    MF2DBuffer_LockFlags_Write = 0x2,
    MF2DBuffer_LockFlags_ReadWrite = 0x3,
    MF2DBuffer_LockFlags_ForceDWORD = 0x7fffffff
} MF2DBuffer_LockFlags;

/*****************************************************************************
 * IMF2DBuffer2 interface
 */
#ifndef __IMF2DBuffer2_INTERFACE_DEFINED__
#define __IMF2DBuffer2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMF2DBuffer2, 0x33ae5ea6, 0x4316, 0x436f, 0x8d,0xdd, 0xd7,0x3d,0x22,0xf8,0x29,0xec);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("33ae5ea6-4316-436f-8ddd-d73d22f829ec")
IMF2DBuffer2 : public IMF2DBuffer
{
    virtual HRESULT STDMETHODCALLTYPE Lock2DSize(
        MF2DBuffer_LockFlags lockFlags,
        BYTE **ppbScanline0,
        LONG *plPitch,
        BYTE **ppbBufferStart,
        DWORD *pcbBufferLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE Copy2DTo(
        IMF2DBuffer2 *pDestBuffer) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMF2DBuffer2, 0x33ae5ea6, 0x4316, 0x436f, 0x8d,0xdd, 0xd7,0x3d,0x22,0xf8,0x29,0xec)
#endif
#else
typedef struct IMF2DBuffer2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMF2DBuffer2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMF2DBuffer2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMF2DBuffer2 *This);

    /*** IMF2DBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *Lock2D)(
        IMF2DBuffer2 *This,
        BYTE **ppbScanline0,
        LONG *plPitch);

    HRESULT (STDMETHODCALLTYPE *Unlock2D)(
        IMF2DBuffer2 *This);

    HRESULT (STDMETHODCALLTYPE *GetScanline0AndPitch)(
        IMF2DBuffer2 *This,
        BYTE **pbScanline0,
        LONG *plPitch);

    HRESULT (STDMETHODCALLTYPE *IsContiguousFormat)(
        IMF2DBuffer2 *This,
        WINBOOL *pfIsContiguous);

    HRESULT (STDMETHODCALLTYPE *GetContiguousLength)(
        IMF2DBuffer2 *This,
        DWORD *pcbLength);

    HRESULT (STDMETHODCALLTYPE *ContiguousCopyTo)(
        IMF2DBuffer2 *This,
        BYTE *pbDestBuffer,
        DWORD cbDestBuffer);

    HRESULT (STDMETHODCALLTYPE *ContiguousCopyFrom)(
        IMF2DBuffer2 *This,
        const BYTE *pbSrcBuffer,
        DWORD cbSrcBuffer);

    /*** IMF2DBuffer2 methods ***/
    HRESULT (STDMETHODCALLTYPE *Lock2DSize)(
        IMF2DBuffer2 *This,
        MF2DBuffer_LockFlags lockFlags,
        BYTE **ppbScanline0,
        LONG *plPitch,
        BYTE **ppbBufferStart,
        DWORD *pcbBufferLength);

    HRESULT (STDMETHODCALLTYPE *Copy2DTo)(
        IMF2DBuffer2 *This,
        IMF2DBuffer2 *pDestBuffer);

    END_INTERFACE
} IMF2DBuffer2Vtbl;

interface IMF2DBuffer2 {
    CONST_VTBL IMF2DBuffer2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMF2DBuffer2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMF2DBuffer2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMF2DBuffer2_Release(This) (This)->lpVtbl->Release(This)
/*** IMF2DBuffer methods ***/
#define IMF2DBuffer2_Lock2D(This,ppbScanline0,plPitch) (This)->lpVtbl->Lock2D(This,ppbScanline0,plPitch)
#define IMF2DBuffer2_Unlock2D(This) (This)->lpVtbl->Unlock2D(This)
#define IMF2DBuffer2_GetScanline0AndPitch(This,pbScanline0,plPitch) (This)->lpVtbl->GetScanline0AndPitch(This,pbScanline0,plPitch)
#define IMF2DBuffer2_IsContiguousFormat(This,pfIsContiguous) (This)->lpVtbl->IsContiguousFormat(This,pfIsContiguous)
#define IMF2DBuffer2_GetContiguousLength(This,pcbLength) (This)->lpVtbl->GetContiguousLength(This,pcbLength)
#define IMF2DBuffer2_ContiguousCopyTo(This,pbDestBuffer,cbDestBuffer) (This)->lpVtbl->ContiguousCopyTo(This,pbDestBuffer,cbDestBuffer)
#define IMF2DBuffer2_ContiguousCopyFrom(This,pbSrcBuffer,cbSrcBuffer) (This)->lpVtbl->ContiguousCopyFrom(This,pbSrcBuffer,cbSrcBuffer)
/*** IMF2DBuffer2 methods ***/
#define IMF2DBuffer2_Lock2DSize(This,lockFlags,ppbScanline0,plPitch,ppbBufferStart,pcbBufferLength) (This)->lpVtbl->Lock2DSize(This,lockFlags,ppbScanline0,plPitch,ppbBufferStart,pcbBufferLength)
#define IMF2DBuffer2_Copy2DTo(This,pDestBuffer) (This)->lpVtbl->Copy2DTo(This,pDestBuffer)
#else
/*** IUnknown methods ***/
static inline HRESULT IMF2DBuffer2_QueryInterface(IMF2DBuffer2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMF2DBuffer2_AddRef(IMF2DBuffer2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMF2DBuffer2_Release(IMF2DBuffer2* This) {
    return This->lpVtbl->Release(This);
}
/*** IMF2DBuffer methods ***/
static inline HRESULT IMF2DBuffer2_Lock2D(IMF2DBuffer2* This,BYTE **ppbScanline0,LONG *plPitch) {
    return This->lpVtbl->Lock2D(This,ppbScanline0,plPitch);
}
static inline HRESULT IMF2DBuffer2_Unlock2D(IMF2DBuffer2* This) {
    return This->lpVtbl->Unlock2D(This);
}
static inline HRESULT IMF2DBuffer2_GetScanline0AndPitch(IMF2DBuffer2* This,BYTE **pbScanline0,LONG *plPitch) {
    return This->lpVtbl->GetScanline0AndPitch(This,pbScanline0,plPitch);
}
static inline HRESULT IMF2DBuffer2_IsContiguousFormat(IMF2DBuffer2* This,WINBOOL *pfIsContiguous) {
    return This->lpVtbl->IsContiguousFormat(This,pfIsContiguous);
}
static inline HRESULT IMF2DBuffer2_GetContiguousLength(IMF2DBuffer2* This,DWORD *pcbLength) {
    return This->lpVtbl->GetContiguousLength(This,pcbLength);
}
static inline HRESULT IMF2DBuffer2_ContiguousCopyTo(IMF2DBuffer2* This,BYTE *pbDestBuffer,DWORD cbDestBuffer) {
    return This->lpVtbl->ContiguousCopyTo(This,pbDestBuffer,cbDestBuffer);
}
static inline HRESULT IMF2DBuffer2_ContiguousCopyFrom(IMF2DBuffer2* This,const BYTE *pbSrcBuffer,DWORD cbSrcBuffer) {
    return This->lpVtbl->ContiguousCopyFrom(This,pbSrcBuffer,cbSrcBuffer);
}
/*** IMF2DBuffer2 methods ***/
static inline HRESULT IMF2DBuffer2_Lock2DSize(IMF2DBuffer2* This,MF2DBuffer_LockFlags lockFlags,BYTE **ppbScanline0,LONG *plPitch,BYTE **ppbBufferStart,DWORD *pcbBufferLength) {
    return This->lpVtbl->Lock2DSize(This,lockFlags,ppbScanline0,plPitch,ppbBufferStart,pcbBufferLength);
}
static inline HRESULT IMF2DBuffer2_Copy2DTo(IMF2DBuffer2* This,IMF2DBuffer2 *pDestBuffer) {
    return This->lpVtbl->Copy2DTo(This,pDestBuffer);
}
#endif
#endif

#endif


#endif  /* __IMF2DBuffer2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFDXGIBuffer interface
 */
#ifndef __IMFDXGIBuffer_INTERFACE_DEFINED__
#define __IMFDXGIBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFDXGIBuffer, 0xe7174cfa, 0x1c9e, 0x48b1, 0x88,0x66, 0x62,0x62,0x26,0xbf,0xc2,0x58);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e7174cfa-1c9e-48b1-8866-626226bfc258")
IMFDXGIBuffer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetResource(
        REFIID riid,
        LPVOID *ppvObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSubresourceIndex(
        UINT *puSubresource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUnknown(
        REFIID guid,
        REFIID riid,
        LPVOID *ppvObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUnknown(
        REFIID guid,
        IUnknown *pUnkData) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFDXGIBuffer, 0xe7174cfa, 0x1c9e, 0x48b1, 0x88,0x66, 0x62,0x62,0x26,0xbf,0xc2,0x58)
#endif
#else
typedef struct IMFDXGIBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFDXGIBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFDXGIBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFDXGIBuffer *This);

    /*** IMFDXGIBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *GetResource)(
        IMFDXGIBuffer *This,
        REFIID riid,
        LPVOID *ppvObject);

    HRESULT (STDMETHODCALLTYPE *GetSubresourceIndex)(
        IMFDXGIBuffer *This,
        UINT *puSubresource);

    HRESULT (STDMETHODCALLTYPE *GetUnknown)(
        IMFDXGIBuffer *This,
        REFIID guid,
        REFIID riid,
        LPVOID *ppvObject);

    HRESULT (STDMETHODCALLTYPE *SetUnknown)(
        IMFDXGIBuffer *This,
        REFIID guid,
        IUnknown *pUnkData);

    END_INTERFACE
} IMFDXGIBufferVtbl;

interface IMFDXGIBuffer {
    CONST_VTBL IMFDXGIBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFDXGIBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFDXGIBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFDXGIBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IMFDXGIBuffer methods ***/
#define IMFDXGIBuffer_GetResource(This,riid,ppvObject) (This)->lpVtbl->GetResource(This,riid,ppvObject)
#define IMFDXGIBuffer_GetSubresourceIndex(This,puSubresource) (This)->lpVtbl->GetSubresourceIndex(This,puSubresource)
#define IMFDXGIBuffer_GetUnknown(This,guid,riid,ppvObject) (This)->lpVtbl->GetUnknown(This,guid,riid,ppvObject)
#define IMFDXGIBuffer_SetUnknown(This,guid,pUnkData) (This)->lpVtbl->SetUnknown(This,guid,pUnkData)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFDXGIBuffer_QueryInterface(IMFDXGIBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFDXGIBuffer_AddRef(IMFDXGIBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFDXGIBuffer_Release(IMFDXGIBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFDXGIBuffer methods ***/
static inline HRESULT IMFDXGIBuffer_GetResource(IMFDXGIBuffer* This,REFIID riid,LPVOID *ppvObject) {
    return This->lpVtbl->GetResource(This,riid,ppvObject);
}
static inline HRESULT IMFDXGIBuffer_GetSubresourceIndex(IMFDXGIBuffer* This,UINT *puSubresource) {
    return This->lpVtbl->GetSubresourceIndex(This,puSubresource);
}
static inline HRESULT IMFDXGIBuffer_GetUnknown(IMFDXGIBuffer* This,REFIID guid,REFIID riid,LPVOID *ppvObject) {
    return This->lpVtbl->GetUnknown(This,guid,riid,ppvObject);
}
static inline HRESULT IMFDXGIBuffer_SetUnknown(IMFDXGIBuffer* This,REFIID guid,IUnknown *pUnkData) {
    return This->lpVtbl->SetUnknown(This,guid,pUnkData);
}
#endif
#endif

#endif


#endif  /* __IMFDXGIBuffer_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFMediaType interface
 */
#ifndef __IMFMediaType_INTERFACE_DEFINED__
#define __IMFMediaType_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaType, 0x44ae0fa8, 0xea31, 0x4109, 0x8d,0x2e, 0x4c,0xae,0x49,0x97,0xc5,0x55);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("44ae0fa8-ea31-4109-8d2e-4cae4997c555")
IMFMediaType : public IMFAttributes
{
    virtual HRESULT STDMETHODCALLTYPE GetMajorType(
        GUID *pguidMajorType) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsCompressedFormat(
        WINBOOL *pfCompressed) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsEqual(
        IMFMediaType *pIMediaType,
        DWORD *pdwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRepresentation(
        GUID guidRepresentation,
        LPVOID *ppvRepresentation) = 0;

    virtual HRESULT STDMETHODCALLTYPE FreeRepresentation(
        GUID guidRepresentation,
        LPVOID pvRepresentation) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaType, 0x44ae0fa8, 0xea31, 0x4109, 0x8d,0x2e, 0x4c,0xae,0x49,0x97,0xc5,0x55)
#endif
#else
typedef struct IMFMediaTypeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaType *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaType *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaType *This);

    /*** IMFAttributes methods ***/
    HRESULT (STDMETHODCALLTYPE *GetItem)(
        IMFMediaType *This,
        REFGUID guidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetItemType)(
        IMFMediaType *This,
        REFGUID guidKey,
        MF_ATTRIBUTE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *CompareItem)(
        IMFMediaType *This,
        REFGUID guidKey,
        REFPROPVARIANT Value,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        IMFMediaType *This,
        IMFAttributes *pTheirs,
        MF_ATTRIBUTES_MATCH_TYPE MatchType,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *GetUINT32)(
        IMFMediaType *This,
        REFGUID guidKey,
        UINT32 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetUINT64)(
        IMFMediaType *This,
        REFGUID guidKey,
        UINT64 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetDouble)(
        IMFMediaType *This,
        REFGUID guidKey,
        double *pfValue);

    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IMFMediaType *This,
        REFGUID guidKey,
        GUID *pguidValue);

    HRESULT (STDMETHODCALLTYPE *GetStringLength)(
        IMFMediaType *This,
        REFGUID guidKey,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetString)(
        IMFMediaType *This,
        REFGUID guidKey,
        LPWSTR pwszValue,
        UINT32 cchBufSize,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedString)(
        IMFMediaType *This,
        REFGUID guidKey,
        LPWSTR *ppwszValue,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetBlobSize)(
        IMFMediaType *This,
        REFGUID guidKey,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetBlob)(
        IMFMediaType *This,
        REFGUID guidKey,
        UINT8 *pBuf,
        UINT32 cbBufSize,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedBlob)(
        IMFMediaType *This,
        REFGUID guidKey,
        UINT8 **ppBuf,
        UINT32 *pcbSize);

    HRESULT (STDMETHODCALLTYPE *GetUnknown)(
        IMFMediaType *This,
        REFGUID guidKey,
        REFIID riid,
        LPVOID *ppv);

    HRESULT (STDMETHODCALLTYPE *SetItem)(
        IMFMediaType *This,
        REFGUID guidKey,
        REFPROPVARIANT Value);

    HRESULT (STDMETHODCALLTYPE *DeleteItem)(
        IMFMediaType *This,
        REFGUID guidKey);

    HRESULT (STDMETHODCALLTYPE *DeleteAllItems)(
        IMFMediaType *This);

    HRESULT (STDMETHODCALLTYPE *SetUINT32)(
        IMFMediaType *This,
        REFGUID guidKey,
        UINT32 unValue);

    HRESULT (STDMETHODCALLTYPE *SetUINT64)(
        IMFMediaType *This,
        REFGUID guidKey,
        UINT64 unValue);

    HRESULT (STDMETHODCALLTYPE *SetDouble)(
        IMFMediaType *This,
        REFGUID guidKey,
        double fValue);

    HRESULT (STDMETHODCALLTYPE *SetGUID)(
        IMFMediaType *This,
        REFGUID guidKey,
        REFGUID guidValue);

    HRESULT (STDMETHODCALLTYPE *SetString)(
        IMFMediaType *This,
        REFGUID guidKey,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SetBlob)(
        IMFMediaType *This,
        REFGUID guidKey,
        const UINT8 *pBuf,
        UINT32 cbBufSize);

    HRESULT (STDMETHODCALLTYPE *SetUnknown)(
        IMFMediaType *This,
        REFGUID guidKey,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *LockStore)(
        IMFMediaType *This);

    HRESULT (STDMETHODCALLTYPE *UnlockStore)(
        IMFMediaType *This);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IMFMediaType *This,
        UINT32 *pcItems);

    HRESULT (STDMETHODCALLTYPE *GetItemByIndex)(
        IMFMediaType *This,
        UINT32 unIndex,
        GUID *pguidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *CopyAllItems)(
        IMFMediaType *This,
        IMFAttributes *pDest);

    /*** IMFMediaType methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMajorType)(
        IMFMediaType *This,
        GUID *pguidMajorType);

    HRESULT (STDMETHODCALLTYPE *IsCompressedFormat)(
        IMFMediaType *This,
        WINBOOL *pfCompressed);

    HRESULT (STDMETHODCALLTYPE *IsEqual)(
        IMFMediaType *This,
        IMFMediaType *pIMediaType,
        DWORD *pdwFlags);

    HRESULT (STDMETHODCALLTYPE *GetRepresentation)(
        IMFMediaType *This,
        GUID guidRepresentation,
        LPVOID *ppvRepresentation);

    HRESULT (STDMETHODCALLTYPE *FreeRepresentation)(
        IMFMediaType *This,
        GUID guidRepresentation,
        LPVOID pvRepresentation);

    END_INTERFACE
} IMFMediaTypeVtbl;

interface IMFMediaType {
    CONST_VTBL IMFMediaTypeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaType_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaType_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaType_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAttributes methods ***/
#define IMFMediaType_GetItem(This,guidKey,pValue) (This)->lpVtbl->GetItem(This,guidKey,pValue)
#define IMFMediaType_GetItemType(This,guidKey,pType) (This)->lpVtbl->GetItemType(This,guidKey,pType)
#define IMFMediaType_CompareItem(This,guidKey,Value,pbResult) (This)->lpVtbl->CompareItem(This,guidKey,Value,pbResult)
#define IMFMediaType_Compare(This,pTheirs,MatchType,pbResult) (This)->lpVtbl->Compare(This,pTheirs,MatchType,pbResult)
#define IMFMediaType_GetUINT32(This,guidKey,punValue) (This)->lpVtbl->GetUINT32(This,guidKey,punValue)
#define IMFMediaType_GetUINT64(This,guidKey,punValue) (This)->lpVtbl->GetUINT64(This,guidKey,punValue)
#define IMFMediaType_GetDouble(This,guidKey,pfValue) (This)->lpVtbl->GetDouble(This,guidKey,pfValue)
#define IMFMediaType_GetGUID(This,guidKey,pguidValue) (This)->lpVtbl->GetGUID(This,guidKey,pguidValue)
#define IMFMediaType_GetStringLength(This,guidKey,pcchLength) (This)->lpVtbl->GetStringLength(This,guidKey,pcchLength)
#define IMFMediaType_GetString(This,guidKey,pwszValue,cchBufSize,pcchLength) (This)->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength)
#define IMFMediaType_GetAllocatedString(This,guidKey,ppwszValue,pcchLength) (This)->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength)
#define IMFMediaType_GetBlobSize(This,guidKey,pcbBlobSize) (This)->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize)
#define IMFMediaType_GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize) (This)->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize)
#define IMFMediaType_GetAllocatedBlob(This,guidKey,ppBuf,pcbSize) (This)->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize)
#define IMFMediaType_GetUnknown(This,guidKey,riid,ppv) (This)->lpVtbl->GetUnknown(This,guidKey,riid,ppv)
#define IMFMediaType_SetItem(This,guidKey,Value) (This)->lpVtbl->SetItem(This,guidKey,Value)
#define IMFMediaType_DeleteItem(This,guidKey) (This)->lpVtbl->DeleteItem(This,guidKey)
#define IMFMediaType_DeleteAllItems(This) (This)->lpVtbl->DeleteAllItems(This)
#define IMFMediaType_SetUINT32(This,guidKey,unValue) (This)->lpVtbl->SetUINT32(This,guidKey,unValue)
#define IMFMediaType_SetUINT64(This,guidKey,unValue) (This)->lpVtbl->SetUINT64(This,guidKey,unValue)
#define IMFMediaType_SetDouble(This,guidKey,fValue) (This)->lpVtbl->SetDouble(This,guidKey,fValue)
#define IMFMediaType_SetGUID(This,guidKey,guidValue) (This)->lpVtbl->SetGUID(This,guidKey,guidValue)
#define IMFMediaType_SetString(This,guidKey,wszValue) (This)->lpVtbl->SetString(This,guidKey,wszValue)
#define IMFMediaType_SetBlob(This,guidKey,pBuf,cbBufSize) (This)->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize)
#define IMFMediaType_SetUnknown(This,guidKey,pUnknown) (This)->lpVtbl->SetUnknown(This,guidKey,pUnknown)
#define IMFMediaType_LockStore(This) (This)->lpVtbl->LockStore(This)
#define IMFMediaType_UnlockStore(This) (This)->lpVtbl->UnlockStore(This)
#define IMFMediaType_GetCount(This,pcItems) (This)->lpVtbl->GetCount(This,pcItems)
#define IMFMediaType_GetItemByIndex(This,unIndex,pguidKey,pValue) (This)->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue)
#define IMFMediaType_CopyAllItems(This,pDest) (This)->lpVtbl->CopyAllItems(This,pDest)
/*** IMFMediaType methods ***/
#define IMFMediaType_GetMajorType(This,pguidMajorType) (This)->lpVtbl->GetMajorType(This,pguidMajorType)
#define IMFMediaType_IsCompressedFormat(This,pfCompressed) (This)->lpVtbl->IsCompressedFormat(This,pfCompressed)
#define IMFMediaType_IsEqual(This,pIMediaType,pdwFlags) (This)->lpVtbl->IsEqual(This,pIMediaType,pdwFlags)
#define IMFMediaType_GetRepresentation(This,guidRepresentation,ppvRepresentation) (This)->lpVtbl->GetRepresentation(This,guidRepresentation,ppvRepresentation)
#define IMFMediaType_FreeRepresentation(This,guidRepresentation,pvRepresentation) (This)->lpVtbl->FreeRepresentation(This,guidRepresentation,pvRepresentation)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaType_QueryInterface(IMFMediaType* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaType_AddRef(IMFMediaType* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaType_Release(IMFMediaType* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAttributes methods ***/
static inline HRESULT IMFMediaType_GetItem(IMFMediaType* This,REFGUID guidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItem(This,guidKey,pValue);
}
static inline HRESULT IMFMediaType_GetItemType(IMFMediaType* This,REFGUID guidKey,MF_ATTRIBUTE_TYPE *pType) {
    return This->lpVtbl->GetItemType(This,guidKey,pType);
}
static inline HRESULT IMFMediaType_CompareItem(IMFMediaType* This,REFGUID guidKey,REFPROPVARIANT Value,WINBOOL *pbResult) {
    return This->lpVtbl->CompareItem(This,guidKey,Value,pbResult);
}
static inline HRESULT IMFMediaType_Compare(IMFMediaType* This,IMFAttributes *pTheirs,MF_ATTRIBUTES_MATCH_TYPE MatchType,WINBOOL *pbResult) {
    return This->lpVtbl->Compare(This,pTheirs,MatchType,pbResult);
}
static inline HRESULT IMFMediaType_GetUINT32(IMFMediaType* This,REFGUID guidKey,UINT32 *punValue) {
    return This->lpVtbl->GetUINT32(This,guidKey,punValue);
}
static inline HRESULT IMFMediaType_GetUINT64(IMFMediaType* This,REFGUID guidKey,UINT64 *punValue) {
    return This->lpVtbl->GetUINT64(This,guidKey,punValue);
}
static inline HRESULT IMFMediaType_GetDouble(IMFMediaType* This,REFGUID guidKey,double *pfValue) {
    return This->lpVtbl->GetDouble(This,guidKey,pfValue);
}
static inline HRESULT IMFMediaType_GetGUID(IMFMediaType* This,REFGUID guidKey,GUID *pguidValue) {
    return This->lpVtbl->GetGUID(This,guidKey,pguidValue);
}
static inline HRESULT IMFMediaType_GetStringLength(IMFMediaType* This,REFGUID guidKey,UINT32 *pcchLength) {
    return This->lpVtbl->GetStringLength(This,guidKey,pcchLength);
}
static inline HRESULT IMFMediaType_GetString(IMFMediaType* This,REFGUID guidKey,LPWSTR pwszValue,UINT32 cchBufSize,UINT32 *pcchLength) {
    return This->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength);
}
static inline HRESULT IMFMediaType_GetAllocatedString(IMFMediaType* This,REFGUID guidKey,LPWSTR *ppwszValue,UINT32 *pcchLength) {
    return This->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength);
}
static inline HRESULT IMFMediaType_GetBlobSize(IMFMediaType* This,REFGUID guidKey,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize);
}
static inline HRESULT IMFMediaType_GetBlob(IMFMediaType* This,REFGUID guidKey,UINT8 *pBuf,UINT32 cbBufSize,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize);
}
static inline HRESULT IMFMediaType_GetAllocatedBlob(IMFMediaType* This,REFGUID guidKey,UINT8 **ppBuf,UINT32 *pcbSize) {
    return This->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize);
}
static inline HRESULT IMFMediaType_GetUnknown(IMFMediaType* This,REFGUID guidKey,REFIID riid,LPVOID *ppv) {
    return This->lpVtbl->GetUnknown(This,guidKey,riid,ppv);
}
static inline HRESULT IMFMediaType_SetItem(IMFMediaType* This,REFGUID guidKey,REFPROPVARIANT Value) {
    return This->lpVtbl->SetItem(This,guidKey,Value);
}
static inline HRESULT IMFMediaType_DeleteItem(IMFMediaType* This,REFGUID guidKey) {
    return This->lpVtbl->DeleteItem(This,guidKey);
}
static inline HRESULT IMFMediaType_DeleteAllItems(IMFMediaType* This) {
    return This->lpVtbl->DeleteAllItems(This);
}
static inline HRESULT IMFMediaType_SetUINT32(IMFMediaType* This,REFGUID guidKey,UINT32 unValue) {
    return This->lpVtbl->SetUINT32(This,guidKey,unValue);
}
static inline HRESULT IMFMediaType_SetUINT64(IMFMediaType* This,REFGUID guidKey,UINT64 unValue) {
    return This->lpVtbl->SetUINT64(This,guidKey,unValue);
}
static inline HRESULT IMFMediaType_SetDouble(IMFMediaType* This,REFGUID guidKey,double fValue) {
    return This->lpVtbl->SetDouble(This,guidKey,fValue);
}
static inline HRESULT IMFMediaType_SetGUID(IMFMediaType* This,REFGUID guidKey,REFGUID guidValue) {
    return This->lpVtbl->SetGUID(This,guidKey,guidValue);
}
static inline HRESULT IMFMediaType_SetString(IMFMediaType* This,REFGUID guidKey,LPCWSTR wszValue) {
    return This->lpVtbl->SetString(This,guidKey,wszValue);
}
static inline HRESULT IMFMediaType_SetBlob(IMFMediaType* This,REFGUID guidKey,const UINT8 *pBuf,UINT32 cbBufSize) {
    return This->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize);
}
static inline HRESULT IMFMediaType_SetUnknown(IMFMediaType* This,REFGUID guidKey,IUnknown *pUnknown) {
    return This->lpVtbl->SetUnknown(This,guidKey,pUnknown);
}
static inline HRESULT IMFMediaType_LockStore(IMFMediaType* This) {
    return This->lpVtbl->LockStore(This);
}
static inline HRESULT IMFMediaType_UnlockStore(IMFMediaType* This) {
    return This->lpVtbl->UnlockStore(This);
}
static inline HRESULT IMFMediaType_GetCount(IMFMediaType* This,UINT32 *pcItems) {
    return This->lpVtbl->GetCount(This,pcItems);
}
static inline HRESULT IMFMediaType_GetItemByIndex(IMFMediaType* This,UINT32 unIndex,GUID *pguidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue);
}
static inline HRESULT IMFMediaType_CopyAllItems(IMFMediaType* This,IMFAttributes *pDest) {
    return This->lpVtbl->CopyAllItems(This,pDest);
}
/*** IMFMediaType methods ***/
static inline HRESULT IMFMediaType_GetMajorType(IMFMediaType* This,GUID *pguidMajorType) {
    return This->lpVtbl->GetMajorType(This,pguidMajorType);
}
static inline HRESULT IMFMediaType_IsCompressedFormat(IMFMediaType* This,WINBOOL *pfCompressed) {
    return This->lpVtbl->IsCompressedFormat(This,pfCompressed);
}
static inline HRESULT IMFMediaType_IsEqual(IMFMediaType* This,IMFMediaType *pIMediaType,DWORD *pdwFlags) {
    return This->lpVtbl->IsEqual(This,pIMediaType,pdwFlags);
}
static inline HRESULT IMFMediaType_GetRepresentation(IMFMediaType* This,GUID guidRepresentation,LPVOID *ppvRepresentation) {
    return This->lpVtbl->GetRepresentation(This,guidRepresentation,ppvRepresentation);
}
static inline HRESULT IMFMediaType_FreeRepresentation(IMFMediaType* This,GUID guidRepresentation,LPVOID pvRepresentation) {
    return This->lpVtbl->FreeRepresentation(This,guidRepresentation,pvRepresentation);
}
#endif
#endif

#endif


#endif  /* __IMFMediaType_INTERFACE_DEFINED__ */


#define MF_MEDIATYPE_EQUAL_MAJOR_TYPES 0x00000001
#define MF_MEDIATYPE_EQUAL_FORMAT_TYPES 0x00000002
#define MF_MEDIATYPE_EQUAL_FORMAT_DATA 0x00000004
#define MF_MEDIATYPE_EQUAL_FORMAT_USER_DATA 0x00000008
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IMFAudioMediaType interface
 */
#ifndef __IMFAudioMediaType_INTERFACE_DEFINED__
#define __IMFAudioMediaType_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFAudioMediaType, 0x26a0adc3, 0xce26, 0x4672, 0x93,0x04, 0x69,0x55,0x2e,0xdd,0x3f,0xaf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("26a0adc3-ce26-4672-9304-69552edd3faf")
IMFAudioMediaType : public IMFMediaType
{
    virtual const WAVEFORMATEX * STDMETHODCALLTYPE GetAudioFormat(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFAudioMediaType, 0x26a0adc3, 0xce26, 0x4672, 0x93,0x04, 0x69,0x55,0x2e,0xdd,0x3f,0xaf)
#endif
#else
typedef struct IMFAudioMediaTypeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFAudioMediaType *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFAudioMediaType *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFAudioMediaType *This);

    /*** IMFAttributes methods ***/
    HRESULT (STDMETHODCALLTYPE *GetItem)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetItemType)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        MF_ATTRIBUTE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *CompareItem)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        REFPROPVARIANT Value,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        IMFAudioMediaType *This,
        IMFAttributes *pTheirs,
        MF_ATTRIBUTES_MATCH_TYPE MatchType,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *GetUINT32)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        UINT32 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetUINT64)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        UINT64 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetDouble)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        double *pfValue);

    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        GUID *pguidValue);

    HRESULT (STDMETHODCALLTYPE *GetStringLength)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetString)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        LPWSTR pwszValue,
        UINT32 cchBufSize,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedString)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        LPWSTR *ppwszValue,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetBlobSize)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetBlob)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        UINT8 *pBuf,
        UINT32 cbBufSize,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedBlob)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        UINT8 **ppBuf,
        UINT32 *pcbSize);

    HRESULT (STDMETHODCALLTYPE *GetUnknown)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        REFIID riid,
        LPVOID *ppv);

    HRESULT (STDMETHODCALLTYPE *SetItem)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        REFPROPVARIANT Value);

    HRESULT (STDMETHODCALLTYPE *DeleteItem)(
        IMFAudioMediaType *This,
        REFGUID guidKey);

    HRESULT (STDMETHODCALLTYPE *DeleteAllItems)(
        IMFAudioMediaType *This);

    HRESULT (STDMETHODCALLTYPE *SetUINT32)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        UINT32 unValue);

    HRESULT (STDMETHODCALLTYPE *SetUINT64)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        UINT64 unValue);

    HRESULT (STDMETHODCALLTYPE *SetDouble)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        double fValue);

    HRESULT (STDMETHODCALLTYPE *SetGUID)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        REFGUID guidValue);

    HRESULT (STDMETHODCALLTYPE *SetString)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SetBlob)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        const UINT8 *pBuf,
        UINT32 cbBufSize);

    HRESULT (STDMETHODCALLTYPE *SetUnknown)(
        IMFAudioMediaType *This,
        REFGUID guidKey,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *LockStore)(
        IMFAudioMediaType *This);

    HRESULT (STDMETHODCALLTYPE *UnlockStore)(
        IMFAudioMediaType *This);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IMFAudioMediaType *This,
        UINT32 *pcItems);

    HRESULT (STDMETHODCALLTYPE *GetItemByIndex)(
        IMFAudioMediaType *This,
        UINT32 unIndex,
        GUID *pguidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *CopyAllItems)(
        IMFAudioMediaType *This,
        IMFAttributes *pDest);

    /*** IMFMediaType methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMajorType)(
        IMFAudioMediaType *This,
        GUID *pguidMajorType);

    HRESULT (STDMETHODCALLTYPE *IsCompressedFormat)(
        IMFAudioMediaType *This,
        WINBOOL *pfCompressed);

    HRESULT (STDMETHODCALLTYPE *IsEqual)(
        IMFAudioMediaType *This,
        IMFMediaType *pIMediaType,
        DWORD *pdwFlags);

    HRESULT (STDMETHODCALLTYPE *GetRepresentation)(
        IMFAudioMediaType *This,
        GUID guidRepresentation,
        LPVOID *ppvRepresentation);

    HRESULT (STDMETHODCALLTYPE *FreeRepresentation)(
        IMFAudioMediaType *This,
        GUID guidRepresentation,
        LPVOID pvRepresentation);

    /*** IMFAudioMediaType methods ***/
    const WAVEFORMATEX * (STDMETHODCALLTYPE *GetAudioFormat)(
        IMFAudioMediaType *This);

    END_INTERFACE
} IMFAudioMediaTypeVtbl;

interface IMFAudioMediaType {
    CONST_VTBL IMFAudioMediaTypeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFAudioMediaType_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFAudioMediaType_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFAudioMediaType_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAttributes methods ***/
#define IMFAudioMediaType_GetItem(This,guidKey,pValue) (This)->lpVtbl->GetItem(This,guidKey,pValue)
#define IMFAudioMediaType_GetItemType(This,guidKey,pType) (This)->lpVtbl->GetItemType(This,guidKey,pType)
#define IMFAudioMediaType_CompareItem(This,guidKey,Value,pbResult) (This)->lpVtbl->CompareItem(This,guidKey,Value,pbResult)
#define IMFAudioMediaType_Compare(This,pTheirs,MatchType,pbResult) (This)->lpVtbl->Compare(This,pTheirs,MatchType,pbResult)
#define IMFAudioMediaType_GetUINT32(This,guidKey,punValue) (This)->lpVtbl->GetUINT32(This,guidKey,punValue)
#define IMFAudioMediaType_GetUINT64(This,guidKey,punValue) (This)->lpVtbl->GetUINT64(This,guidKey,punValue)
#define IMFAudioMediaType_GetDouble(This,guidKey,pfValue) (This)->lpVtbl->GetDouble(This,guidKey,pfValue)
#define IMFAudioMediaType_GetGUID(This,guidKey,pguidValue) (This)->lpVtbl->GetGUID(This,guidKey,pguidValue)
#define IMFAudioMediaType_GetStringLength(This,guidKey,pcchLength) (This)->lpVtbl->GetStringLength(This,guidKey,pcchLength)
#define IMFAudioMediaType_GetString(This,guidKey,pwszValue,cchBufSize,pcchLength) (This)->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength)
#define IMFAudioMediaType_GetAllocatedString(This,guidKey,ppwszValue,pcchLength) (This)->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength)
#define IMFAudioMediaType_GetBlobSize(This,guidKey,pcbBlobSize) (This)->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize)
#define IMFAudioMediaType_GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize) (This)->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize)
#define IMFAudioMediaType_GetAllocatedBlob(This,guidKey,ppBuf,pcbSize) (This)->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize)
#define IMFAudioMediaType_GetUnknown(This,guidKey,riid,ppv) (This)->lpVtbl->GetUnknown(This,guidKey,riid,ppv)
#define IMFAudioMediaType_SetItem(This,guidKey,Value) (This)->lpVtbl->SetItem(This,guidKey,Value)
#define IMFAudioMediaType_DeleteItem(This,guidKey) (This)->lpVtbl->DeleteItem(This,guidKey)
#define IMFAudioMediaType_DeleteAllItems(This) (This)->lpVtbl->DeleteAllItems(This)
#define IMFAudioMediaType_SetUINT32(This,guidKey,unValue) (This)->lpVtbl->SetUINT32(This,guidKey,unValue)
#define IMFAudioMediaType_SetUINT64(This,guidKey,unValue) (This)->lpVtbl->SetUINT64(This,guidKey,unValue)
#define IMFAudioMediaType_SetDouble(This,guidKey,fValue) (This)->lpVtbl->SetDouble(This,guidKey,fValue)
#define IMFAudioMediaType_SetGUID(This,guidKey,guidValue) (This)->lpVtbl->SetGUID(This,guidKey,guidValue)
#define IMFAudioMediaType_SetString(This,guidKey,wszValue) (This)->lpVtbl->SetString(This,guidKey,wszValue)
#define IMFAudioMediaType_SetBlob(This,guidKey,pBuf,cbBufSize) (This)->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize)
#define IMFAudioMediaType_SetUnknown(This,guidKey,pUnknown) (This)->lpVtbl->SetUnknown(This,guidKey,pUnknown)
#define IMFAudioMediaType_LockStore(This) (This)->lpVtbl->LockStore(This)
#define IMFAudioMediaType_UnlockStore(This) (This)->lpVtbl->UnlockStore(This)
#define IMFAudioMediaType_GetCount(This,pcItems) (This)->lpVtbl->GetCount(This,pcItems)
#define IMFAudioMediaType_GetItemByIndex(This,unIndex,pguidKey,pValue) (This)->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue)
#define IMFAudioMediaType_CopyAllItems(This,pDest) (This)->lpVtbl->CopyAllItems(This,pDest)
/*** IMFMediaType methods ***/
#define IMFAudioMediaType_GetMajorType(This,pguidMajorType) (This)->lpVtbl->GetMajorType(This,pguidMajorType)
#define IMFAudioMediaType_IsCompressedFormat(This,pfCompressed) (This)->lpVtbl->IsCompressedFormat(This,pfCompressed)
#define IMFAudioMediaType_IsEqual(This,pIMediaType,pdwFlags) (This)->lpVtbl->IsEqual(This,pIMediaType,pdwFlags)
#define IMFAudioMediaType_GetRepresentation(This,guidRepresentation,ppvRepresentation) (This)->lpVtbl->GetRepresentation(This,guidRepresentation,ppvRepresentation)
#define IMFAudioMediaType_FreeRepresentation(This,guidRepresentation,pvRepresentation) (This)->lpVtbl->FreeRepresentation(This,guidRepresentation,pvRepresentation)
/*** IMFAudioMediaType methods ***/
#define IMFAudioMediaType_GetAudioFormat(This) (This)->lpVtbl->GetAudioFormat(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFAudioMediaType_QueryInterface(IMFAudioMediaType* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFAudioMediaType_AddRef(IMFAudioMediaType* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFAudioMediaType_Release(IMFAudioMediaType* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAttributes methods ***/
static inline HRESULT IMFAudioMediaType_GetItem(IMFAudioMediaType* This,REFGUID guidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItem(This,guidKey,pValue);
}
static inline HRESULT IMFAudioMediaType_GetItemType(IMFAudioMediaType* This,REFGUID guidKey,MF_ATTRIBUTE_TYPE *pType) {
    return This->lpVtbl->GetItemType(This,guidKey,pType);
}
static inline HRESULT IMFAudioMediaType_CompareItem(IMFAudioMediaType* This,REFGUID guidKey,REFPROPVARIANT Value,WINBOOL *pbResult) {
    return This->lpVtbl->CompareItem(This,guidKey,Value,pbResult);
}
static inline HRESULT IMFAudioMediaType_Compare(IMFAudioMediaType* This,IMFAttributes *pTheirs,MF_ATTRIBUTES_MATCH_TYPE MatchType,WINBOOL *pbResult) {
    return This->lpVtbl->Compare(This,pTheirs,MatchType,pbResult);
}
static inline HRESULT IMFAudioMediaType_GetUINT32(IMFAudioMediaType* This,REFGUID guidKey,UINT32 *punValue) {
    return This->lpVtbl->GetUINT32(This,guidKey,punValue);
}
static inline HRESULT IMFAudioMediaType_GetUINT64(IMFAudioMediaType* This,REFGUID guidKey,UINT64 *punValue) {
    return This->lpVtbl->GetUINT64(This,guidKey,punValue);
}
static inline HRESULT IMFAudioMediaType_GetDouble(IMFAudioMediaType* This,REFGUID guidKey,double *pfValue) {
    return This->lpVtbl->GetDouble(This,guidKey,pfValue);
}
static inline HRESULT IMFAudioMediaType_GetGUID(IMFAudioMediaType* This,REFGUID guidKey,GUID *pguidValue) {
    return This->lpVtbl->GetGUID(This,guidKey,pguidValue);
}
static inline HRESULT IMFAudioMediaType_GetStringLength(IMFAudioMediaType* This,REFGUID guidKey,UINT32 *pcchLength) {
    return This->lpVtbl->GetStringLength(This,guidKey,pcchLength);
}
static inline HRESULT IMFAudioMediaType_GetString(IMFAudioMediaType* This,REFGUID guidKey,LPWSTR pwszValue,UINT32 cchBufSize,UINT32 *pcchLength) {
    return This->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength);
}
static inline HRESULT IMFAudioMediaType_GetAllocatedString(IMFAudioMediaType* This,REFGUID guidKey,LPWSTR *ppwszValue,UINT32 *pcchLength) {
    return This->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength);
}
static inline HRESULT IMFAudioMediaType_GetBlobSize(IMFAudioMediaType* This,REFGUID guidKey,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize);
}
static inline HRESULT IMFAudioMediaType_GetBlob(IMFAudioMediaType* This,REFGUID guidKey,UINT8 *pBuf,UINT32 cbBufSize,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize);
}
static inline HRESULT IMFAudioMediaType_GetAllocatedBlob(IMFAudioMediaType* This,REFGUID guidKey,UINT8 **ppBuf,UINT32 *pcbSize) {
    return This->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize);
}
static inline HRESULT IMFAudioMediaType_GetUnknown(IMFAudioMediaType* This,REFGUID guidKey,REFIID riid,LPVOID *ppv) {
    return This->lpVtbl->GetUnknown(This,guidKey,riid,ppv);
}
static inline HRESULT IMFAudioMediaType_SetItem(IMFAudioMediaType* This,REFGUID guidKey,REFPROPVARIANT Value) {
    return This->lpVtbl->SetItem(This,guidKey,Value);
}
static inline HRESULT IMFAudioMediaType_DeleteItem(IMFAudioMediaType* This,REFGUID guidKey) {
    return This->lpVtbl->DeleteItem(This,guidKey);
}
static inline HRESULT IMFAudioMediaType_DeleteAllItems(IMFAudioMediaType* This) {
    return This->lpVtbl->DeleteAllItems(This);
}
static inline HRESULT IMFAudioMediaType_SetUINT32(IMFAudioMediaType* This,REFGUID guidKey,UINT32 unValue) {
    return This->lpVtbl->SetUINT32(This,guidKey,unValue);
}
static inline HRESULT IMFAudioMediaType_SetUINT64(IMFAudioMediaType* This,REFGUID guidKey,UINT64 unValue) {
    return This->lpVtbl->SetUINT64(This,guidKey,unValue);
}
static inline HRESULT IMFAudioMediaType_SetDouble(IMFAudioMediaType* This,REFGUID guidKey,double fValue) {
    return This->lpVtbl->SetDouble(This,guidKey,fValue);
}
static inline HRESULT IMFAudioMediaType_SetGUID(IMFAudioMediaType* This,REFGUID guidKey,REFGUID guidValue) {
    return This->lpVtbl->SetGUID(This,guidKey,guidValue);
}
static inline HRESULT IMFAudioMediaType_SetString(IMFAudioMediaType* This,REFGUID guidKey,LPCWSTR wszValue) {
    return This->lpVtbl->SetString(This,guidKey,wszValue);
}
static inline HRESULT IMFAudioMediaType_SetBlob(IMFAudioMediaType* This,REFGUID guidKey,const UINT8 *pBuf,UINT32 cbBufSize) {
    return This->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize);
}
static inline HRESULT IMFAudioMediaType_SetUnknown(IMFAudioMediaType* This,REFGUID guidKey,IUnknown *pUnknown) {
    return This->lpVtbl->SetUnknown(This,guidKey,pUnknown);
}
static inline HRESULT IMFAudioMediaType_LockStore(IMFAudioMediaType* This) {
    return This->lpVtbl->LockStore(This);
}
static inline HRESULT IMFAudioMediaType_UnlockStore(IMFAudioMediaType* This) {
    return This->lpVtbl->UnlockStore(This);
}
static inline HRESULT IMFAudioMediaType_GetCount(IMFAudioMediaType* This,UINT32 *pcItems) {
    return This->lpVtbl->GetCount(This,pcItems);
}
static inline HRESULT IMFAudioMediaType_GetItemByIndex(IMFAudioMediaType* This,UINT32 unIndex,GUID *pguidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue);
}
static inline HRESULT IMFAudioMediaType_CopyAllItems(IMFAudioMediaType* This,IMFAttributes *pDest) {
    return This->lpVtbl->CopyAllItems(This,pDest);
}
/*** IMFMediaType methods ***/
static inline HRESULT IMFAudioMediaType_GetMajorType(IMFAudioMediaType* This,GUID *pguidMajorType) {
    return This->lpVtbl->GetMajorType(This,pguidMajorType);
}
static inline HRESULT IMFAudioMediaType_IsCompressedFormat(IMFAudioMediaType* This,WINBOOL *pfCompressed) {
    return This->lpVtbl->IsCompressedFormat(This,pfCompressed);
}
static inline HRESULT IMFAudioMediaType_IsEqual(IMFAudioMediaType* This,IMFMediaType *pIMediaType,DWORD *pdwFlags) {
    return This->lpVtbl->IsEqual(This,pIMediaType,pdwFlags);
}
static inline HRESULT IMFAudioMediaType_GetRepresentation(IMFAudioMediaType* This,GUID guidRepresentation,LPVOID *ppvRepresentation) {
    return This->lpVtbl->GetRepresentation(This,guidRepresentation,ppvRepresentation);
}
static inline HRESULT IMFAudioMediaType_FreeRepresentation(IMFAudioMediaType* This,GUID guidRepresentation,LPVOID pvRepresentation) {
    return This->lpVtbl->FreeRepresentation(This,guidRepresentation,pvRepresentation);
}
/*** IMFAudioMediaType methods ***/
static inline const WAVEFORMATEX * IMFAudioMediaType_GetAudioFormat(IMFAudioMediaType* This) {
    return This->lpVtbl->GetAudioFormat(This);
}
#endif
#endif

#endif


#endif  /* __IMFAudioMediaType_INTERFACE_DEFINED__ */


#ifndef _WINGDI_
typedef DWORD RGBQUAD;

typedef struct __WIDL_mfobjects_generated_name_00000021 {
    DWORD biSize;
    LONG biWidth;
    LONG biHeight;
    WORD biPlanes;
    WORD biBitCount;
    DWORD biCompression;
    DWORD biSizeImage;
    LONG biXPelsPerMeter;
    LONG biYPelsPerMeter;
    DWORD biClrUsed;
    DWORD biClrImportant;
} BITMAPINFOHEADER;

typedef struct __WIDL_mfobjects_generated_name_00000022 {
    BITMAPINFOHEADER bmiHeader;
    RGBQUAD bmiColors[1];
} BITMAPINFO;
#endif

typedef struct __WIDL_mfobjects_generated_name_00000023 {
    GUID guidMajorType;
    GUID guidSubtype;
} MFT_REGISTER_TYPE_INFO;
#endif
#ifndef _MFVIDEOFORMAT_
#define _MFVIDEOFORMAT_

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
typedef enum _MFVideoInterlaceMode {
    MFVideoInterlace_Unknown = 0,
    MFVideoInterlace_Progressive = 2,
    MFVideoInterlace_FieldInterleavedUpperFirst = 3,
    MFVideoInterlace_FieldInterleavedLowerFirst = 4,
    MFVideoInterlace_FieldSingleUpper = 5,
    MFVideoInterlace_FieldSingleLower = 6,
    MFVideoInterlace_MixedInterlaceOrProgressive = 7,
    MFVideoInterlace_Last = 8,
    MFVideoInterlace_ForceDWORD = 0x7fffffff
} MFVideoInterlaceMode;

#define MFVideoInterlace_FieldSingleUpperFirst MFVideoInterlace_FieldSingleUpper
#define MFVideoInterlace_FieldSingleLowerFirst MFVideoInterlace_FieldSingleLower

typedef enum _MFVideoTransferFunction {
    MFVideoTransFunc_Unknown = 0,
    MFVideoTransFunc_10 = 1,
    MFVideoTransFunc_18 = 2,
    MFVideoTransFunc_20 = 3,
    MFVideoTransFunc_22 = 4,
    MFVideoTransFunc_709 = 5,
    MFVideoTransFunc_240M = 6,
    MFVideoTransFunc_sRGB = 7,
    MFVideoTransFunc_28 = 8,
    MFVideoTransFunc_Log_100 = 9,
    MFVideoTransFunc_Log_316 = 10,
    MFVideoTransFunc_709_sym = 11,
    MFVideoTransFunc_2020_const = 12,
    MFVideoTransFunc_2020 = 13,
    MFVideoTransFunc_26 = 14,
    MFVideoTransFunc_2084 = 15,
    MFVideoTransFunc_HLG = 16,
    MFVideoTransFunc_10_rel = 17,
    MFVideoTransFunc_Last = 18,
    MFVideoTransFunc_ForceDWORD = 0x7fffffff
} MFVideoTransferFunction;

typedef enum _MFVideoPrimaries {
    MFVideoPrimaries_Unknown = 0,
    MFVideoPrimaries_reserved = 1,
    MFVideoPrimaries_BT709 = 2,
    MFVideoPrimaries_BT470_2_SysM = 3,
    MFVideoPrimaries_BT470_2_SysBG = 4,
    MFVideoPrimaries_SMPTE170M = 5,
    MFVideoPrimaries_SMPTE240M = 6,
    MFVideoPrimaries_EBU3213 = 7,
    MFVideoPrimaries_SMPTE_C = 8,
    MFVideoPrimaries_BT2020 = 9,
    MFVideoPrimaries_XYZ = 10,
    MFVideoPrimaries_DCI_P3 = 11,
    MFVideoPrimaries_ACES = 12,
    MFVideoPrimaries_Last = 13,
    MFVideoPrimaries_ForceDWORD = 0x7fffffff
} MFVideoPrimaries;

typedef enum _MFVideoLighting {
    MFVideoLighting_Unknown = 0,
    MFVideoLighting_bright = 1,
    MFVideoLighting_office = 2,
    MFVideoLighting_dim = 3,
    MFVideoLighting_dark = 4,
    MFVideoLighting_Last = 5,
    MFVideoLighting_ForceDWORD = 0x7fffffff
} MFVideoLighting;

typedef enum _MFVideoTransferMatrix {
    MFVideoTransferMatrix_Unknown = 0,
    MFVideoTransferMatrix_BT709 = 1,
    MFVideoTransferMatrix_BT601 = 2,
    MFVideoTransferMatrix_SMPTE240M = 3,
    MFVideoTransferMatrix_BT2020_10 = 4,
    MFVideoTransferMatrix_BT2020_12 = 5,
    MFVideoTransferMatrix_Last = 6,
    MFVideoTransferMatrix_ForceDWORD = 0x7fffffff
} MFVideoTransferMatrix;

typedef enum _MFVideoChromaSubsampling {
    MFVideoChromaSubsampling_Unknown = 0,
    MFVideoChromaSubsampling_ProgressiveChroma = 0x8,
    MFVideoChromaSubsampling_Horizontally_Cosited = 0x4,
    MFVideoChromaSubsampling_Vertically_Cosited = 0x2,
    MFVideoChromaSubsampling_Vertically_AlignedChromaPlanes = 0x1,
    MFVideoChromaSubsampling_MPEG2 = MFVideoChromaSubsampling_Horizontally_Cosited | MFVideoChromaSubsampling_Vertically_AlignedChromaPlanes,
    MFVideoChromaSubsampling_MPEG1 = MFVideoChromaSubsampling_Vertically_AlignedChromaPlanes,
    MFVideoChromaSubsampling_DV_PAL = MFVideoChromaSubsampling_Horizontally_Cosited | MFVideoChromaSubsampling_Vertically_Cosited,
    MFVideoChromaSubsampling_Cosited = (MFVideoChromaSubsampling_Horizontally_Cosited | MFVideoChromaSubsampling_Vertically_Cosited) | MFVideoChromaSubsampling_Vertically_AlignedChromaPlanes,
    MFVideoChromaSubsampling_Last = MFVideoChromaSubsampling_Cosited + 1,
    MFVideoChromaSubsampling_ForceDWORD = 0x7fffffff
} MFVideoChromaSubsampling;

typedef enum _MFNominalRange {
    MFNominalRange_Unknown = 0,
    MFNominalRange_Normal = 1,
    MFNominalRange_Wide = 2,
    MFNominalRange_0_255 = 1,
    MFNominalRange_16_235 = 2,
    MFNominalRange_48_208 = 3,
    MFNominalRange_64_127 = 4,
    MFNominalRange_Last = 5,
    MFNominalRange_ForceDWORD = 0x7fffffff
} MFNominalRange;
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
typedef enum _MFVideoFlags {
    MFVideoFlag_PAD_TO_Mask = 0x1 | 0x2,
    MFVideoFlag_PAD_TO_None = 0 * 0x1,
    MFVideoFlag_PAD_TO_4x3 = 1 * 0x1,
    MFVideoFlag_PAD_TO_16x9 = 2 * 0x1,
    MFVideoFlag_SrcContentHintMask = (0x4 | 0x8) | 0x10,
    MFVideoFlag_SrcContentHintNone = 0 * 0x4,
    MFVideoFlag_SrcContentHint16x9 = 1 * 0x4,
    MFVideoFlag_SrcContentHint235_1 = 2 * 0x4,
    MFVideoFlag_AnalogProtected = 0x20,
    MFVideoFlag_DigitallyProtected = 0x40,
    MFVideoFlag_ProgressiveContent = 0x80,
    MFVideoFlag_FieldRepeatCountMask = (0x100 | 0x200) | 0x400,
    MFVideoFlag_FieldRepeatCountShift = 8,
    MFVideoFlag_ProgressiveSeqReset = 0x800,
    MFVideoFlag_PanScanEnabled = 0x20000,
    MFVideoFlag_LowerFieldFirst = 0x40000,
    MFVideoFlag_BottomUpLinearRep = 0x80000,
    MFVideoFlags_DXVASurface = 0x100000,
    MFVideoFlags_RenderTargetSurface = 0x400000,
    MFVideoFlags_ForceQWORD = 0x7fffffff
} MFVideoFlags;
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
typedef struct _MFRatio {
    DWORD Numerator;
    DWORD Denominator;
} MFRatio;

typedef struct _MFOffset {
    WORD fract;
    short value;
} MFOffset;
typedef struct _MFVideoArea {
    MFOffset OffsetX;
    MFOffset OffsetY;
    SIZE Area;
} MFVideoArea;
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
typedef struct _MFVideoInfo {
    DWORD dwWidth;
    DWORD dwHeight;
    MFRatio PixelAspectRatio;
    MFVideoChromaSubsampling SourceChromaSubsampling;
    MFVideoInterlaceMode InterlaceMode;
    MFVideoTransferFunction TransferFunction;
    MFVideoPrimaries ColorPrimaries;
    MFVideoTransferMatrix TransferMatrix;
    MFVideoLighting SourceLighting;
    MFRatio FramesPerSecond;
    MFNominalRange NominalRange;
    MFVideoArea GeometricAperture;
    MFVideoArea MinimumDisplayAperture;
    MFVideoArea PanScanAperture;
    UINT64 VideoFlags;
} MFVideoInfo;
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
typedef struct __MFAYUVSample {
    BYTE bCrValue;
    BYTE bCbValue;
    BYTE bYValue;
    BYTE bSampleAlpha8;
} MFAYUVSample;

typedef struct _MFARGB {
    BYTE rgbBlue;
    BYTE rgbGreen;
    BYTE rgbRed;
    BYTE rgbAlpha;
} MFARGB;

typedef union _MFPaletteEntry {
    MFARGB ARGB;
    MFAYUVSample AYCbCr;
} MFPaletteEntry;
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
typedef struct _MFVideoSurfaceInfo {
    DWORD Format;
    DWORD PaletteEntries;
    MFPaletteEntry Palette[1];
} MFVideoSurfaceInfo;

typedef struct _MFVideoCompressedInfo {
    LONGLONG AvgBitrate;
    LONGLONG AvgBitErrorRate;
    DWORD MaxKeyFrameSpacing;
} MFVideoCompressedInfo;

typedef struct _MFVIDEOFORMAT {
    DWORD dwSize;
    MFVideoInfo videoInfo;
    GUID guidFormat;
    MFVideoCompressedInfo compressedInfo;
    MFVideoSurfaceInfo surfaceInfo;
} MFVIDEOFORMAT;

typedef enum _MFStandardVideoFormat {
    MFStdVideoFormat_reserved = 0,
    MFStdVideoFormat_NTSC = 1,
    MFStdVideoFormat_PAL = 2,
    MFStdVideoFormat_DVD_NTSC = 3,
    MFStdVideoFormat_DVD_PAL = 4,
    MFStdVideoFormat_DV_PAL = 5,
    MFStdVideoFormat_DV_NTSC = 6,
    MFStdVideoFormat_ATSC_SD480i = 7,
    MFStdVideoFormat_ATSC_HD1080i = 8,
    MFStdVideoFormat_ATSC_HD720p = 9
} MFStandardVideoFormat;
#endif
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IMFVideoMediaType interface
 */
#ifndef __IMFVideoMediaType_INTERFACE_DEFINED__
#define __IMFVideoMediaType_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFVideoMediaType, 0xb99f381f, 0xa8f9, 0x47a2, 0xa5,0xaf, 0xca,0x3a,0x22,0x5a,0x38,0x90);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b99f381f-a8f9-47a2-a5af-ca3a225a3890")
IMFVideoMediaType : public IMFMediaType
{
    virtual const MFVIDEOFORMAT * STDMETHODCALLTYPE GetVideoFormat(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoRepresentation(
        GUID guidRepresentation,
        LPVOID *ppvRepresentation,
        LONG lStride) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFVideoMediaType, 0xb99f381f, 0xa8f9, 0x47a2, 0xa5,0xaf, 0xca,0x3a,0x22,0x5a,0x38,0x90)
#endif
#else
typedef struct IMFVideoMediaTypeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFVideoMediaType *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFVideoMediaType *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFVideoMediaType *This);

    /*** IMFAttributes methods ***/
    HRESULT (STDMETHODCALLTYPE *GetItem)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetItemType)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        MF_ATTRIBUTE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *CompareItem)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        REFPROPVARIANT Value,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        IMFVideoMediaType *This,
        IMFAttributes *pTheirs,
        MF_ATTRIBUTES_MATCH_TYPE MatchType,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *GetUINT32)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        UINT32 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetUINT64)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        UINT64 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetDouble)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        double *pfValue);

    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        GUID *pguidValue);

    HRESULT (STDMETHODCALLTYPE *GetStringLength)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetString)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        LPWSTR pwszValue,
        UINT32 cchBufSize,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedString)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        LPWSTR *ppwszValue,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetBlobSize)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetBlob)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        UINT8 *pBuf,
        UINT32 cbBufSize,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedBlob)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        UINT8 **ppBuf,
        UINT32 *pcbSize);

    HRESULT (STDMETHODCALLTYPE *GetUnknown)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        REFIID riid,
        LPVOID *ppv);

    HRESULT (STDMETHODCALLTYPE *SetItem)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        REFPROPVARIANT Value);

    HRESULT (STDMETHODCALLTYPE *DeleteItem)(
        IMFVideoMediaType *This,
        REFGUID guidKey);

    HRESULT (STDMETHODCALLTYPE *DeleteAllItems)(
        IMFVideoMediaType *This);

    HRESULT (STDMETHODCALLTYPE *SetUINT32)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        UINT32 unValue);

    HRESULT (STDMETHODCALLTYPE *SetUINT64)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        UINT64 unValue);

    HRESULT (STDMETHODCALLTYPE *SetDouble)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        double fValue);

    HRESULT (STDMETHODCALLTYPE *SetGUID)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        REFGUID guidValue);

    HRESULT (STDMETHODCALLTYPE *SetString)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SetBlob)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        const UINT8 *pBuf,
        UINT32 cbBufSize);

    HRESULT (STDMETHODCALLTYPE *SetUnknown)(
        IMFVideoMediaType *This,
        REFGUID guidKey,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *LockStore)(
        IMFVideoMediaType *This);

    HRESULT (STDMETHODCALLTYPE *UnlockStore)(
        IMFVideoMediaType *This);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IMFVideoMediaType *This,
        UINT32 *pcItems);

    HRESULT (STDMETHODCALLTYPE *GetItemByIndex)(
        IMFVideoMediaType *This,
        UINT32 unIndex,
        GUID *pguidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *CopyAllItems)(
        IMFVideoMediaType *This,
        IMFAttributes *pDest);

    /*** IMFMediaType methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMajorType)(
        IMFVideoMediaType *This,
        GUID *pguidMajorType);

    HRESULT (STDMETHODCALLTYPE *IsCompressedFormat)(
        IMFVideoMediaType *This,
        WINBOOL *pfCompressed);

    HRESULT (STDMETHODCALLTYPE *IsEqual)(
        IMFVideoMediaType *This,
        IMFMediaType *pIMediaType,
        DWORD *pdwFlags);

    HRESULT (STDMETHODCALLTYPE *GetRepresentation)(
        IMFVideoMediaType *This,
        GUID guidRepresentation,
        LPVOID *ppvRepresentation);

    HRESULT (STDMETHODCALLTYPE *FreeRepresentation)(
        IMFVideoMediaType *This,
        GUID guidRepresentation,
        LPVOID pvRepresentation);

    /*** IMFVideoMediaType methods ***/
    const MFVIDEOFORMAT * (STDMETHODCALLTYPE *GetVideoFormat)(
        IMFVideoMediaType *This);

    HRESULT (STDMETHODCALLTYPE *GetVideoRepresentation)(
        IMFVideoMediaType *This,
        GUID guidRepresentation,
        LPVOID *ppvRepresentation,
        LONG lStride);

    END_INTERFACE
} IMFVideoMediaTypeVtbl;

interface IMFVideoMediaType {
    CONST_VTBL IMFVideoMediaTypeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFVideoMediaType_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFVideoMediaType_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFVideoMediaType_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAttributes methods ***/
#define IMFVideoMediaType_GetItem(This,guidKey,pValue) (This)->lpVtbl->GetItem(This,guidKey,pValue)
#define IMFVideoMediaType_GetItemType(This,guidKey,pType) (This)->lpVtbl->GetItemType(This,guidKey,pType)
#define IMFVideoMediaType_CompareItem(This,guidKey,Value,pbResult) (This)->lpVtbl->CompareItem(This,guidKey,Value,pbResult)
#define IMFVideoMediaType_Compare(This,pTheirs,MatchType,pbResult) (This)->lpVtbl->Compare(This,pTheirs,MatchType,pbResult)
#define IMFVideoMediaType_GetUINT32(This,guidKey,punValue) (This)->lpVtbl->GetUINT32(This,guidKey,punValue)
#define IMFVideoMediaType_GetUINT64(This,guidKey,punValue) (This)->lpVtbl->GetUINT64(This,guidKey,punValue)
#define IMFVideoMediaType_GetDouble(This,guidKey,pfValue) (This)->lpVtbl->GetDouble(This,guidKey,pfValue)
#define IMFVideoMediaType_GetGUID(This,guidKey,pguidValue) (This)->lpVtbl->GetGUID(This,guidKey,pguidValue)
#define IMFVideoMediaType_GetStringLength(This,guidKey,pcchLength) (This)->lpVtbl->GetStringLength(This,guidKey,pcchLength)
#define IMFVideoMediaType_GetString(This,guidKey,pwszValue,cchBufSize,pcchLength) (This)->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength)
#define IMFVideoMediaType_GetAllocatedString(This,guidKey,ppwszValue,pcchLength) (This)->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength)
#define IMFVideoMediaType_GetBlobSize(This,guidKey,pcbBlobSize) (This)->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize)
#define IMFVideoMediaType_GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize) (This)->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize)
#define IMFVideoMediaType_GetAllocatedBlob(This,guidKey,ppBuf,pcbSize) (This)->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize)
#define IMFVideoMediaType_GetUnknown(This,guidKey,riid,ppv) (This)->lpVtbl->GetUnknown(This,guidKey,riid,ppv)
#define IMFVideoMediaType_SetItem(This,guidKey,Value) (This)->lpVtbl->SetItem(This,guidKey,Value)
#define IMFVideoMediaType_DeleteItem(This,guidKey) (This)->lpVtbl->DeleteItem(This,guidKey)
#define IMFVideoMediaType_DeleteAllItems(This) (This)->lpVtbl->DeleteAllItems(This)
#define IMFVideoMediaType_SetUINT32(This,guidKey,unValue) (This)->lpVtbl->SetUINT32(This,guidKey,unValue)
#define IMFVideoMediaType_SetUINT64(This,guidKey,unValue) (This)->lpVtbl->SetUINT64(This,guidKey,unValue)
#define IMFVideoMediaType_SetDouble(This,guidKey,fValue) (This)->lpVtbl->SetDouble(This,guidKey,fValue)
#define IMFVideoMediaType_SetGUID(This,guidKey,guidValue) (This)->lpVtbl->SetGUID(This,guidKey,guidValue)
#define IMFVideoMediaType_SetString(This,guidKey,wszValue) (This)->lpVtbl->SetString(This,guidKey,wszValue)
#define IMFVideoMediaType_SetBlob(This,guidKey,pBuf,cbBufSize) (This)->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize)
#define IMFVideoMediaType_SetUnknown(This,guidKey,pUnknown) (This)->lpVtbl->SetUnknown(This,guidKey,pUnknown)
#define IMFVideoMediaType_LockStore(This) (This)->lpVtbl->LockStore(This)
#define IMFVideoMediaType_UnlockStore(This) (This)->lpVtbl->UnlockStore(This)
#define IMFVideoMediaType_GetCount(This,pcItems) (This)->lpVtbl->GetCount(This,pcItems)
#define IMFVideoMediaType_GetItemByIndex(This,unIndex,pguidKey,pValue) (This)->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue)
#define IMFVideoMediaType_CopyAllItems(This,pDest) (This)->lpVtbl->CopyAllItems(This,pDest)
/*** IMFMediaType methods ***/
#define IMFVideoMediaType_GetMajorType(This,pguidMajorType) (This)->lpVtbl->GetMajorType(This,pguidMajorType)
#define IMFVideoMediaType_IsCompressedFormat(This,pfCompressed) (This)->lpVtbl->IsCompressedFormat(This,pfCompressed)
#define IMFVideoMediaType_IsEqual(This,pIMediaType,pdwFlags) (This)->lpVtbl->IsEqual(This,pIMediaType,pdwFlags)
#define IMFVideoMediaType_GetRepresentation(This,guidRepresentation,ppvRepresentation) (This)->lpVtbl->GetRepresentation(This,guidRepresentation,ppvRepresentation)
#define IMFVideoMediaType_FreeRepresentation(This,guidRepresentation,pvRepresentation) (This)->lpVtbl->FreeRepresentation(This,guidRepresentation,pvRepresentation)
/*** IMFVideoMediaType methods ***/
#define IMFVideoMediaType_GetVideoFormat(This) (This)->lpVtbl->GetVideoFormat(This)
#define IMFVideoMediaType_GetVideoRepresentation(This,guidRepresentation,ppvRepresentation,lStride) (This)->lpVtbl->GetVideoRepresentation(This,guidRepresentation,ppvRepresentation,lStride)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFVideoMediaType_QueryInterface(IMFVideoMediaType* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFVideoMediaType_AddRef(IMFVideoMediaType* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFVideoMediaType_Release(IMFVideoMediaType* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAttributes methods ***/
static inline HRESULT IMFVideoMediaType_GetItem(IMFVideoMediaType* This,REFGUID guidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItem(This,guidKey,pValue);
}
static inline HRESULT IMFVideoMediaType_GetItemType(IMFVideoMediaType* This,REFGUID guidKey,MF_ATTRIBUTE_TYPE *pType) {
    return This->lpVtbl->GetItemType(This,guidKey,pType);
}
static inline HRESULT IMFVideoMediaType_CompareItem(IMFVideoMediaType* This,REFGUID guidKey,REFPROPVARIANT Value,WINBOOL *pbResult) {
    return This->lpVtbl->CompareItem(This,guidKey,Value,pbResult);
}
static inline HRESULT IMFVideoMediaType_Compare(IMFVideoMediaType* This,IMFAttributes *pTheirs,MF_ATTRIBUTES_MATCH_TYPE MatchType,WINBOOL *pbResult) {
    return This->lpVtbl->Compare(This,pTheirs,MatchType,pbResult);
}
static inline HRESULT IMFVideoMediaType_GetUINT32(IMFVideoMediaType* This,REFGUID guidKey,UINT32 *punValue) {
    return This->lpVtbl->GetUINT32(This,guidKey,punValue);
}
static inline HRESULT IMFVideoMediaType_GetUINT64(IMFVideoMediaType* This,REFGUID guidKey,UINT64 *punValue) {
    return This->lpVtbl->GetUINT64(This,guidKey,punValue);
}
static inline HRESULT IMFVideoMediaType_GetDouble(IMFVideoMediaType* This,REFGUID guidKey,double *pfValue) {
    return This->lpVtbl->GetDouble(This,guidKey,pfValue);
}
static inline HRESULT IMFVideoMediaType_GetGUID(IMFVideoMediaType* This,REFGUID guidKey,GUID *pguidValue) {
    return This->lpVtbl->GetGUID(This,guidKey,pguidValue);
}
static inline HRESULT IMFVideoMediaType_GetStringLength(IMFVideoMediaType* This,REFGUID guidKey,UINT32 *pcchLength) {
    return This->lpVtbl->GetStringLength(This,guidKey,pcchLength);
}
static inline HRESULT IMFVideoMediaType_GetString(IMFVideoMediaType* This,REFGUID guidKey,LPWSTR pwszValue,UINT32 cchBufSize,UINT32 *pcchLength) {
    return This->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength);
}
static inline HRESULT IMFVideoMediaType_GetAllocatedString(IMFVideoMediaType* This,REFGUID guidKey,LPWSTR *ppwszValue,UINT32 *pcchLength) {
    return This->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength);
}
static inline HRESULT IMFVideoMediaType_GetBlobSize(IMFVideoMediaType* This,REFGUID guidKey,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize);
}
static inline HRESULT IMFVideoMediaType_GetBlob(IMFVideoMediaType* This,REFGUID guidKey,UINT8 *pBuf,UINT32 cbBufSize,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize);
}
static inline HRESULT IMFVideoMediaType_GetAllocatedBlob(IMFVideoMediaType* This,REFGUID guidKey,UINT8 **ppBuf,UINT32 *pcbSize) {
    return This->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize);
}
static inline HRESULT IMFVideoMediaType_GetUnknown(IMFVideoMediaType* This,REFGUID guidKey,REFIID riid,LPVOID *ppv) {
    return This->lpVtbl->GetUnknown(This,guidKey,riid,ppv);
}
static inline HRESULT IMFVideoMediaType_SetItem(IMFVideoMediaType* This,REFGUID guidKey,REFPROPVARIANT Value) {
    return This->lpVtbl->SetItem(This,guidKey,Value);
}
static inline HRESULT IMFVideoMediaType_DeleteItem(IMFVideoMediaType* This,REFGUID guidKey) {
    return This->lpVtbl->DeleteItem(This,guidKey);
}
static inline HRESULT IMFVideoMediaType_DeleteAllItems(IMFVideoMediaType* This) {
    return This->lpVtbl->DeleteAllItems(This);
}
static inline HRESULT IMFVideoMediaType_SetUINT32(IMFVideoMediaType* This,REFGUID guidKey,UINT32 unValue) {
    return This->lpVtbl->SetUINT32(This,guidKey,unValue);
}
static inline HRESULT IMFVideoMediaType_SetUINT64(IMFVideoMediaType* This,REFGUID guidKey,UINT64 unValue) {
    return This->lpVtbl->SetUINT64(This,guidKey,unValue);
}
static inline HRESULT IMFVideoMediaType_SetDouble(IMFVideoMediaType* This,REFGUID guidKey,double fValue) {
    return This->lpVtbl->SetDouble(This,guidKey,fValue);
}
static inline HRESULT IMFVideoMediaType_SetGUID(IMFVideoMediaType* This,REFGUID guidKey,REFGUID guidValue) {
    return This->lpVtbl->SetGUID(This,guidKey,guidValue);
}
static inline HRESULT IMFVideoMediaType_SetString(IMFVideoMediaType* This,REFGUID guidKey,LPCWSTR wszValue) {
    return This->lpVtbl->SetString(This,guidKey,wszValue);
}
static inline HRESULT IMFVideoMediaType_SetBlob(IMFVideoMediaType* This,REFGUID guidKey,const UINT8 *pBuf,UINT32 cbBufSize) {
    return This->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize);
}
static inline HRESULT IMFVideoMediaType_SetUnknown(IMFVideoMediaType* This,REFGUID guidKey,IUnknown *pUnknown) {
    return This->lpVtbl->SetUnknown(This,guidKey,pUnknown);
}
static inline HRESULT IMFVideoMediaType_LockStore(IMFVideoMediaType* This) {
    return This->lpVtbl->LockStore(This);
}
static inline HRESULT IMFVideoMediaType_UnlockStore(IMFVideoMediaType* This) {
    return This->lpVtbl->UnlockStore(This);
}
static inline HRESULT IMFVideoMediaType_GetCount(IMFVideoMediaType* This,UINT32 *pcItems) {
    return This->lpVtbl->GetCount(This,pcItems);
}
static inline HRESULT IMFVideoMediaType_GetItemByIndex(IMFVideoMediaType* This,UINT32 unIndex,GUID *pguidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue);
}
static inline HRESULT IMFVideoMediaType_CopyAllItems(IMFVideoMediaType* This,IMFAttributes *pDest) {
    return This->lpVtbl->CopyAllItems(This,pDest);
}
/*** IMFMediaType methods ***/
static inline HRESULT IMFVideoMediaType_GetMajorType(IMFVideoMediaType* This,GUID *pguidMajorType) {
    return This->lpVtbl->GetMajorType(This,pguidMajorType);
}
static inline HRESULT IMFVideoMediaType_IsCompressedFormat(IMFVideoMediaType* This,WINBOOL *pfCompressed) {
    return This->lpVtbl->IsCompressedFormat(This,pfCompressed);
}
static inline HRESULT IMFVideoMediaType_IsEqual(IMFVideoMediaType* This,IMFMediaType *pIMediaType,DWORD *pdwFlags) {
    return This->lpVtbl->IsEqual(This,pIMediaType,pdwFlags);
}
static inline HRESULT IMFVideoMediaType_GetRepresentation(IMFVideoMediaType* This,GUID guidRepresentation,LPVOID *ppvRepresentation) {
    return This->lpVtbl->GetRepresentation(This,guidRepresentation,ppvRepresentation);
}
static inline HRESULT IMFVideoMediaType_FreeRepresentation(IMFVideoMediaType* This,GUID guidRepresentation,LPVOID pvRepresentation) {
    return This->lpVtbl->FreeRepresentation(This,guidRepresentation,pvRepresentation);
}
/*** IMFVideoMediaType methods ***/
static inline const MFVIDEOFORMAT * IMFVideoMediaType_GetVideoFormat(IMFVideoMediaType* This) {
    return This->lpVtbl->GetVideoFormat(This);
}
static inline HRESULT IMFVideoMediaType_GetVideoRepresentation(IMFVideoMediaType* This,GUID guidRepresentation,LPVOID *ppvRepresentation,LONG lStride) {
    return This->lpVtbl->GetVideoRepresentation(This,guidRepresentation,ppvRepresentation,lStride);
}
#endif
#endif

#endif


#endif  /* __IMFVideoMediaType_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IMFAsyncResult interface
 */
#ifndef __IMFAsyncResult_INTERFACE_DEFINED__
#define __IMFAsyncResult_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFAsyncResult, 0xac6b7889, 0x0740, 0x4d51, 0x86,0x19, 0x90,0x59,0x94,0xa5,0x5c,0xc6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ac6b7889-0740-4d51-8619-905994a55cc6")
IMFAsyncResult : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetState(
        IUnknown **ppunkState) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStatus(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStatus(
        HRESULT hrStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetObject(
        IUnknown **ppObject) = 0;

    virtual IUnknown * STDMETHODCALLTYPE GetStateNoAddRef(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFAsyncResult, 0xac6b7889, 0x0740, 0x4d51, 0x86,0x19, 0x90,0x59,0x94,0xa5,0x5c,0xc6)
#endif
#else
typedef struct IMFAsyncResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFAsyncResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFAsyncResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFAsyncResult *This);

    /*** IMFAsyncResult methods ***/
    HRESULT (STDMETHODCALLTYPE *GetState)(
        IMFAsyncResult *This,
        IUnknown **ppunkState);

    HRESULT (STDMETHODCALLTYPE *GetStatus)(
        IMFAsyncResult *This);

    HRESULT (STDMETHODCALLTYPE *SetStatus)(
        IMFAsyncResult *This,
        HRESULT hrStatus);

    HRESULT (STDMETHODCALLTYPE *GetObject)(
        IMFAsyncResult *This,
        IUnknown **ppObject);

    IUnknown * (STDMETHODCALLTYPE *GetStateNoAddRef)(
        IMFAsyncResult *This);

    END_INTERFACE
} IMFAsyncResultVtbl;

interface IMFAsyncResult {
    CONST_VTBL IMFAsyncResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFAsyncResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFAsyncResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFAsyncResult_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAsyncResult methods ***/
#define IMFAsyncResult_GetState(This,ppunkState) (This)->lpVtbl->GetState(This,ppunkState)
#define IMFAsyncResult_GetStatus(This) (This)->lpVtbl->GetStatus(This)
#define IMFAsyncResult_SetStatus(This,hrStatus) (This)->lpVtbl->SetStatus(This,hrStatus)
#define IMFAsyncResult_GetObject(This,ppObject) (This)->lpVtbl->GetObject(This,ppObject)
#define IMFAsyncResult_GetStateNoAddRef(This) (This)->lpVtbl->GetStateNoAddRef(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFAsyncResult_QueryInterface(IMFAsyncResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFAsyncResult_AddRef(IMFAsyncResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFAsyncResult_Release(IMFAsyncResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAsyncResult methods ***/
static inline HRESULT IMFAsyncResult_GetState(IMFAsyncResult* This,IUnknown **ppunkState) {
    return This->lpVtbl->GetState(This,ppunkState);
}
static inline HRESULT IMFAsyncResult_GetStatus(IMFAsyncResult* This) {
    return This->lpVtbl->GetStatus(This);
}
static inline HRESULT IMFAsyncResult_SetStatus(IMFAsyncResult* This,HRESULT hrStatus) {
    return This->lpVtbl->SetStatus(This,hrStatus);
}
static inline HRESULT IMFAsyncResult_GetObject(IMFAsyncResult* This,IUnknown **ppObject) {
    return This->lpVtbl->GetObject(This,ppObject);
}
static inline IUnknown * IMFAsyncResult_GetStateNoAddRef(IMFAsyncResult* This) {
    return This->lpVtbl->GetStateNoAddRef(This);
}
#endif
#endif

#endif


#endif  /* __IMFAsyncResult_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFAsyncCallback interface
 */
#ifndef __IMFAsyncCallback_INTERFACE_DEFINED__
#define __IMFAsyncCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFAsyncCallback, 0xa27003cf, 0x2354, 0x4f2a, 0x8d,0x6a, 0xab,0x7c,0xff,0x15,0x43,0x7e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a27003cf-2354-4f2a-8d6a-ab7cff15437e")
IMFAsyncCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetParameters(
        DWORD *pdwFlags,
        DWORD *pdwQueue) = 0;

    virtual HRESULT STDMETHODCALLTYPE Invoke(
        IMFAsyncResult *pAsyncResult) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFAsyncCallback, 0xa27003cf, 0x2354, 0x4f2a, 0x8d,0x6a, 0xab,0x7c,0xff,0x15,0x43,0x7e)
#endif
#else
typedef struct IMFAsyncCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFAsyncCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFAsyncCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFAsyncCallback *This);

    /*** IMFAsyncCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *GetParameters)(
        IMFAsyncCallback *This,
        DWORD *pdwFlags,
        DWORD *pdwQueue);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IMFAsyncCallback *This,
        IMFAsyncResult *pAsyncResult);

    END_INTERFACE
} IMFAsyncCallbackVtbl;

interface IMFAsyncCallback {
    CONST_VTBL IMFAsyncCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFAsyncCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFAsyncCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFAsyncCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAsyncCallback methods ***/
#define IMFAsyncCallback_GetParameters(This,pdwFlags,pdwQueue) (This)->lpVtbl->GetParameters(This,pdwFlags,pdwQueue)
#define IMFAsyncCallback_Invoke(This,pAsyncResult) (This)->lpVtbl->Invoke(This,pAsyncResult)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFAsyncCallback_QueryInterface(IMFAsyncCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFAsyncCallback_AddRef(IMFAsyncCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFAsyncCallback_Release(IMFAsyncCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAsyncCallback methods ***/
static inline HRESULT IMFAsyncCallback_GetParameters(IMFAsyncCallback* This,DWORD *pdwFlags,DWORD *pdwQueue) {
    return This->lpVtbl->GetParameters(This,pdwFlags,pdwQueue);
}
static inline HRESULT IMFAsyncCallback_Invoke(IMFAsyncCallback* This,IMFAsyncResult *pAsyncResult) {
    return This->lpVtbl->Invoke(This,pAsyncResult);
}
#endif
#endif

#endif


#endif  /* __IMFAsyncCallback_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IMFAsyncCallbackLogging interface
 */
#ifndef __IMFAsyncCallbackLogging_INTERFACE_DEFINED__
#define __IMFAsyncCallbackLogging_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFAsyncCallbackLogging, 0xc7a4dca1, 0xf5f0, 0x47b6, 0xb9,0x2b, 0xbf,0x01,0x06,0xd2,0x57,0x91);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c7a4dca1-f5f0-47b6-b92b-bf0106d25791")
IMFAsyncCallbackLogging : public IMFAsyncCallback
{
    virtual void * STDMETHODCALLTYPE GetObjectPointer(
        ) = 0;

    virtual DWORD STDMETHODCALLTYPE GetObjectTag(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFAsyncCallbackLogging, 0xc7a4dca1, 0xf5f0, 0x47b6, 0xb9,0x2b, 0xbf,0x01,0x06,0xd2,0x57,0x91)
#endif
#else
typedef struct IMFAsyncCallbackLoggingVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFAsyncCallbackLogging *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFAsyncCallbackLogging *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFAsyncCallbackLogging *This);

    /*** IMFAsyncCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *GetParameters)(
        IMFAsyncCallbackLogging *This,
        DWORD *pdwFlags,
        DWORD *pdwQueue);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IMFAsyncCallbackLogging *This,
        IMFAsyncResult *pAsyncResult);

    /*** IMFAsyncCallbackLogging methods ***/
    void * (STDMETHODCALLTYPE *GetObjectPointer)(
        IMFAsyncCallbackLogging *This);

    DWORD (STDMETHODCALLTYPE *GetObjectTag)(
        IMFAsyncCallbackLogging *This);

    END_INTERFACE
} IMFAsyncCallbackLoggingVtbl;

interface IMFAsyncCallbackLogging {
    CONST_VTBL IMFAsyncCallbackLoggingVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFAsyncCallbackLogging_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFAsyncCallbackLogging_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFAsyncCallbackLogging_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAsyncCallback methods ***/
#define IMFAsyncCallbackLogging_GetParameters(This,pdwFlags,pdwQueue) (This)->lpVtbl->GetParameters(This,pdwFlags,pdwQueue)
#define IMFAsyncCallbackLogging_Invoke(This,pAsyncResult) (This)->lpVtbl->Invoke(This,pAsyncResult)
/*** IMFAsyncCallbackLogging methods ***/
#define IMFAsyncCallbackLogging_GetObjectPointer(This) (This)->lpVtbl->GetObjectPointer(This)
#define IMFAsyncCallbackLogging_GetObjectTag(This) (This)->lpVtbl->GetObjectTag(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFAsyncCallbackLogging_QueryInterface(IMFAsyncCallbackLogging* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFAsyncCallbackLogging_AddRef(IMFAsyncCallbackLogging* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFAsyncCallbackLogging_Release(IMFAsyncCallbackLogging* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAsyncCallback methods ***/
static inline HRESULT IMFAsyncCallbackLogging_GetParameters(IMFAsyncCallbackLogging* This,DWORD *pdwFlags,DWORD *pdwQueue) {
    return This->lpVtbl->GetParameters(This,pdwFlags,pdwQueue);
}
static inline HRESULT IMFAsyncCallbackLogging_Invoke(IMFAsyncCallbackLogging* This,IMFAsyncResult *pAsyncResult) {
    return This->lpVtbl->Invoke(This,pAsyncResult);
}
/*** IMFAsyncCallbackLogging methods ***/
static inline void * IMFAsyncCallbackLogging_GetObjectPointer(IMFAsyncCallbackLogging* This) {
    return This->lpVtbl->GetObjectPointer(This);
}
static inline DWORD IMFAsyncCallbackLogging_GetObjectTag(IMFAsyncCallbackLogging* This) {
    return This->lpVtbl->GetObjectTag(This);
}
#endif
#endif

#endif


#endif  /* __IMFAsyncCallbackLogging_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
#define MFASYNC_FAST_IO_PROCESSING_CALLBACK 0x00000001
#define MFASYNC_SIGNAL_CALLBACK 0x00000002
#define MFASYNC_BLOCKING_CALLBACK 0x00000004
#define MFASYNC_REPLY_CALLBACK 0x00000008
#define MFASYNC_LOCALIZE_REMOTE_CALLBACK 0x00000010

#define MFASYNC_CALLBACK_QUEUE_UNDEFINED 0x00000000
#define MFASYNC_CALLBACK_QUEUE_STANDARD 0x00000001
#define MFASYNC_CALLBACK_QUEUE_RT 0x00000002
#define MFASYNC_CALLBACK_QUEUE_IO 0x00000003
#define MFASYNC_CALLBACK_QUEUE_TIMER 0x00000004
#define MFASYNC_CALLBACK_QUEUE_MULTITHREADED 0x00000005
#define MFASYNC_CALLBACK_QUEUE_LONG_FUNCTION 0x00000007
#define MFASYNC_CALLBACK_QUEUE_PRIVATE_MASK 0xFFFF0000
#define MFASYNC_CALLBACK_QUEUE_ALL 0xFFFFFFFF

enum {
    MEUnknown = 0,
    MEError = 1,
    MEExtendedType = 2,
    MENonFatalError = 3,
    MEGenericV1Anchor = MENonFatalError,
    MESessionUnknown = 100,
    MESessionTopologySet = 101,
    MESessionTopologiesCleared = 102,
    MESessionStarted = 103,
    MESessionPaused = 104,
    MESessionStopped = 105,
    MESessionClosed = 106,
    MESessionEnded = 107,
    MESessionRateChanged = 108,
    MESessionScrubSampleComplete = 109,
    MESessionCapabilitiesChanged = 110,
    MESessionTopologyStatus = 111,
    MESessionNotifyPresentationTime = 112,
    MENewPresentation = 113,
    MELicenseAcquisitionStart = 114,
    MELicenseAcquisitionCompleted = 115,
    MEIndividualizationStart = 116,
    MEIndividualizationCompleted = 117,
    MEEnablerProgress = 118,
    MEEnablerCompleted = 119,
    MEPolicyError = 120,
    MEPolicyReport = 121,
    MEBufferingStarted = 122,
    MEBufferingStopped = 123,
    MEConnectStart = 124,
    MEConnectEnd = 125,
    MEReconnectStart = 126,
    MEReconnectEnd = 127,
    MERendererEvent = 128,
    MESessionStreamSinkFormatChanged = 129,
    MESessionV1Anchor = MESessionStreamSinkFormatChanged,
    MESourceUnknown = 200,
    MESourceStarted = 201,
    MEStreamStarted = 202,
    MESourceSeeked = 203,
    MEStreamSeeked = 204,
    MENewStream = 205,
    MEUpdatedStream = 206,
    MESourceStopped = 207,
    MEStreamStopped = 208,
    MESourcePaused = 209,
    MEStreamPaused = 210,
    MEEndOfPresentation = 211,
    MEEndOfStream = 212,
    MEMediaSample = 213,
    MEStreamTick = 214,
    MEStreamThinMode = 215,
    MEStreamFormatChanged = 216,
    MESourceRateChanged = 217,
    MEEndOfPresentationSegment = 218,
    MESourceCharacteristicsChanged = 219,
    MESourceRateChangeRequested = 220,
    MESourceMetadataChanged = 221,
    MESequencerSourceTopologyUpdated = 222,
    MESourceV1Anchor = MESequencerSourceTopologyUpdated,
    MESinkUnknown = 300,
    MEStreamSinkStarted = 301,
    MEStreamSinkStopped = 302,
    MEStreamSinkPaused = 303,
    MEStreamSinkRateChanged = 304,
    MEStreamSinkRequestSample = 305,
    MEStreamSinkMarker = 306,
    MEStreamSinkPrerolled = 307,
    MEStreamSinkScrubSampleComplete = 308,
    MEStreamSinkFormatChanged = 309,
    MEStreamSinkDeviceChanged = 310,
    MEQualityNotify = 311,
    MESinkInvalidated = 312,
    MEAudioSessionNameChanged = 313,
    MEAudioSessionVolumeChanged = 314,
    MEAudioSessionDeviceRemoved = 315,
    MEAudioSessionServerShutdown = 316,
    MEAudioSessionGroupingParamChanged = 317,
    MEAudioSessionIconChanged = 318,
    MEAudioSessionFormatChanged = 319,
    MEAudioSessionDisconnected = 320,
    MEAudioSessionExclusiveModeOverride = 321,
    MESinkV1Anchor = MEAudioSessionExclusiveModeOverride,
    MECaptureAudioSessionVolumeChanged = 322,
    MECaptureAudioSessionDeviceRemoved = 323,
    MECaptureAudioSessionFormatChanged = 324,
    MECaptureAudioSessionDisconnected = 325,
    MECaptureAudioSessionExclusiveModeOverride = 326,
    MECaptureAudioSessionServerShutdown = 327,
    MESinkV2Anchor = MECaptureAudioSessionServerShutdown,
    METrustUnknown = 400,
    MEPolicyChanged = 401,
    MEContentProtectionMessage = 402,
    MEPolicySet = 403,
    METrustV1Anchor = MEPolicySet,
    MEWMDRMLicenseBackupCompleted = 500,
    MEWMDRMLicenseBackupProgress = 501,
    MEWMDRMLicenseRestoreCompleted = 502,
    MEWMDRMLicenseRestoreProgress = 503,
    MEWMDRMLicenseAcquisitionCompleted = 506,
    MEWMDRMIndividualizationCompleted = 508,
    MEWMDRMIndividualizationProgress = 513,
    MEWMDRMProximityCompleted = 514,
    MEWMDRMLicenseStoreCleaned = 515,
    MEWMDRMRevocationDownloadCompleted = 516,
    MEWMDRMV1Anchor = MEWMDRMRevocationDownloadCompleted,
    METransformUnknown = 600,
    METransformNeedInput = 601,
    METransformHaveOutput = 602,
    METransformDrainComplete = 603,
    METransformMarker = 604,
    METransformInputStreamStateChanged = 605,
    MEByteStreamCharacteristicsChanged = 700,
    MEVideoCaptureDeviceRemoved = 800,
    MEVideoCaptureDevicePreempted = 801,
    MEStreamSinkFormatInvalidated = 802,
    MEEncodingParameters = 803,
    MEContentProtectionMetadata = 900,
    MEDeviceThermalStateChanged = 950,
    MEReservedMax = 10000
};

typedef DWORD MediaEventType;

/*****************************************************************************
 * IMFMediaEvent interface
 */
#ifndef __IMFMediaEvent_INTERFACE_DEFINED__
#define __IMFMediaEvent_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaEvent, 0xdf598932, 0xf10c, 0x4e39, 0xbb,0xa2, 0xc3,0x08,0xf1,0x01,0xda,0xa3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("df598932-f10c-4e39-bba2-c308f101daa3")
IMFMediaEvent : public IMFAttributes
{
    virtual HRESULT STDMETHODCALLTYPE GetType(
        MediaEventType *pmet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetExtendedType(
        GUID *pguidExtendedType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStatus(
        HRESULT *phrStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetValue(
        PROPVARIANT *pvValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaEvent, 0xdf598932, 0xf10c, 0x4e39, 0xbb,0xa2, 0xc3,0x08,0xf1,0x01,0xda,0xa3)
#endif
#else
typedef struct IMFMediaEventVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaEvent *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaEvent *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaEvent *This);

    /*** IMFAttributes methods ***/
    HRESULT (STDMETHODCALLTYPE *GetItem)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetItemType)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        MF_ATTRIBUTE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *CompareItem)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        REFPROPVARIANT Value,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        IMFMediaEvent *This,
        IMFAttributes *pTheirs,
        MF_ATTRIBUTES_MATCH_TYPE MatchType,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *GetUINT32)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        UINT32 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetUINT64)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        UINT64 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetDouble)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        double *pfValue);

    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        GUID *pguidValue);

    HRESULT (STDMETHODCALLTYPE *GetStringLength)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetString)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        LPWSTR pwszValue,
        UINT32 cchBufSize,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedString)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        LPWSTR *ppwszValue,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetBlobSize)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetBlob)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        UINT8 *pBuf,
        UINT32 cbBufSize,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedBlob)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        UINT8 **ppBuf,
        UINT32 *pcbSize);

    HRESULT (STDMETHODCALLTYPE *GetUnknown)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        REFIID riid,
        LPVOID *ppv);

    HRESULT (STDMETHODCALLTYPE *SetItem)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        REFPROPVARIANT Value);

    HRESULT (STDMETHODCALLTYPE *DeleteItem)(
        IMFMediaEvent *This,
        REFGUID guidKey);

    HRESULT (STDMETHODCALLTYPE *DeleteAllItems)(
        IMFMediaEvent *This);

    HRESULT (STDMETHODCALLTYPE *SetUINT32)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        UINT32 unValue);

    HRESULT (STDMETHODCALLTYPE *SetUINT64)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        UINT64 unValue);

    HRESULT (STDMETHODCALLTYPE *SetDouble)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        double fValue);

    HRESULT (STDMETHODCALLTYPE *SetGUID)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        REFGUID guidValue);

    HRESULT (STDMETHODCALLTYPE *SetString)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SetBlob)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        const UINT8 *pBuf,
        UINT32 cbBufSize);

    HRESULT (STDMETHODCALLTYPE *SetUnknown)(
        IMFMediaEvent *This,
        REFGUID guidKey,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *LockStore)(
        IMFMediaEvent *This);

    HRESULT (STDMETHODCALLTYPE *UnlockStore)(
        IMFMediaEvent *This);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IMFMediaEvent *This,
        UINT32 *pcItems);

    HRESULT (STDMETHODCALLTYPE *GetItemByIndex)(
        IMFMediaEvent *This,
        UINT32 unIndex,
        GUID *pguidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *CopyAllItems)(
        IMFMediaEvent *This,
        IMFAttributes *pDest);

    /*** IMFMediaEvent methods ***/
    HRESULT (STDMETHODCALLTYPE *GetType)(
        IMFMediaEvent *This,
        MediaEventType *pmet);

    HRESULT (STDMETHODCALLTYPE *GetExtendedType)(
        IMFMediaEvent *This,
        GUID *pguidExtendedType);

    HRESULT (STDMETHODCALLTYPE *GetStatus)(
        IMFMediaEvent *This,
        HRESULT *phrStatus);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        IMFMediaEvent *This,
        PROPVARIANT *pvValue);

    END_INTERFACE
} IMFMediaEventVtbl;

interface IMFMediaEvent {
    CONST_VTBL IMFMediaEventVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaEvent_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaEvent_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaEvent_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAttributes methods ***/
#define IMFMediaEvent_GetItem(This,guidKey,pValue) (This)->lpVtbl->GetItem(This,guidKey,pValue)
#define IMFMediaEvent_GetItemType(This,guidKey,pType) (This)->lpVtbl->GetItemType(This,guidKey,pType)
#define IMFMediaEvent_CompareItem(This,guidKey,Value,pbResult) (This)->lpVtbl->CompareItem(This,guidKey,Value,pbResult)
#define IMFMediaEvent_Compare(This,pTheirs,MatchType,pbResult) (This)->lpVtbl->Compare(This,pTheirs,MatchType,pbResult)
#define IMFMediaEvent_GetUINT32(This,guidKey,punValue) (This)->lpVtbl->GetUINT32(This,guidKey,punValue)
#define IMFMediaEvent_GetUINT64(This,guidKey,punValue) (This)->lpVtbl->GetUINT64(This,guidKey,punValue)
#define IMFMediaEvent_GetDouble(This,guidKey,pfValue) (This)->lpVtbl->GetDouble(This,guidKey,pfValue)
#define IMFMediaEvent_GetGUID(This,guidKey,pguidValue) (This)->lpVtbl->GetGUID(This,guidKey,pguidValue)
#define IMFMediaEvent_GetStringLength(This,guidKey,pcchLength) (This)->lpVtbl->GetStringLength(This,guidKey,pcchLength)
#define IMFMediaEvent_GetString(This,guidKey,pwszValue,cchBufSize,pcchLength) (This)->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength)
#define IMFMediaEvent_GetAllocatedString(This,guidKey,ppwszValue,pcchLength) (This)->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength)
#define IMFMediaEvent_GetBlobSize(This,guidKey,pcbBlobSize) (This)->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize)
#define IMFMediaEvent_GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize) (This)->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize)
#define IMFMediaEvent_GetAllocatedBlob(This,guidKey,ppBuf,pcbSize) (This)->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize)
#define IMFMediaEvent_GetUnknown(This,guidKey,riid,ppv) (This)->lpVtbl->GetUnknown(This,guidKey,riid,ppv)
#define IMFMediaEvent_SetItem(This,guidKey,Value) (This)->lpVtbl->SetItem(This,guidKey,Value)
#define IMFMediaEvent_DeleteItem(This,guidKey) (This)->lpVtbl->DeleteItem(This,guidKey)
#define IMFMediaEvent_DeleteAllItems(This) (This)->lpVtbl->DeleteAllItems(This)
#define IMFMediaEvent_SetUINT32(This,guidKey,unValue) (This)->lpVtbl->SetUINT32(This,guidKey,unValue)
#define IMFMediaEvent_SetUINT64(This,guidKey,unValue) (This)->lpVtbl->SetUINT64(This,guidKey,unValue)
#define IMFMediaEvent_SetDouble(This,guidKey,fValue) (This)->lpVtbl->SetDouble(This,guidKey,fValue)
#define IMFMediaEvent_SetGUID(This,guidKey,guidValue) (This)->lpVtbl->SetGUID(This,guidKey,guidValue)
#define IMFMediaEvent_SetString(This,guidKey,wszValue) (This)->lpVtbl->SetString(This,guidKey,wszValue)
#define IMFMediaEvent_SetBlob(This,guidKey,pBuf,cbBufSize) (This)->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize)
#define IMFMediaEvent_SetUnknown(This,guidKey,pUnknown) (This)->lpVtbl->SetUnknown(This,guidKey,pUnknown)
#define IMFMediaEvent_LockStore(This) (This)->lpVtbl->LockStore(This)
#define IMFMediaEvent_UnlockStore(This) (This)->lpVtbl->UnlockStore(This)
#define IMFMediaEvent_GetCount(This,pcItems) (This)->lpVtbl->GetCount(This,pcItems)
#define IMFMediaEvent_GetItemByIndex(This,unIndex,pguidKey,pValue) (This)->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue)
#define IMFMediaEvent_CopyAllItems(This,pDest) (This)->lpVtbl->CopyAllItems(This,pDest)
/*** IMFMediaEvent methods ***/
#define IMFMediaEvent_GetType(This,pmet) (This)->lpVtbl->GetType(This,pmet)
#define IMFMediaEvent_GetExtendedType(This,pguidExtendedType) (This)->lpVtbl->GetExtendedType(This,pguidExtendedType)
#define IMFMediaEvent_GetStatus(This,phrStatus) (This)->lpVtbl->GetStatus(This,phrStatus)
#define IMFMediaEvent_GetValue(This,pvValue) (This)->lpVtbl->GetValue(This,pvValue)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaEvent_QueryInterface(IMFMediaEvent* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaEvent_AddRef(IMFMediaEvent* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaEvent_Release(IMFMediaEvent* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAttributes methods ***/
static inline HRESULT IMFMediaEvent_GetItem(IMFMediaEvent* This,REFGUID guidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItem(This,guidKey,pValue);
}
static inline HRESULT IMFMediaEvent_GetItemType(IMFMediaEvent* This,REFGUID guidKey,MF_ATTRIBUTE_TYPE *pType) {
    return This->lpVtbl->GetItemType(This,guidKey,pType);
}
static inline HRESULT IMFMediaEvent_CompareItem(IMFMediaEvent* This,REFGUID guidKey,REFPROPVARIANT Value,WINBOOL *pbResult) {
    return This->lpVtbl->CompareItem(This,guidKey,Value,pbResult);
}
static inline HRESULT IMFMediaEvent_Compare(IMFMediaEvent* This,IMFAttributes *pTheirs,MF_ATTRIBUTES_MATCH_TYPE MatchType,WINBOOL *pbResult) {
    return This->lpVtbl->Compare(This,pTheirs,MatchType,pbResult);
}
static inline HRESULT IMFMediaEvent_GetUINT32(IMFMediaEvent* This,REFGUID guidKey,UINT32 *punValue) {
    return This->lpVtbl->GetUINT32(This,guidKey,punValue);
}
static inline HRESULT IMFMediaEvent_GetUINT64(IMFMediaEvent* This,REFGUID guidKey,UINT64 *punValue) {
    return This->lpVtbl->GetUINT64(This,guidKey,punValue);
}
static inline HRESULT IMFMediaEvent_GetDouble(IMFMediaEvent* This,REFGUID guidKey,double *pfValue) {
    return This->lpVtbl->GetDouble(This,guidKey,pfValue);
}
static inline HRESULT IMFMediaEvent_GetGUID(IMFMediaEvent* This,REFGUID guidKey,GUID *pguidValue) {
    return This->lpVtbl->GetGUID(This,guidKey,pguidValue);
}
static inline HRESULT IMFMediaEvent_GetStringLength(IMFMediaEvent* This,REFGUID guidKey,UINT32 *pcchLength) {
    return This->lpVtbl->GetStringLength(This,guidKey,pcchLength);
}
static inline HRESULT IMFMediaEvent_GetString(IMFMediaEvent* This,REFGUID guidKey,LPWSTR pwszValue,UINT32 cchBufSize,UINT32 *pcchLength) {
    return This->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength);
}
static inline HRESULT IMFMediaEvent_GetAllocatedString(IMFMediaEvent* This,REFGUID guidKey,LPWSTR *ppwszValue,UINT32 *pcchLength) {
    return This->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength);
}
static inline HRESULT IMFMediaEvent_GetBlobSize(IMFMediaEvent* This,REFGUID guidKey,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize);
}
static inline HRESULT IMFMediaEvent_GetBlob(IMFMediaEvent* This,REFGUID guidKey,UINT8 *pBuf,UINT32 cbBufSize,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize);
}
static inline HRESULT IMFMediaEvent_GetAllocatedBlob(IMFMediaEvent* This,REFGUID guidKey,UINT8 **ppBuf,UINT32 *pcbSize) {
    return This->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize);
}
static inline HRESULT IMFMediaEvent_GetUnknown(IMFMediaEvent* This,REFGUID guidKey,REFIID riid,LPVOID *ppv) {
    return This->lpVtbl->GetUnknown(This,guidKey,riid,ppv);
}
static inline HRESULT IMFMediaEvent_SetItem(IMFMediaEvent* This,REFGUID guidKey,REFPROPVARIANT Value) {
    return This->lpVtbl->SetItem(This,guidKey,Value);
}
static inline HRESULT IMFMediaEvent_DeleteItem(IMFMediaEvent* This,REFGUID guidKey) {
    return This->lpVtbl->DeleteItem(This,guidKey);
}
static inline HRESULT IMFMediaEvent_DeleteAllItems(IMFMediaEvent* This) {
    return This->lpVtbl->DeleteAllItems(This);
}
static inline HRESULT IMFMediaEvent_SetUINT32(IMFMediaEvent* This,REFGUID guidKey,UINT32 unValue) {
    return This->lpVtbl->SetUINT32(This,guidKey,unValue);
}
static inline HRESULT IMFMediaEvent_SetUINT64(IMFMediaEvent* This,REFGUID guidKey,UINT64 unValue) {
    return This->lpVtbl->SetUINT64(This,guidKey,unValue);
}
static inline HRESULT IMFMediaEvent_SetDouble(IMFMediaEvent* This,REFGUID guidKey,double fValue) {
    return This->lpVtbl->SetDouble(This,guidKey,fValue);
}
static inline HRESULT IMFMediaEvent_SetGUID(IMFMediaEvent* This,REFGUID guidKey,REFGUID guidValue) {
    return This->lpVtbl->SetGUID(This,guidKey,guidValue);
}
static inline HRESULT IMFMediaEvent_SetString(IMFMediaEvent* This,REFGUID guidKey,LPCWSTR wszValue) {
    return This->lpVtbl->SetString(This,guidKey,wszValue);
}
static inline HRESULT IMFMediaEvent_SetBlob(IMFMediaEvent* This,REFGUID guidKey,const UINT8 *pBuf,UINT32 cbBufSize) {
    return This->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize);
}
static inline HRESULT IMFMediaEvent_SetUnknown(IMFMediaEvent* This,REFGUID guidKey,IUnknown *pUnknown) {
    return This->lpVtbl->SetUnknown(This,guidKey,pUnknown);
}
static inline HRESULT IMFMediaEvent_LockStore(IMFMediaEvent* This) {
    return This->lpVtbl->LockStore(This);
}
static inline HRESULT IMFMediaEvent_UnlockStore(IMFMediaEvent* This) {
    return This->lpVtbl->UnlockStore(This);
}
static inline HRESULT IMFMediaEvent_GetCount(IMFMediaEvent* This,UINT32 *pcItems) {
    return This->lpVtbl->GetCount(This,pcItems);
}
static inline HRESULT IMFMediaEvent_GetItemByIndex(IMFMediaEvent* This,UINT32 unIndex,GUID *pguidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue);
}
static inline HRESULT IMFMediaEvent_CopyAllItems(IMFMediaEvent* This,IMFAttributes *pDest) {
    return This->lpVtbl->CopyAllItems(This,pDest);
}
/*** IMFMediaEvent methods ***/
static inline HRESULT IMFMediaEvent_GetType(IMFMediaEvent* This,MediaEventType *pmet) {
    return This->lpVtbl->GetType(This,pmet);
}
static inline HRESULT IMFMediaEvent_GetExtendedType(IMFMediaEvent* This,GUID *pguidExtendedType) {
    return This->lpVtbl->GetExtendedType(This,pguidExtendedType);
}
static inline HRESULT IMFMediaEvent_GetStatus(IMFMediaEvent* This,HRESULT *phrStatus) {
    return This->lpVtbl->GetStatus(This,phrStatus);
}
static inline HRESULT IMFMediaEvent_GetValue(IMFMediaEvent* This,PROPVARIANT *pvValue) {
    return This->lpVtbl->GetValue(This,pvValue);
}
#endif
#endif

#endif


#endif  /* __IMFMediaEvent_INTERFACE_DEFINED__ */


#define MF_EVENT_FLAG_NO_WAIT 0x00000001

#ifndef __IMFRemoteAsyncCallback_FWD_DEFINED__
#define __IMFRemoteAsyncCallback_FWD_DEFINED__
typedef interface IMFRemoteAsyncCallback IMFRemoteAsyncCallback;
#ifdef __cplusplus
interface IMFRemoteAsyncCallback;
#endif /* __cplusplus */
#endif


/*****************************************************************************
 * IMFMediaEventGenerator interface
 */
#ifndef __IMFMediaEventGenerator_INTERFACE_DEFINED__
#define __IMFMediaEventGenerator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaEventGenerator, 0x2cd0bd52, 0xbcd5, 0x4b89, 0xb6,0x2c, 0xea,0xdc,0x0c,0x03,0x1e,0x7d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2cd0bd52-bcd5-4b89-b62c-eadc0c031e7d")
IMFMediaEventGenerator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetEvent(
        DWORD dwFlags,
        IMFMediaEvent **ppEvent) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginGetEvent(
        IMFAsyncCallback *pCallback,
        IUnknown *punkState) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndGetEvent(
        IMFAsyncResult *pResult,
        IMFMediaEvent **ppEvent) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueueEvent(
        MediaEventType met,
        REFGUID guidExtendedType,
        HRESULT hrStatus,
        const PROPVARIANT *pvValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaEventGenerator, 0x2cd0bd52, 0xbcd5, 0x4b89, 0xb6,0x2c, 0xea,0xdc,0x0c,0x03,0x1e,0x7d)
#endif
#else
typedef struct IMFMediaEventGeneratorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaEventGenerator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaEventGenerator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaEventGenerator *This);

    /*** IMFMediaEventGenerator methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEvent)(
        IMFMediaEventGenerator *This,
        DWORD dwFlags,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *BeginGetEvent)(
        IMFMediaEventGenerator *This,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState);

    HRESULT (STDMETHODCALLTYPE *EndGetEvent)(
        IMFMediaEventGenerator *This,
        IMFAsyncResult *pResult,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *QueueEvent)(
        IMFMediaEventGenerator *This,
        MediaEventType met,
        REFGUID guidExtendedType,
        HRESULT hrStatus,
        const PROPVARIANT *pvValue);

    END_INTERFACE
} IMFMediaEventGeneratorVtbl;

interface IMFMediaEventGenerator {
    CONST_VTBL IMFMediaEventGeneratorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaEventGenerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaEventGenerator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaEventGenerator_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaEventGenerator methods ***/
#define IMFMediaEventGenerator_GetEvent(This,dwFlags,ppEvent) (This)->lpVtbl->GetEvent(This,dwFlags,ppEvent)
#define IMFMediaEventGenerator_BeginGetEvent(This,pCallback,punkState) (This)->lpVtbl->BeginGetEvent(This,pCallback,punkState)
#define IMFMediaEventGenerator_EndGetEvent(This,pResult,ppEvent) (This)->lpVtbl->EndGetEvent(This,pResult,ppEvent)
#define IMFMediaEventGenerator_QueueEvent(This,met,guidExtendedType,hrStatus,pvValue) (This)->lpVtbl->QueueEvent(This,met,guidExtendedType,hrStatus,pvValue)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaEventGenerator_QueryInterface(IMFMediaEventGenerator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaEventGenerator_AddRef(IMFMediaEventGenerator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaEventGenerator_Release(IMFMediaEventGenerator* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaEventGenerator methods ***/
static inline HRESULT IMFMediaEventGenerator_GetEvent(IMFMediaEventGenerator* This,DWORD dwFlags,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->GetEvent(This,dwFlags,ppEvent);
}
static inline HRESULT IMFMediaEventGenerator_BeginGetEvent(IMFMediaEventGenerator* This,IMFAsyncCallback *pCallback,IUnknown *punkState) {
    return This->lpVtbl->BeginGetEvent(This,pCallback,punkState);
}
static inline HRESULT IMFMediaEventGenerator_EndGetEvent(IMFMediaEventGenerator* This,IMFAsyncResult *pResult,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->EndGetEvent(This,pResult,ppEvent);
}
static inline HRESULT IMFMediaEventGenerator_QueueEvent(IMFMediaEventGenerator* This,MediaEventType met,REFGUID guidExtendedType,HRESULT hrStatus,const PROPVARIANT *pvValue) {
    return This->lpVtbl->QueueEvent(This,met,guidExtendedType,hrStatus,pvValue);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMFMediaEventGenerator_RemoteBeginGetEvent_Proxy(
    IMFMediaEventGenerator* This,
    IMFRemoteAsyncCallback *pCallback);
void __RPC_STUB IMFMediaEventGenerator_RemoteBeginGetEvent_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFMediaEventGenerator_RemoteEndGetEvent_Proxy(
    IMFMediaEventGenerator* This,
    IUnknown *pResult,
    DWORD *pcbEvent,
    BYTE **ppbEvent);
void __RPC_STUB IMFMediaEventGenerator_RemoteEndGetEvent_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IMFMediaEventGenerator_BeginGetEvent_Proxy(
    IMFMediaEventGenerator* This,
    IMFAsyncCallback *pCallback,
    IUnknown *punkState);
HRESULT __RPC_STUB IMFMediaEventGenerator_BeginGetEvent_Stub(
    IMFMediaEventGenerator* This,
    IMFRemoteAsyncCallback *pCallback);
HRESULT CALLBACK IMFMediaEventGenerator_EndGetEvent_Proxy(
    IMFMediaEventGenerator* This,
    IMFAsyncResult *pResult,
    IMFMediaEvent **ppEvent);
HRESULT __RPC_STUB IMFMediaEventGenerator_EndGetEvent_Stub(
    IMFMediaEventGenerator* This,
    IUnknown *pResult,
    DWORD *pcbEvent,
    BYTE **ppbEvent);

#endif  /* __IMFMediaEventGenerator_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IMFRemoteAsyncCallback interface
 */
#ifndef __IMFRemoteAsyncCallback_INTERFACE_DEFINED__
#define __IMFRemoteAsyncCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFRemoteAsyncCallback, 0xa27003d0, 0x2354, 0x4f2a, 0x8d,0x6a, 0xab,0x7c,0xff,0x15,0x43,0x7e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a27003d0-2354-4f2a-8d6a-ab7cff15437e")
IMFRemoteAsyncCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Invoke(
        HRESULT hr,
        IUnknown *pRemoteResult) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFRemoteAsyncCallback, 0xa27003d0, 0x2354, 0x4f2a, 0x8d,0x6a, 0xab,0x7c,0xff,0x15,0x43,0x7e)
#endif
#else
typedef struct IMFRemoteAsyncCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFRemoteAsyncCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFRemoteAsyncCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFRemoteAsyncCallback *This);

    /*** IMFRemoteAsyncCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IMFRemoteAsyncCallback *This,
        HRESULT hr,
        IUnknown *pRemoteResult);

    END_INTERFACE
} IMFRemoteAsyncCallbackVtbl;

interface IMFRemoteAsyncCallback {
    CONST_VTBL IMFRemoteAsyncCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFRemoteAsyncCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFRemoteAsyncCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFRemoteAsyncCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IMFRemoteAsyncCallback methods ***/
#define IMFRemoteAsyncCallback_Invoke(This,hr,pRemoteResult) (This)->lpVtbl->Invoke(This,hr,pRemoteResult)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFRemoteAsyncCallback_QueryInterface(IMFRemoteAsyncCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFRemoteAsyncCallback_AddRef(IMFRemoteAsyncCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFRemoteAsyncCallback_Release(IMFRemoteAsyncCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFRemoteAsyncCallback methods ***/
static inline HRESULT IMFRemoteAsyncCallback_Invoke(IMFRemoteAsyncCallback* This,HRESULT hr,IUnknown *pRemoteResult) {
    return This->lpVtbl->Invoke(This,hr,pRemoteResult);
}
#endif
#endif

#endif


#endif  /* __IMFRemoteAsyncCallback_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)

typedef enum _MFBYTESTREAM_SEEK_ORIGIN {
    msoBegin = 0,
    msoCurrent = 1
} MFBYTESTREAM_SEEK_ORIGIN;

/*****************************************************************************
 * IMFByteStream interface
 */
#ifndef __IMFByteStream_INTERFACE_DEFINED__
#define __IMFByteStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFByteStream, 0xad4c1b00, 0x4bf7, 0x422f, 0x91,0x75, 0x75,0x66,0x93,0xd9,0x13,0x0d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ad4c1b00-4bf7-422f-9175-756693d9130d")
IMFByteStream : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCapabilities(
        DWORD *pdwCapabilities) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLength(
        QWORD *pqwLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLength(
        QWORD qwLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentPosition(
        QWORD *pqwPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCurrentPosition(
        QWORD qwPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsEndOfStream(
        WINBOOL *pfEndOfStream) = 0;

    virtual HRESULT STDMETHODCALLTYPE Read(
        BYTE *pb,
        ULONG cb,
        ULONG *pcbRead) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginRead(
        BYTE *pb,
        ULONG cb,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndRead(
        IMFAsyncResult *pResult,
        ULONG *pcbRead) = 0;

    virtual HRESULT STDMETHODCALLTYPE Write(
        const BYTE *pb,
        ULONG cb,
        ULONG *pcbWritten) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginWrite(
        const BYTE *pb,
        ULONG cb,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndWrite(
        IMFAsyncResult *pResult,
        ULONG *pcbWritten) = 0;

    virtual HRESULT STDMETHODCALLTYPE Seek(
        MFBYTESTREAM_SEEK_ORIGIN SeekOrigin,
        LONGLONG llSeekOffset,
        DWORD dwSeekFlags,
        QWORD *pqwCurrentPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE Flush(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFByteStream, 0xad4c1b00, 0x4bf7, 0x422f, 0x91,0x75, 0x75,0x66,0x93,0xd9,0x13,0x0d)
#endif
#else
typedef struct IMFByteStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFByteStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFByteStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFByteStream *This);

    /*** IMFByteStream methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCapabilities)(
        IMFByteStream *This,
        DWORD *pdwCapabilities);

    HRESULT (STDMETHODCALLTYPE *GetLength)(
        IMFByteStream *This,
        QWORD *pqwLength);

    HRESULT (STDMETHODCALLTYPE *SetLength)(
        IMFByteStream *This,
        QWORD qwLength);

    HRESULT (STDMETHODCALLTYPE *GetCurrentPosition)(
        IMFByteStream *This,
        QWORD *pqwPosition);

    HRESULT (STDMETHODCALLTYPE *SetCurrentPosition)(
        IMFByteStream *This,
        QWORD qwPosition);

    HRESULT (STDMETHODCALLTYPE *IsEndOfStream)(
        IMFByteStream *This,
        WINBOOL *pfEndOfStream);

    HRESULT (STDMETHODCALLTYPE *Read)(
        IMFByteStream *This,
        BYTE *pb,
        ULONG cb,
        ULONG *pcbRead);

    HRESULT (STDMETHODCALLTYPE *BeginRead)(
        IMFByteStream *This,
        BYTE *pb,
        ULONG cb,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState);

    HRESULT (STDMETHODCALLTYPE *EndRead)(
        IMFByteStream *This,
        IMFAsyncResult *pResult,
        ULONG *pcbRead);

    HRESULT (STDMETHODCALLTYPE *Write)(
        IMFByteStream *This,
        const BYTE *pb,
        ULONG cb,
        ULONG *pcbWritten);

    HRESULT (STDMETHODCALLTYPE *BeginWrite)(
        IMFByteStream *This,
        const BYTE *pb,
        ULONG cb,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState);

    HRESULT (STDMETHODCALLTYPE *EndWrite)(
        IMFByteStream *This,
        IMFAsyncResult *pResult,
        ULONG *pcbWritten);

    HRESULT (STDMETHODCALLTYPE *Seek)(
        IMFByteStream *This,
        MFBYTESTREAM_SEEK_ORIGIN SeekOrigin,
        LONGLONG llSeekOffset,
        DWORD dwSeekFlags,
        QWORD *pqwCurrentPosition);

    HRESULT (STDMETHODCALLTYPE *Flush)(
        IMFByteStream *This);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IMFByteStream *This);

    END_INTERFACE
} IMFByteStreamVtbl;

interface IMFByteStream {
    CONST_VTBL IMFByteStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFByteStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFByteStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFByteStream_Release(This) (This)->lpVtbl->Release(This)
/*** IMFByteStream methods ***/
#define IMFByteStream_GetCapabilities(This,pdwCapabilities) (This)->lpVtbl->GetCapabilities(This,pdwCapabilities)
#define IMFByteStream_GetLength(This,pqwLength) (This)->lpVtbl->GetLength(This,pqwLength)
#define IMFByteStream_SetLength(This,qwLength) (This)->lpVtbl->SetLength(This,qwLength)
#define IMFByteStream_GetCurrentPosition(This,pqwPosition) (This)->lpVtbl->GetCurrentPosition(This,pqwPosition)
#define IMFByteStream_SetCurrentPosition(This,qwPosition) (This)->lpVtbl->SetCurrentPosition(This,qwPosition)
#define IMFByteStream_IsEndOfStream(This,pfEndOfStream) (This)->lpVtbl->IsEndOfStream(This,pfEndOfStream)
#define IMFByteStream_Read(This,pb,cb,pcbRead) (This)->lpVtbl->Read(This,pb,cb,pcbRead)
#define IMFByteStream_BeginRead(This,pb,cb,pCallback,punkState) (This)->lpVtbl->BeginRead(This,pb,cb,pCallback,punkState)
#define IMFByteStream_EndRead(This,pResult,pcbRead) (This)->lpVtbl->EndRead(This,pResult,pcbRead)
#define IMFByteStream_Write(This,pb,cb,pcbWritten) (This)->lpVtbl->Write(This,pb,cb,pcbWritten)
#define IMFByteStream_BeginWrite(This,pb,cb,pCallback,punkState) (This)->lpVtbl->BeginWrite(This,pb,cb,pCallback,punkState)
#define IMFByteStream_EndWrite(This,pResult,pcbWritten) (This)->lpVtbl->EndWrite(This,pResult,pcbWritten)
#define IMFByteStream_Seek(This,SeekOrigin,llSeekOffset,dwSeekFlags,pqwCurrentPosition) (This)->lpVtbl->Seek(This,SeekOrigin,llSeekOffset,dwSeekFlags,pqwCurrentPosition)
#define IMFByteStream_Flush(This) (This)->lpVtbl->Flush(This)
#define IMFByteStream_Close(This) (This)->lpVtbl->Close(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFByteStream_QueryInterface(IMFByteStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFByteStream_AddRef(IMFByteStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFByteStream_Release(IMFByteStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFByteStream methods ***/
static inline HRESULT IMFByteStream_GetCapabilities(IMFByteStream* This,DWORD *pdwCapabilities) {
    return This->lpVtbl->GetCapabilities(This,pdwCapabilities);
}
static inline HRESULT IMFByteStream_GetLength(IMFByteStream* This,QWORD *pqwLength) {
    return This->lpVtbl->GetLength(This,pqwLength);
}
static inline HRESULT IMFByteStream_SetLength(IMFByteStream* This,QWORD qwLength) {
    return This->lpVtbl->SetLength(This,qwLength);
}
static inline HRESULT IMFByteStream_GetCurrentPosition(IMFByteStream* This,QWORD *pqwPosition) {
    return This->lpVtbl->GetCurrentPosition(This,pqwPosition);
}
static inline HRESULT IMFByteStream_SetCurrentPosition(IMFByteStream* This,QWORD qwPosition) {
    return This->lpVtbl->SetCurrentPosition(This,qwPosition);
}
static inline HRESULT IMFByteStream_IsEndOfStream(IMFByteStream* This,WINBOOL *pfEndOfStream) {
    return This->lpVtbl->IsEndOfStream(This,pfEndOfStream);
}
static inline HRESULT IMFByteStream_Read(IMFByteStream* This,BYTE *pb,ULONG cb,ULONG *pcbRead) {
    return This->lpVtbl->Read(This,pb,cb,pcbRead);
}
static inline HRESULT IMFByteStream_BeginRead(IMFByteStream* This,BYTE *pb,ULONG cb,IMFAsyncCallback *pCallback,IUnknown *punkState) {
    return This->lpVtbl->BeginRead(This,pb,cb,pCallback,punkState);
}
static inline HRESULT IMFByteStream_EndRead(IMFByteStream* This,IMFAsyncResult *pResult,ULONG *pcbRead) {
    return This->lpVtbl->EndRead(This,pResult,pcbRead);
}
static inline HRESULT IMFByteStream_Write(IMFByteStream* This,const BYTE *pb,ULONG cb,ULONG *pcbWritten) {
    return This->lpVtbl->Write(This,pb,cb,pcbWritten);
}
static inline HRESULT IMFByteStream_BeginWrite(IMFByteStream* This,const BYTE *pb,ULONG cb,IMFAsyncCallback *pCallback,IUnknown *punkState) {
    return This->lpVtbl->BeginWrite(This,pb,cb,pCallback,punkState);
}
static inline HRESULT IMFByteStream_EndWrite(IMFByteStream* This,IMFAsyncResult *pResult,ULONG *pcbWritten) {
    return This->lpVtbl->EndWrite(This,pResult,pcbWritten);
}
static inline HRESULT IMFByteStream_Seek(IMFByteStream* This,MFBYTESTREAM_SEEK_ORIGIN SeekOrigin,LONGLONG llSeekOffset,DWORD dwSeekFlags,QWORD *pqwCurrentPosition) {
    return This->lpVtbl->Seek(This,SeekOrigin,llSeekOffset,dwSeekFlags,pqwCurrentPosition);
}
static inline HRESULT IMFByteStream_Flush(IMFByteStream* This) {
    return This->lpVtbl->Flush(This);
}
static inline HRESULT IMFByteStream_Close(IMFByteStream* This) {
    return This->lpVtbl->Close(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IMFByteStream_RemoteBeginRead_Proxy(
    IMFByteStream* This,
    ULONG cb,
    IMFRemoteAsyncCallback *pCallback);
void __RPC_STUB IMFByteStream_RemoteBeginRead_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFByteStream_RemoteEndRead_Proxy(
    IMFByteStream* This,
    IUnknown *punkResult,
    BYTE *pb,
    ULONG cb,
    ULONG *pcbRead);
void __RPC_STUB IMFByteStream_RemoteEndRead_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFByteStream_RemoteBeginWrite_Proxy(
    IMFByteStream* This,
    const BYTE *pb,
    ULONG cb,
    IMFRemoteAsyncCallback *pCallback);
void __RPC_STUB IMFByteStream_RemoteBeginWrite_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IMFByteStream_RemoteEndWrite_Proxy(
    IMFByteStream* This,
    IUnknown *punkResult,
    ULONG *pcbWritten);
void __RPC_STUB IMFByteStream_RemoteEndWrite_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IMFByteStream_BeginRead_Proxy(
    IMFByteStream* This,
    BYTE *pb,
    ULONG cb,
    IMFAsyncCallback *pCallback,
    IUnknown *punkState);
HRESULT __RPC_STUB IMFByteStream_BeginRead_Stub(
    IMFByteStream* This,
    ULONG cb,
    IMFRemoteAsyncCallback *pCallback);
HRESULT CALLBACK IMFByteStream_EndRead_Proxy(
    IMFByteStream* This,
    IMFAsyncResult *pResult,
    ULONG *pcbRead);
HRESULT __RPC_STUB IMFByteStream_EndRead_Stub(
    IMFByteStream* This,
    IUnknown *punkResult,
    BYTE *pb,
    ULONG cb,
    ULONG *pcbRead);
HRESULT CALLBACK IMFByteStream_BeginWrite_Proxy(
    IMFByteStream* This,
    const BYTE *pb,
    ULONG cb,
    IMFAsyncCallback *pCallback,
    IUnknown *punkState);
HRESULT __RPC_STUB IMFByteStream_BeginWrite_Stub(
    IMFByteStream* This,
    const BYTE *pb,
    ULONG cb,
    IMFRemoteAsyncCallback *pCallback);
HRESULT CALLBACK IMFByteStream_EndWrite_Proxy(
    IMFByteStream* This,
    IMFAsyncResult *pResult,
    ULONG *pcbWritten);
HRESULT __RPC_STUB IMFByteStream_EndWrite_Stub(
    IMFByteStream* This,
    IUnknown *punkResult,
    ULONG *pcbWritten);

#endif  /* __IMFByteStream_INTERFACE_DEFINED__ */


#define MFBYTESTREAM_IS_READABLE 0x00000001
#define MFBYTESTREAM_IS_WRITABLE 0x00000002
#define MFBYTESTREAM_IS_SEEKABLE 0x00000004
#define MFBYTESTREAM_IS_REMOTE 0x00000008
#define MFBYTESTREAM_IS_DIRECTORY 0x00000080
#define MFBYTESTREAM_HAS_SLOW_SEEK 0x00000100
#define MFBYTESTREAM_IS_PARTIALLY_DOWNLOADED 0x00000200
#if WINVER >= _WIN32_WINNT_WIN7
#define MFBYTESTREAM_SHARE_WRITE 0x00000400
#endif
#if WINVER >= _WIN32_WINNT_WIN8
#define MFBYTESTREAM_DOES_NOT_USE_NETWORK 0x00000800
#endif

#define MFBYTESTREAM_SEEK_FLAG_CANCEL_PENDING_IO 0x00000001

EXTERN_GUID( MF_BYTESTREAM_ORIGIN_NAME, 0xfc358288, 0x3cb6, 0x460c, 0xa4, 0x24, 0xb6, 0x68, 0x12, 0x60, 0x37, 0x5a);
EXTERN_GUID( MF_BYTESTREAM_CONTENT_TYPE, 0xfc358289, 0x3cb6, 0x460c, 0xa4, 0x24, 0xb6, 0x68, 0x12, 0x60, 0x37, 0x5a);
EXTERN_GUID( MF_BYTESTREAM_DURATION, 0xfc35828a, 0x3cb6, 0x460c, 0xa4, 0x24, 0xb6, 0x68, 0x12, 0x60, 0x37, 0x5a);
EXTERN_GUID( MF_BYTESTREAM_LAST_MODIFIED_TIME, 0xfc35828b, 0x3cb6, 0x460c, 0xa4, 0x24, 0xb6, 0x68, 0x12, 0x60, 0x37, 0x5a);
#if (WINVER >= _WIN32_WINNT_WIN7)
EXTERN_GUID( MF_BYTESTREAM_IFO_FILE_URI, 0xfc35828c, 0x3cb6, 0x460c, 0xa4, 0x24, 0xb6, 0x68, 0x12, 0x60, 0x37, 0x5a);
EXTERN_GUID( MF_BYTESTREAM_DLNA_PROFILE_ID, 0xfc35828d, 0x3cb6, 0x460c, 0xa4, 0x24, 0xb6, 0x68, 0x12, 0x60, 0x37, 0x5a);
EXTERN_GUID( MF_BYTESTREAM_EFFECTIVE_URL, 0x9afa0209, 0x89d1, 0x42af, 0x84, 0x56, 0x1d, 0xe6, 0xb5, 0x62, 0xd6, 0x91);
EXTERN_GUID( MF_BYTESTREAM_TRANSCODED, 0xb6c5c282, 0x4dc9, 0x4db9, 0xab, 0x48, 0xcf, 0x3b, 0x6d, 0x8b, 0xc5, 0xe0 );
#endif
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
EXTERN_GUID(CLSID_MFByteStreamProxyClassFactory, 0x770e8e77, 0x4916, 0x441c, 0xa9, 0xa7, 0xb3, 0x42, 0xd0, 0xee, 0xbc, 0x71 );

/*****************************************************************************
 * IMFByteStreamProxyClassFactory interface
 */
#ifndef __IMFByteStreamProxyClassFactory_INTERFACE_DEFINED__
#define __IMFByteStreamProxyClassFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFByteStreamProxyClassFactory, 0xa6b43f84, 0x5c0a, 0x42e8, 0xa4,0x4d, 0xb1,0x85,0x7a,0x76,0x99,0x2f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a6b43f84-5c0a-42e8-a44d-b1857a76992f")
IMFByteStreamProxyClassFactory : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateByteStreamProxy(
        IMFByteStream *pByteStream,
        IMFAttributes *pAttributes,
        REFIID riid,
        LPVOID *ppvObject) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFByteStreamProxyClassFactory, 0xa6b43f84, 0x5c0a, 0x42e8, 0xa4,0x4d, 0xb1,0x85,0x7a,0x76,0x99,0x2f)
#endif
#else
typedef struct IMFByteStreamProxyClassFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFByteStreamProxyClassFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFByteStreamProxyClassFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFByteStreamProxyClassFactory *This);

    /*** IMFByteStreamProxyClassFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateByteStreamProxy)(
        IMFByteStreamProxyClassFactory *This,
        IMFByteStream *pByteStream,
        IMFAttributes *pAttributes,
        REFIID riid,
        LPVOID *ppvObject);

    END_INTERFACE
} IMFByteStreamProxyClassFactoryVtbl;

interface IMFByteStreamProxyClassFactory {
    CONST_VTBL IMFByteStreamProxyClassFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFByteStreamProxyClassFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFByteStreamProxyClassFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFByteStreamProxyClassFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IMFByteStreamProxyClassFactory methods ***/
#define IMFByteStreamProxyClassFactory_CreateByteStreamProxy(This,pByteStream,pAttributes,riid,ppvObject) (This)->lpVtbl->CreateByteStreamProxy(This,pByteStream,pAttributes,riid,ppvObject)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFByteStreamProxyClassFactory_QueryInterface(IMFByteStreamProxyClassFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFByteStreamProxyClassFactory_AddRef(IMFByteStreamProxyClassFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFByteStreamProxyClassFactory_Release(IMFByteStreamProxyClassFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFByteStreamProxyClassFactory methods ***/
static inline HRESULT IMFByteStreamProxyClassFactory_CreateByteStreamProxy(IMFByteStreamProxyClassFactory* This,IMFByteStream *pByteStream,IMFAttributes *pAttributes,REFIID riid,LPVOID *ppvObject) {
    return This->lpVtbl->CreateByteStreamProxy(This,pByteStream,pAttributes,riid,ppvObject);
}
#endif
#endif

#endif


#endif  /* __IMFByteStreamProxyClassFactory_INTERFACE_DEFINED__ */


typedef enum __WIDL_mfobjects_generated_name_00000024 {
    MF_ACCESSMODE_READ = 1,
    MF_ACCESSMODE_WRITE = 2,
    MF_ACCESSMODE_READWRITE = 3
} MF_FILE_ACCESSMODE;

typedef enum __WIDL_mfobjects_generated_name_00000025 {
    MF_OPENMODE_FAIL_IF_NOT_EXIST = 0,
    MF_OPENMODE_FAIL_IF_EXIST = 1,
    MF_OPENMODE_RESET_IF_EXIST = 2,
    MF_OPENMODE_APPEND_IF_EXIST = 3,
    MF_OPENMODE_DELETE_IF_EXIST = 4
} MF_FILE_OPENMODE;

typedef enum __WIDL_mfobjects_generated_name_00000026 {
    MF_FILEFLAGS_NONE = 0x0,
    MF_FILEFLAGS_NOBUFFERING = 0x1,
    MF_FILEFLAGS_ALLOW_WRITE_SHARING = 0x2
} MF_FILE_FLAGS;
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IMFSampleOutputStream interface
 */
#ifndef __IMFSampleOutputStream_INTERFACE_DEFINED__
#define __IMFSampleOutputStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFSampleOutputStream, 0x8feed468, 0x6f7e, 0x440d, 0x86,0x9a, 0x49,0xbd,0xd2,0x83,0xad,0x0d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8feed468-6f7e-440d-869a-49bdd283ad0d")
IMFSampleOutputStream : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE BeginWriteSample(
        IMFSample *pSample,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndWriteSample(
        IMFAsyncResult *pResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFSampleOutputStream, 0x8feed468, 0x6f7e, 0x440d, 0x86,0x9a, 0x49,0xbd,0xd2,0x83,0xad,0x0d)
#endif
#else
typedef struct IMFSampleOutputStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFSampleOutputStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFSampleOutputStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFSampleOutputStream *This);

    /*** IMFSampleOutputStream methods ***/
    HRESULT (STDMETHODCALLTYPE *BeginWriteSample)(
        IMFSampleOutputStream *This,
        IMFSample *pSample,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState);

    HRESULT (STDMETHODCALLTYPE *EndWriteSample)(
        IMFSampleOutputStream *This,
        IMFAsyncResult *pResult);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IMFSampleOutputStream *This);

    END_INTERFACE
} IMFSampleOutputStreamVtbl;

interface IMFSampleOutputStream {
    CONST_VTBL IMFSampleOutputStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFSampleOutputStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFSampleOutputStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFSampleOutputStream_Release(This) (This)->lpVtbl->Release(This)
/*** IMFSampleOutputStream methods ***/
#define IMFSampleOutputStream_BeginWriteSample(This,pSample,pCallback,punkState) (This)->lpVtbl->BeginWriteSample(This,pSample,pCallback,punkState)
#define IMFSampleOutputStream_EndWriteSample(This,pResult) (This)->lpVtbl->EndWriteSample(This,pResult)
#define IMFSampleOutputStream_Close(This) (This)->lpVtbl->Close(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFSampleOutputStream_QueryInterface(IMFSampleOutputStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFSampleOutputStream_AddRef(IMFSampleOutputStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFSampleOutputStream_Release(IMFSampleOutputStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFSampleOutputStream methods ***/
static inline HRESULT IMFSampleOutputStream_BeginWriteSample(IMFSampleOutputStream* This,IMFSample *pSample,IMFAsyncCallback *pCallback,IUnknown *punkState) {
    return This->lpVtbl->BeginWriteSample(This,pSample,pCallback,punkState);
}
static inline HRESULT IMFSampleOutputStream_EndWriteSample(IMFSampleOutputStream* This,IMFAsyncResult *pResult) {
    return This->lpVtbl->EndWriteSample(This,pResult);
}
static inline HRESULT IMFSampleOutputStream_Close(IMFSampleOutputStream* This) {
    return This->lpVtbl->Close(This);
}
#endif
#endif

#endif


#endif  /* __IMFSampleOutputStream_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFCollection interface
 */
#ifndef __IMFCollection_INTERFACE_DEFINED__
#define __IMFCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFCollection, 0x5bc8a76b, 0x869a, 0x46a3, 0x9b,0x03, 0xfa,0x21,0x8a,0x66,0xae,0xbe);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5bc8a76b-869a-46a3-9b03-fa218a66aebe")
IMFCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetElementCount(
        DWORD *pcElements) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetElement(
        DWORD dwElementIndex,
        IUnknown **ppUnkElement) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddElement(
        IUnknown *pUnkElement) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveElement(
        DWORD dwElementIndex,
        IUnknown **ppUnkElement) = 0;

    virtual HRESULT STDMETHODCALLTYPE InsertElementAt(
        DWORD dwIndex,
        IUnknown *pUnknown) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAllElements(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFCollection, 0x5bc8a76b, 0x869a, 0x46a3, 0x9b,0x03, 0xfa,0x21,0x8a,0x66,0xae,0xbe)
#endif
#else
typedef struct IMFCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFCollection *This);

    /*** IMFCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetElementCount)(
        IMFCollection *This,
        DWORD *pcElements);

    HRESULT (STDMETHODCALLTYPE *GetElement)(
        IMFCollection *This,
        DWORD dwElementIndex,
        IUnknown **ppUnkElement);

    HRESULT (STDMETHODCALLTYPE *AddElement)(
        IMFCollection *This,
        IUnknown *pUnkElement);

    HRESULT (STDMETHODCALLTYPE *RemoveElement)(
        IMFCollection *This,
        DWORD dwElementIndex,
        IUnknown **ppUnkElement);

    HRESULT (STDMETHODCALLTYPE *InsertElementAt)(
        IMFCollection *This,
        DWORD dwIndex,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *RemoveAllElements)(
        IMFCollection *This);

    END_INTERFACE
} IMFCollectionVtbl;

interface IMFCollection {
    CONST_VTBL IMFCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IMFCollection methods ***/
#define IMFCollection_GetElementCount(This,pcElements) (This)->lpVtbl->GetElementCount(This,pcElements)
#define IMFCollection_GetElement(This,dwElementIndex,ppUnkElement) (This)->lpVtbl->GetElement(This,dwElementIndex,ppUnkElement)
#define IMFCollection_AddElement(This,pUnkElement) (This)->lpVtbl->AddElement(This,pUnkElement)
#define IMFCollection_RemoveElement(This,dwElementIndex,ppUnkElement) (This)->lpVtbl->RemoveElement(This,dwElementIndex,ppUnkElement)
#define IMFCollection_InsertElementAt(This,dwIndex,pUnknown) (This)->lpVtbl->InsertElementAt(This,dwIndex,pUnknown)
#define IMFCollection_RemoveAllElements(This) (This)->lpVtbl->RemoveAllElements(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFCollection_QueryInterface(IMFCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFCollection_AddRef(IMFCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFCollection_Release(IMFCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFCollection methods ***/
static inline HRESULT IMFCollection_GetElementCount(IMFCollection* This,DWORD *pcElements) {
    return This->lpVtbl->GetElementCount(This,pcElements);
}
static inline HRESULT IMFCollection_GetElement(IMFCollection* This,DWORD dwElementIndex,IUnknown **ppUnkElement) {
    return This->lpVtbl->GetElement(This,dwElementIndex,ppUnkElement);
}
static inline HRESULT IMFCollection_AddElement(IMFCollection* This,IUnknown *pUnkElement) {
    return This->lpVtbl->AddElement(This,pUnkElement);
}
static inline HRESULT IMFCollection_RemoveElement(IMFCollection* This,DWORD dwElementIndex,IUnknown **ppUnkElement) {
    return This->lpVtbl->RemoveElement(This,dwElementIndex,ppUnkElement);
}
static inline HRESULT IMFCollection_InsertElementAt(IMFCollection* This,DWORD dwIndex,IUnknown *pUnknown) {
    return This->lpVtbl->InsertElementAt(This,dwIndex,pUnknown);
}
static inline HRESULT IMFCollection_RemoveAllElements(IMFCollection* This) {
    return This->lpVtbl->RemoveAllElements(This);
}
#endif
#endif

#endif


#endif  /* __IMFCollection_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFMediaEventQueue interface
 */
#ifndef __IMFMediaEventQueue_INTERFACE_DEFINED__
#define __IMFMediaEventQueue_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMediaEventQueue, 0x36f846fc, 0x2256, 0x48b6, 0xb5,0x8e, 0xe2,0xb6,0x38,0x31,0x65,0x81);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("36f846fc-2256-48b6-b58e-e2b638316581")
IMFMediaEventQueue : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetEvent(
        DWORD dwFlags,
        IMFMediaEvent **ppEvent) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginGetEvent(
        IMFAsyncCallback *pCallback,
        IUnknown *punkState) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndGetEvent(
        IMFAsyncResult *pResult,
        IMFMediaEvent **ppEvent) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueueEvent(
        IMFMediaEvent *pEvent) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueueEventParamVar(
        MediaEventType met,
        REFGUID guidExtendedType,
        HRESULT hrStatus,
        const PROPVARIANT *pvValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueueEventParamUnk(
        MediaEventType met,
        REFGUID guidExtendedType,
        HRESULT hrStatus,
        IUnknown *pUnk) = 0;

    virtual HRESULT STDMETHODCALLTYPE Shutdown(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMediaEventQueue, 0x36f846fc, 0x2256, 0x48b6, 0xb5,0x8e, 0xe2,0xb6,0x38,0x31,0x65,0x81)
#endif
#else
typedef struct IMFMediaEventQueueVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMediaEventQueue *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMediaEventQueue *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMediaEventQueue *This);

    /*** IMFMediaEventQueue methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEvent)(
        IMFMediaEventQueue *This,
        DWORD dwFlags,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *BeginGetEvent)(
        IMFMediaEventQueue *This,
        IMFAsyncCallback *pCallback,
        IUnknown *punkState);

    HRESULT (STDMETHODCALLTYPE *EndGetEvent)(
        IMFMediaEventQueue *This,
        IMFAsyncResult *pResult,
        IMFMediaEvent **ppEvent);

    HRESULT (STDMETHODCALLTYPE *QueueEvent)(
        IMFMediaEventQueue *This,
        IMFMediaEvent *pEvent);

    HRESULT (STDMETHODCALLTYPE *QueueEventParamVar)(
        IMFMediaEventQueue *This,
        MediaEventType met,
        REFGUID guidExtendedType,
        HRESULT hrStatus,
        const PROPVARIANT *pvValue);

    HRESULT (STDMETHODCALLTYPE *QueueEventParamUnk)(
        IMFMediaEventQueue *This,
        MediaEventType met,
        REFGUID guidExtendedType,
        HRESULT hrStatus,
        IUnknown *pUnk);

    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IMFMediaEventQueue *This);

    END_INTERFACE
} IMFMediaEventQueueVtbl;

interface IMFMediaEventQueue {
    CONST_VTBL IMFMediaEventQueueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMediaEventQueue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMediaEventQueue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMediaEventQueue_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMediaEventQueue methods ***/
#define IMFMediaEventQueue_GetEvent(This,dwFlags,ppEvent) (This)->lpVtbl->GetEvent(This,dwFlags,ppEvent)
#define IMFMediaEventQueue_BeginGetEvent(This,pCallback,punkState) (This)->lpVtbl->BeginGetEvent(This,pCallback,punkState)
#define IMFMediaEventQueue_EndGetEvent(This,pResult,ppEvent) (This)->lpVtbl->EndGetEvent(This,pResult,ppEvent)
#define IMFMediaEventQueue_QueueEvent(This,pEvent) (This)->lpVtbl->QueueEvent(This,pEvent)
#define IMFMediaEventQueue_QueueEventParamVar(This,met,guidExtendedType,hrStatus,pvValue) (This)->lpVtbl->QueueEventParamVar(This,met,guidExtendedType,hrStatus,pvValue)
#define IMFMediaEventQueue_QueueEventParamUnk(This,met,guidExtendedType,hrStatus,pUnk) (This)->lpVtbl->QueueEventParamUnk(This,met,guidExtendedType,hrStatus,pUnk)
#define IMFMediaEventQueue_Shutdown(This) (This)->lpVtbl->Shutdown(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMediaEventQueue_QueryInterface(IMFMediaEventQueue* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMediaEventQueue_AddRef(IMFMediaEventQueue* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMediaEventQueue_Release(IMFMediaEventQueue* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMediaEventQueue methods ***/
static inline HRESULT IMFMediaEventQueue_GetEvent(IMFMediaEventQueue* This,DWORD dwFlags,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->GetEvent(This,dwFlags,ppEvent);
}
static inline HRESULT IMFMediaEventQueue_BeginGetEvent(IMFMediaEventQueue* This,IMFAsyncCallback *pCallback,IUnknown *punkState) {
    return This->lpVtbl->BeginGetEvent(This,pCallback,punkState);
}
static inline HRESULT IMFMediaEventQueue_EndGetEvent(IMFMediaEventQueue* This,IMFAsyncResult *pResult,IMFMediaEvent **ppEvent) {
    return This->lpVtbl->EndGetEvent(This,pResult,ppEvent);
}
static inline HRESULT IMFMediaEventQueue_QueueEvent(IMFMediaEventQueue* This,IMFMediaEvent *pEvent) {
    return This->lpVtbl->QueueEvent(This,pEvent);
}
static inline HRESULT IMFMediaEventQueue_QueueEventParamVar(IMFMediaEventQueue* This,MediaEventType met,REFGUID guidExtendedType,HRESULT hrStatus,const PROPVARIANT *pvValue) {
    return This->lpVtbl->QueueEventParamVar(This,met,guidExtendedType,hrStatus,pvValue);
}
static inline HRESULT IMFMediaEventQueue_QueueEventParamUnk(IMFMediaEventQueue* This,MediaEventType met,REFGUID guidExtendedType,HRESULT hrStatus,IUnknown *pUnk) {
    return This->lpVtbl->QueueEventParamUnk(This,met,guidExtendedType,hrStatus,pUnk);
}
static inline HRESULT IMFMediaEventQueue_Shutdown(IMFMediaEventQueue* This) {
    return This->lpVtbl->Shutdown(This);
}
#endif
#endif

#endif


#endif  /* __IMFMediaEventQueue_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFActivate interface
 */
#ifndef __IMFActivate_INTERFACE_DEFINED__
#define __IMFActivate_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFActivate, 0x7fee9e9a, 0x4a89, 0x47a6, 0x89,0x9c, 0xb6,0xa5,0x3a,0x70,0xfb,0x67);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7fee9e9a-4a89-47a6-899c-b6a53a70fb67")
IMFActivate : public IMFAttributes
{
    virtual HRESULT STDMETHODCALLTYPE ActivateObject(
        REFIID riid,
        void **ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShutdownObject(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE DetachObject(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFActivate, 0x7fee9e9a, 0x4a89, 0x47a6, 0x89,0x9c, 0xb6,0xa5,0x3a,0x70,0xfb,0x67)
#endif
#else
typedef struct IMFActivateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFActivate *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFActivate *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFActivate *This);

    /*** IMFAttributes methods ***/
    HRESULT (STDMETHODCALLTYPE *GetItem)(
        IMFActivate *This,
        REFGUID guidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetItemType)(
        IMFActivate *This,
        REFGUID guidKey,
        MF_ATTRIBUTE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *CompareItem)(
        IMFActivate *This,
        REFGUID guidKey,
        REFPROPVARIANT Value,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        IMFActivate *This,
        IMFAttributes *pTheirs,
        MF_ATTRIBUTES_MATCH_TYPE MatchType,
        WINBOOL *pbResult);

    HRESULT (STDMETHODCALLTYPE *GetUINT32)(
        IMFActivate *This,
        REFGUID guidKey,
        UINT32 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetUINT64)(
        IMFActivate *This,
        REFGUID guidKey,
        UINT64 *punValue);

    HRESULT (STDMETHODCALLTYPE *GetDouble)(
        IMFActivate *This,
        REFGUID guidKey,
        double *pfValue);

    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IMFActivate *This,
        REFGUID guidKey,
        GUID *pguidValue);

    HRESULT (STDMETHODCALLTYPE *GetStringLength)(
        IMFActivate *This,
        REFGUID guidKey,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetString)(
        IMFActivate *This,
        REFGUID guidKey,
        LPWSTR pwszValue,
        UINT32 cchBufSize,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedString)(
        IMFActivate *This,
        REFGUID guidKey,
        LPWSTR *ppwszValue,
        UINT32 *pcchLength);

    HRESULT (STDMETHODCALLTYPE *GetBlobSize)(
        IMFActivate *This,
        REFGUID guidKey,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetBlob)(
        IMFActivate *This,
        REFGUID guidKey,
        UINT8 *pBuf,
        UINT32 cbBufSize,
        UINT32 *pcbBlobSize);

    HRESULT (STDMETHODCALLTYPE *GetAllocatedBlob)(
        IMFActivate *This,
        REFGUID guidKey,
        UINT8 **ppBuf,
        UINT32 *pcbSize);

    HRESULT (STDMETHODCALLTYPE *GetUnknown)(
        IMFActivate *This,
        REFGUID guidKey,
        REFIID riid,
        LPVOID *ppv);

    HRESULT (STDMETHODCALLTYPE *SetItem)(
        IMFActivate *This,
        REFGUID guidKey,
        REFPROPVARIANT Value);

    HRESULT (STDMETHODCALLTYPE *DeleteItem)(
        IMFActivate *This,
        REFGUID guidKey);

    HRESULT (STDMETHODCALLTYPE *DeleteAllItems)(
        IMFActivate *This);

    HRESULT (STDMETHODCALLTYPE *SetUINT32)(
        IMFActivate *This,
        REFGUID guidKey,
        UINT32 unValue);

    HRESULT (STDMETHODCALLTYPE *SetUINT64)(
        IMFActivate *This,
        REFGUID guidKey,
        UINT64 unValue);

    HRESULT (STDMETHODCALLTYPE *SetDouble)(
        IMFActivate *This,
        REFGUID guidKey,
        double fValue);

    HRESULT (STDMETHODCALLTYPE *SetGUID)(
        IMFActivate *This,
        REFGUID guidKey,
        REFGUID guidValue);

    HRESULT (STDMETHODCALLTYPE *SetString)(
        IMFActivate *This,
        REFGUID guidKey,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SetBlob)(
        IMFActivate *This,
        REFGUID guidKey,
        const UINT8 *pBuf,
        UINT32 cbBufSize);

    HRESULT (STDMETHODCALLTYPE *SetUnknown)(
        IMFActivate *This,
        REFGUID guidKey,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *LockStore)(
        IMFActivate *This);

    HRESULT (STDMETHODCALLTYPE *UnlockStore)(
        IMFActivate *This);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IMFActivate *This,
        UINT32 *pcItems);

    HRESULT (STDMETHODCALLTYPE *GetItemByIndex)(
        IMFActivate *This,
        UINT32 unIndex,
        GUID *pguidKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *CopyAllItems)(
        IMFActivate *This,
        IMFAttributes *pDest);

    /*** IMFActivate methods ***/
    HRESULT (STDMETHODCALLTYPE *ActivateObject)(
        IMFActivate *This,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *ShutdownObject)(
        IMFActivate *This);

    HRESULT (STDMETHODCALLTYPE *DetachObject)(
        IMFActivate *This);

    END_INTERFACE
} IMFActivateVtbl;

interface IMFActivate {
    CONST_VTBL IMFActivateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFActivate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFActivate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFActivate_Release(This) (This)->lpVtbl->Release(This)
/*** IMFAttributes methods ***/
#define IMFActivate_GetItem(This,guidKey,pValue) (This)->lpVtbl->GetItem(This,guidKey,pValue)
#define IMFActivate_GetItemType(This,guidKey,pType) (This)->lpVtbl->GetItemType(This,guidKey,pType)
#define IMFActivate_CompareItem(This,guidKey,Value,pbResult) (This)->lpVtbl->CompareItem(This,guidKey,Value,pbResult)
#define IMFActivate_Compare(This,pTheirs,MatchType,pbResult) (This)->lpVtbl->Compare(This,pTheirs,MatchType,pbResult)
#define IMFActivate_GetUINT32(This,guidKey,punValue) (This)->lpVtbl->GetUINT32(This,guidKey,punValue)
#define IMFActivate_GetUINT64(This,guidKey,punValue) (This)->lpVtbl->GetUINT64(This,guidKey,punValue)
#define IMFActivate_GetDouble(This,guidKey,pfValue) (This)->lpVtbl->GetDouble(This,guidKey,pfValue)
#define IMFActivate_GetGUID(This,guidKey,pguidValue) (This)->lpVtbl->GetGUID(This,guidKey,pguidValue)
#define IMFActivate_GetStringLength(This,guidKey,pcchLength) (This)->lpVtbl->GetStringLength(This,guidKey,pcchLength)
#define IMFActivate_GetString(This,guidKey,pwszValue,cchBufSize,pcchLength) (This)->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength)
#define IMFActivate_GetAllocatedString(This,guidKey,ppwszValue,pcchLength) (This)->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength)
#define IMFActivate_GetBlobSize(This,guidKey,pcbBlobSize) (This)->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize)
#define IMFActivate_GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize) (This)->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize)
#define IMFActivate_GetAllocatedBlob(This,guidKey,ppBuf,pcbSize) (This)->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize)
#define IMFActivate_GetUnknown(This,guidKey,riid,ppv) (This)->lpVtbl->GetUnknown(This,guidKey,riid,ppv)
#define IMFActivate_SetItem(This,guidKey,Value) (This)->lpVtbl->SetItem(This,guidKey,Value)
#define IMFActivate_DeleteItem(This,guidKey) (This)->lpVtbl->DeleteItem(This,guidKey)
#define IMFActivate_DeleteAllItems(This) (This)->lpVtbl->DeleteAllItems(This)
#define IMFActivate_SetUINT32(This,guidKey,unValue) (This)->lpVtbl->SetUINT32(This,guidKey,unValue)
#define IMFActivate_SetUINT64(This,guidKey,unValue) (This)->lpVtbl->SetUINT64(This,guidKey,unValue)
#define IMFActivate_SetDouble(This,guidKey,fValue) (This)->lpVtbl->SetDouble(This,guidKey,fValue)
#define IMFActivate_SetGUID(This,guidKey,guidValue) (This)->lpVtbl->SetGUID(This,guidKey,guidValue)
#define IMFActivate_SetString(This,guidKey,wszValue) (This)->lpVtbl->SetString(This,guidKey,wszValue)
#define IMFActivate_SetBlob(This,guidKey,pBuf,cbBufSize) (This)->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize)
#define IMFActivate_SetUnknown(This,guidKey,pUnknown) (This)->lpVtbl->SetUnknown(This,guidKey,pUnknown)
#define IMFActivate_LockStore(This) (This)->lpVtbl->LockStore(This)
#define IMFActivate_UnlockStore(This) (This)->lpVtbl->UnlockStore(This)
#define IMFActivate_GetCount(This,pcItems) (This)->lpVtbl->GetCount(This,pcItems)
#define IMFActivate_GetItemByIndex(This,unIndex,pguidKey,pValue) (This)->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue)
#define IMFActivate_CopyAllItems(This,pDest) (This)->lpVtbl->CopyAllItems(This,pDest)
/*** IMFActivate methods ***/
#define IMFActivate_ActivateObject(This,riid,ppv) (This)->lpVtbl->ActivateObject(This,riid,ppv)
#define IMFActivate_ShutdownObject(This) (This)->lpVtbl->ShutdownObject(This)
#define IMFActivate_DetachObject(This) (This)->lpVtbl->DetachObject(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFActivate_QueryInterface(IMFActivate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFActivate_AddRef(IMFActivate* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFActivate_Release(IMFActivate* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFAttributes methods ***/
static inline HRESULT IMFActivate_GetItem(IMFActivate* This,REFGUID guidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItem(This,guidKey,pValue);
}
static inline HRESULT IMFActivate_GetItemType(IMFActivate* This,REFGUID guidKey,MF_ATTRIBUTE_TYPE *pType) {
    return This->lpVtbl->GetItemType(This,guidKey,pType);
}
static inline HRESULT IMFActivate_CompareItem(IMFActivate* This,REFGUID guidKey,REFPROPVARIANT Value,WINBOOL *pbResult) {
    return This->lpVtbl->CompareItem(This,guidKey,Value,pbResult);
}
static inline HRESULT IMFActivate_Compare(IMFActivate* This,IMFAttributes *pTheirs,MF_ATTRIBUTES_MATCH_TYPE MatchType,WINBOOL *pbResult) {
    return This->lpVtbl->Compare(This,pTheirs,MatchType,pbResult);
}
static inline HRESULT IMFActivate_GetUINT32(IMFActivate* This,REFGUID guidKey,UINT32 *punValue) {
    return This->lpVtbl->GetUINT32(This,guidKey,punValue);
}
static inline HRESULT IMFActivate_GetUINT64(IMFActivate* This,REFGUID guidKey,UINT64 *punValue) {
    return This->lpVtbl->GetUINT64(This,guidKey,punValue);
}
static inline HRESULT IMFActivate_GetDouble(IMFActivate* This,REFGUID guidKey,double *pfValue) {
    return This->lpVtbl->GetDouble(This,guidKey,pfValue);
}
static inline HRESULT IMFActivate_GetGUID(IMFActivate* This,REFGUID guidKey,GUID *pguidValue) {
    return This->lpVtbl->GetGUID(This,guidKey,pguidValue);
}
static inline HRESULT IMFActivate_GetStringLength(IMFActivate* This,REFGUID guidKey,UINT32 *pcchLength) {
    return This->lpVtbl->GetStringLength(This,guidKey,pcchLength);
}
static inline HRESULT IMFActivate_GetString(IMFActivate* This,REFGUID guidKey,LPWSTR pwszValue,UINT32 cchBufSize,UINT32 *pcchLength) {
    return This->lpVtbl->GetString(This,guidKey,pwszValue,cchBufSize,pcchLength);
}
static inline HRESULT IMFActivate_GetAllocatedString(IMFActivate* This,REFGUID guidKey,LPWSTR *ppwszValue,UINT32 *pcchLength) {
    return This->lpVtbl->GetAllocatedString(This,guidKey,ppwszValue,pcchLength);
}
static inline HRESULT IMFActivate_GetBlobSize(IMFActivate* This,REFGUID guidKey,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlobSize(This,guidKey,pcbBlobSize);
}
static inline HRESULT IMFActivate_GetBlob(IMFActivate* This,REFGUID guidKey,UINT8 *pBuf,UINT32 cbBufSize,UINT32 *pcbBlobSize) {
    return This->lpVtbl->GetBlob(This,guidKey,pBuf,cbBufSize,pcbBlobSize);
}
static inline HRESULT IMFActivate_GetAllocatedBlob(IMFActivate* This,REFGUID guidKey,UINT8 **ppBuf,UINT32 *pcbSize) {
    return This->lpVtbl->GetAllocatedBlob(This,guidKey,ppBuf,pcbSize);
}
static inline HRESULT IMFActivate_GetUnknown(IMFActivate* This,REFGUID guidKey,REFIID riid,LPVOID *ppv) {
    return This->lpVtbl->GetUnknown(This,guidKey,riid,ppv);
}
static inline HRESULT IMFActivate_SetItem(IMFActivate* This,REFGUID guidKey,REFPROPVARIANT Value) {
    return This->lpVtbl->SetItem(This,guidKey,Value);
}
static inline HRESULT IMFActivate_DeleteItem(IMFActivate* This,REFGUID guidKey) {
    return This->lpVtbl->DeleteItem(This,guidKey);
}
static inline HRESULT IMFActivate_DeleteAllItems(IMFActivate* This) {
    return This->lpVtbl->DeleteAllItems(This);
}
static inline HRESULT IMFActivate_SetUINT32(IMFActivate* This,REFGUID guidKey,UINT32 unValue) {
    return This->lpVtbl->SetUINT32(This,guidKey,unValue);
}
static inline HRESULT IMFActivate_SetUINT64(IMFActivate* This,REFGUID guidKey,UINT64 unValue) {
    return This->lpVtbl->SetUINT64(This,guidKey,unValue);
}
static inline HRESULT IMFActivate_SetDouble(IMFActivate* This,REFGUID guidKey,double fValue) {
    return This->lpVtbl->SetDouble(This,guidKey,fValue);
}
static inline HRESULT IMFActivate_SetGUID(IMFActivate* This,REFGUID guidKey,REFGUID guidValue) {
    return This->lpVtbl->SetGUID(This,guidKey,guidValue);
}
static inline HRESULT IMFActivate_SetString(IMFActivate* This,REFGUID guidKey,LPCWSTR wszValue) {
    return This->lpVtbl->SetString(This,guidKey,wszValue);
}
static inline HRESULT IMFActivate_SetBlob(IMFActivate* This,REFGUID guidKey,const UINT8 *pBuf,UINT32 cbBufSize) {
    return This->lpVtbl->SetBlob(This,guidKey,pBuf,cbBufSize);
}
static inline HRESULT IMFActivate_SetUnknown(IMFActivate* This,REFGUID guidKey,IUnknown *pUnknown) {
    return This->lpVtbl->SetUnknown(This,guidKey,pUnknown);
}
static inline HRESULT IMFActivate_LockStore(IMFActivate* This) {
    return This->lpVtbl->LockStore(This);
}
static inline HRESULT IMFActivate_UnlockStore(IMFActivate* This) {
    return This->lpVtbl->UnlockStore(This);
}
static inline HRESULT IMFActivate_GetCount(IMFActivate* This,UINT32 *pcItems) {
    return This->lpVtbl->GetCount(This,pcItems);
}
static inline HRESULT IMFActivate_GetItemByIndex(IMFActivate* This,UINT32 unIndex,GUID *pguidKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetItemByIndex(This,unIndex,pguidKey,pValue);
}
static inline HRESULT IMFActivate_CopyAllItems(IMFActivate* This,IMFAttributes *pDest) {
    return This->lpVtbl->CopyAllItems(This,pDest);
}
/*** IMFActivate methods ***/
static inline HRESULT IMFActivate_ActivateObject(IMFActivate* This,REFIID riid,void **ppv) {
    return This->lpVtbl->ActivateObject(This,riid,ppv);
}
static inline HRESULT IMFActivate_ShutdownObject(IMFActivate* This) {
    return This->lpVtbl->ShutdownObject(This);
}
static inline HRESULT IMFActivate_DetachObject(IMFActivate* This) {
    return This->lpVtbl->DetachObject(This);
}
#endif
#endif

#endif


#endif  /* __IMFActivate_INTERFACE_DEFINED__ */

#endif

#if WINVER >= _WIN32_WINNT_WIN7
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
typedef enum _MF_Plugin_Type {
    MF_Plugin_Type_MFT = 0,
    MF_Plugin_Type_MediaSource = 1,
    MF_Plugin_Type_MFT_MatchOutputType = 2,
    MF_Plugin_Type_Other = (DWORD)-1
} MF_Plugin_Type;

/*****************************************************************************
 * IMFPluginControl interface
 */
#ifndef __IMFPluginControl_INTERFACE_DEFINED__
#define __IMFPluginControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFPluginControl, 0x5c6c44bf, 0x1db6, 0x435b, 0x92,0x49, 0xe8,0xcd,0x10,0xfd,0xec,0x96);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5c6c44bf-1db6-435b-9249-e8cd10fdec96")
IMFPluginControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetPreferredClsid(
        DWORD pluginType,
        LPCWSTR selector,
        CLSID *clsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPreferredClsidByIndex(
        DWORD pluginType,
        DWORD index,
        LPWSTR *selector,
        CLSID *clsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPreferredClsid(
        DWORD pluginType,
        LPCWSTR selector,
        const CLSID *clsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsDisabled(
        DWORD pluginType,
        REFCLSID clsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDisabledByIndex(
        DWORD pluginType,
        DWORD index,
        CLSID *clsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDisabled(
        DWORD pluginType,
        REFCLSID clsid,
        WINBOOL disabled) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFPluginControl, 0x5c6c44bf, 0x1db6, 0x435b, 0x92,0x49, 0xe8,0xcd,0x10,0xfd,0xec,0x96)
#endif
#else
typedef struct IMFPluginControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFPluginControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFPluginControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFPluginControl *This);

    /*** IMFPluginControl methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPreferredClsid)(
        IMFPluginControl *This,
        DWORD pluginType,
        LPCWSTR selector,
        CLSID *clsid);

    HRESULT (STDMETHODCALLTYPE *GetPreferredClsidByIndex)(
        IMFPluginControl *This,
        DWORD pluginType,
        DWORD index,
        LPWSTR *selector,
        CLSID *clsid);

    HRESULT (STDMETHODCALLTYPE *SetPreferredClsid)(
        IMFPluginControl *This,
        DWORD pluginType,
        LPCWSTR selector,
        const CLSID *clsid);

    HRESULT (STDMETHODCALLTYPE *IsDisabled)(
        IMFPluginControl *This,
        DWORD pluginType,
        REFCLSID clsid);

    HRESULT (STDMETHODCALLTYPE *GetDisabledByIndex)(
        IMFPluginControl *This,
        DWORD pluginType,
        DWORD index,
        CLSID *clsid);

    HRESULT (STDMETHODCALLTYPE *SetDisabled)(
        IMFPluginControl *This,
        DWORD pluginType,
        REFCLSID clsid,
        WINBOOL disabled);

    END_INTERFACE
} IMFPluginControlVtbl;

interface IMFPluginControl {
    CONST_VTBL IMFPluginControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFPluginControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFPluginControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFPluginControl_Release(This) (This)->lpVtbl->Release(This)
/*** IMFPluginControl methods ***/
#define IMFPluginControl_GetPreferredClsid(This,pluginType,selector,clsid) (This)->lpVtbl->GetPreferredClsid(This,pluginType,selector,clsid)
#define IMFPluginControl_GetPreferredClsidByIndex(This,pluginType,index,selector,clsid) (This)->lpVtbl->GetPreferredClsidByIndex(This,pluginType,index,selector,clsid)
#define IMFPluginControl_SetPreferredClsid(This,pluginType,selector,clsid) (This)->lpVtbl->SetPreferredClsid(This,pluginType,selector,clsid)
#define IMFPluginControl_IsDisabled(This,pluginType,clsid) (This)->lpVtbl->IsDisabled(This,pluginType,clsid)
#define IMFPluginControl_GetDisabledByIndex(This,pluginType,index,clsid) (This)->lpVtbl->GetDisabledByIndex(This,pluginType,index,clsid)
#define IMFPluginControl_SetDisabled(This,pluginType,clsid,disabled) (This)->lpVtbl->SetDisabled(This,pluginType,clsid,disabled)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFPluginControl_QueryInterface(IMFPluginControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFPluginControl_AddRef(IMFPluginControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFPluginControl_Release(IMFPluginControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFPluginControl methods ***/
static inline HRESULT IMFPluginControl_GetPreferredClsid(IMFPluginControl* This,DWORD pluginType,LPCWSTR selector,CLSID *clsid) {
    return This->lpVtbl->GetPreferredClsid(This,pluginType,selector,clsid);
}
static inline HRESULT IMFPluginControl_GetPreferredClsidByIndex(IMFPluginControl* This,DWORD pluginType,DWORD index,LPWSTR *selector,CLSID *clsid) {
    return This->lpVtbl->GetPreferredClsidByIndex(This,pluginType,index,selector,clsid);
}
static inline HRESULT IMFPluginControl_SetPreferredClsid(IMFPluginControl* This,DWORD pluginType,LPCWSTR selector,const CLSID *clsid) {
    return This->lpVtbl->SetPreferredClsid(This,pluginType,selector,clsid);
}
static inline HRESULT IMFPluginControl_IsDisabled(IMFPluginControl* This,DWORD pluginType,REFCLSID clsid) {
    return This->lpVtbl->IsDisabled(This,pluginType,clsid);
}
static inline HRESULT IMFPluginControl_GetDisabledByIndex(IMFPluginControl* This,DWORD pluginType,DWORD index,CLSID *clsid) {
    return This->lpVtbl->GetDisabledByIndex(This,pluginType,index,clsid);
}
static inline HRESULT IMFPluginControl_SetDisabled(IMFPluginControl* This,DWORD pluginType,REFCLSID clsid,WINBOOL disabled) {
    return This->lpVtbl->SetDisabled(This,pluginType,clsid,disabled);
}
#endif
#endif

#endif


#endif  /* __IMFPluginControl_INTERFACE_DEFINED__ */


typedef enum MF_PLUGIN_CONTROL_POLICY {
    MF_PLUGIN_CONTROL_POLICY_USE_ALL_PLUGINS = 0,
    MF_PLUGIN_CONTROL_POLICY_USE_APPROVED_PLUGINS = 1,
    MF_PLUGIN_CONTROL_POLICY_USE_WEB_PLUGINS = 2,
    MF_PLUGIN_CONTROL_POLICY_USE_WEB_PLUGINS_EDGEMODE = 3
} MF_PLUGIN_CONTROL_POLICY;

/*****************************************************************************
 * IMFPluginControl2 interface
 */
#ifndef __IMFPluginControl2_INTERFACE_DEFINED__
#define __IMFPluginControl2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFPluginControl2, 0xc6982083, 0x3ddc, 0x45cb, 0xaf,0x5e, 0x0f,0x7a,0x8c,0xe4,0xde,0x77);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c6982083-3ddc-45cb-af5e-0f7a8ce4de77")
IMFPluginControl2 : public IMFPluginControl
{
    virtual HRESULT STDMETHODCALLTYPE SetPolicy(
        MF_PLUGIN_CONTROL_POLICY policy) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFPluginControl2, 0xc6982083, 0x3ddc, 0x45cb, 0xaf,0x5e, 0x0f,0x7a,0x8c,0xe4,0xde,0x77)
#endif
#else
typedef struct IMFPluginControl2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFPluginControl2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFPluginControl2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFPluginControl2 *This);

    /*** IMFPluginControl methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPreferredClsid)(
        IMFPluginControl2 *This,
        DWORD pluginType,
        LPCWSTR selector,
        CLSID *clsid);

    HRESULT (STDMETHODCALLTYPE *GetPreferredClsidByIndex)(
        IMFPluginControl2 *This,
        DWORD pluginType,
        DWORD index,
        LPWSTR *selector,
        CLSID *clsid);

    HRESULT (STDMETHODCALLTYPE *SetPreferredClsid)(
        IMFPluginControl2 *This,
        DWORD pluginType,
        LPCWSTR selector,
        const CLSID *clsid);

    HRESULT (STDMETHODCALLTYPE *IsDisabled)(
        IMFPluginControl2 *This,
        DWORD pluginType,
        REFCLSID clsid);

    HRESULT (STDMETHODCALLTYPE *GetDisabledByIndex)(
        IMFPluginControl2 *This,
        DWORD pluginType,
        DWORD index,
        CLSID *clsid);

    HRESULT (STDMETHODCALLTYPE *SetDisabled)(
        IMFPluginControl2 *This,
        DWORD pluginType,
        REFCLSID clsid,
        WINBOOL disabled);

    /*** IMFPluginControl2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPolicy)(
        IMFPluginControl2 *This,
        MF_PLUGIN_CONTROL_POLICY policy);

    END_INTERFACE
} IMFPluginControl2Vtbl;

interface IMFPluginControl2 {
    CONST_VTBL IMFPluginControl2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFPluginControl2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFPluginControl2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFPluginControl2_Release(This) (This)->lpVtbl->Release(This)
/*** IMFPluginControl methods ***/
#define IMFPluginControl2_GetPreferredClsid(This,pluginType,selector,clsid) (This)->lpVtbl->GetPreferredClsid(This,pluginType,selector,clsid)
#define IMFPluginControl2_GetPreferredClsidByIndex(This,pluginType,index,selector,clsid) (This)->lpVtbl->GetPreferredClsidByIndex(This,pluginType,index,selector,clsid)
#define IMFPluginControl2_SetPreferredClsid(This,pluginType,selector,clsid) (This)->lpVtbl->SetPreferredClsid(This,pluginType,selector,clsid)
#define IMFPluginControl2_IsDisabled(This,pluginType,clsid) (This)->lpVtbl->IsDisabled(This,pluginType,clsid)
#define IMFPluginControl2_GetDisabledByIndex(This,pluginType,index,clsid) (This)->lpVtbl->GetDisabledByIndex(This,pluginType,index,clsid)
#define IMFPluginControl2_SetDisabled(This,pluginType,clsid,disabled) (This)->lpVtbl->SetDisabled(This,pluginType,clsid,disabled)
/*** IMFPluginControl2 methods ***/
#define IMFPluginControl2_SetPolicy(This,policy) (This)->lpVtbl->SetPolicy(This,policy)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFPluginControl2_QueryInterface(IMFPluginControl2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFPluginControl2_AddRef(IMFPluginControl2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFPluginControl2_Release(IMFPluginControl2* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFPluginControl methods ***/
static inline HRESULT IMFPluginControl2_GetPreferredClsid(IMFPluginControl2* This,DWORD pluginType,LPCWSTR selector,CLSID *clsid) {
    return This->lpVtbl->GetPreferredClsid(This,pluginType,selector,clsid);
}
static inline HRESULT IMFPluginControl2_GetPreferredClsidByIndex(IMFPluginControl2* This,DWORD pluginType,DWORD index,LPWSTR *selector,CLSID *clsid) {
    return This->lpVtbl->GetPreferredClsidByIndex(This,pluginType,index,selector,clsid);
}
static inline HRESULT IMFPluginControl2_SetPreferredClsid(IMFPluginControl2* This,DWORD pluginType,LPCWSTR selector,const CLSID *clsid) {
    return This->lpVtbl->SetPreferredClsid(This,pluginType,selector,clsid);
}
static inline HRESULT IMFPluginControl2_IsDisabled(IMFPluginControl2* This,DWORD pluginType,REFCLSID clsid) {
    return This->lpVtbl->IsDisabled(This,pluginType,clsid);
}
static inline HRESULT IMFPluginControl2_GetDisabledByIndex(IMFPluginControl2* This,DWORD pluginType,DWORD index,CLSID *clsid) {
    return This->lpVtbl->GetDisabledByIndex(This,pluginType,index,clsid);
}
static inline HRESULT IMFPluginControl2_SetDisabled(IMFPluginControl2* This,DWORD pluginType,REFCLSID clsid,WINBOOL disabled) {
    return This->lpVtbl->SetDisabled(This,pluginType,clsid,disabled);
}
/*** IMFPluginControl2 methods ***/
static inline HRESULT IMFPluginControl2_SetPolicy(IMFPluginControl2* This,MF_PLUGIN_CONTROL_POLICY policy) {
    return This->lpVtbl->SetPolicy(This,policy);
}
#endif
#endif

#endif


#endif  /* __IMFPluginControl2_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IMFDXGIDeviceManager interface
 */
#ifndef __IMFDXGIDeviceManager_INTERFACE_DEFINED__
#define __IMFDXGIDeviceManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFDXGIDeviceManager, 0xeb533d5d, 0x2db6, 0x40f8, 0x97,0xa9, 0x49,0x46,0x92,0x01,0x4f,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("eb533d5d-2db6-40f8-97a9-494692014f07")
IMFDXGIDeviceManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CloseDeviceHandle(
        HANDLE hDevice) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVideoService(
        HANDLE hDevice,
        REFIID riid,
        void **ppService) = 0;

    virtual HRESULT STDMETHODCALLTYPE LockDevice(
        HANDLE hDevice,
        REFIID riid,
        void **ppUnkDevice,
        WINBOOL fBlock) = 0;

    virtual HRESULT STDMETHODCALLTYPE OpenDeviceHandle(
        HANDLE *phDevice) = 0;

    virtual HRESULT STDMETHODCALLTYPE ResetDevice(
        IUnknown *pUnkDevice,
        UINT resetToken) = 0;

    virtual HRESULT STDMETHODCALLTYPE TestDevice(
        HANDLE hDevice) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnlockDevice(
        HANDLE hDevice,
        WINBOOL fSaveState) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFDXGIDeviceManager, 0xeb533d5d, 0x2db6, 0x40f8, 0x97,0xa9, 0x49,0x46,0x92,0x01,0x4f,0x07)
#endif
#else
typedef struct IMFDXGIDeviceManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFDXGIDeviceManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFDXGIDeviceManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFDXGIDeviceManager *This);

    /*** IMFDXGIDeviceManager methods ***/
    HRESULT (STDMETHODCALLTYPE *CloseDeviceHandle)(
        IMFDXGIDeviceManager *This,
        HANDLE hDevice);

    HRESULT (STDMETHODCALLTYPE *GetVideoService)(
        IMFDXGIDeviceManager *This,
        HANDLE hDevice,
        REFIID riid,
        void **ppService);

    HRESULT (STDMETHODCALLTYPE *LockDevice)(
        IMFDXGIDeviceManager *This,
        HANDLE hDevice,
        REFIID riid,
        void **ppUnkDevice,
        WINBOOL fBlock);

    HRESULT (STDMETHODCALLTYPE *OpenDeviceHandle)(
        IMFDXGIDeviceManager *This,
        HANDLE *phDevice);

    HRESULT (STDMETHODCALLTYPE *ResetDevice)(
        IMFDXGIDeviceManager *This,
        IUnknown *pUnkDevice,
        UINT resetToken);

    HRESULT (STDMETHODCALLTYPE *TestDevice)(
        IMFDXGIDeviceManager *This,
        HANDLE hDevice);

    HRESULT (STDMETHODCALLTYPE *UnlockDevice)(
        IMFDXGIDeviceManager *This,
        HANDLE hDevice,
        WINBOOL fSaveState);

    END_INTERFACE
} IMFDXGIDeviceManagerVtbl;

interface IMFDXGIDeviceManager {
    CONST_VTBL IMFDXGIDeviceManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFDXGIDeviceManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFDXGIDeviceManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFDXGIDeviceManager_Release(This) (This)->lpVtbl->Release(This)
/*** IMFDXGIDeviceManager methods ***/
#define IMFDXGIDeviceManager_CloseDeviceHandle(This,hDevice) (This)->lpVtbl->CloseDeviceHandle(This,hDevice)
#define IMFDXGIDeviceManager_GetVideoService(This,hDevice,riid,ppService) (This)->lpVtbl->GetVideoService(This,hDevice,riid,ppService)
#define IMFDXGIDeviceManager_LockDevice(This,hDevice,riid,ppUnkDevice,fBlock) (This)->lpVtbl->LockDevice(This,hDevice,riid,ppUnkDevice,fBlock)
#define IMFDXGIDeviceManager_OpenDeviceHandle(This,phDevice) (This)->lpVtbl->OpenDeviceHandle(This,phDevice)
#define IMFDXGIDeviceManager_ResetDevice(This,pUnkDevice,resetToken) (This)->lpVtbl->ResetDevice(This,pUnkDevice,resetToken)
#define IMFDXGIDeviceManager_TestDevice(This,hDevice) (This)->lpVtbl->TestDevice(This,hDevice)
#define IMFDXGIDeviceManager_UnlockDevice(This,hDevice,fSaveState) (This)->lpVtbl->UnlockDevice(This,hDevice,fSaveState)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFDXGIDeviceManager_QueryInterface(IMFDXGIDeviceManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFDXGIDeviceManager_AddRef(IMFDXGIDeviceManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFDXGIDeviceManager_Release(IMFDXGIDeviceManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFDXGIDeviceManager methods ***/
static inline HRESULT IMFDXGIDeviceManager_CloseDeviceHandle(IMFDXGIDeviceManager* This,HANDLE hDevice) {
    return This->lpVtbl->CloseDeviceHandle(This,hDevice);
}
static inline HRESULT IMFDXGIDeviceManager_GetVideoService(IMFDXGIDeviceManager* This,HANDLE hDevice,REFIID riid,void **ppService) {
    return This->lpVtbl->GetVideoService(This,hDevice,riid,ppService);
}
static inline HRESULT IMFDXGIDeviceManager_LockDevice(IMFDXGIDeviceManager* This,HANDLE hDevice,REFIID riid,void **ppUnkDevice,WINBOOL fBlock) {
    return This->lpVtbl->LockDevice(This,hDevice,riid,ppUnkDevice,fBlock);
}
static inline HRESULT IMFDXGIDeviceManager_OpenDeviceHandle(IMFDXGIDeviceManager* This,HANDLE *phDevice) {
    return This->lpVtbl->OpenDeviceHandle(This,phDevice);
}
static inline HRESULT IMFDXGIDeviceManager_ResetDevice(IMFDXGIDeviceManager* This,IUnknown *pUnkDevice,UINT resetToken) {
    return This->lpVtbl->ResetDevice(This,pUnkDevice,resetToken);
}
static inline HRESULT IMFDXGIDeviceManager_TestDevice(IMFDXGIDeviceManager* This,HANDLE hDevice) {
    return This->lpVtbl->TestDevice(This,hDevice);
}
static inline HRESULT IMFDXGIDeviceManager_UnlockDevice(IMFDXGIDeviceManager* This,HANDLE hDevice,WINBOOL fSaveState) {
    return This->lpVtbl->UnlockDevice(This,hDevice,fSaveState);
}
#endif
#endif

#endif


#endif  /* __IMFDXGIDeviceManager_INTERFACE_DEFINED__ */


typedef enum _MF_STREAM_STATE {
    MF_STREAM_STATE_STOPPED = 0,
    MF_STREAM_STATE_PAUSED = 1,
    MF_STREAM_STATE_RUNNING = 2
} MF_STREAM_STATE;
#endif

#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#if NTDDI_VERSION >= NTDDI_WIN10_RS2

/*****************************************************************************
 * IMFMuxStreamAttributesManager interface
 */
#ifndef __IMFMuxStreamAttributesManager_INTERFACE_DEFINED__
#define __IMFMuxStreamAttributesManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMuxStreamAttributesManager, 0xce8bd576, 0xe440, 0x43b3, 0xbe,0x34, 0x1e,0x53,0xf5,0x65,0xf7,0xe8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ce8bd576-e440-43b3-be34-1e53f565f7e8")
IMFMuxStreamAttributesManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetStreamCount(
        DWORD *pdwMuxStreamCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAttributes(
        DWORD dwMuxStreamIndex,
        IMFAttributes **ppStreamAttributes) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMuxStreamAttributesManager, 0xce8bd576, 0xe440, 0x43b3, 0xbe,0x34, 0x1e,0x53,0xf5,0x65,0xf7,0xe8)
#endif
#else
typedef struct IMFMuxStreamAttributesManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMuxStreamAttributesManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMuxStreamAttributesManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMuxStreamAttributesManager *This);

    /*** IMFMuxStreamAttributesManager methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStreamCount)(
        IMFMuxStreamAttributesManager *This,
        DWORD *pdwMuxStreamCount);

    HRESULT (STDMETHODCALLTYPE *GetAttributes)(
        IMFMuxStreamAttributesManager *This,
        DWORD dwMuxStreamIndex,
        IMFAttributes **ppStreamAttributes);

    END_INTERFACE
} IMFMuxStreamAttributesManagerVtbl;

interface IMFMuxStreamAttributesManager {
    CONST_VTBL IMFMuxStreamAttributesManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMuxStreamAttributesManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMuxStreamAttributesManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMuxStreamAttributesManager_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMuxStreamAttributesManager methods ***/
#define IMFMuxStreamAttributesManager_GetStreamCount(This,pdwMuxStreamCount) (This)->lpVtbl->GetStreamCount(This,pdwMuxStreamCount)
#define IMFMuxStreamAttributesManager_GetAttributes(This,dwMuxStreamIndex,ppStreamAttributes) (This)->lpVtbl->GetAttributes(This,dwMuxStreamIndex,ppStreamAttributes)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMuxStreamAttributesManager_QueryInterface(IMFMuxStreamAttributesManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMuxStreamAttributesManager_AddRef(IMFMuxStreamAttributesManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMuxStreamAttributesManager_Release(IMFMuxStreamAttributesManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMuxStreamAttributesManager methods ***/
static inline HRESULT IMFMuxStreamAttributesManager_GetStreamCount(IMFMuxStreamAttributesManager* This,DWORD *pdwMuxStreamCount) {
    return This->lpVtbl->GetStreamCount(This,pdwMuxStreamCount);
}
static inline HRESULT IMFMuxStreamAttributesManager_GetAttributes(IMFMuxStreamAttributesManager* This,DWORD dwMuxStreamIndex,IMFAttributes **ppStreamAttributes) {
    return This->lpVtbl->GetAttributes(This,dwMuxStreamIndex,ppStreamAttributes);
}
#endif
#endif

#endif


#endif  /* __IMFMuxStreamAttributesManager_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFMuxStreamMediaTypeManager interface
 */
#ifndef __IMFMuxStreamMediaTypeManager_INTERFACE_DEFINED__
#define __IMFMuxStreamMediaTypeManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMuxStreamMediaTypeManager, 0x505a2c72, 0x42f7, 0x4690, 0xae,0xab, 0x8f,0x51,0x3d,0x0f,0xfd,0xb8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("505a2c72-42f7-4690-aeab-8f513d0ffdb8")
IMFMuxStreamMediaTypeManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetStreamCount(
        DWORD *pdwMuxStreamCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaType(
        DWORD dwMuxStreamIndex,
        IMFMediaType **ppMediaType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamConfigurationCount(
        DWORD *pdwCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddStreamConfiguration(
        ULONGLONG ullStreamMask) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveStreamConfiguration(
        ULONGLONG ullStreamMask) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamConfiguration(
        DWORD ulIndex,
        ULONGLONG *pullStreamMask) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMuxStreamMediaTypeManager, 0x505a2c72, 0x42f7, 0x4690, 0xae,0xab, 0x8f,0x51,0x3d,0x0f,0xfd,0xb8)
#endif
#else
typedef struct IMFMuxStreamMediaTypeManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMuxStreamMediaTypeManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMuxStreamMediaTypeManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMuxStreamMediaTypeManager *This);

    /*** IMFMuxStreamMediaTypeManager methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStreamCount)(
        IMFMuxStreamMediaTypeManager *This,
        DWORD *pdwMuxStreamCount);

    HRESULT (STDMETHODCALLTYPE *GetMediaType)(
        IMFMuxStreamMediaTypeManager *This,
        DWORD dwMuxStreamIndex,
        IMFMediaType **ppMediaType);

    HRESULT (STDMETHODCALLTYPE *GetStreamConfigurationCount)(
        IMFMuxStreamMediaTypeManager *This,
        DWORD *pdwCount);

    HRESULT (STDMETHODCALLTYPE *AddStreamConfiguration)(
        IMFMuxStreamMediaTypeManager *This,
        ULONGLONG ullStreamMask);

    HRESULT (STDMETHODCALLTYPE *RemoveStreamConfiguration)(
        IMFMuxStreamMediaTypeManager *This,
        ULONGLONG ullStreamMask);

    HRESULT (STDMETHODCALLTYPE *GetStreamConfiguration)(
        IMFMuxStreamMediaTypeManager *This,
        DWORD ulIndex,
        ULONGLONG *pullStreamMask);

    END_INTERFACE
} IMFMuxStreamMediaTypeManagerVtbl;

interface IMFMuxStreamMediaTypeManager {
    CONST_VTBL IMFMuxStreamMediaTypeManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMuxStreamMediaTypeManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMuxStreamMediaTypeManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMuxStreamMediaTypeManager_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMuxStreamMediaTypeManager methods ***/
#define IMFMuxStreamMediaTypeManager_GetStreamCount(This,pdwMuxStreamCount) (This)->lpVtbl->GetStreamCount(This,pdwMuxStreamCount)
#define IMFMuxStreamMediaTypeManager_GetMediaType(This,dwMuxStreamIndex,ppMediaType) (This)->lpVtbl->GetMediaType(This,dwMuxStreamIndex,ppMediaType)
#define IMFMuxStreamMediaTypeManager_GetStreamConfigurationCount(This,pdwCount) (This)->lpVtbl->GetStreamConfigurationCount(This,pdwCount)
#define IMFMuxStreamMediaTypeManager_AddStreamConfiguration(This,ullStreamMask) (This)->lpVtbl->AddStreamConfiguration(This,ullStreamMask)
#define IMFMuxStreamMediaTypeManager_RemoveStreamConfiguration(This,ullStreamMask) (This)->lpVtbl->RemoveStreamConfiguration(This,ullStreamMask)
#define IMFMuxStreamMediaTypeManager_GetStreamConfiguration(This,ulIndex,pullStreamMask) (This)->lpVtbl->GetStreamConfiguration(This,ulIndex,pullStreamMask)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMuxStreamMediaTypeManager_QueryInterface(IMFMuxStreamMediaTypeManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMuxStreamMediaTypeManager_AddRef(IMFMuxStreamMediaTypeManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMuxStreamMediaTypeManager_Release(IMFMuxStreamMediaTypeManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMuxStreamMediaTypeManager methods ***/
static inline HRESULT IMFMuxStreamMediaTypeManager_GetStreamCount(IMFMuxStreamMediaTypeManager* This,DWORD *pdwMuxStreamCount) {
    return This->lpVtbl->GetStreamCount(This,pdwMuxStreamCount);
}
static inline HRESULT IMFMuxStreamMediaTypeManager_GetMediaType(IMFMuxStreamMediaTypeManager* This,DWORD dwMuxStreamIndex,IMFMediaType **ppMediaType) {
    return This->lpVtbl->GetMediaType(This,dwMuxStreamIndex,ppMediaType);
}
static inline HRESULT IMFMuxStreamMediaTypeManager_GetStreamConfigurationCount(IMFMuxStreamMediaTypeManager* This,DWORD *pdwCount) {
    return This->lpVtbl->GetStreamConfigurationCount(This,pdwCount);
}
static inline HRESULT IMFMuxStreamMediaTypeManager_AddStreamConfiguration(IMFMuxStreamMediaTypeManager* This,ULONGLONG ullStreamMask) {
    return This->lpVtbl->AddStreamConfiguration(This,ullStreamMask);
}
static inline HRESULT IMFMuxStreamMediaTypeManager_RemoveStreamConfiguration(IMFMuxStreamMediaTypeManager* This,ULONGLONG ullStreamMask) {
    return This->lpVtbl->RemoveStreamConfiguration(This,ullStreamMask);
}
static inline HRESULT IMFMuxStreamMediaTypeManager_GetStreamConfiguration(IMFMuxStreamMediaTypeManager* This,DWORD ulIndex,ULONGLONG *pullStreamMask) {
    return This->lpVtbl->GetStreamConfiguration(This,ulIndex,pullStreamMask);
}
#endif
#endif

#endif


#endif  /* __IMFMuxStreamMediaTypeManager_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMFMuxStreamSampleManager interface
 */
#ifndef __IMFMuxStreamSampleManager_INTERFACE_DEFINED__
#define __IMFMuxStreamSampleManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFMuxStreamSampleManager, 0x74abbc19, 0xb1cc, 0x4e41, 0xbb,0x8b, 0x9d,0x9b,0x86,0xa8,0xf6,0xca);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("74abbc19-b1cc-4e41-bb8b-9d9b86a8f6ca")
IMFMuxStreamSampleManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetStreamCount(
        DWORD *pdwMuxStreamCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSample(
        DWORD dwMuxStreamIndex,
        IMFSample **ppSample) = 0;

    virtual ULONGLONG STDMETHODCALLTYPE GetStreamConfiguration(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFMuxStreamSampleManager, 0x74abbc19, 0xb1cc, 0x4e41, 0xbb,0x8b, 0x9d,0x9b,0x86,0xa8,0xf6,0xca)
#endif
#else
typedef struct IMFMuxStreamSampleManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFMuxStreamSampleManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFMuxStreamSampleManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFMuxStreamSampleManager *This);

    /*** IMFMuxStreamSampleManager methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStreamCount)(
        IMFMuxStreamSampleManager *This,
        DWORD *pdwMuxStreamCount);

    HRESULT (STDMETHODCALLTYPE *GetSample)(
        IMFMuxStreamSampleManager *This,
        DWORD dwMuxStreamIndex,
        IMFSample **ppSample);

    ULONGLONG (STDMETHODCALLTYPE *GetStreamConfiguration)(
        IMFMuxStreamSampleManager *This);

    END_INTERFACE
} IMFMuxStreamSampleManagerVtbl;

interface IMFMuxStreamSampleManager {
    CONST_VTBL IMFMuxStreamSampleManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFMuxStreamSampleManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFMuxStreamSampleManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFMuxStreamSampleManager_Release(This) (This)->lpVtbl->Release(This)
/*** IMFMuxStreamSampleManager methods ***/
#define IMFMuxStreamSampleManager_GetStreamCount(This,pdwMuxStreamCount) (This)->lpVtbl->GetStreamCount(This,pdwMuxStreamCount)
#define IMFMuxStreamSampleManager_GetSample(This,dwMuxStreamIndex,ppSample) (This)->lpVtbl->GetSample(This,dwMuxStreamIndex,ppSample)
#define IMFMuxStreamSampleManager_GetStreamConfiguration(This) (This)->lpVtbl->GetStreamConfiguration(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFMuxStreamSampleManager_QueryInterface(IMFMuxStreamSampleManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFMuxStreamSampleManager_AddRef(IMFMuxStreamSampleManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFMuxStreamSampleManager_Release(IMFMuxStreamSampleManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFMuxStreamSampleManager methods ***/
static inline HRESULT IMFMuxStreamSampleManager_GetStreamCount(IMFMuxStreamSampleManager* This,DWORD *pdwMuxStreamCount) {
    return This->lpVtbl->GetStreamCount(This,pdwMuxStreamCount);
}
static inline HRESULT IMFMuxStreamSampleManager_GetSample(IMFMuxStreamSampleManager* This,DWORD dwMuxStreamIndex,IMFSample **ppSample) {
    return This->lpVtbl->GetSample(This,dwMuxStreamIndex,ppSample);
}
static inline ULONGLONG IMFMuxStreamSampleManager_GetStreamConfiguration(IMFMuxStreamSampleManager* This) {
    return This->lpVtbl->GetStreamConfiguration(This);
}
#endif
#endif

#endif


#endif  /* __IMFMuxStreamSampleManager_INTERFACE_DEFINED__ */

#endif
#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER LPSAFEARRAY_UserSize     (ULONG *, ULONG, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserMarshal  (ULONG *, unsigned char *, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserUnmarshal(ULONG *, unsigned char *, LPSAFEARRAY *);
void            __RPC_USER LPSAFEARRAY_UserFree     (ULONG *, LPSAFEARRAY *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __mfobjects_h__ */
