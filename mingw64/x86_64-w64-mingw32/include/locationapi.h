/*** Autogenerated by WIDL 10.8 from include/locationapi.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __locationapi_h__
#define __locationapi_h__

/* Forward declarations */

#ifndef __ILocationReport_FWD_DEFINED__
#define __ILocationReport_FWD_DEFINED__
typedef interface ILocationReport ILocationReport;
#ifdef __cplusplus
interface ILocationReport;
#endif /* __cplusplus */
#endif

#ifndef __ICivicAddressReport_FWD_DEFINED__
#define __ICivicAddressReport_FWD_DEFINED__
typedef interface ICivicAddressReport ICivicAddressReport;
#ifdef __cplusplus
interface ICivicAddressReport;
#endif /* __cplusplus */
#endif

#ifndef __ILatLongReport_FWD_DEFINED__
#define __ILatLongReport_FWD_DEFINED__
typedef interface ILatLongReport ILatLongReport;
#ifdef __cplusplus
interface ILatLongReport;
#endif /* __cplusplus */
#endif

#ifndef __IDefaultLocation_FWD_DEFINED__
#define __IDefaultLocation_FWD_DEFINED__
typedef interface IDefaultLocation IDefaultLocation;
#ifdef __cplusplus
interface IDefaultLocation;
#endif /* __cplusplus */
#endif

#ifndef __ILocationEvents_FWD_DEFINED__
#define __ILocationEvents_FWD_DEFINED__
typedef interface ILocationEvents ILocationEvents;
#ifdef __cplusplus
interface ILocationEvents;
#endif /* __cplusplus */
#endif

#ifndef __ILocation_FWD_DEFINED__
#define __ILocation_FWD_DEFINED__
typedef interface ILocation ILocation;
#ifdef __cplusplus
interface ILocation;
#endif /* __cplusplus */
#endif

#ifndef __Location_FWD_DEFINED__
#define __Location_FWD_DEFINED__
#ifdef __cplusplus
typedef class Location Location;
#else
typedef struct Location Location;
#endif /* defined __cplusplus */
#endif /* defined __Location_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <sensorsapi.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <propkeydef.h>
typedef enum LOCATION_REPORT_STATUS {
    REPORT_NOT_SUPPORTED = 0,
    REPORT_ERROR = 1,
    REPORT_ACCESS_DENIED = 2,
    REPORT_INITIALIZING = 3,
    REPORT_RUNNING = 4
} LOCATION_REPORT_STATUS;
/*****************************************************************************
 * ILocationReport interface
 */
#ifndef __ILocationReport_INTERFACE_DEFINED__
#define __ILocationReport_INTERFACE_DEFINED__

DEFINE_GUID(IID_ILocationReport, 0xc8b7f7ee, 0x75d0, 0x4db9, 0xb6,0x2d, 0x7a,0x0f,0x36,0x9c,0xa4,0x56);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c8b7f7ee-75d0-4db9-b62d-7a0f369ca456")
ILocationReport : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSensorID(
        SENSOR_ID *pSensorID) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTimestamp(
        SYSTEMTIME *pCreationTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetValue(
        REFPROPERTYKEY pKey,
        PROPVARIANT *pValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ILocationReport, 0xc8b7f7ee, 0x75d0, 0x4db9, 0xb6,0x2d, 0x7a,0x0f,0x36,0x9c,0xa4,0x56)
#endif
#else
typedef struct ILocationReportVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ILocationReport *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ILocationReport *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ILocationReport *This);

    /*** ILocationReport methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSensorID)(
        ILocationReport *This,
        SENSOR_ID *pSensorID);

    HRESULT (STDMETHODCALLTYPE *GetTimestamp)(
        ILocationReport *This,
        SYSTEMTIME *pCreationTime);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        ILocationReport *This,
        REFPROPERTYKEY pKey,
        PROPVARIANT *pValue);

    END_INTERFACE
} ILocationReportVtbl;

interface ILocationReport {
    CONST_VTBL ILocationReportVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ILocationReport_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ILocationReport_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ILocationReport_Release(This) (This)->lpVtbl->Release(This)
/*** ILocationReport methods ***/
#define ILocationReport_GetSensorID(This,pSensorID) (This)->lpVtbl->GetSensorID(This,pSensorID)
#define ILocationReport_GetTimestamp(This,pCreationTime) (This)->lpVtbl->GetTimestamp(This,pCreationTime)
#define ILocationReport_GetValue(This,pKey,pValue) (This)->lpVtbl->GetValue(This,pKey,pValue)
#else
/*** IUnknown methods ***/
static inline HRESULT ILocationReport_QueryInterface(ILocationReport* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ILocationReport_AddRef(ILocationReport* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ILocationReport_Release(ILocationReport* This) {
    return This->lpVtbl->Release(This);
}
/*** ILocationReport methods ***/
static inline HRESULT ILocationReport_GetSensorID(ILocationReport* This,SENSOR_ID *pSensorID) {
    return This->lpVtbl->GetSensorID(This,pSensorID);
}
static inline HRESULT ILocationReport_GetTimestamp(ILocationReport* This,SYSTEMTIME *pCreationTime) {
    return This->lpVtbl->GetTimestamp(This,pCreationTime);
}
static inline HRESULT ILocationReport_GetValue(ILocationReport* This,REFPROPERTYKEY pKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetValue(This,pKey,pValue);
}
#endif
#endif

#endif


#endif  /* __ILocationReport_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICivicAddressReport interface
 */
#ifndef __ICivicAddressReport_INTERFACE_DEFINED__
#define __ICivicAddressReport_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICivicAddressReport, 0xc0b19f70, 0x4adf, 0x445d, 0x87,0xf2, 0xca,0xd8,0xfd,0x71,0x17,0x92);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0b19f70-4adf-445d-87f2-cad8fd711792")
ICivicAddressReport : public ILocationReport
{
    virtual HRESULT STDMETHODCALLTYPE GetAddressLine1(
        BSTR *pbstrAddress1) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAddressLine2(
        BSTR *pbstrAddress2) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCity(
        BSTR *pbstrCity) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStateProvince(
        BSTR *pbstrStateProvince) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPostalCode(
        BSTR *pbstrPostalCode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCountryRegion(
        BSTR *pbstrCountryRegion) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDetailLevel(
        DWORD *pDetailLevel) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICivicAddressReport, 0xc0b19f70, 0x4adf, 0x445d, 0x87,0xf2, 0xca,0xd8,0xfd,0x71,0x17,0x92)
#endif
#else
typedef struct ICivicAddressReportVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICivicAddressReport *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICivicAddressReport *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICivicAddressReport *This);

    /*** ILocationReport methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSensorID)(
        ICivicAddressReport *This,
        SENSOR_ID *pSensorID);

    HRESULT (STDMETHODCALLTYPE *GetTimestamp)(
        ICivicAddressReport *This,
        SYSTEMTIME *pCreationTime);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        ICivicAddressReport *This,
        REFPROPERTYKEY pKey,
        PROPVARIANT *pValue);

    /*** ICivicAddressReport methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAddressLine1)(
        ICivicAddressReport *This,
        BSTR *pbstrAddress1);

    HRESULT (STDMETHODCALLTYPE *GetAddressLine2)(
        ICivicAddressReport *This,
        BSTR *pbstrAddress2);

    HRESULT (STDMETHODCALLTYPE *GetCity)(
        ICivicAddressReport *This,
        BSTR *pbstrCity);

    HRESULT (STDMETHODCALLTYPE *GetStateProvince)(
        ICivicAddressReport *This,
        BSTR *pbstrStateProvince);

    HRESULT (STDMETHODCALLTYPE *GetPostalCode)(
        ICivicAddressReport *This,
        BSTR *pbstrPostalCode);

    HRESULT (STDMETHODCALLTYPE *GetCountryRegion)(
        ICivicAddressReport *This,
        BSTR *pbstrCountryRegion);

    HRESULT (STDMETHODCALLTYPE *GetDetailLevel)(
        ICivicAddressReport *This,
        DWORD *pDetailLevel);

    END_INTERFACE
} ICivicAddressReportVtbl;

interface ICivicAddressReport {
    CONST_VTBL ICivicAddressReportVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICivicAddressReport_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICivicAddressReport_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICivicAddressReport_Release(This) (This)->lpVtbl->Release(This)
/*** ILocationReport methods ***/
#define ICivicAddressReport_GetSensorID(This,pSensorID) (This)->lpVtbl->GetSensorID(This,pSensorID)
#define ICivicAddressReport_GetTimestamp(This,pCreationTime) (This)->lpVtbl->GetTimestamp(This,pCreationTime)
#define ICivicAddressReport_GetValue(This,pKey,pValue) (This)->lpVtbl->GetValue(This,pKey,pValue)
/*** ICivicAddressReport methods ***/
#define ICivicAddressReport_GetAddressLine1(This,pbstrAddress1) (This)->lpVtbl->GetAddressLine1(This,pbstrAddress1)
#define ICivicAddressReport_GetAddressLine2(This,pbstrAddress2) (This)->lpVtbl->GetAddressLine2(This,pbstrAddress2)
#define ICivicAddressReport_GetCity(This,pbstrCity) (This)->lpVtbl->GetCity(This,pbstrCity)
#define ICivicAddressReport_GetStateProvince(This,pbstrStateProvince) (This)->lpVtbl->GetStateProvince(This,pbstrStateProvince)
#define ICivicAddressReport_GetPostalCode(This,pbstrPostalCode) (This)->lpVtbl->GetPostalCode(This,pbstrPostalCode)
#define ICivicAddressReport_GetCountryRegion(This,pbstrCountryRegion) (This)->lpVtbl->GetCountryRegion(This,pbstrCountryRegion)
#define ICivicAddressReport_GetDetailLevel(This,pDetailLevel) (This)->lpVtbl->GetDetailLevel(This,pDetailLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT ICivicAddressReport_QueryInterface(ICivicAddressReport* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICivicAddressReport_AddRef(ICivicAddressReport* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICivicAddressReport_Release(ICivicAddressReport* This) {
    return This->lpVtbl->Release(This);
}
/*** ILocationReport methods ***/
static inline HRESULT ICivicAddressReport_GetSensorID(ICivicAddressReport* This,SENSOR_ID *pSensorID) {
    return This->lpVtbl->GetSensorID(This,pSensorID);
}
static inline HRESULT ICivicAddressReport_GetTimestamp(ICivicAddressReport* This,SYSTEMTIME *pCreationTime) {
    return This->lpVtbl->GetTimestamp(This,pCreationTime);
}
static inline HRESULT ICivicAddressReport_GetValue(ICivicAddressReport* This,REFPROPERTYKEY pKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetValue(This,pKey,pValue);
}
/*** ICivicAddressReport methods ***/
static inline HRESULT ICivicAddressReport_GetAddressLine1(ICivicAddressReport* This,BSTR *pbstrAddress1) {
    return This->lpVtbl->GetAddressLine1(This,pbstrAddress1);
}
static inline HRESULT ICivicAddressReport_GetAddressLine2(ICivicAddressReport* This,BSTR *pbstrAddress2) {
    return This->lpVtbl->GetAddressLine2(This,pbstrAddress2);
}
static inline HRESULT ICivicAddressReport_GetCity(ICivicAddressReport* This,BSTR *pbstrCity) {
    return This->lpVtbl->GetCity(This,pbstrCity);
}
static inline HRESULT ICivicAddressReport_GetStateProvince(ICivicAddressReport* This,BSTR *pbstrStateProvince) {
    return This->lpVtbl->GetStateProvince(This,pbstrStateProvince);
}
static inline HRESULT ICivicAddressReport_GetPostalCode(ICivicAddressReport* This,BSTR *pbstrPostalCode) {
    return This->lpVtbl->GetPostalCode(This,pbstrPostalCode);
}
static inline HRESULT ICivicAddressReport_GetCountryRegion(ICivicAddressReport* This,BSTR *pbstrCountryRegion) {
    return This->lpVtbl->GetCountryRegion(This,pbstrCountryRegion);
}
static inline HRESULT ICivicAddressReport_GetDetailLevel(ICivicAddressReport* This,DWORD *pDetailLevel) {
    return This->lpVtbl->GetDetailLevel(This,pDetailLevel);
}
#endif
#endif

#endif


#endif  /* __ICivicAddressReport_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ILatLongReport interface
 */
#ifndef __ILatLongReport_INTERFACE_DEFINED__
#define __ILatLongReport_INTERFACE_DEFINED__

DEFINE_GUID(IID_ILatLongReport, 0x7fed806d, 0x0ef8, 0x4f07, 0x80,0xac, 0x36,0xa0,0xbe,0xae,0x31,0x34);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7fed806d-0ef8-4f07-80ac-36a0beae3134")
ILatLongReport : public ILocationReport
{
    virtual HRESULT STDMETHODCALLTYPE GetLatitude(
        DOUBLE *pLatitude) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLongitude(
        DOUBLE *pLongitude) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetErrorRadius(
        DOUBLE *pErrorRadius) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAltitude(
        DOUBLE *pAltitude) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAltitudeError(
        DOUBLE *pAltitudeError) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ILatLongReport, 0x7fed806d, 0x0ef8, 0x4f07, 0x80,0xac, 0x36,0xa0,0xbe,0xae,0x31,0x34)
#endif
#else
typedef struct ILatLongReportVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ILatLongReport *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ILatLongReport *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ILatLongReport *This);

    /*** ILocationReport methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSensorID)(
        ILatLongReport *This,
        SENSOR_ID *pSensorID);

    HRESULT (STDMETHODCALLTYPE *GetTimestamp)(
        ILatLongReport *This,
        SYSTEMTIME *pCreationTime);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        ILatLongReport *This,
        REFPROPERTYKEY pKey,
        PROPVARIANT *pValue);

    /*** ILatLongReport methods ***/
    HRESULT (STDMETHODCALLTYPE *GetLatitude)(
        ILatLongReport *This,
        DOUBLE *pLatitude);

    HRESULT (STDMETHODCALLTYPE *GetLongitude)(
        ILatLongReport *This,
        DOUBLE *pLongitude);

    HRESULT (STDMETHODCALLTYPE *GetErrorRadius)(
        ILatLongReport *This,
        DOUBLE *pErrorRadius);

    HRESULT (STDMETHODCALLTYPE *GetAltitude)(
        ILatLongReport *This,
        DOUBLE *pAltitude);

    HRESULT (STDMETHODCALLTYPE *GetAltitudeError)(
        ILatLongReport *This,
        DOUBLE *pAltitudeError);

    END_INTERFACE
} ILatLongReportVtbl;

interface ILatLongReport {
    CONST_VTBL ILatLongReportVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ILatLongReport_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ILatLongReport_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ILatLongReport_Release(This) (This)->lpVtbl->Release(This)
/*** ILocationReport methods ***/
#define ILatLongReport_GetSensorID(This,pSensorID) (This)->lpVtbl->GetSensorID(This,pSensorID)
#define ILatLongReport_GetTimestamp(This,pCreationTime) (This)->lpVtbl->GetTimestamp(This,pCreationTime)
#define ILatLongReport_GetValue(This,pKey,pValue) (This)->lpVtbl->GetValue(This,pKey,pValue)
/*** ILatLongReport methods ***/
#define ILatLongReport_GetLatitude(This,pLatitude) (This)->lpVtbl->GetLatitude(This,pLatitude)
#define ILatLongReport_GetLongitude(This,pLongitude) (This)->lpVtbl->GetLongitude(This,pLongitude)
#define ILatLongReport_GetErrorRadius(This,pErrorRadius) (This)->lpVtbl->GetErrorRadius(This,pErrorRadius)
#define ILatLongReport_GetAltitude(This,pAltitude) (This)->lpVtbl->GetAltitude(This,pAltitude)
#define ILatLongReport_GetAltitudeError(This,pAltitudeError) (This)->lpVtbl->GetAltitudeError(This,pAltitudeError)
#else
/*** IUnknown methods ***/
static inline HRESULT ILatLongReport_QueryInterface(ILatLongReport* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ILatLongReport_AddRef(ILatLongReport* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ILatLongReport_Release(ILatLongReport* This) {
    return This->lpVtbl->Release(This);
}
/*** ILocationReport methods ***/
static inline HRESULT ILatLongReport_GetSensorID(ILatLongReport* This,SENSOR_ID *pSensorID) {
    return This->lpVtbl->GetSensorID(This,pSensorID);
}
static inline HRESULT ILatLongReport_GetTimestamp(ILatLongReport* This,SYSTEMTIME *pCreationTime) {
    return This->lpVtbl->GetTimestamp(This,pCreationTime);
}
static inline HRESULT ILatLongReport_GetValue(ILatLongReport* This,REFPROPERTYKEY pKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetValue(This,pKey,pValue);
}
/*** ILatLongReport methods ***/
static inline HRESULT ILatLongReport_GetLatitude(ILatLongReport* This,DOUBLE *pLatitude) {
    return This->lpVtbl->GetLatitude(This,pLatitude);
}
static inline HRESULT ILatLongReport_GetLongitude(ILatLongReport* This,DOUBLE *pLongitude) {
    return This->lpVtbl->GetLongitude(This,pLongitude);
}
static inline HRESULT ILatLongReport_GetErrorRadius(ILatLongReport* This,DOUBLE *pErrorRadius) {
    return This->lpVtbl->GetErrorRadius(This,pErrorRadius);
}
static inline HRESULT ILatLongReport_GetAltitude(ILatLongReport* This,DOUBLE *pAltitude) {
    return This->lpVtbl->GetAltitude(This,pAltitude);
}
static inline HRESULT ILatLongReport_GetAltitudeError(ILatLongReport* This,DOUBLE *pAltitudeError) {
    return This->lpVtbl->GetAltitudeError(This,pAltitudeError);
}
#endif
#endif

#endif


#endif  /* __ILatLongReport_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDefaultLocation interface
 */
#ifndef __IDefaultLocation_INTERFACE_DEFINED__
#define __IDefaultLocation_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDefaultLocation, 0xa65af77e, 0x969a, 0x4a2e, 0x8a,0xca, 0x33,0xbb,0x7c,0xbb,0x12,0x35);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a65af77e-969a-4a2e-8aca-33bb7cbb1235")
IDefaultLocation : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetReport(
        REFIID reportType,
        ILocationReport *pLocationReport) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetReport(
        REFIID reportType,
        ILocationReport **ppLocationReport) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDefaultLocation, 0xa65af77e, 0x969a, 0x4a2e, 0x8a,0xca, 0x33,0xbb,0x7c,0xbb,0x12,0x35)
#endif
#else
typedef struct IDefaultLocationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDefaultLocation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDefaultLocation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDefaultLocation *This);

    /*** IDefaultLocation methods ***/
    HRESULT (STDMETHODCALLTYPE *SetReport)(
        IDefaultLocation *This,
        REFIID reportType,
        ILocationReport *pLocationReport);

    HRESULT (STDMETHODCALLTYPE *GetReport)(
        IDefaultLocation *This,
        REFIID reportType,
        ILocationReport **ppLocationReport);

    END_INTERFACE
} IDefaultLocationVtbl;

interface IDefaultLocation {
    CONST_VTBL IDefaultLocationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDefaultLocation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDefaultLocation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDefaultLocation_Release(This) (This)->lpVtbl->Release(This)
/*** IDefaultLocation methods ***/
#define IDefaultLocation_SetReport(This,reportType,pLocationReport) (This)->lpVtbl->SetReport(This,reportType,pLocationReport)
#define IDefaultLocation_GetReport(This,reportType,ppLocationReport) (This)->lpVtbl->GetReport(This,reportType,ppLocationReport)
#else
/*** IUnknown methods ***/
static inline HRESULT IDefaultLocation_QueryInterface(IDefaultLocation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDefaultLocation_AddRef(IDefaultLocation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDefaultLocation_Release(IDefaultLocation* This) {
    return This->lpVtbl->Release(This);
}
/*** IDefaultLocation methods ***/
static inline HRESULT IDefaultLocation_SetReport(IDefaultLocation* This,REFIID reportType,ILocationReport *pLocationReport) {
    return This->lpVtbl->SetReport(This,reportType,pLocationReport);
}
static inline HRESULT IDefaultLocation_GetReport(IDefaultLocation* This,REFIID reportType,ILocationReport **ppLocationReport) {
    return This->lpVtbl->GetReport(This,reportType,ppLocationReport);
}
#endif
#endif

#endif


#endif  /* __IDefaultLocation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ILocationEvents interface
 */
#ifndef __ILocationEvents_INTERFACE_DEFINED__
#define __ILocationEvents_INTERFACE_DEFINED__

DEFINE_GUID(IID_ILocationEvents, 0xcae02bbf, 0x798b, 0x4508, 0xa2,0x07, 0x35,0xa7,0x90,0x6d,0xc7,0x3d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cae02bbf-798b-4508-a207-35a7906dc73d")
ILocationEvents : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnLocationChanged(
        REFIID reportType,
        ILocationReport *pLocationReport) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnStatusChanged(
        REFIID reportType,
        LOCATION_REPORT_STATUS newStatus) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ILocationEvents, 0xcae02bbf, 0x798b, 0x4508, 0xa2,0x07, 0x35,0xa7,0x90,0x6d,0xc7,0x3d)
#endif
#else
typedef struct ILocationEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ILocationEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ILocationEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ILocationEvents *This);

    /*** ILocationEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *OnLocationChanged)(
        ILocationEvents *This,
        REFIID reportType,
        ILocationReport *pLocationReport);

    HRESULT (STDMETHODCALLTYPE *OnStatusChanged)(
        ILocationEvents *This,
        REFIID reportType,
        LOCATION_REPORT_STATUS newStatus);

    END_INTERFACE
} ILocationEventsVtbl;

interface ILocationEvents {
    CONST_VTBL ILocationEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ILocationEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ILocationEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ILocationEvents_Release(This) (This)->lpVtbl->Release(This)
/*** ILocationEvents methods ***/
#define ILocationEvents_OnLocationChanged(This,reportType,pLocationReport) (This)->lpVtbl->OnLocationChanged(This,reportType,pLocationReport)
#define ILocationEvents_OnStatusChanged(This,reportType,newStatus) (This)->lpVtbl->OnStatusChanged(This,reportType,newStatus)
#else
/*** IUnknown methods ***/
static inline HRESULT ILocationEvents_QueryInterface(ILocationEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ILocationEvents_AddRef(ILocationEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ILocationEvents_Release(ILocationEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** ILocationEvents methods ***/
static inline HRESULT ILocationEvents_OnLocationChanged(ILocationEvents* This,REFIID reportType,ILocationReport *pLocationReport) {
    return This->lpVtbl->OnLocationChanged(This,reportType,pLocationReport);
}
static inline HRESULT ILocationEvents_OnStatusChanged(ILocationEvents* This,REFIID reportType,LOCATION_REPORT_STATUS newStatus) {
    return This->lpVtbl->OnStatusChanged(This,reportType,newStatus);
}
#endif
#endif

#endif


#endif  /* __ILocationEvents_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ILocation interface
 */
#ifndef __ILocation_INTERFACE_DEFINED__
#define __ILocation_INTERFACE_DEFINED__

DEFINE_GUID(IID_ILocation, 0xab2ece69, 0x56d9, 0x4f28, 0xb5,0x25, 0xde,0x1b,0x0e,0xe4,0x42,0x37);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ab2ece69-56d9-4f28-b525-de1b0ee44237")
ILocation : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE RegisterForReport(
        ILocationEvents *pEvents,
        REFIID reportType,
        DWORD dwRequestedReportInterval) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterForReport(
        REFIID reportType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetReport(
        REFIID reportType,
        ILocationReport **ppLocationReport) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetReportStatus(
        REFIID reportType,
        LOCATION_REPORT_STATUS *pStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetReportInterval(
        REFIID reportType,
        DWORD *pMilliseconds) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetReportInterval(
        REFIID reportType,
        DWORD millisecondsRequested) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDesiredAccuracy(
        REFIID reportType,
        LOCATION_DESIRED_ACCURACY *pDesiredAccuracy) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDesiredAccuracy(
        REFIID reportType,
        LOCATION_DESIRED_ACCURACY desiredAccuracy) = 0;

    virtual HRESULT STDMETHODCALLTYPE RequestPermissions(
        HWND hParent,
        IID pReportTypes[],
        ULONG count,
        WINBOOL fModal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ILocation, 0xab2ece69, 0x56d9, 0x4f28, 0xb5,0x25, 0xde,0x1b,0x0e,0xe4,0x42,0x37)
#endif
#else
typedef struct ILocationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ILocation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ILocation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ILocation *This);

    /*** ILocation methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterForReport)(
        ILocation *This,
        ILocationEvents *pEvents,
        REFIID reportType,
        DWORD dwRequestedReportInterval);

    HRESULT (STDMETHODCALLTYPE *UnregisterForReport)(
        ILocation *This,
        REFIID reportType);

    HRESULT (STDMETHODCALLTYPE *GetReport)(
        ILocation *This,
        REFIID reportType,
        ILocationReport **ppLocationReport);

    HRESULT (STDMETHODCALLTYPE *GetReportStatus)(
        ILocation *This,
        REFIID reportType,
        LOCATION_REPORT_STATUS *pStatus);

    HRESULT (STDMETHODCALLTYPE *GetReportInterval)(
        ILocation *This,
        REFIID reportType,
        DWORD *pMilliseconds);

    HRESULT (STDMETHODCALLTYPE *SetReportInterval)(
        ILocation *This,
        REFIID reportType,
        DWORD millisecondsRequested);

    HRESULT (STDMETHODCALLTYPE *GetDesiredAccuracy)(
        ILocation *This,
        REFIID reportType,
        LOCATION_DESIRED_ACCURACY *pDesiredAccuracy);

    HRESULT (STDMETHODCALLTYPE *SetDesiredAccuracy)(
        ILocation *This,
        REFIID reportType,
        LOCATION_DESIRED_ACCURACY desiredAccuracy);

    HRESULT (STDMETHODCALLTYPE *RequestPermissions)(
        ILocation *This,
        HWND hParent,
        IID pReportTypes[],
        ULONG count,
        WINBOOL fModal);

    END_INTERFACE
} ILocationVtbl;

interface ILocation {
    CONST_VTBL ILocationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ILocation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ILocation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ILocation_Release(This) (This)->lpVtbl->Release(This)
/*** ILocation methods ***/
#define ILocation_RegisterForReport(This,pEvents,reportType,dwRequestedReportInterval) (This)->lpVtbl->RegisterForReport(This,pEvents,reportType,dwRequestedReportInterval)
#define ILocation_UnregisterForReport(This,reportType) (This)->lpVtbl->UnregisterForReport(This,reportType)
#define ILocation_GetReport(This,reportType,ppLocationReport) (This)->lpVtbl->GetReport(This,reportType,ppLocationReport)
#define ILocation_GetReportStatus(This,reportType,pStatus) (This)->lpVtbl->GetReportStatus(This,reportType,pStatus)
#define ILocation_GetReportInterval(This,reportType,pMilliseconds) (This)->lpVtbl->GetReportInterval(This,reportType,pMilliseconds)
#define ILocation_SetReportInterval(This,reportType,millisecondsRequested) (This)->lpVtbl->SetReportInterval(This,reportType,millisecondsRequested)
#define ILocation_GetDesiredAccuracy(This,reportType,pDesiredAccuracy) (This)->lpVtbl->GetDesiredAccuracy(This,reportType,pDesiredAccuracy)
#define ILocation_SetDesiredAccuracy(This,reportType,desiredAccuracy) (This)->lpVtbl->SetDesiredAccuracy(This,reportType,desiredAccuracy)
#define ILocation_RequestPermissions(This,hParent,pReportTypes,count,fModal) (This)->lpVtbl->RequestPermissions(This,hParent,pReportTypes,count,fModal)
#else
/*** IUnknown methods ***/
static inline HRESULT ILocation_QueryInterface(ILocation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ILocation_AddRef(ILocation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ILocation_Release(ILocation* This) {
    return This->lpVtbl->Release(This);
}
/*** ILocation methods ***/
static inline HRESULT ILocation_RegisterForReport(ILocation* This,ILocationEvents *pEvents,REFIID reportType,DWORD dwRequestedReportInterval) {
    return This->lpVtbl->RegisterForReport(This,pEvents,reportType,dwRequestedReportInterval);
}
static inline HRESULT ILocation_UnregisterForReport(ILocation* This,REFIID reportType) {
    return This->lpVtbl->UnregisterForReport(This,reportType);
}
static inline HRESULT ILocation_GetReport(ILocation* This,REFIID reportType,ILocationReport **ppLocationReport) {
    return This->lpVtbl->GetReport(This,reportType,ppLocationReport);
}
static inline HRESULT ILocation_GetReportStatus(ILocation* This,REFIID reportType,LOCATION_REPORT_STATUS *pStatus) {
    return This->lpVtbl->GetReportStatus(This,reportType,pStatus);
}
static inline HRESULT ILocation_GetReportInterval(ILocation* This,REFIID reportType,DWORD *pMilliseconds) {
    return This->lpVtbl->GetReportInterval(This,reportType,pMilliseconds);
}
static inline HRESULT ILocation_SetReportInterval(ILocation* This,REFIID reportType,DWORD millisecondsRequested) {
    return This->lpVtbl->SetReportInterval(This,reportType,millisecondsRequested);
}
static inline HRESULT ILocation_GetDesiredAccuracy(ILocation* This,REFIID reportType,LOCATION_DESIRED_ACCURACY *pDesiredAccuracy) {
    return This->lpVtbl->GetDesiredAccuracy(This,reportType,pDesiredAccuracy);
}
static inline HRESULT ILocation_SetDesiredAccuracy(ILocation* This,REFIID reportType,LOCATION_DESIRED_ACCURACY desiredAccuracy) {
    return This->lpVtbl->SetDesiredAccuracy(This,reportType,desiredAccuracy);
}
static inline HRESULT ILocation_RequestPermissions(ILocation* This,HWND hParent,IID pReportTypes[],ULONG count,WINBOOL fModal) {
    return This->lpVtbl->RequestPermissions(This,hParent,pReportTypes,count,fModal);
}
#endif
#endif

#endif


#endif  /* __ILocation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * Location coclass
 */

DEFINE_GUID(CLSID_Location, 0xe5b8e079, 0xee6d, 0x4e33, 0xa4,0x38, 0xc8,0x7f,0x2e,0x95,0x92,0x54);

#ifdef __cplusplus
class DECLSPEC_UUID("e5b8e079-ee6d-4e33-a438-c87f2e959254") Location;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(Location, 0xe5b8e079, 0xee6d, 0x4e33, 0xa4,0x38, 0xc8,0x7f,0x2e,0x95,0x92,0x54)
#endif
#endif

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER LPSAFEARRAY_UserSize     (ULONG *, ULONG, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserMarshal  (ULONG *, unsigned char *, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserUnmarshal(ULONG *, unsigned char *, LPSAFEARRAY *);
void            __RPC_USER LPSAFEARRAY_UserFree     (ULONG *, LPSAFEARRAY *);
ULONG           __RPC_USER HWND_UserSize     (ULONG *, ULONG, HWND *);
unsigned char * __RPC_USER HWND_UserMarshal  (ULONG *, unsigned char *, HWND *);
unsigned char * __RPC_USER HWND_UserUnmarshal(ULONG *, unsigned char *, HWND *);
void            __RPC_USER HWND_UserFree     (ULONG *, HWND *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __locationapi_h__ */
