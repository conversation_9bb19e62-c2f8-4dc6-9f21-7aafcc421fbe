/*
 * Copyright (C) 2005 <PERSON>
 * Copyright (C) 2008 <PERSON>
 * Copyright (C) 2010 Nikolay Sivov for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#include <msxml2did.h>
#include <idispids.h>

import "unknwn.idl";
import "objidl.idl";
import "oaidl.idl";

#if !defined(progid) && !defined(__WIDL__)
#define threading(model)
#define progid(str)
#define vi_progid(str)
#endif

cpp_quote("#ifdef __ISAXXMLReader_INTERFACE_DEFINED__")
cpp_quote("#undef __MSXML2_LIBRARY_DEFINED__")
cpp_quote("#endif")

[
    uuid(f5078f18-c551-11d3-89b9-0000f81fe221),
    version(3.0),
    helpstring("Microsoft XML, v3.0")
]
library MSXML2
{

importlib("stdole2.tlb");

interface IXMLDOMImplementation;
interface IXMLDOMNode;
interface IXMLDOMDocumentFragment;
interface IXMLDOMDocument;
interface IXMLDOMSchemaCollection;
interface IXMLDOMDocument2;
interface IXMLDOMNodeList;
interface IXMLDOMNamedNodeMap;
interface IXMLDOMCharacterData;
interface IXMLDOMAttribute;
interface IXMLDOMElement;
interface IXMLDOMText;
interface IXMLDOMComment;
interface IXMLDOMProcessingInstruction;
interface IXMLDOMCDATASection;
interface IXMLDOMDocumentType;
interface IXMLDOMNotation;
interface IXMLDOMEntity;
interface IXMLDOMEntityReference;
interface IXMLDOMParseError;
interface IXMLDOMParseErrorCollection;
interface IXTLRuntime;
interface IXSLTemplate;
interface IXSLProcessor;

/* SAX Interfaces */
interface ISAXAttributes;
interface ISAXContentHandler;
interface ISAXDeclHandler;
interface ISAXDTDHandler;
interface ISAXEntityResolver;
interface ISAXErrorHandler;
interface ISAXLexicalHandler;
interface ISAXLocator;
interface ISAXXMLFilter;
interface ISAXXMLReader;

interface IVBSAXAttributes;
interface IVBSAXContentHandler;
interface IVBSAXDeclHandler;
interface IVBSAXDTDHandler;
interface IVBSAXEntityResolver;
interface IVBSAXErrorHandler;
interface IVBSAXLexicalHandler;
interface IVBSAXLocator;
interface IVBSAXXMLFilter;
interface IVBSAXXMLReader;

interface IMXAttributes;
interface IMXReaderControl;
interface IMXWriter;

interface IXMLDOMSchemaCollection2;
interface ISchemaStringCollection;
interface ISchemaItemCollection;
interface ISchemaItem;
interface ISchema;
interface ISchemaParticle;
interface ISchemaAttribute;
interface ISchemaElement;
interface ISchemaType;
interface ISchemaComplexType;
interface ISchemaAttributeGroup;
interface ISchemaModelGroup;
interface ISchemaAny;
interface ISchemaIdentityConstraint;
interface ISchemaNotation;


cpp_quote("#define DOMDocument DOMDocument2")
cpp_quote("#define CLSID_DOMDocument CLSID_DOMDocument2")

cpp_quote("#ifndef __MSXML_DOMNODETYPE_DEFINED")
cpp_quote("#define __MSXML_DOMNODETYPE_DEFINED")
typedef enum tagDOMNodeType 
{  
    NODE_INVALID  		= 0,
    NODE_ELEMENT  		= 1,
    NODE_ATTRIBUTE		= 2,
    NODE_TEXT			= 3,
    NODE_CDATA_SECTION		= 4,
    NODE_ENTITY_REFERENCE 	= 5,
    NODE_ENTITY 		= 6,
    NODE_PROCESSING_INSTRUCTION = 7,
    NODE_COMMENT		= 8,
    NODE_DOCUMENT		= 9,
    NODE_DOCUMENT_TYPE		= 10,
    NODE_DOCUMENT_FRAGMENT 	= 11,
    NODE_NOTATION 		= 12
} DOMNodeType;
cpp_quote("#endif")

cpp_quote("#ifndef __msxml_som_enums__")
cpp_quote("#define __msxml_som_enums__")
typedef enum _SOMITEMTYPE
{
    SOMITEM_SCHEMA                      = 0x1000,
    SOMITEM_ATTRIBUTE                   = 0x1001,
    SOMITEM_ATTRIBUTEGROUP              = 0x1002,
    SOMITEM_NOTATION                    = 0x1003,
    SOMITEM_IDENTITYCONSTRAINT          = 0x1100,
    SOMITEM_KEY                         = 0x1101,
    SOMITEM_KEYREF                      = 0x1102,
    SOMITEM_UNIQUE                      = 0x1103,
    SOMITEM_ANYTYPE                     = 0x2000,
    SOMITEM_DATATYPE                    = 0x2100,
    SOMITEM_DATATYPE_ANYTYPE            = 0x2101,
    SOMITEM_DATATYPE_ANYURI             = 0x2102,
    SOMITEM_DATATYPE_BASE64BINARY       = 0x2103,
    SOMITEM_DATATYPE_BOOLEAN            = 0x2104,
    SOMITEM_DATATYPE_BYTE               = 0x2105,
    SOMITEM_DATATYPE_DATE               = 0x2106,
    SOMITEM_DATATYPE_DATETIME           = 0x2107,
    SOMITEM_DATATYPE_DAY                = 0x2108,
    SOMITEM_DATATYPE_DECIMAL            = 0x2109,
    SOMITEM_DATATYPE_DOUBLE             = 0x210A,
    SOMITEM_DATATYPE_DURATION           = 0x210B,
    SOMITEM_DATATYPE_ENTITIES           = 0x210C,
    SOMITEM_DATATYPE_ENTITY             = 0x210D,
    SOMITEM_DATATYPE_FLOAT              = 0x210E,
    SOMITEM_DATATYPE_HEXBINARY          = 0x210F,
    SOMITEM_DATATYPE_ID                 = 0x2110,
    SOMITEM_DATATYPE_IDREF              = 0x2111,
    SOMITEM_DATATYPE_IDREFS             = 0x2112,
    SOMITEM_DATATYPE_INT                = 0x2113,
    SOMITEM_DATATYPE_INTEGER            = 0x2114,
    SOMITEM_DATATYPE_LANGUAGE           = 0x2115,
    SOMITEM_DATATYPE_LONG               = 0x2116,
    SOMITEM_DATATYPE_MONTH              = 0x2117,
    SOMITEM_DATATYPE_MONTHDAY           = 0x2118,
    SOMITEM_DATATYPE_NAME               = 0x2119,
    SOMITEM_DATATYPE_NCNAME             = 0x211A,
    SOMITEM_DATATYPE_NEGATIVEINTEGER    = 0x211B,
    SOMITEM_DATATYPE_NMTOKEN            = 0x211C,
    SOMITEM_DATATYPE_NMTOKENS           = 0x211D,
    SOMITEM_DATATYPE_NONNEGATIVEINTEGER = 0x211E,
    SOMITEM_DATATYPE_NONPOSITIVEINTEGER = 0x211F,
    SOMITEM_DATATYPE_NORMALIZEDSTRING   = 0x2120,
    SOMITEM_DATATYPE_NOTATION           = 0x2121,
    SOMITEM_DATATYPE_POSITIVEINTEGER    = 0x2122,
    SOMITEM_DATATYPE_QNAME              = 0x2123,
    SOMITEM_DATATYPE_SHORT              = 0x2124,
    SOMITEM_DATATYPE_STRING             = 0x2125,
    SOMITEM_DATATYPE_TIME               = 0x2126,
    SOMITEM_DATATYPE_TOKEN              = 0x2127,
    SOMITEM_DATATYPE_UNSIGNEDBYTE       = 0x2128,
    SOMITEM_DATATYPE_UNSIGNEDINT        = 0x2129,
    SOMITEM_DATATYPE_UNSIGNEDLONG       = 0x212A,
    SOMITEM_DATATYPE_UNSIGNEDSHORT      = 0x212B,
    SOMITEM_DATATYPE_YEAR               = 0x212C,
    SOMITEM_DATATYPE_YEARMONTH          = 0x212D,
    SOMITEM_DATATYPE_ANYSIMPLETYPE      = 0x21FF,
    SOMITEM_SIMPLETYPE                  = 0x2200,
    SOMITEM_COMPLEXTYPE                 = 0x2400,
    SOMITEM_PARTICLE                    = 0x4000,
    SOMITEM_ANY                         = 0x4001,
    SOMITEM_ANYATTRIBUTE                = 0x4002,
    SOMITEM_ELEMENT                     = 0x4003,
    SOMITEM_GROUP                       = 0x4100,
    SOMITEM_ALL                         = 0x4101,
    SOMITEM_CHOICE                      = 0x4102,
    SOMITEM_SEQUENCE                    = 0x4103,
    SOMITEM_EMPTYPARTICLE               = 0x4104,
    SOMITEM_NULL                        = 0x0800,
    SOMITEM_NULL_TYPE                   = 0x2800,
    SOMITEM_NULL_ANY                    = 0x4801,
    SOMITEM_NULL_ANYATTRIBUTE           = 0x4802,
    SOMITEM_NULL_ELEMENT                = 0x4803,
} SOMITEMTYPE;

typedef enum _SCHEMAUSE
{
    SCHEMAUSE_OPTIONAL,
    SCHEMAUSE_PROHIBITED,
    SCHEMAUSE_REQUIRED,
} SCHEMAUSE;

typedef enum _SCHEMADERIVATIONMETHOD
{
    SCHEMADERIVATIONMETHOD_EMPTY        = 0x0000,
    SCHEMADERIVATIONMETHOD_SUBSTITUTION = 0x0001,
    SCHEMADERIVATIONMETHOD_EXTENSION    = 0x0002,
    SCHEMADERIVATIONMETHOD_RESTRICTION  = 0x0004,
    SCHEMADERIVATIONMETHOD_LIST         = 0x0008,
    SCHEMADERIVATIONMETHOD_UNION        = 0x0010,
    SCHEMADERIVATIONMETHOD_ALL          = 0x00FF,
    SCHEMADERIVATIONMETHOD_NONE         = 0x0100,
} SCHEMADERIVATIONMETHOD;

typedef enum _SCHEMACONTENTTYPE
{
    SCHEMACONTENTTYPE_EMPTY,
    SCHEMACONTENTTYPE_TEXTONLY,
    SCHEMACONTENTTYPE_ELEMENTONLY,
    SCHEMACONTENTTYPE_MIXED,
} SCHEMACONTENTTYPE;

typedef enum _SCHEMAPROCESSCONTENTS
{
    SCHEMAPROCESSCONTENTS_NONE,
    SCHEMAPROCESSCONTENTS_SKIP,
    SCHEMAPROCESSCONTENTS_LAX,
    SCHEMAPROCESSCONTENTS_STRICT,
} SCHEMAPROCESSCONTENTS;

typedef enum _SCHEMAWHITESPACE
{
    SCHEMAWHITESPACE_NONE       = -1,
    SCHEMAWHITESPACE_PRESERVE   =  0,
    SCHEMAWHITESPACE_REPLACE    =  1,
    SCHEMAWHITESPACE_COLLAPSE   =  2,
} SCHEMAWHITESPACE;


typedef enum _SCHEMATYPEVARIETY
{
    SCHEMATYPEVARIETY_NONE      = -1,
    SCHEMATYPEVARIETY_ATOMIC    =  0,
    SCHEMATYPEVARIETY_LIST      =  1,
    SCHEMATYPEVARIETY_UNION     =  2,
} SCHEMATYPEVARIETY;
cpp_quote("#endif /* __msxml_som_enums__ */")

[
local,
object,
odl,
dual,
oleautomation,
uuid(65725580-9b5d-11d0-9bfe-00c04fc99c8e)
]
interface IXMLElementCollection : IDispatch
{
    [propput, id(DISPID_XMLELEMENTCOLLECTION_LENGTH)]
    HRESULT length( [in] LONG v );

    [propget, id(DISPID_XMLELEMENTCOLLECTION_LENGTH)]
    HRESULT length( [retval, out] LONG *p );

    [propget, id(DISPID_XMLELEMENTCOLLECTION_NEWENUM)]
    HRESULT _newEnum( [retval, out] IUnknown ** ppUnk );

    [id(DISPID_XMLELEMENTCOLLECTION_ITEM)]
    HRESULT item(
            [optional, in] VARIANT var1,
            [optional, in] VARIANT var2,
            [retval, out] IDispatch ** ppDisp );
}

[
local,
object,
uuid(3f7f31ac-e15f-11d0-9c25-00c04fc99c8e)
]
interface IXMLElement : IDispatch
{
    [propget, id(DISPID_XMLELEMENT_TAGNAME)]
    HRESULT tagName( [retval, out] BSTR *p);

    [propput, id(DISPID_XMLELEMENT_TAGNAME)]
    HRESULT tagName( [in] BSTR p );

    [propget, id(DISPID_XMLELEMENT_PARENT)]
    HRESULT parent( [retval, out] IXMLElement **parent );

    [id(DISPID_XMLELEMENT_SETATTRIBUTE)]
    HRESULT setAttribute(
            [in] BSTR strPropertyName,
            [in] VARIANT PropertyValue );

    [id(DISPID_XMLELEMENT_GETATTRIBUTE)]
    HRESULT getAttribute(
            [in] BSTR strPropertyName,
            [retval, out] VARIANT *PropertyValue );

    [id(DISPID_XMLELEMENT_REMOVEATTRIBUTE)]
    HRESULT removeAttribute(
            [in] BSTR strPropertyName );

    [propget, id(DISPID_XMLELEMENT_CHILDREN)]
    HRESULT children( [retval, out] IXMLElementCollection **p );

    [propget, id(DISPID_XMLELEMENT_TYPE)]
    HRESULT type( [retval, out] LONG *p );

    [propget, id(DISPID_XMLELEMENT_TEXT)]
    HRESULT text( [retval, out] BSTR *p );

    [propput, id(DISPID_XMLELEMENT_TEXT)]
    HRESULT text( [in] BSTR p );

    [id(DISPID_XMLELEMENT_ADDCHILD)]
    HRESULT addChild(
            [in] IXMLElement *pChildElem,
            [in] LONG lIndex,
            [in] LONG lreserved );

    [id(DISPID_XMLELEMENT_REMOVECHILD)]
    HRESULT removeChild(
            [in] IXMLElement *pChildElem );
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF80-7B36-11d2-B20E-00C04F983E60)
]
interface IXMLDOMNode : IDispatch
{
    [propget, id(DISPID_DOM_NODE_NODENAME)]
    HRESULT nodeName([retval, out] BSTR *p);

    [propget, id(DISPID_DOM_NODE_NODEVALUE)]
    HRESULT nodeValue([retval, out] VARIANT *var1);

    [propput, id(DISPID_DOM_NODE_NODEVALUE)]
    HRESULT nodeValue([in] VARIANT var1);

    [propget, id(DISPID_DOM_NODE_NODETYPE)]
    HRESULT nodeType([retval, out] DOMNodeType *domNodeType);

    [propget, id(DISPID_DOM_NODE_PARENTNODE)]
    HRESULT parentNode([retval, out] IXMLDOMNode **parent);

    [propget, id(DISPID_DOM_NODE_CHILDNODES)]
    HRESULT childNodes([retval, out] IXMLDOMNodeList **outList);

    [propget, id(DISPID_DOM_NODE_FIRSTCHILD)]
    HRESULT firstChild([retval, out] IXMLDOMNode **domNode);

    [propget, id(DISPID_DOM_NODE_LASTCHILD)]
    HRESULT lastChild([retval, out] IXMLDOMNode **domNode);

    [propget, id(DISPID_DOM_NODE_PREVIOUSSIBLING)]
    HRESULT previousSibling([retval, out] IXMLDOMNode **domNode);

    [propget, id(DISPID_DOM_NODE_NEXTSIBLING)]
    HRESULT nextSibling([retval, out] IXMLDOMNode **domNode);

    [propget, id(DISPID_DOM_NODE_ATTRIBUTES)]
    HRESULT attributes([retval, out] IXMLDOMNamedNodeMap **attributeMap);

    [id(DISPID_DOM_NODE_INSERTBEFORE)]
    HRESULT insertBefore(
        [in] IXMLDOMNode *newNode,
        [in] VARIANT var1,
        [retval, out] IXMLDOMNode **outOldNode);

    [id(DISPID_DOM_NODE_REPLACECHILD)]
    HRESULT replaceChild(
        [in] IXMLDOMNode *newNode,
        [in] IXMLDOMNode *oldNode,
        [retval, out] IXMLDOMNode **outOldNode);

    [id(DISPID_DOM_NODE_REMOVECHILD)]
    HRESULT removeChild(
        [in] IXMLDOMNode *domNode,
        [retval, out] IXMLDOMNode **oldNode);

    [id(DISPID_DOM_NODE_APPENDCHILD)]
    HRESULT appendChild(
        [in] IXMLDOMNode *newNode,
        [retval, out] IXMLDOMNode **outNewNode);

    [id(DISPID_DOM_NODE_HASCHILDNODES)]
    HRESULT hasChildNodes(
        [retval, out] VARIANT_BOOL *pbool);

    [propget, id(DISPID_DOM_NODE_OWNERDOC)]
    HRESULT ownerDocument([retval, out] IXMLDOMDocument **domDocument);

    [id(DISPID_DOM_NODE_CLONENODE)]
    HRESULT cloneNode(
        [in] VARIANT_BOOL pbool,
        [retval, out] IXMLDOMNode **outNode);

    [propget, id(DISPID_XMLDOM_NODE_STRINGTYPE)]
    HRESULT nodeTypeString([retval, out] BSTR *p);

    [propget, id(DISPID_XMLDOM_NODE_TEXT)]
    HRESULT text([retval, out] BSTR *p);

    [propput, id(DISPID_XMLDOM_NODE_TEXT)]
    HRESULT text([in] BSTR p);

    [propget, id(DISPID_XMLDOM_NODE_SPECIFIED)]
    HRESULT specified([retval, out] VARIANT_BOOL *pbool);

    [propget, id(DISPID_XMLDOM_NODE_DEFINITION)]
    HRESULT definition([retval,out] IXMLDOMNode **domNode);

    [propget, id(DISPID_XMLDOM_NODE_NODETYPEDVALUE)]
    HRESULT nodeTypedValue([retval, out] VARIANT *var1);

    [propput, id(DISPID_XMLDOM_NODE_NODETYPEDVALUE)]
    HRESULT nodeTypedValue([in] VARIANT var1);

    [propget, id(DISPID_XMLDOM_NODE_DATATYPE)]
    HRESULT dataType([retval, out] VARIANT *var1);

    [propput, id(DISPID_XMLDOM_NODE_DATATYPE)]
    HRESULT dataType([in] BSTR p);

    [propget, id(DISPID_XMLDOM_NODE_XML)]
    HRESULT xml([retval, out] BSTR *p);

    [id(DISPID_XMLDOM_NODE_TRANSFORMNODE)]
    HRESULT transformNode(
        [in] IXMLDOMNode *domNode,
        [retval, out] BSTR *p);

    [id(DISPID_XMLDOM_NODE_SELECTNODES)]
    HRESULT selectNodes(
        [in] BSTR p,
        [retval, out] IXMLDOMNodeList **outList);

    [id(DISPID_XMLDOM_NODE_SELECTSINGLENODE)]
    HRESULT selectSingleNode(
        [in] BSTR p,
        [retval, out] IXMLDOMNode **outNode);

    [propget,id(DISPID_XMLDOM_NODE_PARSED)]
    HRESULT parsed([retval, out] VARIANT_BOOL *pbool);

    [propget, id(DISPID_XMLDOM_NODE_NAMESPACE)]
    HRESULT namespaceURI([retval, out] BSTR *p);

    [propget, id(DISPID_XMLDOM_NODE_PREFIX)]
    HRESULT prefix([retval, out] BSTR *p);

    [propget, id(DISPID_XMLDOM_NODE_BASENAME)]
    HRESULT baseName([retval, out] BSTR *p);

    [id(DISPID_XMLDOM_NODE_TRANSFORMNODETOOBJECT)]
    HRESULT transformNodeToObject(
        [in] IXMLDOMNode *domNode,
        [in] VARIANT var1);
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF81-7B36-11d2-B20E-00C04F983E60)
]
interface IXMLDOMDocument : IXMLDOMNode
{
    [propget, id(DISPID_DOM_DOCUMENT_DOCTYPE)]
    HRESULT doctype([retval, out] IXMLDOMDocumentType **docType);

    [propget, id(DISPID_DOM_DOCUMENT_IMPLEMENTATION)]
    HRESULT implementation([retval, out] IXMLDOMImplementation **impl);

    [propget, id(DISPID_DOM_DOCUMENT_DOCUMENTELEMENT)]
    HRESULT documentElement([retval, out] IXMLDOMElement **domElement);

    [propputref, id(DISPID_DOM_DOCUMENT_DOCUMENTELEMENT)]
    HRESULT documentElement([in] IXMLDOMElement *domElement);

    [id(DISPID_DOM_DOCUMENT_CREATEELEMENT)]
    HRESULT createElement(
        [in] BSTR p,
        [retval, out] IXMLDOMElement **domElement);

    [id(DISPID_DOM_DOCUMENT_CREATEDOCUMENTFRAGMENT)]
    HRESULT createDocumentFragment(
        [retval, out] IXMLDOMDocumentFragment **docFrag);

    [id(DISPID_DOM_DOCUMENT_CREATETEXTNODE)]
    HRESULT createTextNode(
        [in] BSTR p,
        [retval, out] IXMLDOMText **text);

    [id(DISPID_DOM_DOCUMENT_CREATECOMMENT)]
    HRESULT createComment(
        [in] BSTR p,
        [retval, out] IXMLDOMComment **comment);

    [id(DISPID_DOM_DOCUMENT_CREATECDATASECTION)]
    HRESULT createCDATASection(
        [in] BSTR p,
        [retval, out] IXMLDOMCDATASection **cdata);

    [id(DISPID_DOM_DOCUMENT_CREATEPROCESSINGINSTRUCTION)]
    HRESULT createProcessingInstruction(
        [in] BSTR target,
        [in] BSTR data,
        [retval, out] IXMLDOMProcessingInstruction **pi);

    [id(DISPID_DOM_DOCUMENT_CREATEATTRIBUTE)]
    HRESULT createAttribute(
        [in] BSTR p,
        [retval, out] IXMLDOMAttribute **attribute);

    [id(DISPID_DOM_DOCUMENT_CREATEENTITYREFERENCE)]
    HRESULT createEntityReference(
        [in] BSTR p,
        [retval, out] IXMLDOMEntityReference **entityRef);

    [id(DISPID_DOM_DOCUMENT_GETELEMENTSBYTAGNAME)]
    HRESULT getElementsByTagName(
        [in] BSTR p,
        [retval, out] IXMLDOMNodeList **resultList);

    [id(DISPID_XMLDOM_DOCUMENT_CREATENODE)]
    HRESULT createNode(
        [in] VARIANT var, 
        [in] BSTR name, 
        [in] BSTR uri,
        [retval, out] IXMLDOMNode **node);

    [id(DISPID_XMLDOM_DOCUMENT_NODEFROMID)]
    HRESULT nodeFromID( 
        [in] BSTR id,
        [retval, out] IXMLDOMNode **node);

    [id(DISPID_XMLDOM_DOCUMENT_LOAD)]
    HRESULT load(
        [in] VARIANT var1,
        [retval, out] VARIANT_BOOL *pbool);

    [propget, id(DISPID_READYSTATE)]
    HRESULT readyState([retval, out] LONG *value);

    [propget, id(DISPID_XMLDOM_DOCUMENT_PARSEERROR)]
    HRESULT parseError([retval, out] IXMLDOMParseError **err);

    [propget, id(DISPID_XMLDOM_DOCUMENT_URL)]
    HRESULT url([retval, out] BSTR *p);

    [propget, id(DISPID_XMLDOM_DOCUMENT_ASYNC)]
    HRESULT async([retval, out] VARIANT_BOOL *pbool);

    [propput, id(DISPID_XMLDOM_DOCUMENT_ASYNC)]
    HRESULT async([in] VARIANT_BOOL pbool);

    [id(DISPID_XMLDOM_DOCUMENT_ABORT)]
    HRESULT abort();

    [id(DISPID_XMLDOM_DOCUMENT_LOADXML)]
    HRESULT loadXML( 
        [in] BSTR p,
        [out, retval] VARIANT_BOOL *pbool);

    [id(DISPID_XMLDOM_DOCUMENT_SAVE)]
    HRESULT save( 
        [in] VARIANT var1);

    [propget, id(DISPID_XMLDOM_DOCUMENT_VALIDATE)]
    HRESULT validateOnParse([retval, out] VARIANT_BOOL *pbool);

    [propput, id(DISPID_XMLDOM_DOCUMENT_VALIDATE)]
    HRESULT validateOnParse([in] VARIANT_BOOL pbool);

    [propget, id(DISPID_XMLDOM_DOCUMENT_RESOLVENAMESPACE)]
    HRESULT resolveExternals([retval,out] VARIANT_BOOL *pbool);

    [propput, id(DISPID_XMLDOM_DOCUMENT_RESOLVENAMESPACE)]
    HRESULT resolveExternals([in] VARIANT_BOOL pbool);

    [propget, id(DISPID_XMLDOM_DOCUMENT_PRESERVEWHITESPACE)]
    HRESULT preserveWhiteSpace([retval,out] VARIANT_BOOL *pbool);

    [propput, id(DISPID_XMLDOM_DOCUMENT_PRESERVEWHITESPACE)]
    HRESULT preserveWhiteSpace([in] VARIANT_BOOL pbool);

    [propput, id(DISPID_XMLDOM_DOCUMENT_ONREADYSTATECHANGE)]
    HRESULT onreadystatechange([in] VARIANT var1);

    [propput, id(DISPID_XMLDOM_DOCUMENT_ONDATAAVAILABLE)]
    HRESULT ondataavailable([in] VARIANT var1);

    [propput, id(DISPID_XMLDOM_DOCUMENT_ONTRANSFORMNODE)]
    HRESULT ontransformnode([in] VARIANT var1);
}

[
local,
object,
dual,
oleautomation,
uuid (373984c8-b845-449b-91e7-45ac83036ade)
]
interface IXMLDOMSchemaCollection : IDispatch
{
    [id(DISPID_XMLDOM_SCHEMACOLLECTION_ADD)]
    HRESULT add(
        [in] BSTR uri,
        [in] VARIANT var);

    [id(DISPID_XMLDOM_SCHEMACOLLECTION_GET)]
    HRESULT get(
        [in] BSTR uri,
        [retval, out] IXMLDOMNode **node);

    [id(DISPID_XMLDOM_SCHEMACOLLECTION_REMOVE)]
    HRESULT remove(
        [in] BSTR uri);

    [propget, id(DISPID_XMLDOM_SCHEMACOLLECTION_LENGTH)]
    HRESULT length(
        [retval, out] LONG *length);

    [propget, id(DISPID_VALUE)]
    HRESULT namespaceURI([in] LONG index, [out, retval] BSTR *len);

    [id(DISPID_XMLDOM_SCHEMACOLLECTION_ADDCOLLECTION)]
    HRESULT addCollection(
        [in] IXMLDOMSchemaCollection *otherCollection);

    [propget, id(DISPID_NEWENUM)]
    HRESULT _newEnum([retval, out] IUnknown **ppUnk);
}

[
    local,
    object,
    dual,
    oleautomation,
    uuid(50ea08b0-dd1b-4664-9a50-c2f40f4bd79a),
]
interface IXMLDOMSchemaCollection2 : IXMLDOMSchemaCollection
{
    [id(DISPID_SOM_VALIDATE)]
    HRESULT validate();

    [propput, id(DISPID_SOM_VALIDATEONLOAD)]
    HRESULT validateOnLoad(
        [in] VARIANT_BOOL validateOnLoad);

    [propget, id(DISPID_SOM_VALIDATEONLOAD)]
    HRESULT validateOnLoad(
        [out,retval] VARIANT_BOOL* validateOnLoad);

    [id(DISPID_SOM_GETSCHEMA)]
    HRESULT getSchema(
        [in] BSTR namespaceURI,
        [out,retval] ISchema** schema);

    [id(DISPID_SOM_GETDECLARATION)]
    HRESULT getDeclaration(
        [in] IXMLDOMNode* node,
        [out,retval]ISchemaItem** item);
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF95-7B36-11d2-B20E-00C04F983E60)
]
interface IXMLDOMDocument2 : IXMLDOMDocument
{
    [propget, id(DISPID_XMLDOM_DOCUMENT2_NAMESPACES)]
    HRESULT namespaces([retval, out] IXMLDOMSchemaCollection **schemaCollection);

    [propget, id(DISPID_XMLDOM_DOCUMENT2_SCHEMAS)]
    HRESULT schemas([retval, out] VARIANT *var1);

    [propputref, id(DISPID_XMLDOM_DOCUMENT2_SCHEMAS)]
    HRESULT schemas([in] VARIANT var1);

    [id(DISPID_XMLDOM_DOCUMENT2_VALIDATE)]
    HRESULT validate(
        [retval, out] IXMLDOMParseError **err);

    [id(DISPID_XMLDOM_DOCUMENT2_SETPROPERTY)]
    HRESULT setProperty(
        [in] BSTR p,
        [in] VARIANT var);

    [id(DISPID_XMLDOM_DOCUMENT2_GETPROPERTY)]
    HRESULT getProperty(
        [in] BSTR p,
        [retval, out] VARIANT *var);
}

[
    local,
    object,
    dual,
    uuid(2933bf96-7b36-11d2-b20e-00c04f983e60),
    oleautomation,
]
interface IXMLDOMDocument3 : IXMLDOMDocument2
{
    [id(DISPID_XMLDOM_DOCUMENT3_VALIDATENODE)]
    HRESULT validateNode(
        [in] IXMLDOMNode *node,
        [out, retval] IXMLDOMParseError **error);

    [id(DISPID_XMLDOM_DOCUMENT3_IMPORTNODE)]
    HRESULT importNode(
        [in] IXMLDOMNode *node,
        [in] VARIANT_BOOL deep,
        [out, retval] IXMLDOMNode **clone);
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF82-7B36-11d2-B20E-00C04F983E60)
]
interface IXMLDOMNodeList : IDispatch
{
    [propget, id(DISPID_VALUE)]
    HRESULT item([in] LONG lIndex, [retval, out] IXMLDOMNode **outNode);

    [propget, id(DISPID_DOM_NODELIST_LENGTH)]
    HRESULT length([retval, out] LONG *lLength);

    [id(DISPID_XMLDOM_NODELIST_NEXTNODE)]
    HRESULT nextNode(
        [retval, out] IXMLDOMNode **outNode);

    [id(DISPID_XMLDOM_NODELIST_RESET)]
    HRESULT reset();

    [propget, id(DISPID_NEWENUM)]
    HRESULT _newEnum([retval, out] IUnknown **ppUnk);
}

[
local,
object,
dual,
oleautomation,
uuid (AA634FC7-5888-44a7-A257-3A47150D3A0E)
]
interface IXMLDOMSelection : IXMLDOMNodeList
{
    [propget, id(DISPID_XMLDOM_SELECTION_EXPR)]
    HRESULT expr([retval, out] BSTR *p);

    [propput, id(DISPID_XMLDOM_SELECTION_EXPR)]
    HRESULT expr([in] BSTR p);

    [propget, id(DISPID_XMLDOM_SELECTION_CONTEXT)]
    HRESULT context([retval, out] IXMLDOMNode **ppNode);

    [propputref, id(DISPID_XMLDOM_SELECTION_CONTEXT)]
    HRESULT context([in] IXMLDOMNode * pNode);

    [id(DISPID_XMLDOM_SELECTION_PEEKNODE)]
    HRESULT peekNode(
        [retval, out] IXMLDOMNode **ppNode);

    [id(DISPID_XMLDOM_SELECTION_MATCHES)]
    HRESULT matches(
        [in] IXMLDOMNode *pNode,
        [retval, out] IXMLDOMNode **ppNode);

    [id(DISPID_XMLDOM_SELECTION_REMOVENEXT)] 
    HRESULT removeNext(
        [retval, out] IXMLDOMNode **ppNode);

    [id(DISPID_XMLDOM_SELECTION_REMOVEALL)]
    HRESULT removeAll();

    [id(DISPID_XMLDOM_SELECTION_CLONE)]
    HRESULT clone(
        [retval, out] IXMLDOMSelection **ppNode);

    [id(DISPID_XMLDOM_SELECTION_GETPROPERTY)]
    HRESULT getProperty(
        [in] BSTR p,
        [retval, out] VARIANT * var);

    [id(DISPID_XMLDOM_SELECTION_SETPROPERTY)]
    HRESULT setProperty(
        [in] BSTR p,
        [in] VARIANT var);
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF83-7B36-11d2-B20E-00C04F983E60)
]
interface IXMLDOMNamedNodeMap : IDispatch
{
    [id(DISPID_DOM_NAMEDNODEMAP_GETNAMEDITEM)]
    HRESULT getNamedItem(
        [in] BSTR p,
        [retval, out] IXMLDOMNode **outNode);

    [id(DISPID_DOM_NAMEDNODEMAP_SETNAMEDITEM)]
    HRESULT setNamedItem(
        [in] IXMLDOMNode *newNode,
        [retval, out] IXMLDOMNode **outNode);

    [id(DISPID_DOM_NAMEDNODEMAP_REMOVENAMEDITEM)]
    HRESULT removeNamedItem(
        [in] BSTR p,
        [retval, out] IXMLDOMNode **outNode);

    [propget, id(DISPID_VALUE)]
    HRESULT item([in] LONG lIndex, [retval, out] IXMLDOMNode **outNode);

    [propget, id(DISPID_DOM_NODELIST_LENGTH)]
    HRESULT length([retval, out] LONG *lLength);

    [id(DISPID_XMLDOM_NAMEDNODEMAP_GETQUALIFIEDITEM)]
    HRESULT getQualifiedItem(
        [in] BSTR name,
        [in] BSTR uri,
        [retval, out] IXMLDOMNode **outNode);

    [id(DISPID_XMLDOM_NAMEDNODEMAP_REMOVEQUALIFIEDITEM)]
    HRESULT removeQualifiedItem(
        [in] BSTR name,
        [in] BSTR uri,
        [retval, out] IXMLDOMNode **outNode);

    [id(DISPID_XMLDOM_NAMEDNODEMAP_NEXTNODE)]
    HRESULT nextNode(
        [retval, out] IXMLDOMNode **outNode);

    [id(DISPID_XMLDOM_NAMEDNODEMAP_RESET)]
    HRESULT reset();

    [propget, id(DISPID_NEWENUM)]
    HRESULT _newEnum([retval, out] IUnknown **ppUnk);
}
 
[
local,
object,
dual,
oleautomation,
uuid (3efaa413-272f-11d2-836f-0000f87a7782)
]
interface IXMLDOMDocumentFragment : IXMLDOMNode 
{
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF84-7B36-11d2-B20E-00C04F983E60)
]
interface IXMLDOMCharacterData : IXMLDOMNode 
{
    [propget, id(DISPID_DOM_DATA_DATA)]
    HRESULT data([retval, out] BSTR *p);

    [propput, id(DISPID_DOM_DATA_DATA)]
    HRESULT data([in] BSTR data);

    [propget, id(DISPID_DOM_DATA_LENGTH)]
    HRESULT length([retval, out] LONG *len);

    [id(DISPID_DOM_DATA_SUBSTRING)]
    HRESULT substringData(
        [in] LONG offset,
        [in] LONG count,
        [retval, out] BSTR *p);

    [id(DISPID_DOM_DATA_APPEND)]
    HRESULT appendData(
        [in] BSTR p);

    [id(DISPID_DOM_DATA_INSERT)]
    HRESULT insertData(
        [in] LONG offset,
        [in] BSTR p);

    [id(DISPID_DOM_DATA_DELETE)]
    HRESULT deleteData(
        [in] LONG offset,
        [in] LONG count);

    [id(DISPID_DOM_DATA_REPLACE)]
    HRESULT replaceData(
        [in] LONG offset,
        [in] LONG count,
        [in] BSTR p);
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF85-7B36-11d2-B20E-00C04F983E60)
]
interface IXMLDOMAttribute : IXMLDOMNode 
{
    [propget, id(DISPID_DOM_ATTRIBUTE_GETNAME)]
    HRESULT name([retval, out] BSTR *p);

    [propget, id(DISPID_DOM_ATTRIBUTE_VALUE)]
    HRESULT value([retval, out] VARIANT *var1);

    [propput, id(DISPID_DOM_ATTRIBUTE_VALUE)]
    HRESULT value([in] VARIANT var1);
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF86-7B36-11d2-B20E-00C04F983E60)
]
interface IXMLDOMElement : IXMLDOMNode 
{
    [propget, id(DISPID_DOM_ELEMENT_GETTAGNAME)]
    HRESULT tagName([retval, out] BSTR *p);

    [id(DISPID_DOM_ELEMENT_GETATTRIBUTE)]
    HRESULT getAttribute(
        [in] BSTR p,
        [retval, out] VARIANT *var);

    [id(DISPID_DOM_ELEMENT_SETATTRIBUTE)]
    HRESULT setAttribute(
        [in] BSTR p,
        [in] VARIANT var);

    [id(DISPID_DOM_ELEMENT_REMOVEATTRIBUTE)]
    HRESULT removeAttribute(
        [in] BSTR p);

    [id(DISPID_DOM_ELEMENT_GETATTRIBUTENODE)]
    HRESULT getAttributeNode(
        [in] BSTR p,
        [retval, out] IXMLDOMAttribute **attributeNode);

    [id(DISPID_DOM_ELEMENT_SETATTRIBUTENODE)]
    HRESULT setAttributeNode(
        [in] IXMLDOMAttribute *domAttribute,
        [retval, out] IXMLDOMAttribute **attributeNode);

    [id(DISPID_DOM_ELEMENT_REMOVEATTRIBUTENODE)]
    HRESULT removeAttributeNode(
        [in] IXMLDOMAttribute *domAttribute,
        [retval, out] IXMLDOMAttribute **attributeNode);

    [id(DISPID_DOM_ELEMENT_GETELEMENTSBYTAGNAME)]
    HRESULT getElementsByTagName(
        [in] BSTR p,
        [retval, out] IXMLDOMNodeList **resultList);

    [id(DISPID_DOM_ELEMENT_NORMALIZE)]
    HRESULT normalize();
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF87-7B36-11d2-B20E-00C04F983E60)
]
interface IXMLDOMText : IXMLDOMCharacterData 
{
    [id(DISPID_DOM_TEXT_SPLITTEXT)]
    HRESULT splitText(
        [in] LONG offset,
        [retval, out] IXMLDOMText **txtNode);
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF88-7B36-11d2-B20E-00C04F983E60)
]
interface IXMLDOMComment : IXMLDOMCharacterData 
{
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF89-7B36-11d2-B20E-00C04F983E60)
]
interface IXMLDOMProcessingInstruction : IXMLDOMNode 
{
    [propget, id(DISPID_DOM_PI_TARGET)]
    HRESULT target([retval, out] BSTR *p);

    [propget, id(DISPID_DOM_PI_DATA)]
    HRESULT data([retval, out] BSTR *p);

    [propput, id(DISPID_DOM_PI_DATA)]
    HRESULT data([in] BSTR p);
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF8A-7B36-11d2-B20E-00C04F983E60)
]
interface IXMLDOMCDATASection : IXMLDOMText 
{
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF8B-7B36-11d2-B20E-00C04F983E60)
]
interface IXMLDOMDocumentType : IXMLDOMNode 
{
    [propget, id(DISPID_DOM_DOCUMENTTYPE_NAME)]
    HRESULT name([retval, out] BSTR *p);

    [propget, id(DISPID_DOM_DOCUMENTTYPE_ENTITIES)]
    HRESULT entities([retval, out] IXMLDOMNamedNodeMap **entityMap);

    [propget, id(DISPID_DOM_DOCUMENTTYPE_NOTATIONS)]
    HRESULT notations([retval, out] IXMLDOMNamedNodeMap **notationMap);
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF8C-7B36-11d2-B20E-00C04F983E60)
]
interface IXMLDOMNotation : IXMLDOMNode 
{
    [propget, id(DISPID_DOM_NOTATION_PUBLICID)]
    HRESULT publicId([retval, out] VARIANT *id);

    [propget, id(DISPID_DOM_NOTATION_SYSTEMID)]
    HRESULT systemId([retval, out] VARIANT *id);
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF8D-7B36-11d2-B20E-00C04F983E60)
]
interface IXMLDOMEntity : IXMLDOMNode 
{
    [propget, id(DISPID_DOM_ENTITY_PUBLICID)]
    HRESULT publicId([retval, out] VARIANT *id1);

    [propget, id(DISPID_DOM_ENTITY_SYSTEMID)]
    HRESULT systemId([retval, out] VARIANT *id1);

    [propget, id(DISPID_DOM_ENTITY_NOTATIONNAME)]
    HRESULT notationName([retval, out] BSTR *p);
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF8E-7B36-11d2-B20E-00C04F983E60)
]
interface IXMLDOMEntityReference : IXMLDOMNode 
{
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF8F-7B36-11d2-B20E-00C04F983E60)
]
interface IXMLDOMImplementation : IDispatch
{
    [id(DISPID_DOM_IMPLEMENTATION_HASFEATURE)]
    HRESULT hasFeature(
        [in] BSTR feature,
        [in] BSTR version,
        [retval, out] VARIANT_BOOL *pbool);
}

[
local,
object,
dual,
oleautomation,
uuid (3efaa425-272f-11d2-836f-0000f87a7782)
]
interface IXTLRuntime : IXMLDOMNode
{
    [id(DISPID_XTLRUNTIME_UNIQUEID)] 
    HRESULT uniqueID(
        [in]IXMLDOMNode *pNode, 
        [retval, out]LONG *pID);

    [id(DISPID_XTLRUNTIME_DEPTH)]
    HRESULT depth(
        [in] IXMLDOMNode *pNode, 
        [retval, out]LONG * pDepth);

    [id(DISPID_XTLRUNTIME_CHILDNUMBER)] 
    HRESULT childNumber(
        [in]IXMLDOMNode *pNode, 
        [retval, out] LONG *pNumber);

    [id(DISPID_XTLRUNTIME_ANCESTORCHILDNUMBER)]
    HRESULT ancestorChildNumber(
        [in]BSTR bstr, 
        [in]IXMLDOMNode *pNode, 
        [retval, out]LONG *pNumber);

    [id(DISPID_XTLRUNTIME_ABSOLUTECHILDNUMBER)]
    HRESULT absoluteChildNumber(
        [in]IXMLDOMNode *pNode, 
        [retval, out]LONG *pNumber);

    [id(DISPID_XTLRUNTIME_FORMATINDEX)]
    HRESULT formatIndex(
        [in] LONG lIndex,
        [in] BSTR bstr, 
        [retval, out]BSTR *pbstr);

    [id(DISPID_XTLRUNTIME_FORMATNUMBER)]
    HRESULT formatNumber(
        [in] double dblNumber, 
        [in] BSTR bstr, 
        [retval, out]BSTR *pbstr);

    [id(DISPID_XTLRUNTIME_FORMATDATE)]
    HRESULT formatDate(
        [in] VARIANT var1, 
        [in] BSTR bstr, 
        [optional, in] VARIANT var2,
        [retval, out]BSTR *pbstr);

    [id(DISPID_XTLRUNTIME_FORMATTIME)]
    HRESULT formatTime(
        [in] VARIANT var1, 
        [in] BSTR bstr, 
        [optional, in] VARIANT var2,
        [retval, out]BSTR *pbstr);

}

[
local,
object,
odl,
dual,
oleautomation,
uuid(310afa62-0575-11d2-9ca9-0060b0ec3d39),
pointer_default(unique)
]
interface IDSOControl : IDispatch
{
	[propget,id(DISPID_XMLDSO_DOCUMENT)]
        HRESULT XMLDocument([out, retval] IXMLDOMDocument** ppDoc);

	[propput,id(DISPID_XMLDSO_DOCUMENT)]
        HRESULT XMLDocument([in] IXMLDOMDocument* ppDoc);

	[propget,id(DISPID_XMLDSO_JAVADSOCOMPATIBLE)]
        HRESULT JavaDSOCompatible([out, retval] BOOL* fJavaDSOCompatible);

	[propput,id(DISPID_XMLDSO_JAVADSOCOMPATIBLE)]
        HRESULT JavaDSOCompatible([in]  BOOL fJavaDSOCompatible);

	[propget, id(DISPID_READYSTATE)]
        HRESULT readyState([out, retval] long *state);
}

[
    uuid(3efaa427-272f-11d2-836f-0000f87a7782)
]
dispinterface XMLDOMDocumentEvents 
{
    properties:
    methods:
    [id (DISPID_XMLDOMEVENT_ONDATAAVAILABLE)]  
    HRESULT ondataavailable();
 
    [id (DISPID_XMLDOMEVENT_ONREADYSTATECHANGE)] 
    HRESULT onreadystatechange();
}

[
local,
object,
dual,
oleautomation,
uuid (2933BF92-7B36-11d2-B20E-00C04F983E60)
]
interface IXSLProcessor : IDispatch
{
    [propput, id(DISPID_XMLDOM_PROCESSOR_INPUT)]
    HRESULT input([in] VARIANT var);

    [propget, id(DISPID_XMLDOM_PROCESSOR_INPUT)]
    HRESULT input([retval, out] VARIANT *var);

    [propget, id(DISPID_XMLDOM_PROCESSOR_XSLTEMPLATE)]
    HRESULT ownerTemplate([retval, out] IXSLTemplate **ppTemplate);

    [id(DISPID_XMLDOM_PROCESSOR_SETSTARTMODE)]
    HRESULT setStartMode(
        [in] BSTR p, 
        [in, defaultvalue("")] BSTR uri);

    [propget, id(DISPID_XMLDOM_PROCESSOR_STARTMODE)]
    HRESULT startMode([retval, out] BSTR *p);

    [propget, id(DISPID_XMLDOM_PROCESSOR_STARTMODEURI)]
    HRESULT startModeURI([retval, out] BSTR *uri);

    [propput, id(DISPID_XMLDOM_PROCESSOR_OUTPUT)]
    HRESULT output([in] VARIANT var);

    [propget, id(DISPID_XMLDOM_PROCESSOR_OUTPUT)]
    HRESULT output([retval, out] VARIANT *var);

    [id(DISPID_XMLDOM_PROCESSOR_TRANSFORM)]
    HRESULT transform(
        [retval, out] VARIANT_BOOL *pbool);

    [id(DISPID_XMLDOM_PROCESSOR_RESET)]
    HRESULT reset();

    [propget, id(DISPID_XMLDOM_PROCESSOR_READYSTATE)]
    HRESULT readyState([retval, out] LONG *pstate);

    [id(DISPID_XMLDOM_PROCESSOR_ADDPARAMETER)]
    HRESULT addParameter(
        [in] BSTR p, 
        [in] VARIANT var, 
        [in, defaultvalue("")] BSTR uri);

    [id(DISPID_XMLDOM_PROCESSOR_ADDOBJECT)]
    HRESULT addObject(
        [in] IDispatch* obj, 
        [in] BSTR uri);

    [propget, id(DISPID_XMLDOM_PROCESSOR_STYLESHEET)]
    HRESULT stylesheet([retval, out] IXMLDOMNode **node);
} 

[
local,
object,
dual,
oleautomation,
uuid (2933BF93-7B36-11d2-B20E-00C04F983E60)
]
interface IXSLTemplate : IDispatch
{
    [propputref, id(DISPID_XMLDOM_TEMPLATE_STYLESHEET)]
    HRESULT stylesheet([in] IXMLDOMNode *node);

    [propget, id(DISPID_XMLDOM_TEMPLATE_STYLESHEET)]
    HRESULT stylesheet([retval, out] IXMLDOMNode **node);

    [id(DISPID_XMLDOM_TEMPLATE_CREATEPROCESSOR)]
    HRESULT createProcessor(
        [retval, out] IXSLProcessor **ppProcessor);
} 

[
    object,
    uuid(ED8C108D-4349-11D2-91A4-00C04F7969E8),
    odl,
    dual,
    oleautomation,
    pointer_default(unique)
]
interface IXMLHTTPRequest : IDispatch
{
    [id(1)]
    HRESULT open([in] BSTR bstrMethod, [in] BSTR bstrUrl, [in, optional] VARIANT varAsync,
                 [in,optional] VARIANT username, [in,optional] VARIANT password);

    [id(2)]
    HRESULT setRequestHeader([in] BSTR bstrHeader, [in] BSTR bstrValue);

    [id(3)]
    HRESULT getResponseHeader([in] BSTR bstrHeader, [out, retval] BSTR * value);

    [id(4)]
    HRESULT getAllResponseHeaders([out, retval] BSTR * pbstrHeaders);

    [id(5)]
    HRESULT send([in, optional] VARIANT body);

    [id(6)]
    HRESULT abort();

    [propget, id(7)]
    HRESULT status([out, retval] LONG *pStatus);

    [propget, id(8)]
    HRESULT statusText([out, retval] BSTR *pStatus);

    [propget, id(9)]
    HRESULT responseXML([out, retval] IDispatch **pBody);

    [propget, id(10)]
    HRESULT responseText([out, retval] BSTR *pBody);

    [propget, id(11)]
    HRESULT responseBody([out, retval] VARIANT *pBody);

    [propget, id(12)]
    HRESULT responseStream([out, retval] VARIANT *pBody);

    [propget, id(13)]
    HRESULT readyState([out, retval] LONG *pState);

    [propput, id(14)]
    HRESULT onreadystatechange([in] IDispatch *pReadyStateSink);
}

[
object,
dual,
oleautomation,
uuid(2e9196bf-13ba-4dd4-91ca-6c571f281495)
]
interface IServerXMLHTTPRequest : IXMLHTTPRequest
{
    typedef enum _SERVERXMLHTTP_OPTION
    {
        SXH_OPTION_URL = -1,
        SXH_OPTION_URL_CODEPAGE,
        SXH_OPTION_ESCAPE_PERCENT_IN_URL,
        SXH_OPTION_IGNORE_SERVER_SSL_CERT_ERROR_FLAGS,
        SXH_OPTION_SELECT_CLIENT_SSL_CERT
    } SERVERXMLHTTP_OPTION;

    [id(15)]
    HRESULT setTimeouts(
        [in] long resolveTimeout,
        [in] long connectTimeout,
        [in] long sendTimeout,
        [in] long receiveTimeout);

    [id(16)]
    HRESULT waitForResponse(
        [in, optional] VARIANT timeoutInSeconds,
        [out, retval] VARIANT_BOOL * isSuccessful);

    [id(17)]
    HRESULT getOption(
        [in] SERVERXMLHTTP_OPTION option,
        [out, retval] VARIANT * value);

    [id(18)]
    HRESULT setOption(
        [in] SERVERXMLHTTP_OPTION option,
        [in] VARIANT value);
}

[
object,
dual,
oleautomation,
uuid(2e01311b-c322-4b0a-bd77-b90cfdc8dce7)
]
interface IServerXMLHTTPRequest2 : IServerXMLHTTPRequest
{
    typedef enum _SXH_PROXY_SETTING
    {
        SXH_PROXY_SET_DEFAULT = 0,
        SXH_PROXY_SET_PRECONFIG = 0,
        SXH_PROXY_SET_DIRECT,
        SXH_PROXY_SET_PROXY
    } SXH_PROXY_SETTING;

    [id(19)]
    HRESULT setProxy(
        [in] SXH_PROXY_SETTING proxySetting,
        [in, optional] VARIANT varProxyServer,
        [in, optional] VARIANT varBypassList);

    [id(20)]
    HRESULT setProxyCredentials(
        [in] BSTR bstrUserName,
        [in] BSTR bstrPassword);
}

[
local,
object,
dual,
oleautomation,
uuid (3efaa426-272f-11d2-836f-0000f87a7782)
]
interface IXMLDOMParseError : IDispatch
{
    [propget, id(DISPID_VALUE)]
    HRESULT errorCode([retval, out] LONG *errCode);

    [propget, id(DISPID_DOM_ERROR_URL)]
    HRESULT url([retval, out] BSTR *p);

    [propget, id(DISPID_DOM_ERROR_REASON)]
    HRESULT reason([retval, out] BSTR *p);

    [propget, id(DISPID_DOM_ERROR_SRCTEXT)]
    HRESULT srcText([retval, out] BSTR *p);

    [propget, id(DISPID_DOM_ERROR_LINE)]
    HRESULT line([retval, out] LONG *lineNo);

    [propget, id(DISPID_DOM_ERROR_LINEPOS)]
    HRESULT linepos([retval, out] LONG * linePos);

    [propget, id(DISPID_DOM_ERROR_FILEPOS)]
    HRESULT filepos([retval, out] LONG * filePos);
}

[
local,
object,
dual,
oleautomation,
uuid (3efaa428-272f-11d2-836f-0000f87a7782)
]
interface IXMLDOMParseError2 : IXMLDOMParseError
{
    [propget, id(DISPID_DOM_ERROR2_ERRORXPATH)]
    HRESULT errorXPath([retval, out] BSTR *xpathexpr);

    [propget, id(DISPID_DOM_ERROR2_ALLERRORS)]
    HRESULT allErrors([retval, out] IXMLDOMParseErrorCollection **allErrors);

    [id(DISPID_DOM_ERROR2_ERRORPARAMETERS)]
    HRESULT errorParameters(
        [in] long index,
        [retval, out] BSTR *param);

    [propget, id(DISPID_DOM_ERROR2_ERRORPARAMETERSCOUNT)]
    HRESULT errorParametersCount([retval, out] long *count);
}

[
local,
object,
dual,
oleautomation,
uuid (3efaa429-272f-11d2-836f-0000f87a7782)
]
interface IXMLDOMParseErrorCollection : IDispatch
{
    [propget, id(DISPID_VALUE)]
    HRESULT item(
        [in] long index,
        [retval, out] IXMLDOMParseError2 **error);

    [propget, id(DISPID_DOM_ERRORCOLLECTION_LENGTH)]
    HRESULT length( [retval, out] long *length);

    [propget, id(DISPID_DOM_ERRORCOLLECTION_NEXT)]
    HRESULT next( [retval, out] IXMLDOMParseError2 **error);

    [id(DISPID_DOM_ERRORCOLLECTION_RESET)]
    HRESULT reset();

    [propget, hidden, restricted, id(DISPID_NEWENUM)]
    HRESULT _newEnum( [retval, out] IUnknown **ppunk);
}

[
    uuid(f5078f1b-c551-11d3-89b9-0000f81fe221)
]
coclass DOMDocument26
{
    [default] interface IXMLDOMDocument2;
    [default, source] dispinterface XMLDOMDocumentEvents;
}

[
    helpstring("XML DOM Document 3.0"),
    progid("Msxml2.DOMDocument.3.0"),
    vi_progid("Msxml2.DOMDocument"),
    threading(both),
    uuid(f5078f32-c551-11d3-89b9-0000f81fe221)
]
coclass DOMDocument30
{
    [default] interface IXMLDOMDocument2;
    [default, source] dispinterface XMLDOMDocumentEvents;
}

[
    uuid(88d969c0-f192-11d4-a65f-0040963251e5)
]
coclass DOMDocument40
{
    [default] interface IXMLDOMDocument2;
    [default, source] dispinterface XMLDOMDocumentEvents;
}

[
    uuid(88d96a05-f192-11d4-a65f-0040963251e5)
]
coclass DOMDocument60
{
    [default] interface IXMLDOMDocument3;
    [default, source] dispinterface XMLDOMDocumentEvents;
}

[
    helpstring("XML DOM Document"),
    progid("Msxml2.DOMDocument"),
    vi_progid("Msxml2.DOMDocument"),
    threading(both),
    uuid(f6d90f11-9c73-11d3-b32e-00c04f990bb4)
]
coclass DOMDocument
{
    [default] interface IXMLDOMDocument2;
    [default, source] dispinterface XMLDOMDocumentEvents;
}

[
    uuid(f5078f1c-c551-11d3-89b9-0000f81fe221)
]
coclass FreeThreadedDOMDocument26
{
    [default] interface IXMLDOMDocument2;
    [default, source] dispinterface XMLDOMDocumentEvents;
}

[
    helpstring("Free threaded XML DOM Document 3.0"),
    progid("Msxml2.FreeThreadedDOMDocument.3.0"),
    vi_progid("Msxml2.FreeThreadedDOMDocument"),
    threading(both),
    uuid(f5078f33-c551-11d3-89b9-0000f81fe221)
]
coclass FreeThreadedDOMDocument30
{
    [default] interface IXMLDOMDocument2;
    [default, source] dispinterface XMLDOMDocumentEvents;
}

[
    uuid(88d969c1-f192-11d4-a65f-0040963251e5)
]
coclass FreeThreadedDOMDocument40
{
    [default] interface IXMLDOMDocument2;
    [default, source] dispinterface XMLDOMDocumentEvents;
}

[
    uuid(88d96a06-f192-11d4-a65f-0040963251e5),
]
coclass FreeThreadedDOMDocument60
{
    [default] interface IXMLDOMDocument3;
    [default, source] dispinterface XMLDOMDocumentEvents;
}

[
    helpstring("Free threaded XML DOM Document"),
    progid("Msxml2.FreeThreadedDOMDocument"),
    vi_progid("Msxml2.FreeThreadedDOMDocument"),
    threading(both),
    uuid(f6d90f12-9c73-11d3-b32e-00c04f990bb4)
]
coclass FreeThreadedDOMDocument
{
    [default] interface IXMLDOMDocument2;
    [default, source] dispinterface XMLDOMDocumentEvents;
}

[
    uuid(f5078f1e-c551-11d3-89b9-0000f81fe221)
]
coclass XMLHTTP26
{
    [default] interface IXMLHTTPRequest;
}

[
    helpstring("XML HTTP 3.0"),
    progid("Msxml2.XMLHTTP.3.0"),
    vi_progid("Msxml2.XMLHTTP"),
    threading(apartment),
    uuid(f5078f35-c551-11d3-89b9-0000f81fe221)
]
coclass XMLHTTP30
{
    [default] interface IXMLHTTPRequest;
}

[
    uuid(88d969c5-f192-11d4-a65f-0040963251e5)
]
coclass XMLHTTP40
{
    [default] interface IXMLHTTPRequest;
}

[
    uuid(88d96a0a-f192-11d4-a65f-0040963251e5)
]
coclass XMLHTTP60
{
    [default] interface IXMLHTTPRequest;
}

[
    helpstring("XML HTTP"),
    progid("Msxml2.XMLHTTP"),
    vi_progid("Msxml2.XMLHTTP"),
    threading(apartment),
    uuid(f6d90f16-9c73-11d3-b32e-00c04f990bb4)
]
coclass XMLHTTP
{
    [default] interface IXMLHTTPRequest;
}

[
    helpstring("Server XML HTTP 3.0"),
    progid("Msxml2.ServerXMLHTTP.3.0"),
    vi_progid("Msxml2.ServerXMLHTTP"),
    threading(apartment),
    uuid(afb40ffd-b609-40a3-9828-f88bbe11e4e3)
]
coclass ServerXMLHTTP30
{
    [default] interface IServerXMLHTTPRequest;
}

[
    uuid(88d969c6-f192-11d4-a65f-0040963251e5)
]
coclass ServerXMLHTTP40
{
    [default] interface IServerXMLHTTPRequest2;
}

[
    uuid(88d96a0b-f192-11d4-a65f-0040963251e5)
]
coclass ServerXMLHTTP60
{
    [default] interface IServerXMLHTTPRequest2;
}

[
    helpstring("Server XML HTTP"),
    progid("Msxml2.ServerXMLHTTP"),
    vi_progid("Msxml2.ServerXMLHTTP"),
    threading(apartment),
    uuid(afba6b42-5692-48ea-8141-dc517dcf0ef1)
]
coclass ServerXMLHTTP
{
    [default] interface IServerXMLHTTPRequest;
}

[
    uuid(f5078f1d-c551-11d3-89b9-0000f81fe221)
]
coclass XMLSchemaCache26
{
    [default] interface IXMLDOMSchemaCollection;
}

[
    helpstring("XML Schema Cache 3.0"),
    progid("Msxml2.XMLSchemaCache.3.0"),
    vi_progid("Msxml2.XMLSchemaCache"),
    threading(both),
    uuid(f5078f34-c551-11d3-89b9-0000f81fe221)
]
coclass XMLSchemaCache30
{
    [default] interface IXMLDOMSchemaCollection;
}

[
    uuid(88d969c2-f192-11d4-a65f-0040963251e5)
]
coclass XMLSchemaCache40
{
    [default] interface IXMLDOMSchemaCollection2;
}

[
    helpstring("XML Schema Cache"),
    progid("Msxml2.XMLSchemaCache"),
    vi_progid("Msxml2.XMLSchemaCache"),
    threading(both),
    uuid(373984c9-b845-449b-91e7-45ac83036ade)
]
coclass XMLSchemaCache
{
    [default] interface IXMLDOMSchemaCollection;
}

[
    uuid(f5078f21-c551-11d3-89b9-0000f81fe221)
]
coclass XSLTemplate26
{
    [default] interface IXSLTemplate;
}

[
    helpstring("XSL Template 3.0"),
    progid("Msxml2.XSLTemplate.3.0"),
    vi_progid("Msxml2.XSLTemplate"),
    threading(both),
    uuid(f5078f36-c551-11d3-89b9-0000f81fe221)
]
coclass XSLTemplate30
{
    [default] interface IXSLTemplate;
}

[
    uuid(88d969c3-f192-11d4-a65f-0040963251e5)
]
coclass XSLTemplate40
{
    [default] interface IXSLTemplate;
}

[
    uuid(88d96a08-f192-11d4-a65f-0040963251e5)
]
coclass XSLTemplate60
{
    [default] interface IXSLTemplate;
}

[
    helpstring("XSL Template"),
    progid("Msxml2.XSLTemplate"),
    vi_progid("Msxml2.XSLTemplate"),
    threading(both),
    uuid(2933BF94-7B36-11d2-B20E-00C04F983E60)
]
coclass XSLTemplate
{
    [default] interface IXSLTemplate;
}

/*
 * Sax Interfaces
 */
[
    object,
    local,
    uuid(f078abe1-45d2-4832-91ea-4466ce2f25c9)
]
interface ISAXAttributes : IUnknown
{
    HRESULT getLength(
        [out, retval] int *length);

    HRESULT getURI(
        [in] int nIndex,
        [out] const WCHAR **pUrl,
        [out] int *pUriSize);

    HRESULT getLocalName(
        [in] int nIndex,
        [out] const WCHAR **pLocalName,
        [out] int *pLocalNameLength);

    HRESULT getQName(
        [in] int nIndex,
        [out] const WCHAR **pQName,
        [out] int *pQNameLength);

    HRESULT getName(
        [in] int nIndex,
        [out] const WCHAR **pUri,
        [out] int * pUriLength,
        [out] const WCHAR ** pLocalName,
        [out] int * pLocalNameSize,
        [out] const WCHAR ** pQName,
        [out] int * pQNameLength);

    HRESULT getIndexFromName(
        [in] const WCHAR * pUri,
        [in] int cUriLength,
        [in] const WCHAR * pLocalName,
        [in] int cocalNameLength,
        [out, retval] int * index);

    HRESULT getIndexFromQName(
        [in] const WCHAR * pQName,
        [in] int nQNameLength,
        [out, retval] int * index);

    HRESULT getType(
        [in] int nIndex,
        [out] const WCHAR ** pType,
        [out] int * pTypeLength);

    HRESULT getTypeFromName(
        [in] const WCHAR * pUri,
        [in] int nUri,
        [in] const WCHAR * pLocalName,
        [in] int nLocalName,
        [out] const WCHAR ** pType,
        [out] int * nType);

    HRESULT getTypeFromQName(
        [in] const WCHAR * pQName,
        [in] int nQName,
        [out] const WCHAR ** pType,
        [out] int * nType);

    HRESULT getValue(
        [in] int nIndex,
        [out] const WCHAR ** pValue,
        [out] int * nValue);

    HRESULT getValueFromName(
        [in] const WCHAR * pUri,
        [in] int nUri,
        [in] const WCHAR * pLocalName,
        [in] int nLocalName,
        [out] const WCHAR ** pValue,
        [out] int * nValue);

    HRESULT getValueFromQName(
        [in] const WCHAR * pQName,
        [in] int nQName,
        [out] const WCHAR ** pValue,
        [out] int * nValue);
}

[
    object,
    local,
    uuid(1545cdfa-9e4e-4497-a8a4-2bf7d0112c44)
]
interface ISAXContentHandler : IUnknown
{
    HRESULT putDocumentLocator(
        [in] ISAXLocator * pLocator);

    HRESULT startDocument();

    HRESULT endDocument();

    HRESULT startPrefixMapping(
        [in] const WCHAR * pPrefix,
        [in] int nPrefix,
        [in] const WCHAR * pUri,
        [in] int nUri);

    HRESULT endPrefixMapping(
        [in] const WCHAR * pPrefix,
        [in] int nPrefix);

    HRESULT startElement(
        [in] const WCHAR * pNamespaceUri,
        [in] int nNamespaceUri,
        [in] const WCHAR * pLocalName,
        [in] int nLocalName,
        [in] const WCHAR * pQName,
        [in] int nQName,
        [in] ISAXAttributes * pAttr);

    HRESULT endElement(
        [in] const WCHAR * pNamespaceUri,
        [in] int nNamespaceUri,
        [in] const WCHAR * pLocalName,
        [in] int nLocalName,
        [in] const WCHAR * pQName,
        [in] int nQName);

    HRESULT characters(
        [in] const WCHAR * pChars,
        [in] int nChars);

    HRESULT ignorableWhitespace(
        [in] const WCHAR * pChars,
        [in] int nChars);

    HRESULT processingInstruction(
        [in] const WCHAR * pTarget,
        [in] int nTarget,
        [in] const WCHAR * pData,
        [in] int nData);

    HRESULT skippedEntity(
        [in] const WCHAR * pName,
        [in] int nName);
}

[
    object,
    local,
    uuid(862629ac-771a-47b2-8337-4e6843c1be90)
]
interface ISAXDeclHandler : IUnknown
{
    HRESULT elementDecl(
        [in] const WCHAR * pName,
        [in] int nName,
        [in] const WCHAR * pModel,
        [in] int nModel);

    HRESULT attributeDecl(
        [in] const WCHAR * pElementName,
        [in] int nElementName,
        [in] const WCHAR * pAttributeName,
        [in] int nAttributeName,
        [in] const WCHAR * pType,
        [in] int nType,
        [in] const WCHAR * pValueDefault,
        [in] int nValueDefault,
        [in] const WCHAR * pValue,
        [in] int nValue);

    HRESULT internalEntityDecl(
        [in] const WCHAR * pName,
        [in] int nName,
        [in] const WCHAR * pValue,
        [in] int nValue);

    HRESULT externalEntityDecl(
        [in] const WCHAR * pName,
        [in] int nName,
        [in] const WCHAR * pPublicId,
        [in] int nPublicId,
        [in] const WCHAR * pSystemId,
        [in] int nSystemId);
}

[
    object,
    local,
    uuid(e15c1baf-afb3-4d60-8c36-19a8c45defed)
]
interface ISAXDTDHandler : IUnknown
{
    HRESULT notationDecl(
        [in] const WCHAR * pName,
        [in] int nName,
        [in] const WCHAR * pPublicId,
        [in] int nPublicId,
        [in] const WCHAR * pSystemId,
        [in] int nSystemId);

    HRESULT unparsedEntityDecl(
        [in] const WCHAR * pName,
        [in] int nName,
        [in] const WCHAR * pPublicId,
        [in] int nPublicId,
        [in] const WCHAR * pSystemId,
        [in] int nSystemId,
        [in] const WCHAR * pNotationName,
        [in] int nNotationName);
}

[
    object,
    local,
    uuid(99bca7bd-e8c4-4d5f-a0cf-6d907901ff07),
]
interface ISAXEntityResolver : IUnknown
{
    HRESULT resolveEntity(
        [in] const WCHAR * pPublicId,
        [in] const WCHAR * pSystemId,
        [out, retval] VARIANT * ret);
}

[
    object,
    local,
    uuid(a60511c4-ccf5-479e-98a3-dc8dc545b7d0)
]
interface ISAXErrorHandler : IUnknown
{
    HRESULT error(
        [in] ISAXLocator * pLocator,
        [in] const WCHAR * pErrorMessage,
        [in] HRESULT hrErrorCode);

    HRESULT fatalError(
        [in] ISAXLocator * pLocator,
        [in] const WCHAR * pErrorMessage,
        [in] HRESULT hrErrorCode);

    HRESULT ignorableWarning(
        [in] ISAXLocator * pLocator,
        [in] const WCHAR * pErrorMessage,
        [in] HRESULT hrErrorCode);
}

[
    object,
    local,
    uuid(7f85d5f5-47a8-4497-bda5-84ba04819ea6)
]
interface ISAXLexicalHandler : IUnknown
{
    HRESULT startDTD(
        [in] const WCHAR * pName,
        [in] int nName,
        [in] const WCHAR * pPublicId,
        [in] int nPublicId,
        [in] const WCHAR * pSystemId,
        [in] int nSystemId);

    HRESULT endDTD();

    HRESULT startEntity(
        [in] const WCHAR * pName,
        [in] int nName);

    HRESULT endEntity(
        [in] const WCHAR * pName,
        [in] int nName);

    HRESULT startCDATA();

    HRESULT endCDATA();

    HRESULT comment(
        [in] const WCHAR * pChars,
        [in] int nChars);
}

[
    object,
    local,
    uuid(9b7e472a-0de4-4640-bff3-84d38a051c31)
]
interface ISAXLocator : IUnknown
{
    HRESULT getColumnNumber(
        [out, retval] int * nColumn);

    HRESULT getLineNumber(
        [out, retval] int * nLine);

    HRESULT getPublicId(
        [out, retval] const WCHAR ** publicId);

    HRESULT getSystemId(
        [out, retval] const WCHAR ** systemId);
}

[
    local,
    object,
    uuid(a4f96ed0-f829-476e-81c0-cdc7bd2a0802)
]
interface ISAXXMLReader : IUnknown
{
    HRESULT getFeature(
        [in] const WCHAR * pFeature,
        [out, retval] VARIANT_BOOL * pValue);
    HRESULT putFeature(
        [in] const WCHAR * pFeature,
        [in] VARIANT_BOOL vfValue);

    HRESULT getProperty(
        [in] const WCHAR * pProp,
        [out, retval] VARIANT * pValue);
    HRESULT putProperty(
        [in] const WCHAR * pProp,
        [in] VARIANT value);

    HRESULT getEntityResolver(
        [out, retval] ISAXEntityResolver ** ppEntityResolver);
    HRESULT putEntityResolver(
        [in] ISAXEntityResolver * pEntityResolver);

    HRESULT getContentHandler(
        [out, retval] ISAXContentHandler ** pContentHandler);
    HRESULT putContentHandler(
        [in] ISAXContentHandler * contentHandler);

    HRESULT getDTDHandler(
        [out, retval] ISAXDTDHandler ** pDTDHandler);
    HRESULT putDTDHandler(
        [in] ISAXDTDHandler * pDTDHandler);

    HRESULT getErrorHandler(
        [out, retval] ISAXErrorHandler ** pErrorHandler);
    HRESULT putErrorHandler(
        [in] ISAXErrorHandler * errorHandler);

    HRESULT getBaseURL(
        [out, retval] const WCHAR ** pBaseUrl);
    HRESULT putBaseURL(
        [in] const WCHAR * pBaseUrl);

    HRESULT getSecureBaseURL(
        [out, retval] const WCHAR ** pSecureBaseUrl);
    HRESULT putSecureBaseURL(
        [in] const WCHAR * secureBaseUrl);

    HRESULT parse(
        [in] VARIANT varInput);
    HRESULT parseURL(
        [in] const WCHAR * url);
}

[
    local,
    object,
    uuid(70409222-ca09-4475-acb8-40312fe8d145)
]
interface ISAXXMLFilter : ISAXXMLReader
{
    HRESULT getParent(
        [out, retval] ISAXXMLReader ** pReader);
    HRESULT putParent(
        [in] ISAXXMLReader * reader);
}

[
    object,
    dual,
    oleautomation,
    uuid(10dc0586-132b-4cac-8bb3-db00ac8b7ee0)
]
interface IVBSAXAttributes : IDispatch
{
    [propget, id(DISPID_SAX_ATTRIBUTES_LENGTH)]
    HRESULT length( [out, retval] int * nLength);

    [id(DISPID_SAX_ATTRIBUTES_GETURI)]
    HRESULT getURI( [in] int nIndex, [out, retval] BSTR * uri);

    [id(DISPID_SAX_ATTRIBUTES_GETLOCALNAME)]
    HRESULT getLocalName( [in] int nIndex, [out, retval] BSTR * localName);

    [id(DISPID_SAX_ATTRIBUTES_GETQNAME)]
    HRESULT getQName( [in] int nIndex, [out, retval] BSTR * QName);

    [id(DISPID_SAX_ATTRIBUTES_GETINDEXFROMNAME)]
    HRESULT getIndexFromName( [in] BSTR uri,
                              [in] BSTR localName, [out, retval] int * nIndex);

    [id(DISPID_SAX_ATTRIBUTES_GETINDEXFROMQNAME)]
    HRESULT getIndexFromQName( [in] BSTR QName, [out, retval] int * nIndex);

    [id(DISPID_SAX_ATTRIBUTES_GETTYPE)]
    HRESULT getType( [in] int nIndex, [out, retval] BSTR * type);

    [id(DISPID_SAX_ATTRIBUTES_GETTYPEFROMNAME)]
    HRESULT getTypeFromName( [in] BSTR uri, [in] BSTR localName,
                             [out, retval] BSTR * type);

    [id(DISPID_SAX_ATTRIBUTES_GETTYPEFROMQNAME)]
    HRESULT getTypeFromQName( [in] BSTR QName, [out, retval] BSTR * type);

    [id(DISPID_SAX_ATTRIBUTES_GETVALUE)]
    HRESULT getValue( [in] int nIndex, [out, retval] BSTR * value);

    [id(DISPID_SAX_ATTRIBUTES_GETVALUEFROMNAME)]
    HRESULT getValueFromName( [in] BSTR uri,
                              [in] BSTR localName,
                              [out, retval] BSTR * value);

    [id(DISPID_SAX_ATTRIBUTES_GETVALUEFROMQNAME)]
    HRESULT getValueFromQName( [in] BSTR QName, [out, retval] BSTR * value);
}

[
    object,
    dual,
    oleautomation,
    uuid(2ed7290a-4dd5-4b46-bb26-4e4155e77faa)
]
interface IVBSAXContentHandler : IDispatch
{
    [propputref, id(DISPID_SAX_CONTENTHANDLER_DOCUMENTLOCATOR)]
    HRESULT documentLocator( [in] IVBSAXLocator * oLocator);

    [id(DISPID_SAX_CONTENTHANDLER_STARTDOCUMENT)]
    HRESULT startDocument();

    [id(DISPID_SAX_CONTENTHANDLER_ENDDOCUMENT)]
    HRESULT endDocument();

    [id(DISPID_SAX_CONTENTHANDLER_STARTPREFIXMAPPING)]
    HRESULT startPrefixMapping( [in, out] BSTR * prefix, [in, out] BSTR * uri);

    [id(DISPID_SAX_CONTENTHANDLER_ENDPREFIXMAPPING)]
    HRESULT endPrefixMapping( [in, out] BSTR * prefix);

    [id(DISPID_SAX_CONTENTHANDLER_STARTELEMENT)]
    HRESULT startElement( [in, out] BSTR * namespaceURI,
                          [in, out] BSTR * localName,
                          [in, out] BSTR * QName,
                          [in] IVBSAXAttributes * oAttributes);

    [id(DISPID_SAX_CONTENTHANDLER_ENDELEMENT)]
    HRESULT endElement( [in, out] BSTR * namespaceURI,
                        [in, out] BSTR * localName, [in, out] BSTR * strQName);

    [id(DISPID_SAX_CONTENTHANDLER_CHARACTERS)]
    HRESULT characters( [in, out] BSTR * chars);

    [id(DISPID_SAX_CONTENTHANDLER_IGNORABLEWHITESPACE)]
    HRESULT ignorableWhitespace( [in, out] BSTR * chars);

    [id(DISPID_SAX_CONTENTHANDLER_PROCESSINGINSTRUCTION)]
    HRESULT processingInstruction( [in, out] BSTR * target,
                                   [in, out] BSTR * data);

    [id(DISPID_SAX_CONTENTHANDLER_SKIPPEDENTITY)]
    HRESULT skippedEntity( [in, out] BSTR * name);
}

[
    object,
    dual,
    oleautomation,
    uuid(e8917260-7579-4be1-b5dd-7afbfa6f077b)
]
interface IVBSAXDeclHandler : IDispatch
{
    [id(DISPID_SAX_DECLHANDLER_ELEMENTDECL)]
    HRESULT elementDecl(
        [in, out] BSTR * name,
        [in, out] BSTR * model);

    [id(DISPID_SAX_DECLHANDLER_ATTRIBUTEDECL)]
    HRESULT attributeDecl(
        [in, out] BSTR * elementName,
        [in, out] BSTR * attributeName,
        [in, out] BSTR * type,
        [in, out] BSTR * valueDefault,
        [in, out] BSTR * value);

    [id(DISPID_SAX_DECLHANDLER_INTERNALENTITYDECL)]
    HRESULT internalEntityDecl(
        [in, out] BSTR * name,
        [in, out] BSTR * value);

    [id(DISPID_SAX_DECLHANDLER_EXTERNALENTITYDECL)]
    HRESULT externalEntityDecl(
        [in, out] BSTR * name,
        [in, out] BSTR * publicId,
        [in, out] BSTR * systemId);
}

[
    object,
    dual,
    oleautomation,
    uuid(24fb3297-302d-4620-ba39-3a732d850558)
]
interface IVBSAXDTDHandler : IDispatch
{
    [id(DISPID_SAX_DTDHANDLER_NOTATIONDECL)]
    HRESULT notationDecl(
        [in, out] BSTR * name,
        [in, out] BSTR * publicId,
        [in, out] BSTR * systemId);

    [id(DISPID_SAX_DTDHANDLER_UNPARSEDENTITYDECL)]
    HRESULT unparsedEntityDecl(
        [in, out] BSTR * name,
        [in, out] BSTR * publicId,
        [in, out] BSTR * systemId,
        [in, out] BSTR * notationName);
}

[
    object,
    dual,
    oleautomation,
    uuid(0c05d096-f45b-4aca-ad1a-aa0bc25518dc)
]
interface IVBSAXEntityResolver : IDispatch
{
    [id(DISPID_SAX_ENTITYRESOLVER_RESOLVEENTITY)]
    HRESULT resolveEntity(
        [in, out] BSTR * publicId,
        [in, out] BSTR * systemId,
        [out, retval] VARIANT * ret);
}

[
    object,
    dual,
    oleautomation,
    uuid(d963d3fe-173c-4862-9095-b92f66995f52)
]
interface IVBSAXErrorHandler : IDispatch
{
    [id(DISPID_SAX_ERRORHANDLER_ERROR)]
    HRESULT error(
            [in] IVBSAXLocator * locator,
	    [in, out] BSTR * errorMessage,
            [in] LONG errorCode);

    [id(DISPID_SAX_ERRORHANDLER_FATALERROR)]
    HRESULT fatalError(
	    [in] IVBSAXLocator * locator,
	    [in, out] BSTR * errorMessage,
            [in] LONG errorCode);

    [id(DISPID_SAX_ERRORHANDLER_IGNORABLEWARNING)]
    HRESULT ignorableWarning(
	    [in] IVBSAXLocator * locator,
	    [in, out] BSTR * errorMessage,
            [in] LONG errorCode);
}

[
    object,
    dual,
    oleautomation,
    uuid(032aac35-8c0e-4d9d-979f-e3b702935576)
]
interface IVBSAXLexicalHandler : IDispatch
{
    [id(DISPID_SAX_LEXICALHANDLER_STARTDTD)]
    HRESULT startDTD(
        [in, out] BSTR * name,
        [in, out] BSTR * publicId,
        [in, out] BSTR * systemId);
    [id(DISPID_SAX_LEXICALHANDLER_ENDDTD)]
    HRESULT endDTD();

    [id(DISPID_SAX_LEXICALHANDLER_STARTENTITY)]
    HRESULT startEntity([in, out] BSTR * name);
    [id(DISPID_SAX_LEXICALHANDLER_ENDENTITY)]
    HRESULT endEntity([in, out] BSTR * name);

    [id(DISPID_SAX_LEXICALHANDLER_STARTCDATA)]
    HRESULT startCDATA();
    [id(DISPID_SAX_LEXICALHANDLER_ENDCDATA)]
    HRESULT endCDATA();

    [id(DISPID_SAX_LEXICALHANDLER_COMMENT)]
    HRESULT comment( [in, out] BSTR * chars);
}

[
    object,
    dual,
    oleautomation,
    uuid(796e7ac5-5aa2-4eff-acad-3faaf01a3288)
]
interface IVBSAXLocator : IDispatch
{
    [propget, id(DISPID_SAX_LOCATOR_COLUMNNUMBER)]
    HRESULT columnNumber( [out, retval] int * column);

    [propget, id(DISPID_SAX_LOCATOR_LINENUMBER)]
    HRESULT lineNumber( [out, retval] int * line);

    [propget, id(DISPID_SAX_LOCATOR_PUBLICID)]
    HRESULT publicId( [out, retval] BSTR * publicId);

    [propget, id(DISPID_SAX_LOCATOR_SYSTEMID)]
    HRESULT systemId( [out, retval] BSTR * systemId);
}

[
    object,
    dual,
    oleautomation,
    uuid(1299eb1b-5b88-433e-82de-82ca75ad4e04)
]
interface IVBSAXXMLFilter : IDispatch
{
    [propget, id(DISPID_SAX_XMLFILTER_PARENT)]
    HRESULT parent( [out, retval] IVBSAXXMLReader ** reader);
    [propputref, id(DISPID_SAX_XMLFILTER_PARENT)]
    HRESULT parent( [in] IVBSAXXMLReader * reader);

}

[
    dual,
    oleautomation,
    object,
    uuid (8c033caa-6cd6-4f73-b728-4531af74945f)
]
interface IVBSAXXMLReader : IDispatch
{
    [id(DISPID_SAX_XMLREADER_GETFEATURE)]
    HRESULT getFeature( [in] BSTR pFeature,
                        [out, retval] VARIANT_BOOL * pValue);
    [id(DISPID_SAX_XMLREADER_PUTFEATURE)]
    HRESULT putFeature( [in] BSTR pFeature,
                        [in] VARIANT_BOOL vfValue);

    [id(DISPID_SAX_XMLREADER_GETPROPERTY)]
    HRESULT getProperty( [in] BSTR pProp,
                         [out, retval] VARIANT * pValue);
    [id(DISPID_SAX_XMLREADER_PUTPROPERTY)]
    HRESULT putProperty( [in] BSTR pProp, [in] VARIANT value);

    [propget, id(DISPID_SAX_XMLREADER_ENTITYRESOLVER)]
    HRESULT entityResolver(
                    [out, retval] IVBSAXEntityResolver ** ppEntityResolver);
    [propputref, id(DISPID_SAX_XMLREADER_ENTITYRESOLVER)]
    HRESULT entityResolver( [in] IVBSAXEntityResolver * pEntityResolver);

    [propget, id(DISPID_SAX_XMLREADER_CONTENTHANDLER)]
    HRESULT contentHandler(
                        [out, retval] IVBSAXContentHandler ** pContentHandler);
    [propputref, id(DISPID_SAX_XMLREADER_CONTENTHANDLER)]
    HRESULT contentHandler([in] IVBSAXContentHandler * contentHandler);

    [propget, id(DISPID_SAX_XMLREADER_DTDHANDLER)]
    HRESULT dtdHandler([out, retval] IVBSAXDTDHandler ** pDTDHandler);
    [propputref, id(DISPID_SAX_XMLREADER_DTDHANDLER)]
    HRESULT dtdHandler([in] IVBSAXDTDHandler * pDTDHandler);

    [propget, id(DISPID_SAX_XMLREADER_ERRORHANDLER)]
    HRESULT errorHandler([out, retval] IVBSAXErrorHandler ** pErrorHandler);
    [propputref, id(DISPID_SAX_XMLREADER_ERRORHANDLER)]
    HRESULT errorHandler([in] IVBSAXErrorHandler * errorHandler);

    [propget, id(DISPID_SAX_XMLREADER_BASEURL)]
    HRESULT baseURL([out, retval] BSTR *pBaseUrl);
    [propput, id(DISPID_SAX_XMLREADER_BASEURL)]
    HRESULT baseURL([in] BSTR pBaseUrl);

    [propget, id(DISPID_SAX_XMLREADER_SECUREBASEURL)]
    HRESULT secureBaseURL([out, retval] BSTR *pSecureBaseUrl);
    [propput, id(DISPID_SAX_XMLREADER_SECUREBASEURL)]
    HRESULT secureBaseURL([in] BSTR secureBaseUrl);

    [id(DISPID_SAX_XMLREADER_PARSE)]
    HRESULT parse( [in] VARIANT varInput);
    [id(DISPID_SAX_XMLREADER_PARSEURL)]
    HRESULT parseURL([in] BSTR url);
}

[
    object,
    dual,
    oleautomation,
    uuid(f10d27cc-3ec0-415c-8ed8-77ab1c5e7262)
]
interface IMXAttributes : IDispatch
{
    [id(DISPID_MX_ATTRIBUTES_ADDATTRIBUTE)]
    HRESULT addAttribute(
        [in] BSTR uri,
        [in] BSTR localName,
        [in] BSTR QName,
        [in] BSTR type,
        [in] BSTR value);

    [id(DISPID_MX_ATTRIBUTES_ADDATTRIBUTEFROMINDEX)]
    HRESULT addAttributeFromIndex( [in] VARIANT atts,
                                   [in] int index);

    [id(DISPID_MX_ATTRIBUTES_CLEAR)]
    HRESULT clear();

    [id(DISPID_MX_ATTRIBUTES_REMOVEATTRIBUTE)]
    HRESULT removeAttribute( [in] int index);

    [id(DISPID_MX_ATTRIBUTES_SETATTRIBUTE)]
    HRESULT setAttribute(
        [in] int index,
        [in] BSTR uri,
        [in] BSTR localName,
        [in] BSTR QName,
        [in] BSTR type,
        [in] BSTR value);

    [id(DISPID_MX_ATTRIBUTES_SETATTRIBUTES)]
    HRESULT setAttributes( [in] VARIANT atts);

    [id(DISPID_MX_ATTRIBUTES_SETLOCALNAME)]
    HRESULT setLocalName( [in] int index,
        [in] BSTR localName);

    [id(DISPID_MX_ATTRIBUTES_SETQNAME)]
    HRESULT setQName(
        [in] int index,
        [in] BSTR QName);

    [id(DISPID_MX_ATTRIBUTES_SETTYPE)]
    HRESULT setType( [in] int index, [in] BSTR type);

    [id(DISPID_MX_ATTRIBUTES_SETURI)]
    HRESULT setURI( [in] int index,  [in] BSTR uri);

    [id(DISPID_MX_ATTRIBUTES_SETVALUE)]
    HRESULT setValue([in] int index, [in] BSTR value);
}

[
    local,
    object,
    dual,
    oleautomation,
    uuid(fa4bb38c-faf9-4cca-9302-d1dd0fe520db)
]
interface IMXSchemaDeclHandler : IDispatch
{
    [id(DISPID_MX_SCHEMADECLHANDLER_SCHEMAELEMENTDECL)]
    HRESULT schemaElementDecl( [in] ISchemaElement *oSchemaElement );
}

[
    object,
    dual,
    oleautomation,
    uuid(808f4e35-8d5a-4fbe-8466-33a41279ed30)
]
interface IMXReaderControl : IDispatch
{
    [id(DISPID_MX_READER_CONTROL_ABORT)]
    HRESULT abort();

    [id(DISPID_MX_READER_CONTROL_RESUME)]
    HRESULT resume();

    [id(DISPID_MX_READER_CONTROL_SUSPEND)]
    HRESULT suspend();
}

[
    object,
    dual,
    oleautomation,
    uuid(4d7ff4ba-1565-4ea8-94e1-6e724a46f98d)
]
interface IMXWriter :  IDispatch
{
    [propput, id(DISPID_MX_WRITER_OUTPUT)]
    HRESULT output ( [in] VARIANT Destination);
    [propget, id(DISPID_MX_WRITER_OUTPUT)]
    HRESULT output ( [out, retval] VARIANT * Destination);

    [propput, id(DISPID_MX_WRITER_ENCODING)]
    HRESULT encoding ([in] BSTR encoding);
    [propget, id(DISPID_MX_WRITER_ENCODING)]
    HRESULT encoding ([out, retval] BSTR * encoding);

    [propput, id(DISPID_MX_WRITER_BYTEORDERMARK)]
    HRESULT byteOrderMark ([in] VARIANT_BOOL writeByteOrderMark);
    [propget, id(DISPID_MX_WRITER_BYTEORDERMARK)]
    HRESULT byteOrderMark ([out, retval] VARIANT_BOOL * writeByteOrderMark);

    [propput, id(DISPID_MX_WRITER_INDENT)]
    HRESULT indent ([in] VARIANT_BOOL indentMode);
    [propget, id(DISPID_MX_WRITER_INDENT)]
    HRESULT indent ([out, retval] VARIANT_BOOL * indentMode);

    [propput, id(DISPID_MX_WRITER_STANDALONE)]
    HRESULT standalone ([in] VARIANT_BOOL value);
    [propget, id(DISPID_MX_WRITER_STANDALONE)]
    HRESULT standalone ([out, retval] VARIANT_BOOL * value);

    [propput, id(DISPID_MX_WRITER_OMITXMLDECLARATION)]
    HRESULT omitXMLDeclaration ([in] VARIANT_BOOL value);
    [propget, id(DISPID_MX_WRITER_OMITXMLDECLARATION)]
    HRESULT omitXMLDeclaration ([out, retval] VARIANT_BOOL * value);

    [propput, id(DISPID_MX_WRITER_VERSION)]
    HRESULT version ([in] BSTR version);
    [propget, id(DISPID_MX_WRITER_VERSION)]
    HRESULT version ([out, retval] BSTR * version);

    [propput, id(DISPID_MX_WRITER_DISABLEOUTPUTESCAPING)]
    HRESULT disableOutputEscaping([in] VARIANT_BOOL value);
    [propget, id(DISPID_MX_WRITER_DISABLEOUTPUTESCAPING)]
    HRESULT disableOutputEscaping([out, retval] VARIANT_BOOL * value);

    [id(DISPID_MX_WRITER_FLUSH)]
    HRESULT flush();
}

[
    local,
    object,
    dual,
    oleautomation,
    uuid(c90352f4-643c-4fbc-bb23-e996eb2d51fd)
]
interface IMXNamespacePrefixes : IDispatch
{
    [propget, id(DISPID_VALUE)]
    HRESULT item(
        [in] long index,
        [out, retval] BSTR *prefix);

    [propget, id(DISPID_MX_NSMGR_LENGTH)]
    HRESULT length( [out,retval] long *length );

    [propget, restricted, hidden, id(DISPID_NEWENUM)]
    HRESULT _newEnum( [out, retval] IUnknown **ppUnk );
}

[
    local,
    object,
    hidden,
    uuid(c90352f6-643c-4fbc-bb23-e996eb2d51fd)
]
interface IMXNamespaceManager : IUnknown
{
    HRESULT putAllowOverride([in] VARIANT_BOOL fOverride);

    HRESULT getAllowOverride([out, retval] VARIANT_BOOL *fOverride);

    HRESULT reset();

    HRESULT pushContext();

    HRESULT pushNodeContext(
        [in] IXMLDOMNode *contextNode,
        [in] VARIANT_BOOL fDeep);

    HRESULT popContext();

    HRESULT declarePrefix(
        [in] const WCHAR *prefix,
        [in] const WCHAR *namespaceURI);

    HRESULT getDeclaredPrefix(
        [in] long nIndex,
        [in, out] WCHAR *pwchPrefix,
        [in, out] int *pcchPrefix);

    HRESULT getPrefix(
        [in] const WCHAR *pwszNamespaceURI,
        [in] long nIndex,
        [in, out] WCHAR *pwchPrefix,
        [in, out] int *pcchPrefix);

    HRESULT getURI(
        [in] const WCHAR *pwchPrefix,
        [in] IXMLDOMNode* pContextNode,
        [in, out] WCHAR *pwchUri,
        [in, out] int *pcchUri);
}

[
    local,
    object,
    dual,
    oleautomation,
    uuid(c90352f5-643c-4fbc-bb23-e996eb2d51fd)
]
interface IVBMXNamespaceManager : IDispatch
{
    [propput, id(DISPID_MX_NSMGR_ALLOWOVERRIDE)]
    HRESULT allowOverride([in] VARIANT_BOOL fOverride);

    [propget, id(DISPID_MX_NSMGR_ALLOWOVERRIDE)]
    HRESULT allowOverride([out,retval] VARIANT_BOOL* fOverride);

    [id(DISPID_MX_NSMGR_RESET)]
    HRESULT reset();

    [id(DISPID_MX_NSMGR_PUSHCONTEXT)]
    HRESULT pushContext();

    [id(DISPID_MX_NSMGR_PUSHNODECONTEXT)]
    HRESULT pushNodeContext(
        [in] IXMLDOMNode* contextNode,
        [in, defaultvalue(-1)] VARIANT_BOOL fDeep);

    [id(DISPID_MX_NSMGR_POPCONTEXT)]
    HRESULT popContext();

    [id(DISPID_MX_NSMGR_DECLAREPREFIX)]
    HRESULT declarePrefix(
        [in] BSTR prefix,
        [in] BSTR namespaceURI);

    [id(DISPID_MX_NSMGR_GETDECLAREDPREFIXES)]
    HRESULT getDeclaredPrefixes([out, retval] IMXNamespacePrefixes** prefixes);

    [id(DISPID_MX_NSMGR_GETPREFIXES)]
    HRESULT getPrefixes(
        [in] BSTR namespaceURI,
        [out, retval] IMXNamespacePrefixes** prefixes);

    [id(DISPID_MX_NSMGR_GETURI)]
    HRESULT getURI(
        [in] BSTR prefix,
        [out, retval] VARIANT* uri);

    [id(DISPID_MX_NSMGR_GETURIFROMNODE)]
    HRESULT getURIFromNode(
        [in] BSTR strPrefix,
        [in] IXMLDOMNode* contextNode,
        [out, retval] VARIANT* uri);
}

[
    local,
    object,
    dual,
    oleautomation,
    uuid(c90352f7-643c-4fbc-bb23-e996eb2d51fd)
]
interface IMXXMLFilter : IDispatch
{
    [id(DISPID_MXXML_FILTER_GETFEATURE)]
    HRESULT getFeature(
        [in] BSTR strName,
        [out, retval] VARIANT_BOOL * fValue);

    [id(DISPID_MXXML_FILTER_PUTFEATURE)]
    HRESULT putFeature(
        [in] BSTR strName,
        [in] VARIANT_BOOL fValue);

    [id(DISPID_MXXML_FILTER_GETPROPERTY)]
    HRESULT getProperty(
        [in] BSTR strName,
        [out, retval] VARIANT * varValue);

    [id(DISPID_MXXML_FILTER_PUTPROPERTY)]
    HRESULT putProperty(
        [in] BSTR strName,
        [in] VARIANT varValue);

    [id(DISPID_MXXML_FILTER_ENTITYRESOLVER), propget]
    HRESULT entityResolver( [out, retval] IUnknown **oResolver );

    [id(DISPID_MXXML_FILTER_ENTITYRESOLVER), propputref]
    HRESULT entityResolver( [in] IUnknown *oResolver );

    [id(DISPID_MXXML_FILTER_CONTENTHANDLER), propget]
    HRESULT contentHandler( [out, retval] IUnknown **oHandler );

    [id(DISPID_MXXML_FILTER_CONTENTHANDLER), propputref]
    HRESULT contentHandler( [in] IUnknown *oHandler );

    [id(DISPID_MXXML_FILTER_DTDHANDLER), propget]
    HRESULT dtdHandler( [out, retval] IUnknown **oHandler );

    [id(DISPID_MXXML_FILTER_DTDHANDLER), propputref]
    HRESULT dtdHandler( [in] IUnknown *oHandler );

    [id(DISPID_MXXML_FILTER_ERRORHANDLER), propget]
    HRESULT errorHandler( [out, retval] IUnknown **oHandler );

    [id(DISPID_MXXML_FILTER_ERRORHANDLER), propputref]
    HRESULT errorHandler( [in] IUnknown *oHandler );
}

[
    local,
    object,
    uuid(50ea08b1-dd1b-4664-9a50-c2f40f4bd79a),
    oleautomation,
    dual
]
interface ISchemaStringCollection : IDispatch
{
    [id(DISPID_VALUE), propget]
    HRESULT item(
        [in] long index,
        [out,retval] BSTR* bstr);

    [id(DISPID_SOM_LENGTH), propget]
    HRESULT length(
        [out,retval] long* length);

    [id(DISPID_NEWENUM), hidden, restricted, propget]
    HRESULT _newEnum(
        [out,retval] IUnknown** ppunk);
}

[
    local,
    object,
    uuid(50ea08b2-dd1b-4664-9a50-c2f40f4bd79a),
    oleautomation,
    dual
]
interface ISchemaItemCollection : IDispatch
{
    [id(DISPID_VALUE), propget]
    HRESULT item(
        [in] long index,
        [out,retval]ISchemaItem** item);

    [id(DISPID_SOM_ITEMBYNAME)]
    HRESULT itemByName(
        [in] BSTR name,
        [out,retval] ISchemaItem** item);

    [id(DISPID_SOM_ITEMBYQNAME)]
    HRESULT itemByQName(
        [in] BSTR name,
        [in] BSTR namespaceURI,
        [out,retval] ISchemaItem** item);

    [id(DISPID_SOM_LENGTH), propget]
    HRESULT length(
        [out,retval]long* length);

    [id(DISPID_NEWENUM), hidden, restricted, propget]
    HRESULT _newEnum(
        [out,retval]IUnknown** ppunk);
}

[
    local,
    object,
    uuid(50ea08b3-dd1b-4664-9a50-c2f40f4bd79a),
    oleautomation,
    dual
]
interface ISchemaItem : IDispatch
{
    [id(DISPID_SOM_NAME), propget]
    HRESULT name(
        [out,retval] BSTR* name);

    [id(DISPID_SOM_NAMESPACEURI), propget]
    HRESULT namespaceURI(
        [out,retval] BSTR* namespaceURI);

    [id(DISPID_SOM_SCHEMA), propget]
    HRESULT schema(
        [out,retval] ISchema** schema);

    [id(DISPID_SOM_ID), propget]
    HRESULT id(
        [out,retval] BSTR* id);

    [id(DISPID_SOM_ITEMTYPE), propget]
    HRESULT itemType(
        [out,retval] SOMITEMTYPE* itemType);

    [id(DISPID_SOM_UNHANDLEDATTRS), propget]
    HRESULT unhandledAttributes(
        [out,retval] IVBSAXAttributes** attributes);

    [id(DISPID_SOM_WRITEANNOTATION)]
    HRESULT writeAnnotation(
        [in] IUnknown* annotationSink,
        [out,retval] VARIANT_BOOL* isWritten);
}

[
    local,
    object,
    uuid(50ea08b4-dd1b-4664-9a50-c2f40f4bd79a),
    oleautomation,
    dual
]
interface ISchema : ISchemaItem
{
    [id(DISPID_SOM_TARGETNAMESPACE), propget]
    HRESULT targetNamespace(
        [out,retval] BSTR* targetNamespace);

    [id(DISPID_SOM_VERSION), propget]
    HRESULT version(
        [out,retval] BSTR* version);

    [id(DISPID_SOM_TYPES), propget]
    HRESULT types(
        [out,retval] ISchemaItemCollection** types);

    [id(DISPID_SOM_ELEMENTS), propget]
    HRESULT elements(
        [out,retval] ISchemaItemCollection** elements);

    [id(DISPID_SOM_ATTRIBUTES), propget]
    HRESULT attributes(
        [out,retval] ISchemaItemCollection** attributes);

    [id(DISPID_SOM_ATTRIBUTEGROUPS), propget]
    HRESULT attributeGroups(
        [out,retval] ISchemaItemCollection** attributeGroups);

    [id(DISPID_SOM_MODELGROUPS), propget]
    HRESULT modelGroups(
        [out,retval] ISchemaItemCollection** modelGroups);

    [id(DISPID_SOM_NOTATIONS), propget]
    HRESULT notations(
        [out,retval] ISchemaItemCollection** notations);

    [id(DISPID_SOM_SCHEMALOCATIONS), propget]
    HRESULT schemaLocations(
        [out,retval] ISchemaStringCollection** schemaLocations);
}

[
    local,
    object,
    uuid(50ea08b5-dd1b-4664-9a50-c2f40f4bd79a),
    oleautomation,
    dual
]
interface ISchemaParticle : ISchemaItem
{
    [id(DISPID_SOM_MINOCCURS), propget]
    HRESULT minOccurs(
        [out,retval] VARIANT* minOccurs);

    [id(DISPID_SOM_MAXOCCURS), propget]
    HRESULT maxOccurs(
        [out,retval] VARIANT* maxOccurs);
}

[
    object,
    uuid(50ea08b6-dd1b-4664-9a50-c2f40f4bd79a),
    oleautomation,
    dual,
]
interface ISchemaAttribute : ISchemaItem
{
    [id(DISPID_SOM_TYPE), propget]
    HRESULT type(
        [out,retval] ISchemaType** type);

    [id(DISPID_SOM_SCOPE), propget]
    HRESULT scope(
        [out,retval] ISchemaComplexType** scope);

    [id(DISPID_SOM_DEFAULTVALUE), propget]
    HRESULT defaultValue(
        [out,retval]BSTR* defaultValue);

    [id(DISPID_SOM_FIXEDVALUE), propget]
    HRESULT fixedValue(
        [out,retval] BSTR* fixedValue);

    [id(DISPID_SOM_USE), propget]
    HRESULT use(
        [out,retval] SCHEMAUSE* use);

    [id(DISPID_SOM_ISREFERENCE), propget]
    HRESULT isReference(
        [out,retval] VARIANT_BOOL* reference);
}

[
    local,
    object,
    uuid(50ea08b7-dd1b-4664-9a50-c2f40f4bd79a),
    oleautomation,
    dual
]
interface ISchemaElement : ISchemaParticle
{
    [id(DISPID_SOM_TYPE), propget]
    HRESULT type(
        [out,retval] ISchemaType** type);

    [id(DISPID_SOM_SCOPE), propget]
    HRESULT scope(
        [out,retval] ISchemaComplexType** scope);

    [id(DISPID_SOM_DEFAULTVALUE), propget]
    HRESULT defaultValue(
        [out,retval] BSTR* defaultValue);

    [id(DISPID_SOM_FIXEDVALUE), propget]
    HRESULT fixedValue(
        [out,retval] BSTR* fixedValue);

    [id(DISPID_SOM_ISNILLABLE), propget]
    HRESULT isNillable(
        [out,retval] VARIANT_BOOL* nillable);

    [id(DISPID_SOM_IDCONSTRAINTS), propget]
    HRESULT identityConstraints(
        [out,retval] ISchemaItemCollection** constraints);

    [id(DISPID_SOM_SUBSTITUTIONGROUP), propget]
    HRESULT substitutionGroup(
        [out,retval] ISchemaElement** element);

    [id(DISPID_SOM_EXCLUSIONS), propget]
    HRESULT substitutionGroupExclusions(
        [out,retval] SCHEMADERIVATIONMETHOD* exclusions);

    [id(DISPID_SOM_DISALLOWED), propget]
    HRESULT disallowedSubstitutions(
        [out,retval] SCHEMADERIVATIONMETHOD* disallowed);

    [id(DISPID_SOM_ISABSTRACT), propget]
    HRESULT isAbstract(
        [out,retval] VARIANT_BOOL* abstract);

    [id(DISPID_SOM_ISREFERENCE), propget]
    HRESULT isReference(
        [out,retval] VARIANT_BOOL* reference);
}

[
    local,
    object,
    uuid(50ea08b8-dd1b-4664-9a50-c2f40f4bd79a),
    oleautomation,
    dual
]
interface ISchemaType : ISchemaItem
{
    [id(DISPID_SOM_BASETYPES), propget]
    HRESULT baseTypes(
        [out,retval] ISchemaItemCollection** baseTypes);

    [id(DISPID_SOM_FINAL), propget]
    HRESULT final(
        [out,retval] SCHEMADERIVATIONMETHOD* final);

    [id(DISPID_SOM_VARIETY), propget]
    HRESULT variety(
        [out,retval] SCHEMATYPEVARIETY* variety);

    [id(DISPID_SOM_DERIVEDBY), propget]
    HRESULT derivedBy(
        [out,retval] SCHEMADERIVATIONMETHOD* derivedBy);

    [id(DISPID_SOM_ISVALID)]
    HRESULT isValid(
        [in] BSTR data,
        [out,retval] VARIANT_BOOL* valid);

    [id(DISPID_SOM_MINEXCLUSIVE), propget]
    HRESULT minExclusive(
        [out,retval]BSTR* minExclusive);

    [id(DISPID_SOM_MININCLUSIVE), propget]
    HRESULT minInclusive(
        [out,retval] BSTR* minInclusive);

    [id(DISPID_SOM_MAXEXCLUSIVE), propget]
    HRESULT maxExclusive(
        [out,retval] BSTR* maxExclusive);

    [id(DISPID_SOM_MAXINCLUSIVE), propget]
    HRESULT maxInclusive(
        [out,retval] BSTR* maxInclusive);

    [id(DISPID_SOM_TOTALDIGITS), propget]
    HRESULT totalDigits(
        [out,retval] VARIANT* totalDigits);

    [id(DISPID_SOM_FRACTIONDIGITS), propget]
    HRESULT fractionDigits(
        [out,retval] VARIANT* fractionDigits);

    [id(DISPID_SOM_LENGTH), propget]
    HRESULT length(
        [out,retval] VARIANT* length);

    [id(DISPID_SOM_MINLENGTH), propget]
    HRESULT minLength(
        [out,retval]VARIANT* minLength);

    [id(DISPID_SOM_MAXLENGTH), propget]
    HRESULT maxLength(
        [out,retval]VARIANT* maxLength);

    [id(DISPID_SOM_ENUMERATION), propget]
    HRESULT enumeration(
        [out,retval] ISchemaStringCollection** enumeration);

    [id(DISPID_SOM_WHITESPACE), propget]
    HRESULT whitespace(
        [out,retval]SCHEMAWHITESPACE* whitespace);

    [id(DISPID_SOM_PATTERNS), propget]
    HRESULT patterns(
        [out,retval] ISchemaStringCollection** patterns);
}

[
    local,
    object,
    uuid(50ea08b9-dd1b-4664-9a50-c2f40f4bd79a),
    oleautomation,
    dual,
]
interface ISchemaComplexType : ISchemaType
{
    [id(DISPID_SOM_ISABSTRACT), propget]
    HRESULT isAbstract(
        [out,retval] VARIANT_BOOL* abstract);

    [id(DISPID_SOM_ANYATTRIBUTE), propget]
    HRESULT anyAttribute(
        [out,retval] ISchemaAny** anyAttribute);

    [id(DISPID_SOM_ATTRIBUTES), propget]
    HRESULT attributes(
        [out,retval] ISchemaItemCollection** attributes);

    [id(DISPID_SOM_CONTENTTYPE), propget]
    HRESULT contentType(
        [out,retval] SCHEMACONTENTTYPE* contentType);

    [id(DISPID_SOM_CONTENTMODEL), propget]
    HRESULT contentModel(
        [out,retval] ISchemaModelGroup** contentModel);

    [id(DISPID_SOM_PROHIBITED), propget]
    HRESULT prohibitedSubstitutions(
        [out,retval] SCHEMADERIVATIONMETHOD* prohibited);
}

[
    local,
    object,
    uuid(50ea08ba-dd1b-4664-9a50-c2f40f4bd79a),
    oleautomation,
    dual,
]
interface ISchemaAttributeGroup : ISchemaItem
{
    [id(DISPID_SOM_ANYATTRIBUTE), propget]
    HRESULT anyAttribute(
        [out,retval] ISchemaAny** anyAttribute);

    [id(DISPID_SOM_ATTRIBUTES), propget]
        HRESULT attributes(
            [out,retval] ISchemaItemCollection** attributes);
}

[
    local,
    object,
    uuid(50ea08bb-dd1b-4664-9a50-c2f40f4bd79a),
    oleautomation,
    dual,
]
interface ISchemaModelGroup : ISchemaParticle
{
    [id(DISPID_SOM_PARTICLES), propget]
    HRESULT particles(
        [out,retval] ISchemaItemCollection** particles);
}

[
    local,
    object,
    uuid(50ea08bc-dd1b-4664-9a50-c2f40f4bd79a),
    oleautomation,
    dual
]
interface ISchemaAny : ISchemaParticle
{
    [id(DISPID_SOM_NAMESPACES), propget]
    HRESULT namespaces(
        [out,retval] ISchemaStringCollection** namespaces);

    [id(DISPID_SOM_PROCESSCONTENTS), propget]
    HRESULT processContents(
        [out,retval] SCHEMAPROCESSCONTENTS* processContents);
}

[
    local,
    object,
    uuid(50ea08bd-dd1b-4664-9a50-c2f40f4bd79a),
    oleautomation,
    dual
]
interface ISchemaIdentityConstraint : ISchemaItem
{
    [id(DISPID_SOM_SELECTOR), propget]
    HRESULT selector(
        [out,retval] BSTR* selector);

    [id(DISPID_SOM_FIELDS), propget]
        HRESULT fields(
            [out,retval] ISchemaStringCollection** fields);

    [id(DISPID_SOM_REFERENCEDKEY), propget]
    HRESULT referencedKey(
        [out,retval] ISchemaIdentityConstraint** key);
}

[
    local,
    object,
    uuid(50ea08be-dd1b-4664-9a50-c2f40f4bd79a),
    oleautomation,
    dual
]
interface ISchemaNotation : ISchemaItem
{
    [id(DISPID_SOM_SYSTEMIDENTIFIER), propget]
    HRESULT systemIdentifier(
        [out,retval] BSTR* uri);

    [id(DISPID_SOM_PUBLICIDENTIFIER), propget]
    HRESULT publicIdentifier(
        [out,retval] BSTR* uri);
}


[
    helpstring("SAX XML Reader 3.0"),
    progid("Msxml2.SAXXMLReader.3.0"),
    vi_progid("Msxml2.SAXXMLReader"),
    threading(both),
    uuid(3124c396-fb13-4836-a6ad-1317f1713688)
]
coclass SAXXMLReader30
{
    [default] interface IVBSAXXMLReader;
    interface ISAXXMLReader;
    interface IMXReaderControl;
}

[
    uuid(7c6e29bc-8b8b-4c3d-859e-af6cd158be0f)
]
coclass SAXXMLReader40
{
    [default] interface IVBSAXXMLReader;
    interface ISAXXMLReader;
}

[
    uuid(88d96a0c-f192-11d4-a65f-0040963251e5)
]
coclass SAXXMLReader60
{
    [default] interface IVBSAXXMLReader;
    interface ISAXXMLReader;
}

[
    helpstring("SAX XML Reader"),
    progid("Msxml2.SAXXMLReader"),
    vi_progid("Msxml2.SAXXMLReader"),
    threading(both),
    uuid(079aa557-4a18-424a-8eee-e39f0a8d41b9)
]
coclass SAXXMLReader
{
    [default] interface IVBSAXXMLReader;
    interface ISAXXMLReader;
    interface IMXReaderControl;
}

[
    uuid(a4c23ec3-6b70-4466-9127-************)
]
coclass MXHTMLWriter
{
    [default] interface IMXWriter;

    interface ISAXContentHandler;
    interface ISAXErrorHandler;
    interface ISAXDTDHandler;
    interface ISAXLexicalHandler;
    interface ISAXDeclHandler;

    interface IVBSAXContentHandler;
    interface IVBSAXDeclHandler;
    interface IVBSAXDTDHandler;
    interface IVBSAXErrorHandler;
    interface IVBSAXLexicalHandler;
}

[
    uuid(853d1540-c1a7-4aa9-a226-4d3bd301146d)
]
coclass MXHTMLWriter30
{
    [default] interface IMXWriter;

    interface ISAXContentHandler;
    interface ISAXDeclHandler;
    interface ISAXDTDHandler;
    interface ISAXErrorHandler;
    interface ISAXLexicalHandler;

    interface IVBSAXContentHandler;
    interface IVBSAXDeclHandler;
    interface IVBSAXDTDHandler;
    interface IVBSAXErrorHandler;
    interface IVBSAXLexicalHandler;
}

[
    uuid(88d969c9-f192-11d4-a65f-0040963251e5)
]
coclass MXHTMLWriter40
{
    [default] interface IMXWriter;

    interface ISAXContentHandler;
    interface ISAXDeclHandler;
    interface ISAXDTDHandler;
    interface ISAXErrorHandler;
    interface ISAXLexicalHandler;

    interface IVBSAXContentHandler;
    interface IVBSAXDeclHandler;
    interface IVBSAXDTDHandler;
    interface IVBSAXErrorHandler;
    interface IVBSAXLexicalHandler;
}

[
    uuid(88d96a10-f192-11d4-a65f-0040963251e5)
]
coclass MXHTMLWriter60
{
    [default] interface IMXWriter;

    interface ISAXContentHandler;
    interface ISAXDeclHandler;
    interface ISAXDTDHandler;
    interface ISAXErrorHandler;
    interface ISAXLexicalHandler;

    interface IVBSAXContentHandler;
    interface IVBSAXDeclHandler;
    interface IVBSAXDTDHandler;
    interface IVBSAXErrorHandler;
    interface IVBSAXLexicalHandler;
}

[
    helpstring("MXXMLWriter 3.0"),
    progid("Msxml2.MXXMLWriter.3.0"),
    vi_progid("Msxml2.MXXMLWriter"),
    threading(both),
    uuid(3d813dfe-6c91-4a4e-8f41-04346a841d9c)
]
coclass MXXMLWriter30
{
    [default] interface IMXWriter;

    interface ISAXContentHandler;
    interface ISAXDeclHandler;
    interface ISAXDTDHandler;
    interface ISAXErrorHandler;
    interface ISAXLexicalHandler;

    interface IVBSAXContentHandler;
    interface IVBSAXDeclHandler;
    interface IVBSAXDTDHandler;
    interface IVBSAXErrorHandler;
    interface IVBSAXLexicalHandler;
}

[
    uuid(88d969c8-f192-11d4-a65f-0040963251e5),
]
coclass MXXMLWriter40
{
    [default] interface IMXWriter;

    interface ISAXContentHandler;
    interface ISAXDeclHandler;
    interface ISAXDTDHandler;
    interface ISAXErrorHandler;
    interface ISAXLexicalHandler;

    interface IVBSAXContentHandler;
    interface IVBSAXDeclHandler;
    interface IVBSAXDTDHandler;
    interface IVBSAXErrorHandler;
    interface IVBSAXLexicalHandler;
}

[
    uuid(88d96a0f-f192-11d4-a65f-0040963251e5)
]
coclass MXXMLWriter60
{
    [default] interface IMXWriter;

    interface ISAXContentHandler;
    interface ISAXDeclHandler;
    interface ISAXDTDHandler;
    interface ISAXErrorHandler;
    interface ISAXLexicalHandler;

    interface IVBSAXContentHandler;
    interface IVBSAXDeclHandler;
    interface IVBSAXDTDHandler;
    interface IVBSAXErrorHandler;
    interface IVBSAXLexicalHandler;
}

[
    helpstring("MXXMLWriter"),
    progid("Msxml2.MXXMLWriter"),
    vi_progid("Msxml2.MXXMLWriter"),
    threading(both),
    uuid(fc220ad8-a72a-4ee8-926e-0b7ad152a020)
]
coclass MXXMLWriter
{
    [default] interface IMXWriter;

    interface ISAXContentHandler;
    interface ISAXErrorHandler;
    interface ISAXDTDHandler;
    interface ISAXLexicalHandler;
    interface ISAXDeclHandler;

    interface IVBSAXContentHandler;
    interface IVBSAXDeclHandler;
    interface IVBSAXDTDHandler;
    interface IVBSAXErrorHandler;
    interface IVBSAXLexicalHandler;
}

[
    uuid(88d969d5-f192-11d4-a65f-0040963251e5)
]
coclass MXNamespaceManager
{
    [default] interface IVBMXNamespaceManager;
    interface IMXNamespaceManager;
}

[
    uuid(88d969d6-f192-11d4-a65f-0040963251e5)
]
coclass MXNamespaceManager40
{
    [default] interface IVBMXNamespaceManager;
    interface IMXNamespaceManager;
}

[
    uuid(88d96a11-f192-11d4-a65f-0040963251e5)
]
coclass MXNamespaceManager60
{
    [default] interface IVBMXNamespaceManager;
    interface IMXNamespaceManager;
}

[
    helpstring("SAXAttributes 3.0"),
    progid("Msxml2.SAXAttributes.3.0"),
    vi_progid("Msxml2.SAXAttributes"),
    threading(both),
    uuid(3e784a01-f3ae-4dc0-9354-9526b9370eba)
]
coclass SAXAttributes30
{
    [default] interface IMXAttributes;
    interface IVBSAXAttributes;
    interface ISAXAttributes;
}

[
    uuid(88d969ca-f192-11d4-a65f-0040963251e5),
]
coclass SAXAttributes40
{
    [default] interface IMXAttributes;
    interface IVBSAXAttributes;
    interface ISAXAttributes;
}

[
    uuid(88d96a0e-f192-11d4-a65f-0040963251e5)
]
coclass SAXAttributes60
{
    [default] interface IMXAttributes;
    interface IVBSAXAttributes;
    interface ISAXAttributes;
}

[
    helpstring("SAXAttributes"),
    progid("Msxml2.SAXAttributes"),
    vi_progid("Msxml2.SAXAttributes"),
    threading(both),
    uuid(4dd441ad-526d-4a77-9f1b-9841ed802fb0)
]
coclass SAXAttributes
{
    [default] interface IMXAttributes;
    interface IVBSAXAttributes;
    interface ISAXAttributes;
}

/*
 * Error Codes
 */
cpp_quote("#define E_XML_NOTWF              0xC00CE223")
cpp_quote("#define E_XML_NODTD              0xC00CE224")
cpp_quote("#define E_XML_INVALID            0xC00CE225")
cpp_quote("#define E_XML_BUFFERTOOSMALL     0xC00CE226")

} /* Library MSXML */
