/*** Autogenerated by WIDL 10.8 from include/mpeg2structs.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __mpeg2structs_h__
#define __mpeg2structs_h__

/* Forward declarations */

/* Headers for imported files */

#include <wtypes.h>

#ifdef __cplusplus
extern "C" {
#endif

#pragma pack(push)
#pragma pack(1)
typedef struct __WIDL_mpeg2structs_generated_name_0000000C {
    WORD Bits;
} PID_BITS_MIDL;
typedef struct __WIDL_mpeg2structs_generated_name_0000000D {
    WORD Bits;
} MPEG_HEADER_BITS_MIDL;
typedef struct __WIDL_mpeg2structs_generated_name_0000000E {
    BYTE Bits;
} MPEG_HEADER_VERSION_BITS_MIDL;
#pragma pack(pop)
typedef WORD PID;
typedef BYTE TID;
typedef WORD TEID;
typedef UINT ClientKey;
typedef enum __WIDL_mpeg2structs_generated_name_0000000F {
    MPEG_SECTION_IS_NEXT = 0,
    MPEG_SECTION_IS_CURRENT = 1
} MPEG_CURRENT_NEXT_BIT;
typedef struct __WIDL_mpeg2structs_generated_name_00000010 {
    WORD wTidExt;
    WORD wCount;
} TID_EXTENSION;
typedef struct __WIDL_mpeg2structs_generated_name_00000010 *PTID_EXTENSION;
typedef struct __WIDL_mpeg2structs_generated_name_00000011 {
    TID TableId;
    union {
        MPEG_HEADER_BITS_MIDL S;
        WORD W;
    } Header;
    BYTE SectionData[1];
} SECTION;
typedef struct __WIDL_mpeg2structs_generated_name_00000011 *PSECTION;
typedef struct __WIDL_mpeg2structs_generated_name_00000012 {
    TID TableId;
    union {
        MPEG_HEADER_BITS_MIDL S;
        WORD W;
    } Header;
    TEID TableIdExtension;
    union {
        MPEG_HEADER_VERSION_BITS_MIDL S;
        BYTE B;
    } Version;
    BYTE SectionNumber;
    BYTE LastSectionNumber;
    BYTE RemainingData[1];
} LONG_SECTION;
typedef struct __WIDL_mpeg2structs_generated_name_00000012 *PLONG_SECTION;
typedef struct __WIDL_mpeg2structs_generated_name_00000013 {
    TID TableId;
    union {
        MPEG_HEADER_BITS_MIDL S;
        WORD W;
    } Header;
    TEID TableIdExtension;
    union {
        MPEG_HEADER_VERSION_BITS_MIDL S;
        BYTE B;
    } Version;
    BYTE SectionNumber;
    BYTE LastSectionNumber;
    BYTE ProtocolDiscriminator;
    BYTE DsmccType;
    WORD MessageId;
    DWORD TransactionId;
    BYTE Reserved;
    BYTE AdaptationLength;
    WORD MessageLength;
    BYTE RemainingData[1];
} DSMCC_SECTION;
typedef struct __WIDL_mpeg2structs_generated_name_00000013 *PDSMCC_SECTION;
typedef struct __WIDL_mpeg2structs_generated_name_00000014 {
    DWORD dwLength;
    PSECTION pSection;
} MPEG_RQST_PACKET;
typedef struct __WIDL_mpeg2structs_generated_name_00000014 *PMPEG_RQST_PACKET;
typedef struct __WIDL_mpeg2structs_generated_name_00000015 {
    WORD wPacketCount;
    PMPEG_RQST_PACKET PacketList[1];
} MPEG_PACKET_LIST;
typedef struct __WIDL_mpeg2structs_generated_name_00000015 *PMPEG_PACKET_LIST;
typedef struct __WIDL_mpeg2structs_generated_name_00000016 {
    WINBOOL fSpecifyProtocol;
    BYTE Protocol;
    WINBOOL fSpecifyType;
    BYTE Type;
    WINBOOL fSpecifyMessageId;
    WORD MessageId;
    WINBOOL fSpecifyTransactionId;
    WINBOOL fUseTrxIdMessageIdMask;
    DWORD TransactionId;
    WINBOOL fSpecifyModuleVersion;
    BYTE ModuleVersion;
    WINBOOL fSpecifyBlockNumber;
    WORD BlockNumber;
    WINBOOL fGetModuleCall;
    WORD NumberOfBlocksInModule;
} DSMCC_FILTER_OPTIONS;
typedef struct __WIDL_mpeg2structs_generated_name_00000017 {
    WINBOOL fSpecifyEtmId;
    DWORD EtmId;
} ATSC_FILTER_OPTIONS;
typedef struct __WIDL_mpeg2structs_generated_name_00000018 {
    WINBOOL fSpecifySegment;
    BYTE bSegment;
} DVB_EIT_FILTER_OPTIONS;
typedef struct __WIDL_mpeg2structs_generated_name_00000019 {
    BYTE bVersionNumber;
    WORD wFilterSize;
    WINBOOL fUseRawFilteringBits;
    BYTE Filter[16];
    BYTE Mask[16];
    WINBOOL fSpecifyTableIdExtension;
    WORD TableIdExtension;
    WINBOOL fSpecifyVersion;
    BYTE Version;
    WINBOOL fSpecifySectionNumber;
    BYTE SectionNumber;
    WINBOOL fSpecifyCurrentNext;
    WINBOOL fNext;
    WINBOOL fSpecifyDsmccOptions;
    DSMCC_FILTER_OPTIONS Dsmcc;
    WINBOOL fSpecifyAtscOptions;
    ATSC_FILTER_OPTIONS Atsc;
} MPEG2_FILTER;
typedef struct __WIDL_mpeg2structs_generated_name_00000019 *PMPEG2_FILTER;
typedef struct __WIDL_mpeg2structs_generated_name_0000001A {
    __C89_NAMELESS union {
        __C89_NAMELESS struct {
            BYTE bVersionNumber;
            WORD wFilterSize;
            WINBOOL fUseRawFilteringBits;
            BYTE Filter[16];
            BYTE Mask[16];
            WINBOOL fSpecifyTableIdExtension;
            WORD TableIdExtension;
            WINBOOL fSpecifyVersion;
            BYTE Version;
            WINBOOL fSpecifySectionNumber;
            BYTE SectionNumber;
            WINBOOL fSpecifyCurrentNext;
            WINBOOL fNext;
            WINBOOL fSpecifyDsmccOptions;
            DSMCC_FILTER_OPTIONS Dsmcc;
            WINBOOL fSpecifyAtscOptions;
            ATSC_FILTER_OPTIONS Atsc;
        } __C89_NAMELESSSTRUCTNAME;
        BYTE bVersion1Bytes[124];
    } __C89_NAMELESSUNIONNAME;
    WINBOOL fSpecifyDvbEitOptions;
    DVB_EIT_FILTER_OPTIONS DvbEit;
} MPEG2_FILTER2;
typedef struct __WIDL_mpeg2structs_generated_name_0000001A *PMPEG2_FILTER2;
#define MPEG2_FILTER_VERSION_1_SIZE 124
#define MPEG2_FILTER_VERSION_2_SIZE 133
typedef struct __WIDL_mpeg2structs_generated_name_0000001B {
    HRESULT hr;
    DWORD dwDataBufferSize;
    DWORD dwSizeOfDataRead;
    BYTE *pDataBuffer;
} MPEG_STREAM_BUFFER;
typedef struct __WIDL_mpeg2structs_generated_name_0000001B *PMPEG_STREAM_BUFFER;
typedef struct __WIDL_mpeg2structs_generated_name_0000001C {
    BYTE Hours;
    BYTE Minutes;
    BYTE Seconds;
} MPEG_TIME;
typedef MPEG_TIME MPEG_DURATION;
typedef struct __WIDL_mpeg2structs_generated_name_0000001D {
    BYTE Date;
    BYTE Month;
    WORD Year;
} MPEG_DATE;
typedef struct __WIDL_mpeg2structs_generated_name_0000001E {
    MPEG_DATE D;
    MPEG_TIME T;
} MPEG_DATE_AND_TIME;
typedef enum __WIDL_mpeg2structs_generated_name_0000001F {
    MPEG_CONTEXT_BCS_DEMUX = 0,
    MPEG_CONTEXT_WINSOCK = 1
} MPEG_CONTEXT_TYPE;
typedef struct __WIDL_mpeg2structs_generated_name_00000020 {
    DWORD AVMGraphId;
} MPEG_BCS_DEMUX;
typedef struct __WIDL_mpeg2structs_generated_name_00000021 {
    DWORD AVMGraphId;
} MPEG_WINSOCK;
typedef struct __WIDL_mpeg2structs_generated_name_00000022 {
    MPEG_CONTEXT_TYPE Type;
    union {
        MPEG_BCS_DEMUX Demux;
        MPEG_WINSOCK Winsock;
    } U;
} MPEG_CONTEXT;
typedef struct __WIDL_mpeg2structs_generated_name_00000022 *PMPEG_CONTEXT;
typedef enum __WIDL_mpeg2structs_generated_name_00000023 {
    MPEG_RQST_UNKNOWN = 0,
    MPEG_RQST_GET_SECTION = 1,
    MPEG_RQST_GET_SECTION_ASYNC = 2,
    MPEG_RQST_GET_TABLE = 3,
    MPEG_RQST_GET_TABLE_ASYNC = 4,
    MPEG_RQST_GET_SECTIONS_STREAM = 5,
    MPEG_RQST_GET_PES_STREAM = 6,
    MPEG_RQST_GET_TS_STREAM = 7,
    MPEG_RQST_START_MPE_STREAM = 8
} MPEG_REQUEST_TYPE;
typedef struct __WIDL_mpeg2structs_generated_name_00000024 {
    MPEG_REQUEST_TYPE Type;
    MPEG_CONTEXT Context;
    PID Pid;
    TID TableId;
    MPEG2_FILTER Filter;
    DWORD Flags;
} MPEG_SERVICE_REQUEST;
typedef struct __WIDL_mpeg2structs_generated_name_00000024 *PMPEG_SERVICE_REQUEST;
typedef struct __WIDL_mpeg2structs_generated_name_00000025 {
    DWORD IPAddress;
    WORD Port;
} MPEG_SERVICE_RESPONSE;
typedef struct __WIDL_mpeg2structs_generated_name_00000025 *PMPEG_SERVICE_RESPONSE;
typedef struct _DSMCC_ELEMENT {
    PID pid;
    BYTE bComponentTag;
    DWORD dwCarouselId;
    DWORD dwTransactionId;
    struct _DSMCC_ELEMENT *pNext;
} DSMCC_ELEMENT;
typedef struct _DSMCC_ELEMENT *PDSMCC_ELEMENT;
typedef struct _MPE_ELEMENT {
    PID pid;
    BYTE bComponentTag;
    struct _MPE_ELEMENT *pNext;
} MPE_ELEMENT;
typedef struct _MPE_ELEMENT *PMPE_ELEMENT;
typedef struct _MPEG_STREAM_FILTER {
    WORD wPidValue;
    DWORD dwFilterSize;
    WINBOOL fCrcEnabled;
    BYTE rgchFilter[16];
    BYTE rgchMask[16];
} MPEG_STREAM_FILTER;
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __mpeg2structs_h__ */
