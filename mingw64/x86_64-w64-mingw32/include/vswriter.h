/*** Autogenerated by WIDL 10.8 from include/vswriter.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __vswriter_h__
#define __vswriter_h__

/* Forward declarations */

#ifndef __IVssWMFiledesc_FWD_DEFINED__
#define __IVssWMFiledesc_FWD_DEFINED__
typedef interface IVssWMFiledesc IVssWMFiledesc;
#ifdef __cplusplus
interface IVssWMFiledesc;
#endif /* __cplusplus */
#endif

#ifndef __IVssWMDependency_FWD_DEFINED__
#define __IVssWMDependency_FWD_DEFINED__
typedef interface IVssWMDependency IVssWMDependency;
#ifdef __cplusplus
interface IVssWMDependency;
#endif /* __cplusplus */
#endif

#ifndef __IVssComponent_FWD_DEFINED__
#define __IVssComponent_FWD_DEFINED__
typedef interface IVssComponent IVssComponent;
#ifdef __cplusplus
interface IVssComponent;
#endif /* __cplusplus */
#endif

#ifndef __IVssWriterComponents_FWD_DEFINED__
#define __IVssWriterComponents_FWD_DEFINED__
typedef interface IVssWriterComponents IVssWriterComponents;
#ifdef __cplusplus
interface IVssWriterComponents;
#endif /* __cplusplus */
#endif

#ifndef __IVssComponentEx_FWD_DEFINED__
#define __IVssComponentEx_FWD_DEFINED__
typedef interface IVssComponentEx IVssComponentEx;
#ifdef __cplusplus
interface IVssComponentEx;
#endif /* __cplusplus */
#endif

#ifndef __IVssComponentEx2_FWD_DEFINED__
#define __IVssComponentEx2_FWD_DEFINED__
typedef interface IVssComponentEx2 IVssComponentEx2;
#ifdef __cplusplus
interface IVssComponentEx2;
#endif /* __cplusplus */
#endif

#ifndef __IVssCreateWriterMetadata_FWD_DEFINED__
#define __IVssCreateWriterMetadata_FWD_DEFINED__
typedef interface IVssCreateWriterMetadata IVssCreateWriterMetadata;
#ifdef __cplusplus
interface IVssCreateWriterMetadata;
#endif /* __cplusplus */
#endif

#ifndef __IVssCreateWriterMetadataEx_FWD_DEFINED__
#define __IVssCreateWriterMetadataEx_FWD_DEFINED__
typedef interface IVssCreateWriterMetadataEx IVssCreateWriterMetadataEx;
#ifdef __cplusplus
interface IVssCreateWriterMetadataEx;
#endif /* __cplusplus */
#endif

#ifndef __IVssWriterImpl_FWD_DEFINED__
#define __IVssWriterImpl_FWD_DEFINED__
typedef interface IVssWriterImpl IVssWriterImpl;
#ifdef __cplusplus
interface IVssWriterImpl;
#endif /* __cplusplus */
#endif

#ifndef __IVssCreateExpressWriterMetadata_FWD_DEFINED__
#define __IVssCreateExpressWriterMetadata_FWD_DEFINED__
typedef interface IVssCreateExpressWriterMetadata IVssCreateExpressWriterMetadata;
#ifdef __cplusplus
interface IVssCreateExpressWriterMetadata;
#endif /* __cplusplus */
#endif

#ifndef __IVssExpressWriter_FWD_DEFINED__
#define __IVssExpressWriter_FWD_DEFINED__
typedef interface IVssExpressWriter IVssExpressWriter;
#ifdef __cplusplus
interface IVssExpressWriter;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <vss.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#include "vsserror.h" 
typedef enum VSS_USAGE_TYPE {
    VSS_UT_UNDEFINED = 0,
    VSS_UT_BOOTABLESYSTEMSTATE = 1,
    VSS_UT_SYSTEMSERVICE = 2,
    VSS_UT_USERDATA = 3,
    VSS_UT_OTHER = 4
} VSS_USAGE_TYPE;
typedef enum VSS_SOURCE_TYPE {
    VSS_ST_UNDEFINED = 0,
    VSS_ST_TRANSACTEDDB = 1,
    VSS_ST_NONTRANSACTEDDB = 2,
    VSS_ST_OTHER = 3
} VSS_SOURCE_TYPE;
typedef enum VSS_RESTOREMETHOD_ENUM {
    VSS_RME_UNDEFINED = 0,
    VSS_RME_RESTORE_IF_NOT_THERE = 1,
    VSS_RME_RESTORE_IF_CAN_REPLACE = 2,
    VSS_RME_STOP_RESTORE_START = 3,
    VSS_RME_RESTORE_TO_ALTERNATE_LOCATION = 4,
    VSS_RME_RESTORE_AT_REBOOT = 5,
    VSS_RME_RESTORE_AT_REBOOT_IF_CANNOT_REPLACE = 6,
    VSS_RME_CUSTOM = 7,
    VSS_RME_RESTORE_STOP_START = 8
} VSS_RESTOREMETHOD_ENUM;
typedef enum VSS_WRITERRESTORE_ENUM {
    VSS_WRE_UNDEFINED = 0,
    VSS_WRE_NEVER = 1,
    VSS_WRE_IF_REPLACE_FAILS = 2,
    VSS_WRE_ALWAYS = 3
} VSS_WRITERRESTORE_ENUM;
typedef enum VSS_COMPONENT_TYPE {
    VSS_CT_UNDEFINED = 0,
    VSS_CT_DATABASE = 1,
    VSS_CT_FILEGROUP = 2
} VSS_COMPONENT_TYPE;
typedef enum VSS_ALTERNATE_WRITER_STATE {
    VSS_AWS_UNDEFINED = 0,
    VSS_AWS_NO_ALTERNATE_WRITER = 1,
    VSS_AWS_ALTERNATE_WRITER_EXISTS = 2,
    VSS_AWS_THIS_IS_ALTERNATE_WRITER = 3
} VSS_ALTERNATE_WRITER_STATE;
typedef enum VSS_SUBSCRIBE_MASK {
    VSS_SM_POST_SNAPSHOT_FLAG = 0x1,
    VSS_SM_BACKUP_EVENTS_FLAG = 0x2,
    VSS_SM_RESTORE_EVENTS_FLAG = 0x4,
    VSS_SM_IO_THROTTLING_FLAG = 0x8,
    VSS_SM_ALL_FLAGS = 0xffffffff
} VSS_SUBSCRIBE_MASK;
typedef enum VSS_RESTORE_TARGET {
    VSS_RT_UNDEFINED = 0,
    VSS_RT_ORIGINAL = 1,
    VSS_RT_ALTERNATE = 2,
    VSS_RT_DIRECTED = 3,
    VSS_RT_ORIGINAL_LOCATION = 4
} VSS_RESTORE_TARGET;
typedef enum VSS_FILE_RESTORE_STATUS {
    VSS_RS_UNDEFINED = 0,
    VSS_RS_NONE = 1,
    VSS_RS_ALL = 2,
    VSS_RS_FAILED = 3
} VSS_FILE_RESTORE_STATUS;
typedef enum VSS_COMPONENT_FLAGS {
    VSS_CF_BACKUP_RECOVERY = 0x1,
    VSS_CF_APP_ROLLBACK_RECOVERY = 0x2,
    VSS_CF_NOT_SYSTEM_STATE = 0x4
} VSS_COMPONENT_FLAGS;
#ifndef __IVssExamineWriterMetadata_FWD_DEFINED__
#define __IVssExamineWriterMetadata_FWD_DEFINED__
typedef interface IVssExamineWriterMetadata IVssExamineWriterMetadata;
#ifdef __cplusplus
interface IVssExamineWriterMetadata;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IVssWMFiledesc interface
 */
#ifndef __IVssWMFiledesc_INTERFACE_DEFINED__
#define __IVssWMFiledesc_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssWMFiledesc, 0x00000000, 0x0000, 0x0000, 0x00,0x00, 0x00,0x00,0x00,0x00,0x00,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000000-0000-0000-0000-000000000000")
IVssWMFiledesc : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetPath(
        BSTR *pbstrPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFilespec(
        BSTR *pbstrFilespec) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRecursive(
        boolean *pbRecursive) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAlternateLocation(
        BSTR *pbstrAlternateLocation) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBackupTypeMask(
        DWORD *pdwTypeMask) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssWMFiledesc, 0x00000000, 0x0000, 0x0000, 0x00,0x00, 0x00,0x00,0x00,0x00,0x00,0x00)
#endif
#else
typedef struct IVssWMFiledescVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssWMFiledesc *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssWMFiledesc *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssWMFiledesc *This);

    /*** IVssWMFiledesc methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPath)(
        IVssWMFiledesc *This,
        BSTR *pbstrPath);

    HRESULT (STDMETHODCALLTYPE *GetFilespec)(
        IVssWMFiledesc *This,
        BSTR *pbstrFilespec);

    HRESULT (STDMETHODCALLTYPE *GetRecursive)(
        IVssWMFiledesc *This,
        boolean *pbRecursive);

    HRESULT (STDMETHODCALLTYPE *GetAlternateLocation)(
        IVssWMFiledesc *This,
        BSTR *pbstrAlternateLocation);

    HRESULT (STDMETHODCALLTYPE *GetBackupTypeMask)(
        IVssWMFiledesc *This,
        DWORD *pdwTypeMask);

    END_INTERFACE
} IVssWMFiledescVtbl;

interface IVssWMFiledesc {
    CONST_VTBL IVssWMFiledescVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssWMFiledesc_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssWMFiledesc_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssWMFiledesc_Release(This) (This)->lpVtbl->Release(This)
/*** IVssWMFiledesc methods ***/
#define IVssWMFiledesc_GetPath(This,pbstrPath) (This)->lpVtbl->GetPath(This,pbstrPath)
#define IVssWMFiledesc_GetFilespec(This,pbstrFilespec) (This)->lpVtbl->GetFilespec(This,pbstrFilespec)
#define IVssWMFiledesc_GetRecursive(This,pbRecursive) (This)->lpVtbl->GetRecursive(This,pbRecursive)
#define IVssWMFiledesc_GetAlternateLocation(This,pbstrAlternateLocation) (This)->lpVtbl->GetAlternateLocation(This,pbstrAlternateLocation)
#define IVssWMFiledesc_GetBackupTypeMask(This,pdwTypeMask) (This)->lpVtbl->GetBackupTypeMask(This,pdwTypeMask)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssWMFiledesc_QueryInterface(IVssWMFiledesc* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssWMFiledesc_AddRef(IVssWMFiledesc* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssWMFiledesc_Release(IVssWMFiledesc* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssWMFiledesc methods ***/
static inline HRESULT IVssWMFiledesc_GetPath(IVssWMFiledesc* This,BSTR *pbstrPath) {
    return This->lpVtbl->GetPath(This,pbstrPath);
}
static inline HRESULT IVssWMFiledesc_GetFilespec(IVssWMFiledesc* This,BSTR *pbstrFilespec) {
    return This->lpVtbl->GetFilespec(This,pbstrFilespec);
}
static inline HRESULT IVssWMFiledesc_GetRecursive(IVssWMFiledesc* This,boolean *pbRecursive) {
    return This->lpVtbl->GetRecursive(This,pbRecursive);
}
static inline HRESULT IVssWMFiledesc_GetAlternateLocation(IVssWMFiledesc* This,BSTR *pbstrAlternateLocation) {
    return This->lpVtbl->GetAlternateLocation(This,pbstrAlternateLocation);
}
static inline HRESULT IVssWMFiledesc_GetBackupTypeMask(IVssWMFiledesc* This,DWORD *pdwTypeMask) {
    return This->lpVtbl->GetBackupTypeMask(This,pdwTypeMask);
}
#endif
#endif

#endif


#endif  /* __IVssWMFiledesc_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssWMDependency interface
 */
#ifndef __IVssWMDependency_INTERFACE_DEFINED__
#define __IVssWMDependency_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssWMDependency, 0x00000000, 0x0000, 0x0000, 0x00,0x00, 0x00,0x00,0x00,0x00,0x00,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000000-0000-0000-0000-000000000000")
IVssWMDependency : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetWriterId(
        VSS_ID *pWriterId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLogicalPath(
        BSTR *pbstrLogicalPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetComponentName(
        BSTR *pbstrComponentName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssWMDependency, 0x00000000, 0x0000, 0x0000, 0x00,0x00, 0x00,0x00,0x00,0x00,0x00,0x00)
#endif
#else
typedef struct IVssWMDependencyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssWMDependency *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssWMDependency *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssWMDependency *This);

    /*** IVssWMDependency methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWriterId)(
        IVssWMDependency *This,
        VSS_ID *pWriterId);

    HRESULT (STDMETHODCALLTYPE *GetLogicalPath)(
        IVssWMDependency *This,
        BSTR *pbstrLogicalPath);

    HRESULT (STDMETHODCALLTYPE *GetComponentName)(
        IVssWMDependency *This,
        BSTR *pbstrComponentName);

    END_INTERFACE
} IVssWMDependencyVtbl;

interface IVssWMDependency {
    CONST_VTBL IVssWMDependencyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssWMDependency_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssWMDependency_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssWMDependency_Release(This) (This)->lpVtbl->Release(This)
/*** IVssWMDependency methods ***/
#define IVssWMDependency_GetWriterId(This,pWriterId) (This)->lpVtbl->GetWriterId(This,pWriterId)
#define IVssWMDependency_GetLogicalPath(This,pbstrLogicalPath) (This)->lpVtbl->GetLogicalPath(This,pbstrLogicalPath)
#define IVssWMDependency_GetComponentName(This,pbstrComponentName) (This)->lpVtbl->GetComponentName(This,pbstrComponentName)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssWMDependency_QueryInterface(IVssWMDependency* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssWMDependency_AddRef(IVssWMDependency* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssWMDependency_Release(IVssWMDependency* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssWMDependency methods ***/
static inline HRESULT IVssWMDependency_GetWriterId(IVssWMDependency* This,VSS_ID *pWriterId) {
    return This->lpVtbl->GetWriterId(This,pWriterId);
}
static inline HRESULT IVssWMDependency_GetLogicalPath(IVssWMDependency* This,BSTR *pbstrLogicalPath) {
    return This->lpVtbl->GetLogicalPath(This,pbstrLogicalPath);
}
static inline HRESULT IVssWMDependency_GetComponentName(IVssWMDependency* This,BSTR *pbstrComponentName) {
    return This->lpVtbl->GetComponentName(This,pbstrComponentName);
}
#endif
#endif

#endif


#endif  /* __IVssWMDependency_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssComponent interface
 */
#ifndef __IVssComponent_INTERFACE_DEFINED__
#define __IVssComponent_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssComponent, 0xd2c72c96, 0xc121, 0x4518, 0xb6,0x27, 0xe5,0xa9,0x3d,0x01,0x0e,0xad);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d2c72c96-c121-4518-b627-e5a93d010ead")
IVssComponent : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetLogicalPath(
        BSTR *pbstrPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetComponentType(
        VSS_COMPONENT_TYPE *pct) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetComponentName(
        BSTR *pbstrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBackupSucceeded(
        boolean *pbSucceeded) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAlternateLocationMappingCount(
        UINT *pcMappings) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAlternateLocationMapping(
        UINT iMapping,
        IVssWMFiledesc **ppFiledesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBackupMetadata(
        LPCWSTR wszData) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBackupMetadata(
        BSTR *pbstrData) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddPartialFile(
        LPCWSTR wszPath,
        LPCWSTR wszFilename,
        LPCWSTR wszRanges,
        LPCWSTR wszMetadata) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPartialFileCount(
        UINT *pcPartialFiles) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPartialFile(
        UINT iPartialFile,
        BSTR *pbstrPath,
        BSTR *pbstrFilename,
        BSTR *pbstrRange,
        BSTR *pbstrMetadata) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsSelectedForRestore(
        boolean *pbSelectedForRestore) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAdditionalRestores(
        boolean *pbAdditionalRestores) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNewTargetCount(
        UINT *pcNewTarget) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNewTarget(
        UINT iNewTarget,
        IVssWMFiledesc **ppFiledesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddDirectedTarget(
        LPCWSTR wszSourcePath,
        LPCWSTR wszSourceFilename,
        LPCWSTR wszSourceRangeList,
        LPCWSTR wszDestinationPath,
        LPCWSTR wszDestinationFilename,
        LPCWSTR wszDestinationRangeList) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDirectedTargetCount(
        UINT *pcDirectedTarget) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDirectedTarget(
        UINT iDirectedTarget,
        BSTR *pbstrSourcePath,
        BSTR *pbstrSourceFileName,
        BSTR *pbstrSourceRangeList,
        BSTR *pbstrDestinationPath,
        BSTR *pbstrDestinationFilename,
        BSTR *pbstrDestinationRangeList) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRestoreMetadata(
        LPCWSTR wszRestoreMetadata) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRestoreMetadata(
        BSTR *pbstrRestoreMetadata) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRestoreTarget(
        VSS_RESTORE_TARGET target) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRestoreTarget(
        VSS_RESTORE_TARGET *pTarget) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPreRestoreFailureMsg(
        LPCWSTR wszPreRestoreFailureMsg) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPreRestoreFailureMsg(
        BSTR *pbstrPreRestoreFailureMsg) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPostRestoreFailureMsg(
        LPCWSTR wszPostRestoreFailureMsg) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPostRestoreFailureMsg(
        BSTR *pbstrPostRestoreFailureMsg) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBackupStamp(
        LPCWSTR wszBackupStamp) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBackupStamp(
        BSTR *pbstrBackupStamp) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPreviousBackupStamp(
        BSTR *pbstrBackupStamp) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBackupOptions(
        BSTR *pbstrBackupOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRestoreOptions(
        BSTR *pbstrRestoreOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRestoreSubcomponentCount(
        UINT *pcRestoreSubcomponent) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRestoreSubcomponent(
        UINT iComponent,
        BSTR *pbstrLogicalPath,
        BSTR *pbstrComponentName,
        boolean *pbRepair) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFileRestoreStatus(
        VSS_FILE_RESTORE_STATUS *pStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddDifferencedFilesByLastModifyTime(
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        WINBOOL bRecursive,
        FILETIME ftLastModifyTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddDifferencedFilesByLastModifyLSN(
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        WINBOOL bRecursive,
        BSTR bstrLsnString) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDifferencedFilesCount(
        UINT *pcDifferencedFiles) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDifferencedFile(
        UINT iDifferencedFile,
        BSTR *pbstrPath,
        BSTR *pbstrFilespec,
        WINBOOL *pbRecursive,
        BSTR *pbstrLsnString,
        FILETIME *pftLastModifyTime) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssComponent, 0xd2c72c96, 0xc121, 0x4518, 0xb6,0x27, 0xe5,0xa9,0x3d,0x01,0x0e,0xad)
#endif
#else
typedef struct IVssComponentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssComponent *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssComponent *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssComponent *This);

    /*** IVssComponent methods ***/
    HRESULT (STDMETHODCALLTYPE *GetLogicalPath)(
        IVssComponent *This,
        BSTR *pbstrPath);

    HRESULT (STDMETHODCALLTYPE *GetComponentType)(
        IVssComponent *This,
        VSS_COMPONENT_TYPE *pct);

    HRESULT (STDMETHODCALLTYPE *GetComponentName)(
        IVssComponent *This,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *GetBackupSucceeded)(
        IVssComponent *This,
        boolean *pbSucceeded);

    HRESULT (STDMETHODCALLTYPE *GetAlternateLocationMappingCount)(
        IVssComponent *This,
        UINT *pcMappings);

    HRESULT (STDMETHODCALLTYPE *GetAlternateLocationMapping)(
        IVssComponent *This,
        UINT iMapping,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *SetBackupMetadata)(
        IVssComponent *This,
        LPCWSTR wszData);

    HRESULT (STDMETHODCALLTYPE *GetBackupMetadata)(
        IVssComponent *This,
        BSTR *pbstrData);

    HRESULT (STDMETHODCALLTYPE *AddPartialFile)(
        IVssComponent *This,
        LPCWSTR wszPath,
        LPCWSTR wszFilename,
        LPCWSTR wszRanges,
        LPCWSTR wszMetadata);

    HRESULT (STDMETHODCALLTYPE *GetPartialFileCount)(
        IVssComponent *This,
        UINT *pcPartialFiles);

    HRESULT (STDMETHODCALLTYPE *GetPartialFile)(
        IVssComponent *This,
        UINT iPartialFile,
        BSTR *pbstrPath,
        BSTR *pbstrFilename,
        BSTR *pbstrRange,
        BSTR *pbstrMetadata);

    HRESULT (STDMETHODCALLTYPE *IsSelectedForRestore)(
        IVssComponent *This,
        boolean *pbSelectedForRestore);

    HRESULT (STDMETHODCALLTYPE *GetAdditionalRestores)(
        IVssComponent *This,
        boolean *pbAdditionalRestores);

    HRESULT (STDMETHODCALLTYPE *GetNewTargetCount)(
        IVssComponent *This,
        UINT *pcNewTarget);

    HRESULT (STDMETHODCALLTYPE *GetNewTarget)(
        IVssComponent *This,
        UINT iNewTarget,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *AddDirectedTarget)(
        IVssComponent *This,
        LPCWSTR wszSourcePath,
        LPCWSTR wszSourceFilename,
        LPCWSTR wszSourceRangeList,
        LPCWSTR wszDestinationPath,
        LPCWSTR wszDestinationFilename,
        LPCWSTR wszDestinationRangeList);

    HRESULT (STDMETHODCALLTYPE *GetDirectedTargetCount)(
        IVssComponent *This,
        UINT *pcDirectedTarget);

    HRESULT (STDMETHODCALLTYPE *GetDirectedTarget)(
        IVssComponent *This,
        UINT iDirectedTarget,
        BSTR *pbstrSourcePath,
        BSTR *pbstrSourceFileName,
        BSTR *pbstrSourceRangeList,
        BSTR *pbstrDestinationPath,
        BSTR *pbstrDestinationFilename,
        BSTR *pbstrDestinationRangeList);

    HRESULT (STDMETHODCALLTYPE *SetRestoreMetadata)(
        IVssComponent *This,
        LPCWSTR wszRestoreMetadata);

    HRESULT (STDMETHODCALLTYPE *GetRestoreMetadata)(
        IVssComponent *This,
        BSTR *pbstrRestoreMetadata);

    HRESULT (STDMETHODCALLTYPE *SetRestoreTarget)(
        IVssComponent *This,
        VSS_RESTORE_TARGET target);

    HRESULT (STDMETHODCALLTYPE *GetRestoreTarget)(
        IVssComponent *This,
        VSS_RESTORE_TARGET *pTarget);

    HRESULT (STDMETHODCALLTYPE *SetPreRestoreFailureMsg)(
        IVssComponent *This,
        LPCWSTR wszPreRestoreFailureMsg);

    HRESULT (STDMETHODCALLTYPE *GetPreRestoreFailureMsg)(
        IVssComponent *This,
        BSTR *pbstrPreRestoreFailureMsg);

    HRESULT (STDMETHODCALLTYPE *SetPostRestoreFailureMsg)(
        IVssComponent *This,
        LPCWSTR wszPostRestoreFailureMsg);

    HRESULT (STDMETHODCALLTYPE *GetPostRestoreFailureMsg)(
        IVssComponent *This,
        BSTR *pbstrPostRestoreFailureMsg);

    HRESULT (STDMETHODCALLTYPE *SetBackupStamp)(
        IVssComponent *This,
        LPCWSTR wszBackupStamp);

    HRESULT (STDMETHODCALLTYPE *GetBackupStamp)(
        IVssComponent *This,
        BSTR *pbstrBackupStamp);

    HRESULT (STDMETHODCALLTYPE *GetPreviousBackupStamp)(
        IVssComponent *This,
        BSTR *pbstrBackupStamp);

    HRESULT (STDMETHODCALLTYPE *GetBackupOptions)(
        IVssComponent *This,
        BSTR *pbstrBackupOptions);

    HRESULT (STDMETHODCALLTYPE *GetRestoreOptions)(
        IVssComponent *This,
        BSTR *pbstrRestoreOptions);

    HRESULT (STDMETHODCALLTYPE *GetRestoreSubcomponentCount)(
        IVssComponent *This,
        UINT *pcRestoreSubcomponent);

    HRESULT (STDMETHODCALLTYPE *GetRestoreSubcomponent)(
        IVssComponent *This,
        UINT iComponent,
        BSTR *pbstrLogicalPath,
        BSTR *pbstrComponentName,
        boolean *pbRepair);

    HRESULT (STDMETHODCALLTYPE *GetFileRestoreStatus)(
        IVssComponent *This,
        VSS_FILE_RESTORE_STATUS *pStatus);

    HRESULT (STDMETHODCALLTYPE *AddDifferencedFilesByLastModifyTime)(
        IVssComponent *This,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        WINBOOL bRecursive,
        FILETIME ftLastModifyTime);

    HRESULT (STDMETHODCALLTYPE *AddDifferencedFilesByLastModifyLSN)(
        IVssComponent *This,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        WINBOOL bRecursive,
        BSTR bstrLsnString);

    HRESULT (STDMETHODCALLTYPE *GetDifferencedFilesCount)(
        IVssComponent *This,
        UINT *pcDifferencedFiles);

    HRESULT (STDMETHODCALLTYPE *GetDifferencedFile)(
        IVssComponent *This,
        UINT iDifferencedFile,
        BSTR *pbstrPath,
        BSTR *pbstrFilespec,
        WINBOOL *pbRecursive,
        BSTR *pbstrLsnString,
        FILETIME *pftLastModifyTime);

    END_INTERFACE
} IVssComponentVtbl;

interface IVssComponent {
    CONST_VTBL IVssComponentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssComponent_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssComponent_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssComponent_Release(This) (This)->lpVtbl->Release(This)
/*** IVssComponent methods ***/
#define IVssComponent_GetLogicalPath(This,pbstrPath) (This)->lpVtbl->GetLogicalPath(This,pbstrPath)
#define IVssComponent_GetComponentType(This,pct) (This)->lpVtbl->GetComponentType(This,pct)
#define IVssComponent_GetComponentName(This,pbstrName) (This)->lpVtbl->GetComponentName(This,pbstrName)
#define IVssComponent_GetBackupSucceeded(This,pbSucceeded) (This)->lpVtbl->GetBackupSucceeded(This,pbSucceeded)
#define IVssComponent_GetAlternateLocationMappingCount(This,pcMappings) (This)->lpVtbl->GetAlternateLocationMappingCount(This,pcMappings)
#define IVssComponent_GetAlternateLocationMapping(This,iMapping,ppFiledesc) (This)->lpVtbl->GetAlternateLocationMapping(This,iMapping,ppFiledesc)
#define IVssComponent_SetBackupMetadata(This,wszData) (This)->lpVtbl->SetBackupMetadata(This,wszData)
#define IVssComponent_GetBackupMetadata(This,pbstrData) (This)->lpVtbl->GetBackupMetadata(This,pbstrData)
#define IVssComponent_AddPartialFile(This,wszPath,wszFilename,wszRanges,wszMetadata) (This)->lpVtbl->AddPartialFile(This,wszPath,wszFilename,wszRanges,wszMetadata)
#define IVssComponent_GetPartialFileCount(This,pcPartialFiles) (This)->lpVtbl->GetPartialFileCount(This,pcPartialFiles)
#define IVssComponent_GetPartialFile(This,iPartialFile,pbstrPath,pbstrFilename,pbstrRange,pbstrMetadata) (This)->lpVtbl->GetPartialFile(This,iPartialFile,pbstrPath,pbstrFilename,pbstrRange,pbstrMetadata)
#define IVssComponent_IsSelectedForRestore(This,pbSelectedForRestore) (This)->lpVtbl->IsSelectedForRestore(This,pbSelectedForRestore)
#define IVssComponent_GetAdditionalRestores(This,pbAdditionalRestores) (This)->lpVtbl->GetAdditionalRestores(This,pbAdditionalRestores)
#define IVssComponent_GetNewTargetCount(This,pcNewTarget) (This)->lpVtbl->GetNewTargetCount(This,pcNewTarget)
#define IVssComponent_GetNewTarget(This,iNewTarget,ppFiledesc) (This)->lpVtbl->GetNewTarget(This,iNewTarget,ppFiledesc)
#define IVssComponent_AddDirectedTarget(This,wszSourcePath,wszSourceFilename,wszSourceRangeList,wszDestinationPath,wszDestinationFilename,wszDestinationRangeList) (This)->lpVtbl->AddDirectedTarget(This,wszSourcePath,wszSourceFilename,wszSourceRangeList,wszDestinationPath,wszDestinationFilename,wszDestinationRangeList)
#define IVssComponent_GetDirectedTargetCount(This,pcDirectedTarget) (This)->lpVtbl->GetDirectedTargetCount(This,pcDirectedTarget)
#define IVssComponent_GetDirectedTarget(This,iDirectedTarget,pbstrSourcePath,pbstrSourceFileName,pbstrSourceRangeList,pbstrDestinationPath,pbstrDestinationFilename,pbstrDestinationRangeList) (This)->lpVtbl->GetDirectedTarget(This,iDirectedTarget,pbstrSourcePath,pbstrSourceFileName,pbstrSourceRangeList,pbstrDestinationPath,pbstrDestinationFilename,pbstrDestinationRangeList)
#define IVssComponent_SetRestoreMetadata(This,wszRestoreMetadata) (This)->lpVtbl->SetRestoreMetadata(This,wszRestoreMetadata)
#define IVssComponent_GetRestoreMetadata(This,pbstrRestoreMetadata) (This)->lpVtbl->GetRestoreMetadata(This,pbstrRestoreMetadata)
#define IVssComponent_SetRestoreTarget(This,target) (This)->lpVtbl->SetRestoreTarget(This,target)
#define IVssComponent_GetRestoreTarget(This,pTarget) (This)->lpVtbl->GetRestoreTarget(This,pTarget)
#define IVssComponent_SetPreRestoreFailureMsg(This,wszPreRestoreFailureMsg) (This)->lpVtbl->SetPreRestoreFailureMsg(This,wszPreRestoreFailureMsg)
#define IVssComponent_GetPreRestoreFailureMsg(This,pbstrPreRestoreFailureMsg) (This)->lpVtbl->GetPreRestoreFailureMsg(This,pbstrPreRestoreFailureMsg)
#define IVssComponent_SetPostRestoreFailureMsg(This,wszPostRestoreFailureMsg) (This)->lpVtbl->SetPostRestoreFailureMsg(This,wszPostRestoreFailureMsg)
#define IVssComponent_GetPostRestoreFailureMsg(This,pbstrPostRestoreFailureMsg) (This)->lpVtbl->GetPostRestoreFailureMsg(This,pbstrPostRestoreFailureMsg)
#define IVssComponent_SetBackupStamp(This,wszBackupStamp) (This)->lpVtbl->SetBackupStamp(This,wszBackupStamp)
#define IVssComponent_GetBackupStamp(This,pbstrBackupStamp) (This)->lpVtbl->GetBackupStamp(This,pbstrBackupStamp)
#define IVssComponent_GetPreviousBackupStamp(This,pbstrBackupStamp) (This)->lpVtbl->GetPreviousBackupStamp(This,pbstrBackupStamp)
#define IVssComponent_GetBackupOptions(This,pbstrBackupOptions) (This)->lpVtbl->GetBackupOptions(This,pbstrBackupOptions)
#define IVssComponent_GetRestoreOptions(This,pbstrRestoreOptions) (This)->lpVtbl->GetRestoreOptions(This,pbstrRestoreOptions)
#define IVssComponent_GetRestoreSubcomponentCount(This,pcRestoreSubcomponent) (This)->lpVtbl->GetRestoreSubcomponentCount(This,pcRestoreSubcomponent)
#define IVssComponent_GetRestoreSubcomponent(This,iComponent,pbstrLogicalPath,pbstrComponentName,pbRepair) (This)->lpVtbl->GetRestoreSubcomponent(This,iComponent,pbstrLogicalPath,pbstrComponentName,pbRepair)
#define IVssComponent_GetFileRestoreStatus(This,pStatus) (This)->lpVtbl->GetFileRestoreStatus(This,pStatus)
#define IVssComponent_AddDifferencedFilesByLastModifyTime(This,wszPath,wszFilespec,bRecursive,ftLastModifyTime) (This)->lpVtbl->AddDifferencedFilesByLastModifyTime(This,wszPath,wszFilespec,bRecursive,ftLastModifyTime)
#define IVssComponent_AddDifferencedFilesByLastModifyLSN(This,wszPath,wszFilespec,bRecursive,bstrLsnString) (This)->lpVtbl->AddDifferencedFilesByLastModifyLSN(This,wszPath,wszFilespec,bRecursive,bstrLsnString)
#define IVssComponent_GetDifferencedFilesCount(This,pcDifferencedFiles) (This)->lpVtbl->GetDifferencedFilesCount(This,pcDifferencedFiles)
#define IVssComponent_GetDifferencedFile(This,iDifferencedFile,pbstrPath,pbstrFilespec,pbRecursive,pbstrLsnString,pftLastModifyTime) (This)->lpVtbl->GetDifferencedFile(This,iDifferencedFile,pbstrPath,pbstrFilespec,pbRecursive,pbstrLsnString,pftLastModifyTime)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssComponent_QueryInterface(IVssComponent* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssComponent_AddRef(IVssComponent* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssComponent_Release(IVssComponent* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssComponent methods ***/
static inline HRESULT IVssComponent_GetLogicalPath(IVssComponent* This,BSTR *pbstrPath) {
    return This->lpVtbl->GetLogicalPath(This,pbstrPath);
}
static inline HRESULT IVssComponent_GetComponentType(IVssComponent* This,VSS_COMPONENT_TYPE *pct) {
    return This->lpVtbl->GetComponentType(This,pct);
}
static inline HRESULT IVssComponent_GetComponentName(IVssComponent* This,BSTR *pbstrName) {
    return This->lpVtbl->GetComponentName(This,pbstrName);
}
static inline HRESULT IVssComponent_GetBackupSucceeded(IVssComponent* This,boolean *pbSucceeded) {
    return This->lpVtbl->GetBackupSucceeded(This,pbSucceeded);
}
static inline HRESULT IVssComponent_GetAlternateLocationMappingCount(IVssComponent* This,UINT *pcMappings) {
    return This->lpVtbl->GetAlternateLocationMappingCount(This,pcMappings);
}
static inline HRESULT IVssComponent_GetAlternateLocationMapping(IVssComponent* This,UINT iMapping,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetAlternateLocationMapping(This,iMapping,ppFiledesc);
}
static inline HRESULT IVssComponent_SetBackupMetadata(IVssComponent* This,LPCWSTR wszData) {
    return This->lpVtbl->SetBackupMetadata(This,wszData);
}
static inline HRESULT IVssComponent_GetBackupMetadata(IVssComponent* This,BSTR *pbstrData) {
    return This->lpVtbl->GetBackupMetadata(This,pbstrData);
}
static inline HRESULT IVssComponent_AddPartialFile(IVssComponent* This,LPCWSTR wszPath,LPCWSTR wszFilename,LPCWSTR wszRanges,LPCWSTR wszMetadata) {
    return This->lpVtbl->AddPartialFile(This,wszPath,wszFilename,wszRanges,wszMetadata);
}
static inline HRESULT IVssComponent_GetPartialFileCount(IVssComponent* This,UINT *pcPartialFiles) {
    return This->lpVtbl->GetPartialFileCount(This,pcPartialFiles);
}
static inline HRESULT IVssComponent_GetPartialFile(IVssComponent* This,UINT iPartialFile,BSTR *pbstrPath,BSTR *pbstrFilename,BSTR *pbstrRange,BSTR *pbstrMetadata) {
    return This->lpVtbl->GetPartialFile(This,iPartialFile,pbstrPath,pbstrFilename,pbstrRange,pbstrMetadata);
}
static inline HRESULT IVssComponent_IsSelectedForRestore(IVssComponent* This,boolean *pbSelectedForRestore) {
    return This->lpVtbl->IsSelectedForRestore(This,pbSelectedForRestore);
}
static inline HRESULT IVssComponent_GetAdditionalRestores(IVssComponent* This,boolean *pbAdditionalRestores) {
    return This->lpVtbl->GetAdditionalRestores(This,pbAdditionalRestores);
}
static inline HRESULT IVssComponent_GetNewTargetCount(IVssComponent* This,UINT *pcNewTarget) {
    return This->lpVtbl->GetNewTargetCount(This,pcNewTarget);
}
static inline HRESULT IVssComponent_GetNewTarget(IVssComponent* This,UINT iNewTarget,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetNewTarget(This,iNewTarget,ppFiledesc);
}
static inline HRESULT IVssComponent_AddDirectedTarget(IVssComponent* This,LPCWSTR wszSourcePath,LPCWSTR wszSourceFilename,LPCWSTR wszSourceRangeList,LPCWSTR wszDestinationPath,LPCWSTR wszDestinationFilename,LPCWSTR wszDestinationRangeList) {
    return This->lpVtbl->AddDirectedTarget(This,wszSourcePath,wszSourceFilename,wszSourceRangeList,wszDestinationPath,wszDestinationFilename,wszDestinationRangeList);
}
static inline HRESULT IVssComponent_GetDirectedTargetCount(IVssComponent* This,UINT *pcDirectedTarget) {
    return This->lpVtbl->GetDirectedTargetCount(This,pcDirectedTarget);
}
static inline HRESULT IVssComponent_GetDirectedTarget(IVssComponent* This,UINT iDirectedTarget,BSTR *pbstrSourcePath,BSTR *pbstrSourceFileName,BSTR *pbstrSourceRangeList,BSTR *pbstrDestinationPath,BSTR *pbstrDestinationFilename,BSTR *pbstrDestinationRangeList) {
    return This->lpVtbl->GetDirectedTarget(This,iDirectedTarget,pbstrSourcePath,pbstrSourceFileName,pbstrSourceRangeList,pbstrDestinationPath,pbstrDestinationFilename,pbstrDestinationRangeList);
}
static inline HRESULT IVssComponent_SetRestoreMetadata(IVssComponent* This,LPCWSTR wszRestoreMetadata) {
    return This->lpVtbl->SetRestoreMetadata(This,wszRestoreMetadata);
}
static inline HRESULT IVssComponent_GetRestoreMetadata(IVssComponent* This,BSTR *pbstrRestoreMetadata) {
    return This->lpVtbl->GetRestoreMetadata(This,pbstrRestoreMetadata);
}
static inline HRESULT IVssComponent_SetRestoreTarget(IVssComponent* This,VSS_RESTORE_TARGET target) {
    return This->lpVtbl->SetRestoreTarget(This,target);
}
static inline HRESULT IVssComponent_GetRestoreTarget(IVssComponent* This,VSS_RESTORE_TARGET *pTarget) {
    return This->lpVtbl->GetRestoreTarget(This,pTarget);
}
static inline HRESULT IVssComponent_SetPreRestoreFailureMsg(IVssComponent* This,LPCWSTR wszPreRestoreFailureMsg) {
    return This->lpVtbl->SetPreRestoreFailureMsg(This,wszPreRestoreFailureMsg);
}
static inline HRESULT IVssComponent_GetPreRestoreFailureMsg(IVssComponent* This,BSTR *pbstrPreRestoreFailureMsg) {
    return This->lpVtbl->GetPreRestoreFailureMsg(This,pbstrPreRestoreFailureMsg);
}
static inline HRESULT IVssComponent_SetPostRestoreFailureMsg(IVssComponent* This,LPCWSTR wszPostRestoreFailureMsg) {
    return This->lpVtbl->SetPostRestoreFailureMsg(This,wszPostRestoreFailureMsg);
}
static inline HRESULT IVssComponent_GetPostRestoreFailureMsg(IVssComponent* This,BSTR *pbstrPostRestoreFailureMsg) {
    return This->lpVtbl->GetPostRestoreFailureMsg(This,pbstrPostRestoreFailureMsg);
}
static inline HRESULT IVssComponent_SetBackupStamp(IVssComponent* This,LPCWSTR wszBackupStamp) {
    return This->lpVtbl->SetBackupStamp(This,wszBackupStamp);
}
static inline HRESULT IVssComponent_GetBackupStamp(IVssComponent* This,BSTR *pbstrBackupStamp) {
    return This->lpVtbl->GetBackupStamp(This,pbstrBackupStamp);
}
static inline HRESULT IVssComponent_GetPreviousBackupStamp(IVssComponent* This,BSTR *pbstrBackupStamp) {
    return This->lpVtbl->GetPreviousBackupStamp(This,pbstrBackupStamp);
}
static inline HRESULT IVssComponent_GetBackupOptions(IVssComponent* This,BSTR *pbstrBackupOptions) {
    return This->lpVtbl->GetBackupOptions(This,pbstrBackupOptions);
}
static inline HRESULT IVssComponent_GetRestoreOptions(IVssComponent* This,BSTR *pbstrRestoreOptions) {
    return This->lpVtbl->GetRestoreOptions(This,pbstrRestoreOptions);
}
static inline HRESULT IVssComponent_GetRestoreSubcomponentCount(IVssComponent* This,UINT *pcRestoreSubcomponent) {
    return This->lpVtbl->GetRestoreSubcomponentCount(This,pcRestoreSubcomponent);
}
static inline HRESULT IVssComponent_GetRestoreSubcomponent(IVssComponent* This,UINT iComponent,BSTR *pbstrLogicalPath,BSTR *pbstrComponentName,boolean *pbRepair) {
    return This->lpVtbl->GetRestoreSubcomponent(This,iComponent,pbstrLogicalPath,pbstrComponentName,pbRepair);
}
static inline HRESULT IVssComponent_GetFileRestoreStatus(IVssComponent* This,VSS_FILE_RESTORE_STATUS *pStatus) {
    return This->lpVtbl->GetFileRestoreStatus(This,pStatus);
}
static inline HRESULT IVssComponent_AddDifferencedFilesByLastModifyTime(IVssComponent* This,LPCWSTR wszPath,LPCWSTR wszFilespec,WINBOOL bRecursive,FILETIME ftLastModifyTime) {
    return This->lpVtbl->AddDifferencedFilesByLastModifyTime(This,wszPath,wszFilespec,bRecursive,ftLastModifyTime);
}
static inline HRESULT IVssComponent_AddDifferencedFilesByLastModifyLSN(IVssComponent* This,LPCWSTR wszPath,LPCWSTR wszFilespec,WINBOOL bRecursive,BSTR bstrLsnString) {
    return This->lpVtbl->AddDifferencedFilesByLastModifyLSN(This,wszPath,wszFilespec,bRecursive,bstrLsnString);
}
static inline HRESULT IVssComponent_GetDifferencedFilesCount(IVssComponent* This,UINT *pcDifferencedFiles) {
    return This->lpVtbl->GetDifferencedFilesCount(This,pcDifferencedFiles);
}
static inline HRESULT IVssComponent_GetDifferencedFile(IVssComponent* This,UINT iDifferencedFile,BSTR *pbstrPath,BSTR *pbstrFilespec,WINBOOL *pbRecursive,BSTR *pbstrLsnString,FILETIME *pftLastModifyTime) {
    return This->lpVtbl->GetDifferencedFile(This,iDifferencedFile,pbstrPath,pbstrFilespec,pbRecursive,pbstrLsnString,pftLastModifyTime);
}
#endif
#endif

#endif


#endif  /* __IVssComponent_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssWriterComponents interface
 */
#ifndef __IVssWriterComponents_INTERFACE_DEFINED__
#define __IVssWriterComponents_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssWriterComponents, 0x00000000, 0x0000, 0x0000, 0x00,0x00, 0x00,0x00,0x00,0x00,0x00,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000000-0000-0000-0000-000000000000")
IVssWriterComponents
{

    BEGIN_INTERFACE

    virtual HRESULT STDMETHODCALLTYPE GetComponentCount(
        UINT *pcComponents) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetWriterInfo(
        VSS_ID *pidInstance,
        VSS_ID *pidWriter) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetComponent(
        UINT iComponent,
        IVssComponent **ppComponent) = 0;

    END_INTERFACE

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssWriterComponents, 0x00000000, 0x0000, 0x0000, 0x00,0x00, 0x00,0x00,0x00,0x00,0x00,0x00)
#endif
#else
typedef struct IVssWriterComponentsVtbl {
    BEGIN_INTERFACE

    /*** IVssWriterComponents methods ***/
    HRESULT (STDMETHODCALLTYPE *GetComponentCount)(
        IVssWriterComponents *This,
        UINT *pcComponents);

    HRESULT (STDMETHODCALLTYPE *GetWriterInfo)(
        IVssWriterComponents *This,
        VSS_ID *pidInstance,
        VSS_ID *pidWriter);

    HRESULT (STDMETHODCALLTYPE *GetComponent)(
        IVssWriterComponents *This,
        UINT iComponent,
        IVssComponent **ppComponent);

    END_INTERFACE
} IVssWriterComponentsVtbl;

interface IVssWriterComponents {
    CONST_VTBL IVssWriterComponentsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IVssWriterComponents methods ***/
#define IVssWriterComponents_GetComponentCount(This,pcComponents) (This)->lpVtbl->GetComponentCount(This,pcComponents)
#define IVssWriterComponents_GetWriterInfo(This,pidInstance,pidWriter) (This)->lpVtbl->GetWriterInfo(This,pidInstance,pidWriter)
#define IVssWriterComponents_GetComponent(This,iComponent,ppComponent) (This)->lpVtbl->GetComponent(This,iComponent,ppComponent)
#else
/*** IVssWriterComponents methods ***/
static inline HRESULT IVssWriterComponents_GetComponentCount(IVssWriterComponents* This,UINT *pcComponents) {
    return This->lpVtbl->GetComponentCount(This,pcComponents);
}
static inline HRESULT IVssWriterComponents_GetWriterInfo(IVssWriterComponents* This,VSS_ID *pidInstance,VSS_ID *pidWriter) {
    return This->lpVtbl->GetWriterInfo(This,pidInstance,pidWriter);
}
static inline HRESULT IVssWriterComponents_GetComponent(IVssWriterComponents* This,UINT iComponent,IVssComponent **ppComponent) {
    return This->lpVtbl->GetComponent(This,iComponent,ppComponent);
}
#endif
#endif

#endif


#endif  /* __IVssWriterComponents_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssComponentEx interface
 */
#ifndef __IVssComponentEx_INTERFACE_DEFINED__
#define __IVssComponentEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssComponentEx, 0x156c8b5e, 0xf131, 0x4bd7, 0x9c,0x97, 0xd1,0x92,0x3b,0xe7,0xe1,0xfa);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("156c8b5e-f131-4bd7-9c97-d1923be7e1fa")
IVssComponentEx : public IVssComponent
{
    virtual HRESULT STDMETHODCALLTYPE SetPrepareForBackupFailureMsg(
        LPCWSTR wszFailureMsg) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPostSnapshotFailureMsg(
        LPCWSTR wszFailureMsg) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPrepareForBackupFailureMsg(
        BSTR *pbstrFailureMsg) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPostSnapshotFailureMsg(
        BSTR *pbstrFailureMsg) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAuthoritativeRestore(
        boolean *pbAuth) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRollForward(
        VSS_ROLLFORWARD_TYPE *pRollType,
        BSTR *pbstrPoint) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRestoreName(
        BSTR *pbstrName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssComponentEx, 0x156c8b5e, 0xf131, 0x4bd7, 0x9c,0x97, 0xd1,0x92,0x3b,0xe7,0xe1,0xfa)
#endif
#else
typedef struct IVssComponentExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssComponentEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssComponentEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssComponentEx *This);

    /*** IVssComponent methods ***/
    HRESULT (STDMETHODCALLTYPE *GetLogicalPath)(
        IVssComponentEx *This,
        BSTR *pbstrPath);

    HRESULT (STDMETHODCALLTYPE *GetComponentType)(
        IVssComponentEx *This,
        VSS_COMPONENT_TYPE *pct);

    HRESULT (STDMETHODCALLTYPE *GetComponentName)(
        IVssComponentEx *This,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *GetBackupSucceeded)(
        IVssComponentEx *This,
        boolean *pbSucceeded);

    HRESULT (STDMETHODCALLTYPE *GetAlternateLocationMappingCount)(
        IVssComponentEx *This,
        UINT *pcMappings);

    HRESULT (STDMETHODCALLTYPE *GetAlternateLocationMapping)(
        IVssComponentEx *This,
        UINT iMapping,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *SetBackupMetadata)(
        IVssComponentEx *This,
        LPCWSTR wszData);

    HRESULT (STDMETHODCALLTYPE *GetBackupMetadata)(
        IVssComponentEx *This,
        BSTR *pbstrData);

    HRESULT (STDMETHODCALLTYPE *AddPartialFile)(
        IVssComponentEx *This,
        LPCWSTR wszPath,
        LPCWSTR wszFilename,
        LPCWSTR wszRanges,
        LPCWSTR wszMetadata);

    HRESULT (STDMETHODCALLTYPE *GetPartialFileCount)(
        IVssComponentEx *This,
        UINT *pcPartialFiles);

    HRESULT (STDMETHODCALLTYPE *GetPartialFile)(
        IVssComponentEx *This,
        UINT iPartialFile,
        BSTR *pbstrPath,
        BSTR *pbstrFilename,
        BSTR *pbstrRange,
        BSTR *pbstrMetadata);

    HRESULT (STDMETHODCALLTYPE *IsSelectedForRestore)(
        IVssComponentEx *This,
        boolean *pbSelectedForRestore);

    HRESULT (STDMETHODCALLTYPE *GetAdditionalRestores)(
        IVssComponentEx *This,
        boolean *pbAdditionalRestores);

    HRESULT (STDMETHODCALLTYPE *GetNewTargetCount)(
        IVssComponentEx *This,
        UINT *pcNewTarget);

    HRESULT (STDMETHODCALLTYPE *GetNewTarget)(
        IVssComponentEx *This,
        UINT iNewTarget,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *AddDirectedTarget)(
        IVssComponentEx *This,
        LPCWSTR wszSourcePath,
        LPCWSTR wszSourceFilename,
        LPCWSTR wszSourceRangeList,
        LPCWSTR wszDestinationPath,
        LPCWSTR wszDestinationFilename,
        LPCWSTR wszDestinationRangeList);

    HRESULT (STDMETHODCALLTYPE *GetDirectedTargetCount)(
        IVssComponentEx *This,
        UINT *pcDirectedTarget);

    HRESULT (STDMETHODCALLTYPE *GetDirectedTarget)(
        IVssComponentEx *This,
        UINT iDirectedTarget,
        BSTR *pbstrSourcePath,
        BSTR *pbstrSourceFileName,
        BSTR *pbstrSourceRangeList,
        BSTR *pbstrDestinationPath,
        BSTR *pbstrDestinationFilename,
        BSTR *pbstrDestinationRangeList);

    HRESULT (STDMETHODCALLTYPE *SetRestoreMetadata)(
        IVssComponentEx *This,
        LPCWSTR wszRestoreMetadata);

    HRESULT (STDMETHODCALLTYPE *GetRestoreMetadata)(
        IVssComponentEx *This,
        BSTR *pbstrRestoreMetadata);

    HRESULT (STDMETHODCALLTYPE *SetRestoreTarget)(
        IVssComponentEx *This,
        VSS_RESTORE_TARGET target);

    HRESULT (STDMETHODCALLTYPE *GetRestoreTarget)(
        IVssComponentEx *This,
        VSS_RESTORE_TARGET *pTarget);

    HRESULT (STDMETHODCALLTYPE *SetPreRestoreFailureMsg)(
        IVssComponentEx *This,
        LPCWSTR wszPreRestoreFailureMsg);

    HRESULT (STDMETHODCALLTYPE *GetPreRestoreFailureMsg)(
        IVssComponentEx *This,
        BSTR *pbstrPreRestoreFailureMsg);

    HRESULT (STDMETHODCALLTYPE *SetPostRestoreFailureMsg)(
        IVssComponentEx *This,
        LPCWSTR wszPostRestoreFailureMsg);

    HRESULT (STDMETHODCALLTYPE *GetPostRestoreFailureMsg)(
        IVssComponentEx *This,
        BSTR *pbstrPostRestoreFailureMsg);

    HRESULT (STDMETHODCALLTYPE *SetBackupStamp)(
        IVssComponentEx *This,
        LPCWSTR wszBackupStamp);

    HRESULT (STDMETHODCALLTYPE *GetBackupStamp)(
        IVssComponentEx *This,
        BSTR *pbstrBackupStamp);

    HRESULT (STDMETHODCALLTYPE *GetPreviousBackupStamp)(
        IVssComponentEx *This,
        BSTR *pbstrBackupStamp);

    HRESULT (STDMETHODCALLTYPE *GetBackupOptions)(
        IVssComponentEx *This,
        BSTR *pbstrBackupOptions);

    HRESULT (STDMETHODCALLTYPE *GetRestoreOptions)(
        IVssComponentEx *This,
        BSTR *pbstrRestoreOptions);

    HRESULT (STDMETHODCALLTYPE *GetRestoreSubcomponentCount)(
        IVssComponentEx *This,
        UINT *pcRestoreSubcomponent);

    HRESULT (STDMETHODCALLTYPE *GetRestoreSubcomponent)(
        IVssComponentEx *This,
        UINT iComponent,
        BSTR *pbstrLogicalPath,
        BSTR *pbstrComponentName,
        boolean *pbRepair);

    HRESULT (STDMETHODCALLTYPE *GetFileRestoreStatus)(
        IVssComponentEx *This,
        VSS_FILE_RESTORE_STATUS *pStatus);

    HRESULT (STDMETHODCALLTYPE *AddDifferencedFilesByLastModifyTime)(
        IVssComponentEx *This,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        WINBOOL bRecursive,
        FILETIME ftLastModifyTime);

    HRESULT (STDMETHODCALLTYPE *AddDifferencedFilesByLastModifyLSN)(
        IVssComponentEx *This,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        WINBOOL bRecursive,
        BSTR bstrLsnString);

    HRESULT (STDMETHODCALLTYPE *GetDifferencedFilesCount)(
        IVssComponentEx *This,
        UINT *pcDifferencedFiles);

    HRESULT (STDMETHODCALLTYPE *GetDifferencedFile)(
        IVssComponentEx *This,
        UINT iDifferencedFile,
        BSTR *pbstrPath,
        BSTR *pbstrFilespec,
        WINBOOL *pbRecursive,
        BSTR *pbstrLsnString,
        FILETIME *pftLastModifyTime);

    /*** IVssComponentEx methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrepareForBackupFailureMsg)(
        IVssComponentEx *This,
        LPCWSTR wszFailureMsg);

    HRESULT (STDMETHODCALLTYPE *SetPostSnapshotFailureMsg)(
        IVssComponentEx *This,
        LPCWSTR wszFailureMsg);

    HRESULT (STDMETHODCALLTYPE *GetPrepareForBackupFailureMsg)(
        IVssComponentEx *This,
        BSTR *pbstrFailureMsg);

    HRESULT (STDMETHODCALLTYPE *GetPostSnapshotFailureMsg)(
        IVssComponentEx *This,
        BSTR *pbstrFailureMsg);

    HRESULT (STDMETHODCALLTYPE *GetAuthoritativeRestore)(
        IVssComponentEx *This,
        boolean *pbAuth);

    HRESULT (STDMETHODCALLTYPE *GetRollForward)(
        IVssComponentEx *This,
        VSS_ROLLFORWARD_TYPE *pRollType,
        BSTR *pbstrPoint);

    HRESULT (STDMETHODCALLTYPE *GetRestoreName)(
        IVssComponentEx *This,
        BSTR *pbstrName);

    END_INTERFACE
} IVssComponentExVtbl;

interface IVssComponentEx {
    CONST_VTBL IVssComponentExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssComponentEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssComponentEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssComponentEx_Release(This) (This)->lpVtbl->Release(This)
/*** IVssComponent methods ***/
#define IVssComponentEx_GetLogicalPath(This,pbstrPath) (This)->lpVtbl->GetLogicalPath(This,pbstrPath)
#define IVssComponentEx_GetComponentType(This,pct) (This)->lpVtbl->GetComponentType(This,pct)
#define IVssComponentEx_GetComponentName(This,pbstrName) (This)->lpVtbl->GetComponentName(This,pbstrName)
#define IVssComponentEx_GetBackupSucceeded(This,pbSucceeded) (This)->lpVtbl->GetBackupSucceeded(This,pbSucceeded)
#define IVssComponentEx_GetAlternateLocationMappingCount(This,pcMappings) (This)->lpVtbl->GetAlternateLocationMappingCount(This,pcMappings)
#define IVssComponentEx_GetAlternateLocationMapping(This,iMapping,ppFiledesc) (This)->lpVtbl->GetAlternateLocationMapping(This,iMapping,ppFiledesc)
#define IVssComponentEx_SetBackupMetadata(This,wszData) (This)->lpVtbl->SetBackupMetadata(This,wszData)
#define IVssComponentEx_GetBackupMetadata(This,pbstrData) (This)->lpVtbl->GetBackupMetadata(This,pbstrData)
#define IVssComponentEx_AddPartialFile(This,wszPath,wszFilename,wszRanges,wszMetadata) (This)->lpVtbl->AddPartialFile(This,wszPath,wszFilename,wszRanges,wszMetadata)
#define IVssComponentEx_GetPartialFileCount(This,pcPartialFiles) (This)->lpVtbl->GetPartialFileCount(This,pcPartialFiles)
#define IVssComponentEx_GetPartialFile(This,iPartialFile,pbstrPath,pbstrFilename,pbstrRange,pbstrMetadata) (This)->lpVtbl->GetPartialFile(This,iPartialFile,pbstrPath,pbstrFilename,pbstrRange,pbstrMetadata)
#define IVssComponentEx_IsSelectedForRestore(This,pbSelectedForRestore) (This)->lpVtbl->IsSelectedForRestore(This,pbSelectedForRestore)
#define IVssComponentEx_GetAdditionalRestores(This,pbAdditionalRestores) (This)->lpVtbl->GetAdditionalRestores(This,pbAdditionalRestores)
#define IVssComponentEx_GetNewTargetCount(This,pcNewTarget) (This)->lpVtbl->GetNewTargetCount(This,pcNewTarget)
#define IVssComponentEx_GetNewTarget(This,iNewTarget,ppFiledesc) (This)->lpVtbl->GetNewTarget(This,iNewTarget,ppFiledesc)
#define IVssComponentEx_AddDirectedTarget(This,wszSourcePath,wszSourceFilename,wszSourceRangeList,wszDestinationPath,wszDestinationFilename,wszDestinationRangeList) (This)->lpVtbl->AddDirectedTarget(This,wszSourcePath,wszSourceFilename,wszSourceRangeList,wszDestinationPath,wszDestinationFilename,wszDestinationRangeList)
#define IVssComponentEx_GetDirectedTargetCount(This,pcDirectedTarget) (This)->lpVtbl->GetDirectedTargetCount(This,pcDirectedTarget)
#define IVssComponentEx_GetDirectedTarget(This,iDirectedTarget,pbstrSourcePath,pbstrSourceFileName,pbstrSourceRangeList,pbstrDestinationPath,pbstrDestinationFilename,pbstrDestinationRangeList) (This)->lpVtbl->GetDirectedTarget(This,iDirectedTarget,pbstrSourcePath,pbstrSourceFileName,pbstrSourceRangeList,pbstrDestinationPath,pbstrDestinationFilename,pbstrDestinationRangeList)
#define IVssComponentEx_SetRestoreMetadata(This,wszRestoreMetadata) (This)->lpVtbl->SetRestoreMetadata(This,wszRestoreMetadata)
#define IVssComponentEx_GetRestoreMetadata(This,pbstrRestoreMetadata) (This)->lpVtbl->GetRestoreMetadata(This,pbstrRestoreMetadata)
#define IVssComponentEx_SetRestoreTarget(This,target) (This)->lpVtbl->SetRestoreTarget(This,target)
#define IVssComponentEx_GetRestoreTarget(This,pTarget) (This)->lpVtbl->GetRestoreTarget(This,pTarget)
#define IVssComponentEx_SetPreRestoreFailureMsg(This,wszPreRestoreFailureMsg) (This)->lpVtbl->SetPreRestoreFailureMsg(This,wszPreRestoreFailureMsg)
#define IVssComponentEx_GetPreRestoreFailureMsg(This,pbstrPreRestoreFailureMsg) (This)->lpVtbl->GetPreRestoreFailureMsg(This,pbstrPreRestoreFailureMsg)
#define IVssComponentEx_SetPostRestoreFailureMsg(This,wszPostRestoreFailureMsg) (This)->lpVtbl->SetPostRestoreFailureMsg(This,wszPostRestoreFailureMsg)
#define IVssComponentEx_GetPostRestoreFailureMsg(This,pbstrPostRestoreFailureMsg) (This)->lpVtbl->GetPostRestoreFailureMsg(This,pbstrPostRestoreFailureMsg)
#define IVssComponentEx_SetBackupStamp(This,wszBackupStamp) (This)->lpVtbl->SetBackupStamp(This,wszBackupStamp)
#define IVssComponentEx_GetBackupStamp(This,pbstrBackupStamp) (This)->lpVtbl->GetBackupStamp(This,pbstrBackupStamp)
#define IVssComponentEx_GetPreviousBackupStamp(This,pbstrBackupStamp) (This)->lpVtbl->GetPreviousBackupStamp(This,pbstrBackupStamp)
#define IVssComponentEx_GetBackupOptions(This,pbstrBackupOptions) (This)->lpVtbl->GetBackupOptions(This,pbstrBackupOptions)
#define IVssComponentEx_GetRestoreOptions(This,pbstrRestoreOptions) (This)->lpVtbl->GetRestoreOptions(This,pbstrRestoreOptions)
#define IVssComponentEx_GetRestoreSubcomponentCount(This,pcRestoreSubcomponent) (This)->lpVtbl->GetRestoreSubcomponentCount(This,pcRestoreSubcomponent)
#define IVssComponentEx_GetRestoreSubcomponent(This,iComponent,pbstrLogicalPath,pbstrComponentName,pbRepair) (This)->lpVtbl->GetRestoreSubcomponent(This,iComponent,pbstrLogicalPath,pbstrComponentName,pbRepair)
#define IVssComponentEx_GetFileRestoreStatus(This,pStatus) (This)->lpVtbl->GetFileRestoreStatus(This,pStatus)
#define IVssComponentEx_AddDifferencedFilesByLastModifyTime(This,wszPath,wszFilespec,bRecursive,ftLastModifyTime) (This)->lpVtbl->AddDifferencedFilesByLastModifyTime(This,wszPath,wszFilespec,bRecursive,ftLastModifyTime)
#define IVssComponentEx_AddDifferencedFilesByLastModifyLSN(This,wszPath,wszFilespec,bRecursive,bstrLsnString) (This)->lpVtbl->AddDifferencedFilesByLastModifyLSN(This,wszPath,wszFilespec,bRecursive,bstrLsnString)
#define IVssComponentEx_GetDifferencedFilesCount(This,pcDifferencedFiles) (This)->lpVtbl->GetDifferencedFilesCount(This,pcDifferencedFiles)
#define IVssComponentEx_GetDifferencedFile(This,iDifferencedFile,pbstrPath,pbstrFilespec,pbRecursive,pbstrLsnString,pftLastModifyTime) (This)->lpVtbl->GetDifferencedFile(This,iDifferencedFile,pbstrPath,pbstrFilespec,pbRecursive,pbstrLsnString,pftLastModifyTime)
/*** IVssComponentEx methods ***/
#define IVssComponentEx_SetPrepareForBackupFailureMsg(This,wszFailureMsg) (This)->lpVtbl->SetPrepareForBackupFailureMsg(This,wszFailureMsg)
#define IVssComponentEx_SetPostSnapshotFailureMsg(This,wszFailureMsg) (This)->lpVtbl->SetPostSnapshotFailureMsg(This,wszFailureMsg)
#define IVssComponentEx_GetPrepareForBackupFailureMsg(This,pbstrFailureMsg) (This)->lpVtbl->GetPrepareForBackupFailureMsg(This,pbstrFailureMsg)
#define IVssComponentEx_GetPostSnapshotFailureMsg(This,pbstrFailureMsg) (This)->lpVtbl->GetPostSnapshotFailureMsg(This,pbstrFailureMsg)
#define IVssComponentEx_GetAuthoritativeRestore(This,pbAuth) (This)->lpVtbl->GetAuthoritativeRestore(This,pbAuth)
#define IVssComponentEx_GetRollForward(This,pRollType,pbstrPoint) (This)->lpVtbl->GetRollForward(This,pRollType,pbstrPoint)
#define IVssComponentEx_GetRestoreName(This,pbstrName) (This)->lpVtbl->GetRestoreName(This,pbstrName)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssComponentEx_QueryInterface(IVssComponentEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssComponentEx_AddRef(IVssComponentEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssComponentEx_Release(IVssComponentEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssComponent methods ***/
static inline HRESULT IVssComponentEx_GetLogicalPath(IVssComponentEx* This,BSTR *pbstrPath) {
    return This->lpVtbl->GetLogicalPath(This,pbstrPath);
}
static inline HRESULT IVssComponentEx_GetComponentType(IVssComponentEx* This,VSS_COMPONENT_TYPE *pct) {
    return This->lpVtbl->GetComponentType(This,pct);
}
static inline HRESULT IVssComponentEx_GetComponentName(IVssComponentEx* This,BSTR *pbstrName) {
    return This->lpVtbl->GetComponentName(This,pbstrName);
}
static inline HRESULT IVssComponentEx_GetBackupSucceeded(IVssComponentEx* This,boolean *pbSucceeded) {
    return This->lpVtbl->GetBackupSucceeded(This,pbSucceeded);
}
static inline HRESULT IVssComponentEx_GetAlternateLocationMappingCount(IVssComponentEx* This,UINT *pcMappings) {
    return This->lpVtbl->GetAlternateLocationMappingCount(This,pcMappings);
}
static inline HRESULT IVssComponentEx_GetAlternateLocationMapping(IVssComponentEx* This,UINT iMapping,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetAlternateLocationMapping(This,iMapping,ppFiledesc);
}
static inline HRESULT IVssComponentEx_SetBackupMetadata(IVssComponentEx* This,LPCWSTR wszData) {
    return This->lpVtbl->SetBackupMetadata(This,wszData);
}
static inline HRESULT IVssComponentEx_GetBackupMetadata(IVssComponentEx* This,BSTR *pbstrData) {
    return This->lpVtbl->GetBackupMetadata(This,pbstrData);
}
static inline HRESULT IVssComponentEx_AddPartialFile(IVssComponentEx* This,LPCWSTR wszPath,LPCWSTR wszFilename,LPCWSTR wszRanges,LPCWSTR wszMetadata) {
    return This->lpVtbl->AddPartialFile(This,wszPath,wszFilename,wszRanges,wszMetadata);
}
static inline HRESULT IVssComponentEx_GetPartialFileCount(IVssComponentEx* This,UINT *pcPartialFiles) {
    return This->lpVtbl->GetPartialFileCount(This,pcPartialFiles);
}
static inline HRESULT IVssComponentEx_GetPartialFile(IVssComponentEx* This,UINT iPartialFile,BSTR *pbstrPath,BSTR *pbstrFilename,BSTR *pbstrRange,BSTR *pbstrMetadata) {
    return This->lpVtbl->GetPartialFile(This,iPartialFile,pbstrPath,pbstrFilename,pbstrRange,pbstrMetadata);
}
static inline HRESULT IVssComponentEx_IsSelectedForRestore(IVssComponentEx* This,boolean *pbSelectedForRestore) {
    return This->lpVtbl->IsSelectedForRestore(This,pbSelectedForRestore);
}
static inline HRESULT IVssComponentEx_GetAdditionalRestores(IVssComponentEx* This,boolean *pbAdditionalRestores) {
    return This->lpVtbl->GetAdditionalRestores(This,pbAdditionalRestores);
}
static inline HRESULT IVssComponentEx_GetNewTargetCount(IVssComponentEx* This,UINT *pcNewTarget) {
    return This->lpVtbl->GetNewTargetCount(This,pcNewTarget);
}
static inline HRESULT IVssComponentEx_GetNewTarget(IVssComponentEx* This,UINT iNewTarget,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetNewTarget(This,iNewTarget,ppFiledesc);
}
static inline HRESULT IVssComponentEx_AddDirectedTarget(IVssComponentEx* This,LPCWSTR wszSourcePath,LPCWSTR wszSourceFilename,LPCWSTR wszSourceRangeList,LPCWSTR wszDestinationPath,LPCWSTR wszDestinationFilename,LPCWSTR wszDestinationRangeList) {
    return This->lpVtbl->AddDirectedTarget(This,wszSourcePath,wszSourceFilename,wszSourceRangeList,wszDestinationPath,wszDestinationFilename,wszDestinationRangeList);
}
static inline HRESULT IVssComponentEx_GetDirectedTargetCount(IVssComponentEx* This,UINT *pcDirectedTarget) {
    return This->lpVtbl->GetDirectedTargetCount(This,pcDirectedTarget);
}
static inline HRESULT IVssComponentEx_GetDirectedTarget(IVssComponentEx* This,UINT iDirectedTarget,BSTR *pbstrSourcePath,BSTR *pbstrSourceFileName,BSTR *pbstrSourceRangeList,BSTR *pbstrDestinationPath,BSTR *pbstrDestinationFilename,BSTR *pbstrDestinationRangeList) {
    return This->lpVtbl->GetDirectedTarget(This,iDirectedTarget,pbstrSourcePath,pbstrSourceFileName,pbstrSourceRangeList,pbstrDestinationPath,pbstrDestinationFilename,pbstrDestinationRangeList);
}
static inline HRESULT IVssComponentEx_SetRestoreMetadata(IVssComponentEx* This,LPCWSTR wszRestoreMetadata) {
    return This->lpVtbl->SetRestoreMetadata(This,wszRestoreMetadata);
}
static inline HRESULT IVssComponentEx_GetRestoreMetadata(IVssComponentEx* This,BSTR *pbstrRestoreMetadata) {
    return This->lpVtbl->GetRestoreMetadata(This,pbstrRestoreMetadata);
}
static inline HRESULT IVssComponentEx_SetRestoreTarget(IVssComponentEx* This,VSS_RESTORE_TARGET target) {
    return This->lpVtbl->SetRestoreTarget(This,target);
}
static inline HRESULT IVssComponentEx_GetRestoreTarget(IVssComponentEx* This,VSS_RESTORE_TARGET *pTarget) {
    return This->lpVtbl->GetRestoreTarget(This,pTarget);
}
static inline HRESULT IVssComponentEx_SetPreRestoreFailureMsg(IVssComponentEx* This,LPCWSTR wszPreRestoreFailureMsg) {
    return This->lpVtbl->SetPreRestoreFailureMsg(This,wszPreRestoreFailureMsg);
}
static inline HRESULT IVssComponentEx_GetPreRestoreFailureMsg(IVssComponentEx* This,BSTR *pbstrPreRestoreFailureMsg) {
    return This->lpVtbl->GetPreRestoreFailureMsg(This,pbstrPreRestoreFailureMsg);
}
static inline HRESULT IVssComponentEx_SetPostRestoreFailureMsg(IVssComponentEx* This,LPCWSTR wszPostRestoreFailureMsg) {
    return This->lpVtbl->SetPostRestoreFailureMsg(This,wszPostRestoreFailureMsg);
}
static inline HRESULT IVssComponentEx_GetPostRestoreFailureMsg(IVssComponentEx* This,BSTR *pbstrPostRestoreFailureMsg) {
    return This->lpVtbl->GetPostRestoreFailureMsg(This,pbstrPostRestoreFailureMsg);
}
static inline HRESULT IVssComponentEx_SetBackupStamp(IVssComponentEx* This,LPCWSTR wszBackupStamp) {
    return This->lpVtbl->SetBackupStamp(This,wszBackupStamp);
}
static inline HRESULT IVssComponentEx_GetBackupStamp(IVssComponentEx* This,BSTR *pbstrBackupStamp) {
    return This->lpVtbl->GetBackupStamp(This,pbstrBackupStamp);
}
static inline HRESULT IVssComponentEx_GetPreviousBackupStamp(IVssComponentEx* This,BSTR *pbstrBackupStamp) {
    return This->lpVtbl->GetPreviousBackupStamp(This,pbstrBackupStamp);
}
static inline HRESULT IVssComponentEx_GetBackupOptions(IVssComponentEx* This,BSTR *pbstrBackupOptions) {
    return This->lpVtbl->GetBackupOptions(This,pbstrBackupOptions);
}
static inline HRESULT IVssComponentEx_GetRestoreOptions(IVssComponentEx* This,BSTR *pbstrRestoreOptions) {
    return This->lpVtbl->GetRestoreOptions(This,pbstrRestoreOptions);
}
static inline HRESULT IVssComponentEx_GetRestoreSubcomponentCount(IVssComponentEx* This,UINT *pcRestoreSubcomponent) {
    return This->lpVtbl->GetRestoreSubcomponentCount(This,pcRestoreSubcomponent);
}
static inline HRESULT IVssComponentEx_GetRestoreSubcomponent(IVssComponentEx* This,UINT iComponent,BSTR *pbstrLogicalPath,BSTR *pbstrComponentName,boolean *pbRepair) {
    return This->lpVtbl->GetRestoreSubcomponent(This,iComponent,pbstrLogicalPath,pbstrComponentName,pbRepair);
}
static inline HRESULT IVssComponentEx_GetFileRestoreStatus(IVssComponentEx* This,VSS_FILE_RESTORE_STATUS *pStatus) {
    return This->lpVtbl->GetFileRestoreStatus(This,pStatus);
}
static inline HRESULT IVssComponentEx_AddDifferencedFilesByLastModifyTime(IVssComponentEx* This,LPCWSTR wszPath,LPCWSTR wszFilespec,WINBOOL bRecursive,FILETIME ftLastModifyTime) {
    return This->lpVtbl->AddDifferencedFilesByLastModifyTime(This,wszPath,wszFilespec,bRecursive,ftLastModifyTime);
}
static inline HRESULT IVssComponentEx_AddDifferencedFilesByLastModifyLSN(IVssComponentEx* This,LPCWSTR wszPath,LPCWSTR wszFilespec,WINBOOL bRecursive,BSTR bstrLsnString) {
    return This->lpVtbl->AddDifferencedFilesByLastModifyLSN(This,wszPath,wszFilespec,bRecursive,bstrLsnString);
}
static inline HRESULT IVssComponentEx_GetDifferencedFilesCount(IVssComponentEx* This,UINT *pcDifferencedFiles) {
    return This->lpVtbl->GetDifferencedFilesCount(This,pcDifferencedFiles);
}
static inline HRESULT IVssComponentEx_GetDifferencedFile(IVssComponentEx* This,UINT iDifferencedFile,BSTR *pbstrPath,BSTR *pbstrFilespec,WINBOOL *pbRecursive,BSTR *pbstrLsnString,FILETIME *pftLastModifyTime) {
    return This->lpVtbl->GetDifferencedFile(This,iDifferencedFile,pbstrPath,pbstrFilespec,pbRecursive,pbstrLsnString,pftLastModifyTime);
}
/*** IVssComponentEx methods ***/
static inline HRESULT IVssComponentEx_SetPrepareForBackupFailureMsg(IVssComponentEx* This,LPCWSTR wszFailureMsg) {
    return This->lpVtbl->SetPrepareForBackupFailureMsg(This,wszFailureMsg);
}
static inline HRESULT IVssComponentEx_SetPostSnapshotFailureMsg(IVssComponentEx* This,LPCWSTR wszFailureMsg) {
    return This->lpVtbl->SetPostSnapshotFailureMsg(This,wszFailureMsg);
}
static inline HRESULT IVssComponentEx_GetPrepareForBackupFailureMsg(IVssComponentEx* This,BSTR *pbstrFailureMsg) {
    return This->lpVtbl->GetPrepareForBackupFailureMsg(This,pbstrFailureMsg);
}
static inline HRESULT IVssComponentEx_GetPostSnapshotFailureMsg(IVssComponentEx* This,BSTR *pbstrFailureMsg) {
    return This->lpVtbl->GetPostSnapshotFailureMsg(This,pbstrFailureMsg);
}
static inline HRESULT IVssComponentEx_GetAuthoritativeRestore(IVssComponentEx* This,boolean *pbAuth) {
    return This->lpVtbl->GetAuthoritativeRestore(This,pbAuth);
}
static inline HRESULT IVssComponentEx_GetRollForward(IVssComponentEx* This,VSS_ROLLFORWARD_TYPE *pRollType,BSTR *pbstrPoint) {
    return This->lpVtbl->GetRollForward(This,pRollType,pbstrPoint);
}
static inline HRESULT IVssComponentEx_GetRestoreName(IVssComponentEx* This,BSTR *pbstrName) {
    return This->lpVtbl->GetRestoreName(This,pbstrName);
}
#endif
#endif

#endif


#endif  /* __IVssComponentEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssComponentEx2 interface
 */
#ifndef __IVssComponentEx2_INTERFACE_DEFINED__
#define __IVssComponentEx2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssComponentEx2, 0x3b5be0f2, 0x07a9, 0x4e4b, 0xbd,0xd3, 0xcf,0xdc,0x8e,0x2c,0x0d,0x2d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3b5be0f2-07a9-4e4b-bdd3-cfdc8e2c0d2d")
IVssComponentEx2 : public IVssComponentEx
{
    virtual HRESULT STDMETHODCALLTYPE SetFailure(
        HRESULT hr,
        HRESULT hrApplication,
        LPCWSTR wszApplicationMessage,
        DWORD dwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFailure(
        HRESULT *phr,
        HRESULT *phrApplication,
        BSTR *pbstrApplicationMessage,
        DWORD *pdwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssComponentEx2, 0x3b5be0f2, 0x07a9, 0x4e4b, 0xbd,0xd3, 0xcf,0xdc,0x8e,0x2c,0x0d,0x2d)
#endif
#else
typedef struct IVssComponentEx2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssComponentEx2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssComponentEx2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssComponentEx2 *This);

    /*** IVssComponent methods ***/
    HRESULT (STDMETHODCALLTYPE *GetLogicalPath)(
        IVssComponentEx2 *This,
        BSTR *pbstrPath);

    HRESULT (STDMETHODCALLTYPE *GetComponentType)(
        IVssComponentEx2 *This,
        VSS_COMPONENT_TYPE *pct);

    HRESULT (STDMETHODCALLTYPE *GetComponentName)(
        IVssComponentEx2 *This,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *GetBackupSucceeded)(
        IVssComponentEx2 *This,
        boolean *pbSucceeded);

    HRESULT (STDMETHODCALLTYPE *GetAlternateLocationMappingCount)(
        IVssComponentEx2 *This,
        UINT *pcMappings);

    HRESULT (STDMETHODCALLTYPE *GetAlternateLocationMapping)(
        IVssComponentEx2 *This,
        UINT iMapping,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *SetBackupMetadata)(
        IVssComponentEx2 *This,
        LPCWSTR wszData);

    HRESULT (STDMETHODCALLTYPE *GetBackupMetadata)(
        IVssComponentEx2 *This,
        BSTR *pbstrData);

    HRESULT (STDMETHODCALLTYPE *AddPartialFile)(
        IVssComponentEx2 *This,
        LPCWSTR wszPath,
        LPCWSTR wszFilename,
        LPCWSTR wszRanges,
        LPCWSTR wszMetadata);

    HRESULT (STDMETHODCALLTYPE *GetPartialFileCount)(
        IVssComponentEx2 *This,
        UINT *pcPartialFiles);

    HRESULT (STDMETHODCALLTYPE *GetPartialFile)(
        IVssComponentEx2 *This,
        UINT iPartialFile,
        BSTR *pbstrPath,
        BSTR *pbstrFilename,
        BSTR *pbstrRange,
        BSTR *pbstrMetadata);

    HRESULT (STDMETHODCALLTYPE *IsSelectedForRestore)(
        IVssComponentEx2 *This,
        boolean *pbSelectedForRestore);

    HRESULT (STDMETHODCALLTYPE *GetAdditionalRestores)(
        IVssComponentEx2 *This,
        boolean *pbAdditionalRestores);

    HRESULT (STDMETHODCALLTYPE *GetNewTargetCount)(
        IVssComponentEx2 *This,
        UINT *pcNewTarget);

    HRESULT (STDMETHODCALLTYPE *GetNewTarget)(
        IVssComponentEx2 *This,
        UINT iNewTarget,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *AddDirectedTarget)(
        IVssComponentEx2 *This,
        LPCWSTR wszSourcePath,
        LPCWSTR wszSourceFilename,
        LPCWSTR wszSourceRangeList,
        LPCWSTR wszDestinationPath,
        LPCWSTR wszDestinationFilename,
        LPCWSTR wszDestinationRangeList);

    HRESULT (STDMETHODCALLTYPE *GetDirectedTargetCount)(
        IVssComponentEx2 *This,
        UINT *pcDirectedTarget);

    HRESULT (STDMETHODCALLTYPE *GetDirectedTarget)(
        IVssComponentEx2 *This,
        UINT iDirectedTarget,
        BSTR *pbstrSourcePath,
        BSTR *pbstrSourceFileName,
        BSTR *pbstrSourceRangeList,
        BSTR *pbstrDestinationPath,
        BSTR *pbstrDestinationFilename,
        BSTR *pbstrDestinationRangeList);

    HRESULT (STDMETHODCALLTYPE *SetRestoreMetadata)(
        IVssComponentEx2 *This,
        LPCWSTR wszRestoreMetadata);

    HRESULT (STDMETHODCALLTYPE *GetRestoreMetadata)(
        IVssComponentEx2 *This,
        BSTR *pbstrRestoreMetadata);

    HRESULT (STDMETHODCALLTYPE *SetRestoreTarget)(
        IVssComponentEx2 *This,
        VSS_RESTORE_TARGET target);

    HRESULT (STDMETHODCALLTYPE *GetRestoreTarget)(
        IVssComponentEx2 *This,
        VSS_RESTORE_TARGET *pTarget);

    HRESULT (STDMETHODCALLTYPE *SetPreRestoreFailureMsg)(
        IVssComponentEx2 *This,
        LPCWSTR wszPreRestoreFailureMsg);

    HRESULT (STDMETHODCALLTYPE *GetPreRestoreFailureMsg)(
        IVssComponentEx2 *This,
        BSTR *pbstrPreRestoreFailureMsg);

    HRESULT (STDMETHODCALLTYPE *SetPostRestoreFailureMsg)(
        IVssComponentEx2 *This,
        LPCWSTR wszPostRestoreFailureMsg);

    HRESULT (STDMETHODCALLTYPE *GetPostRestoreFailureMsg)(
        IVssComponentEx2 *This,
        BSTR *pbstrPostRestoreFailureMsg);

    HRESULT (STDMETHODCALLTYPE *SetBackupStamp)(
        IVssComponentEx2 *This,
        LPCWSTR wszBackupStamp);

    HRESULT (STDMETHODCALLTYPE *GetBackupStamp)(
        IVssComponentEx2 *This,
        BSTR *pbstrBackupStamp);

    HRESULT (STDMETHODCALLTYPE *GetPreviousBackupStamp)(
        IVssComponentEx2 *This,
        BSTR *pbstrBackupStamp);

    HRESULT (STDMETHODCALLTYPE *GetBackupOptions)(
        IVssComponentEx2 *This,
        BSTR *pbstrBackupOptions);

    HRESULT (STDMETHODCALLTYPE *GetRestoreOptions)(
        IVssComponentEx2 *This,
        BSTR *pbstrRestoreOptions);

    HRESULT (STDMETHODCALLTYPE *GetRestoreSubcomponentCount)(
        IVssComponentEx2 *This,
        UINT *pcRestoreSubcomponent);

    HRESULT (STDMETHODCALLTYPE *GetRestoreSubcomponent)(
        IVssComponentEx2 *This,
        UINT iComponent,
        BSTR *pbstrLogicalPath,
        BSTR *pbstrComponentName,
        boolean *pbRepair);

    HRESULT (STDMETHODCALLTYPE *GetFileRestoreStatus)(
        IVssComponentEx2 *This,
        VSS_FILE_RESTORE_STATUS *pStatus);

    HRESULT (STDMETHODCALLTYPE *AddDifferencedFilesByLastModifyTime)(
        IVssComponentEx2 *This,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        WINBOOL bRecursive,
        FILETIME ftLastModifyTime);

    HRESULT (STDMETHODCALLTYPE *AddDifferencedFilesByLastModifyLSN)(
        IVssComponentEx2 *This,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        WINBOOL bRecursive,
        BSTR bstrLsnString);

    HRESULT (STDMETHODCALLTYPE *GetDifferencedFilesCount)(
        IVssComponentEx2 *This,
        UINT *pcDifferencedFiles);

    HRESULT (STDMETHODCALLTYPE *GetDifferencedFile)(
        IVssComponentEx2 *This,
        UINT iDifferencedFile,
        BSTR *pbstrPath,
        BSTR *pbstrFilespec,
        WINBOOL *pbRecursive,
        BSTR *pbstrLsnString,
        FILETIME *pftLastModifyTime);

    /*** IVssComponentEx methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrepareForBackupFailureMsg)(
        IVssComponentEx2 *This,
        LPCWSTR wszFailureMsg);

    HRESULT (STDMETHODCALLTYPE *SetPostSnapshotFailureMsg)(
        IVssComponentEx2 *This,
        LPCWSTR wszFailureMsg);

    HRESULT (STDMETHODCALLTYPE *GetPrepareForBackupFailureMsg)(
        IVssComponentEx2 *This,
        BSTR *pbstrFailureMsg);

    HRESULT (STDMETHODCALLTYPE *GetPostSnapshotFailureMsg)(
        IVssComponentEx2 *This,
        BSTR *pbstrFailureMsg);

    HRESULT (STDMETHODCALLTYPE *GetAuthoritativeRestore)(
        IVssComponentEx2 *This,
        boolean *pbAuth);

    HRESULT (STDMETHODCALLTYPE *GetRollForward)(
        IVssComponentEx2 *This,
        VSS_ROLLFORWARD_TYPE *pRollType,
        BSTR *pbstrPoint);

    HRESULT (STDMETHODCALLTYPE *GetRestoreName)(
        IVssComponentEx2 *This,
        BSTR *pbstrName);

    /*** IVssComponentEx2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetFailure)(
        IVssComponentEx2 *This,
        HRESULT hr,
        HRESULT hrApplication,
        LPCWSTR wszApplicationMessage,
        DWORD dwReserved);

    HRESULT (STDMETHODCALLTYPE *GetFailure)(
        IVssComponentEx2 *This,
        HRESULT *phr,
        HRESULT *phrApplication,
        BSTR *pbstrApplicationMessage,
        DWORD *pdwReserved);

    END_INTERFACE
} IVssComponentEx2Vtbl;

interface IVssComponentEx2 {
    CONST_VTBL IVssComponentEx2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssComponentEx2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssComponentEx2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssComponentEx2_Release(This) (This)->lpVtbl->Release(This)
/*** IVssComponent methods ***/
#define IVssComponentEx2_GetLogicalPath(This,pbstrPath) (This)->lpVtbl->GetLogicalPath(This,pbstrPath)
#define IVssComponentEx2_GetComponentType(This,pct) (This)->lpVtbl->GetComponentType(This,pct)
#define IVssComponentEx2_GetComponentName(This,pbstrName) (This)->lpVtbl->GetComponentName(This,pbstrName)
#define IVssComponentEx2_GetBackupSucceeded(This,pbSucceeded) (This)->lpVtbl->GetBackupSucceeded(This,pbSucceeded)
#define IVssComponentEx2_GetAlternateLocationMappingCount(This,pcMappings) (This)->lpVtbl->GetAlternateLocationMappingCount(This,pcMappings)
#define IVssComponentEx2_GetAlternateLocationMapping(This,iMapping,ppFiledesc) (This)->lpVtbl->GetAlternateLocationMapping(This,iMapping,ppFiledesc)
#define IVssComponentEx2_SetBackupMetadata(This,wszData) (This)->lpVtbl->SetBackupMetadata(This,wszData)
#define IVssComponentEx2_GetBackupMetadata(This,pbstrData) (This)->lpVtbl->GetBackupMetadata(This,pbstrData)
#define IVssComponentEx2_AddPartialFile(This,wszPath,wszFilename,wszRanges,wszMetadata) (This)->lpVtbl->AddPartialFile(This,wszPath,wszFilename,wszRanges,wszMetadata)
#define IVssComponentEx2_GetPartialFileCount(This,pcPartialFiles) (This)->lpVtbl->GetPartialFileCount(This,pcPartialFiles)
#define IVssComponentEx2_GetPartialFile(This,iPartialFile,pbstrPath,pbstrFilename,pbstrRange,pbstrMetadata) (This)->lpVtbl->GetPartialFile(This,iPartialFile,pbstrPath,pbstrFilename,pbstrRange,pbstrMetadata)
#define IVssComponentEx2_IsSelectedForRestore(This,pbSelectedForRestore) (This)->lpVtbl->IsSelectedForRestore(This,pbSelectedForRestore)
#define IVssComponentEx2_GetAdditionalRestores(This,pbAdditionalRestores) (This)->lpVtbl->GetAdditionalRestores(This,pbAdditionalRestores)
#define IVssComponentEx2_GetNewTargetCount(This,pcNewTarget) (This)->lpVtbl->GetNewTargetCount(This,pcNewTarget)
#define IVssComponentEx2_GetNewTarget(This,iNewTarget,ppFiledesc) (This)->lpVtbl->GetNewTarget(This,iNewTarget,ppFiledesc)
#define IVssComponentEx2_AddDirectedTarget(This,wszSourcePath,wszSourceFilename,wszSourceRangeList,wszDestinationPath,wszDestinationFilename,wszDestinationRangeList) (This)->lpVtbl->AddDirectedTarget(This,wszSourcePath,wszSourceFilename,wszSourceRangeList,wszDestinationPath,wszDestinationFilename,wszDestinationRangeList)
#define IVssComponentEx2_GetDirectedTargetCount(This,pcDirectedTarget) (This)->lpVtbl->GetDirectedTargetCount(This,pcDirectedTarget)
#define IVssComponentEx2_GetDirectedTarget(This,iDirectedTarget,pbstrSourcePath,pbstrSourceFileName,pbstrSourceRangeList,pbstrDestinationPath,pbstrDestinationFilename,pbstrDestinationRangeList) (This)->lpVtbl->GetDirectedTarget(This,iDirectedTarget,pbstrSourcePath,pbstrSourceFileName,pbstrSourceRangeList,pbstrDestinationPath,pbstrDestinationFilename,pbstrDestinationRangeList)
#define IVssComponentEx2_SetRestoreMetadata(This,wszRestoreMetadata) (This)->lpVtbl->SetRestoreMetadata(This,wszRestoreMetadata)
#define IVssComponentEx2_GetRestoreMetadata(This,pbstrRestoreMetadata) (This)->lpVtbl->GetRestoreMetadata(This,pbstrRestoreMetadata)
#define IVssComponentEx2_SetRestoreTarget(This,target) (This)->lpVtbl->SetRestoreTarget(This,target)
#define IVssComponentEx2_GetRestoreTarget(This,pTarget) (This)->lpVtbl->GetRestoreTarget(This,pTarget)
#define IVssComponentEx2_SetPreRestoreFailureMsg(This,wszPreRestoreFailureMsg) (This)->lpVtbl->SetPreRestoreFailureMsg(This,wszPreRestoreFailureMsg)
#define IVssComponentEx2_GetPreRestoreFailureMsg(This,pbstrPreRestoreFailureMsg) (This)->lpVtbl->GetPreRestoreFailureMsg(This,pbstrPreRestoreFailureMsg)
#define IVssComponentEx2_SetPostRestoreFailureMsg(This,wszPostRestoreFailureMsg) (This)->lpVtbl->SetPostRestoreFailureMsg(This,wszPostRestoreFailureMsg)
#define IVssComponentEx2_GetPostRestoreFailureMsg(This,pbstrPostRestoreFailureMsg) (This)->lpVtbl->GetPostRestoreFailureMsg(This,pbstrPostRestoreFailureMsg)
#define IVssComponentEx2_SetBackupStamp(This,wszBackupStamp) (This)->lpVtbl->SetBackupStamp(This,wszBackupStamp)
#define IVssComponentEx2_GetBackupStamp(This,pbstrBackupStamp) (This)->lpVtbl->GetBackupStamp(This,pbstrBackupStamp)
#define IVssComponentEx2_GetPreviousBackupStamp(This,pbstrBackupStamp) (This)->lpVtbl->GetPreviousBackupStamp(This,pbstrBackupStamp)
#define IVssComponentEx2_GetBackupOptions(This,pbstrBackupOptions) (This)->lpVtbl->GetBackupOptions(This,pbstrBackupOptions)
#define IVssComponentEx2_GetRestoreOptions(This,pbstrRestoreOptions) (This)->lpVtbl->GetRestoreOptions(This,pbstrRestoreOptions)
#define IVssComponentEx2_GetRestoreSubcomponentCount(This,pcRestoreSubcomponent) (This)->lpVtbl->GetRestoreSubcomponentCount(This,pcRestoreSubcomponent)
#define IVssComponentEx2_GetRestoreSubcomponent(This,iComponent,pbstrLogicalPath,pbstrComponentName,pbRepair) (This)->lpVtbl->GetRestoreSubcomponent(This,iComponent,pbstrLogicalPath,pbstrComponentName,pbRepair)
#define IVssComponentEx2_GetFileRestoreStatus(This,pStatus) (This)->lpVtbl->GetFileRestoreStatus(This,pStatus)
#define IVssComponentEx2_AddDifferencedFilesByLastModifyTime(This,wszPath,wszFilespec,bRecursive,ftLastModifyTime) (This)->lpVtbl->AddDifferencedFilesByLastModifyTime(This,wszPath,wszFilespec,bRecursive,ftLastModifyTime)
#define IVssComponentEx2_AddDifferencedFilesByLastModifyLSN(This,wszPath,wszFilespec,bRecursive,bstrLsnString) (This)->lpVtbl->AddDifferencedFilesByLastModifyLSN(This,wszPath,wszFilespec,bRecursive,bstrLsnString)
#define IVssComponentEx2_GetDifferencedFilesCount(This,pcDifferencedFiles) (This)->lpVtbl->GetDifferencedFilesCount(This,pcDifferencedFiles)
#define IVssComponentEx2_GetDifferencedFile(This,iDifferencedFile,pbstrPath,pbstrFilespec,pbRecursive,pbstrLsnString,pftLastModifyTime) (This)->lpVtbl->GetDifferencedFile(This,iDifferencedFile,pbstrPath,pbstrFilespec,pbRecursive,pbstrLsnString,pftLastModifyTime)
/*** IVssComponentEx methods ***/
#define IVssComponentEx2_SetPrepareForBackupFailureMsg(This,wszFailureMsg) (This)->lpVtbl->SetPrepareForBackupFailureMsg(This,wszFailureMsg)
#define IVssComponentEx2_SetPostSnapshotFailureMsg(This,wszFailureMsg) (This)->lpVtbl->SetPostSnapshotFailureMsg(This,wszFailureMsg)
#define IVssComponentEx2_GetPrepareForBackupFailureMsg(This,pbstrFailureMsg) (This)->lpVtbl->GetPrepareForBackupFailureMsg(This,pbstrFailureMsg)
#define IVssComponentEx2_GetPostSnapshotFailureMsg(This,pbstrFailureMsg) (This)->lpVtbl->GetPostSnapshotFailureMsg(This,pbstrFailureMsg)
#define IVssComponentEx2_GetAuthoritativeRestore(This,pbAuth) (This)->lpVtbl->GetAuthoritativeRestore(This,pbAuth)
#define IVssComponentEx2_GetRollForward(This,pRollType,pbstrPoint) (This)->lpVtbl->GetRollForward(This,pRollType,pbstrPoint)
#define IVssComponentEx2_GetRestoreName(This,pbstrName) (This)->lpVtbl->GetRestoreName(This,pbstrName)
/*** IVssComponentEx2 methods ***/
#define IVssComponentEx2_SetFailure(This,hr,hrApplication,wszApplicationMessage,dwReserved) (This)->lpVtbl->SetFailure(This,hr,hrApplication,wszApplicationMessage,dwReserved)
#define IVssComponentEx2_GetFailure(This,phr,phrApplication,pbstrApplicationMessage,pdwReserved) (This)->lpVtbl->GetFailure(This,phr,phrApplication,pbstrApplicationMessage,pdwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssComponentEx2_QueryInterface(IVssComponentEx2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssComponentEx2_AddRef(IVssComponentEx2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssComponentEx2_Release(IVssComponentEx2* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssComponent methods ***/
static inline HRESULT IVssComponentEx2_GetLogicalPath(IVssComponentEx2* This,BSTR *pbstrPath) {
    return This->lpVtbl->GetLogicalPath(This,pbstrPath);
}
static inline HRESULT IVssComponentEx2_GetComponentType(IVssComponentEx2* This,VSS_COMPONENT_TYPE *pct) {
    return This->lpVtbl->GetComponentType(This,pct);
}
static inline HRESULT IVssComponentEx2_GetComponentName(IVssComponentEx2* This,BSTR *pbstrName) {
    return This->lpVtbl->GetComponentName(This,pbstrName);
}
static inline HRESULT IVssComponentEx2_GetBackupSucceeded(IVssComponentEx2* This,boolean *pbSucceeded) {
    return This->lpVtbl->GetBackupSucceeded(This,pbSucceeded);
}
static inline HRESULT IVssComponentEx2_GetAlternateLocationMappingCount(IVssComponentEx2* This,UINT *pcMappings) {
    return This->lpVtbl->GetAlternateLocationMappingCount(This,pcMappings);
}
static inline HRESULT IVssComponentEx2_GetAlternateLocationMapping(IVssComponentEx2* This,UINT iMapping,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetAlternateLocationMapping(This,iMapping,ppFiledesc);
}
static inline HRESULT IVssComponentEx2_SetBackupMetadata(IVssComponentEx2* This,LPCWSTR wszData) {
    return This->lpVtbl->SetBackupMetadata(This,wszData);
}
static inline HRESULT IVssComponentEx2_GetBackupMetadata(IVssComponentEx2* This,BSTR *pbstrData) {
    return This->lpVtbl->GetBackupMetadata(This,pbstrData);
}
static inline HRESULT IVssComponentEx2_AddPartialFile(IVssComponentEx2* This,LPCWSTR wszPath,LPCWSTR wszFilename,LPCWSTR wszRanges,LPCWSTR wszMetadata) {
    return This->lpVtbl->AddPartialFile(This,wszPath,wszFilename,wszRanges,wszMetadata);
}
static inline HRESULT IVssComponentEx2_GetPartialFileCount(IVssComponentEx2* This,UINT *pcPartialFiles) {
    return This->lpVtbl->GetPartialFileCount(This,pcPartialFiles);
}
static inline HRESULT IVssComponentEx2_GetPartialFile(IVssComponentEx2* This,UINT iPartialFile,BSTR *pbstrPath,BSTR *pbstrFilename,BSTR *pbstrRange,BSTR *pbstrMetadata) {
    return This->lpVtbl->GetPartialFile(This,iPartialFile,pbstrPath,pbstrFilename,pbstrRange,pbstrMetadata);
}
static inline HRESULT IVssComponentEx2_IsSelectedForRestore(IVssComponentEx2* This,boolean *pbSelectedForRestore) {
    return This->lpVtbl->IsSelectedForRestore(This,pbSelectedForRestore);
}
static inline HRESULT IVssComponentEx2_GetAdditionalRestores(IVssComponentEx2* This,boolean *pbAdditionalRestores) {
    return This->lpVtbl->GetAdditionalRestores(This,pbAdditionalRestores);
}
static inline HRESULT IVssComponentEx2_GetNewTargetCount(IVssComponentEx2* This,UINT *pcNewTarget) {
    return This->lpVtbl->GetNewTargetCount(This,pcNewTarget);
}
static inline HRESULT IVssComponentEx2_GetNewTarget(IVssComponentEx2* This,UINT iNewTarget,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetNewTarget(This,iNewTarget,ppFiledesc);
}
static inline HRESULT IVssComponentEx2_AddDirectedTarget(IVssComponentEx2* This,LPCWSTR wszSourcePath,LPCWSTR wszSourceFilename,LPCWSTR wszSourceRangeList,LPCWSTR wszDestinationPath,LPCWSTR wszDestinationFilename,LPCWSTR wszDestinationRangeList) {
    return This->lpVtbl->AddDirectedTarget(This,wszSourcePath,wszSourceFilename,wszSourceRangeList,wszDestinationPath,wszDestinationFilename,wszDestinationRangeList);
}
static inline HRESULT IVssComponentEx2_GetDirectedTargetCount(IVssComponentEx2* This,UINT *pcDirectedTarget) {
    return This->lpVtbl->GetDirectedTargetCount(This,pcDirectedTarget);
}
static inline HRESULT IVssComponentEx2_GetDirectedTarget(IVssComponentEx2* This,UINT iDirectedTarget,BSTR *pbstrSourcePath,BSTR *pbstrSourceFileName,BSTR *pbstrSourceRangeList,BSTR *pbstrDestinationPath,BSTR *pbstrDestinationFilename,BSTR *pbstrDestinationRangeList) {
    return This->lpVtbl->GetDirectedTarget(This,iDirectedTarget,pbstrSourcePath,pbstrSourceFileName,pbstrSourceRangeList,pbstrDestinationPath,pbstrDestinationFilename,pbstrDestinationRangeList);
}
static inline HRESULT IVssComponentEx2_SetRestoreMetadata(IVssComponentEx2* This,LPCWSTR wszRestoreMetadata) {
    return This->lpVtbl->SetRestoreMetadata(This,wszRestoreMetadata);
}
static inline HRESULT IVssComponentEx2_GetRestoreMetadata(IVssComponentEx2* This,BSTR *pbstrRestoreMetadata) {
    return This->lpVtbl->GetRestoreMetadata(This,pbstrRestoreMetadata);
}
static inline HRESULT IVssComponentEx2_SetRestoreTarget(IVssComponentEx2* This,VSS_RESTORE_TARGET target) {
    return This->lpVtbl->SetRestoreTarget(This,target);
}
static inline HRESULT IVssComponentEx2_GetRestoreTarget(IVssComponentEx2* This,VSS_RESTORE_TARGET *pTarget) {
    return This->lpVtbl->GetRestoreTarget(This,pTarget);
}
static inline HRESULT IVssComponentEx2_SetPreRestoreFailureMsg(IVssComponentEx2* This,LPCWSTR wszPreRestoreFailureMsg) {
    return This->lpVtbl->SetPreRestoreFailureMsg(This,wszPreRestoreFailureMsg);
}
static inline HRESULT IVssComponentEx2_GetPreRestoreFailureMsg(IVssComponentEx2* This,BSTR *pbstrPreRestoreFailureMsg) {
    return This->lpVtbl->GetPreRestoreFailureMsg(This,pbstrPreRestoreFailureMsg);
}
static inline HRESULT IVssComponentEx2_SetPostRestoreFailureMsg(IVssComponentEx2* This,LPCWSTR wszPostRestoreFailureMsg) {
    return This->lpVtbl->SetPostRestoreFailureMsg(This,wszPostRestoreFailureMsg);
}
static inline HRESULT IVssComponentEx2_GetPostRestoreFailureMsg(IVssComponentEx2* This,BSTR *pbstrPostRestoreFailureMsg) {
    return This->lpVtbl->GetPostRestoreFailureMsg(This,pbstrPostRestoreFailureMsg);
}
static inline HRESULT IVssComponentEx2_SetBackupStamp(IVssComponentEx2* This,LPCWSTR wszBackupStamp) {
    return This->lpVtbl->SetBackupStamp(This,wszBackupStamp);
}
static inline HRESULT IVssComponentEx2_GetBackupStamp(IVssComponentEx2* This,BSTR *pbstrBackupStamp) {
    return This->lpVtbl->GetBackupStamp(This,pbstrBackupStamp);
}
static inline HRESULT IVssComponentEx2_GetPreviousBackupStamp(IVssComponentEx2* This,BSTR *pbstrBackupStamp) {
    return This->lpVtbl->GetPreviousBackupStamp(This,pbstrBackupStamp);
}
static inline HRESULT IVssComponentEx2_GetBackupOptions(IVssComponentEx2* This,BSTR *pbstrBackupOptions) {
    return This->lpVtbl->GetBackupOptions(This,pbstrBackupOptions);
}
static inline HRESULT IVssComponentEx2_GetRestoreOptions(IVssComponentEx2* This,BSTR *pbstrRestoreOptions) {
    return This->lpVtbl->GetRestoreOptions(This,pbstrRestoreOptions);
}
static inline HRESULT IVssComponentEx2_GetRestoreSubcomponentCount(IVssComponentEx2* This,UINT *pcRestoreSubcomponent) {
    return This->lpVtbl->GetRestoreSubcomponentCount(This,pcRestoreSubcomponent);
}
static inline HRESULT IVssComponentEx2_GetRestoreSubcomponent(IVssComponentEx2* This,UINT iComponent,BSTR *pbstrLogicalPath,BSTR *pbstrComponentName,boolean *pbRepair) {
    return This->lpVtbl->GetRestoreSubcomponent(This,iComponent,pbstrLogicalPath,pbstrComponentName,pbRepair);
}
static inline HRESULT IVssComponentEx2_GetFileRestoreStatus(IVssComponentEx2* This,VSS_FILE_RESTORE_STATUS *pStatus) {
    return This->lpVtbl->GetFileRestoreStatus(This,pStatus);
}
static inline HRESULT IVssComponentEx2_AddDifferencedFilesByLastModifyTime(IVssComponentEx2* This,LPCWSTR wszPath,LPCWSTR wszFilespec,WINBOOL bRecursive,FILETIME ftLastModifyTime) {
    return This->lpVtbl->AddDifferencedFilesByLastModifyTime(This,wszPath,wszFilespec,bRecursive,ftLastModifyTime);
}
static inline HRESULT IVssComponentEx2_AddDifferencedFilesByLastModifyLSN(IVssComponentEx2* This,LPCWSTR wszPath,LPCWSTR wszFilespec,WINBOOL bRecursive,BSTR bstrLsnString) {
    return This->lpVtbl->AddDifferencedFilesByLastModifyLSN(This,wszPath,wszFilespec,bRecursive,bstrLsnString);
}
static inline HRESULT IVssComponentEx2_GetDifferencedFilesCount(IVssComponentEx2* This,UINT *pcDifferencedFiles) {
    return This->lpVtbl->GetDifferencedFilesCount(This,pcDifferencedFiles);
}
static inline HRESULT IVssComponentEx2_GetDifferencedFile(IVssComponentEx2* This,UINT iDifferencedFile,BSTR *pbstrPath,BSTR *pbstrFilespec,WINBOOL *pbRecursive,BSTR *pbstrLsnString,FILETIME *pftLastModifyTime) {
    return This->lpVtbl->GetDifferencedFile(This,iDifferencedFile,pbstrPath,pbstrFilespec,pbRecursive,pbstrLsnString,pftLastModifyTime);
}
/*** IVssComponentEx methods ***/
static inline HRESULT IVssComponentEx2_SetPrepareForBackupFailureMsg(IVssComponentEx2* This,LPCWSTR wszFailureMsg) {
    return This->lpVtbl->SetPrepareForBackupFailureMsg(This,wszFailureMsg);
}
static inline HRESULT IVssComponentEx2_SetPostSnapshotFailureMsg(IVssComponentEx2* This,LPCWSTR wszFailureMsg) {
    return This->lpVtbl->SetPostSnapshotFailureMsg(This,wszFailureMsg);
}
static inline HRESULT IVssComponentEx2_GetPrepareForBackupFailureMsg(IVssComponentEx2* This,BSTR *pbstrFailureMsg) {
    return This->lpVtbl->GetPrepareForBackupFailureMsg(This,pbstrFailureMsg);
}
static inline HRESULT IVssComponentEx2_GetPostSnapshotFailureMsg(IVssComponentEx2* This,BSTR *pbstrFailureMsg) {
    return This->lpVtbl->GetPostSnapshotFailureMsg(This,pbstrFailureMsg);
}
static inline HRESULT IVssComponentEx2_GetAuthoritativeRestore(IVssComponentEx2* This,boolean *pbAuth) {
    return This->lpVtbl->GetAuthoritativeRestore(This,pbAuth);
}
static inline HRESULT IVssComponentEx2_GetRollForward(IVssComponentEx2* This,VSS_ROLLFORWARD_TYPE *pRollType,BSTR *pbstrPoint) {
    return This->lpVtbl->GetRollForward(This,pRollType,pbstrPoint);
}
static inline HRESULT IVssComponentEx2_GetRestoreName(IVssComponentEx2* This,BSTR *pbstrName) {
    return This->lpVtbl->GetRestoreName(This,pbstrName);
}
/*** IVssComponentEx2 methods ***/
static inline HRESULT IVssComponentEx2_SetFailure(IVssComponentEx2* This,HRESULT hr,HRESULT hrApplication,LPCWSTR wszApplicationMessage,DWORD dwReserved) {
    return This->lpVtbl->SetFailure(This,hr,hrApplication,wszApplicationMessage,dwReserved);
}
static inline HRESULT IVssComponentEx2_GetFailure(IVssComponentEx2* This,HRESULT *phr,HRESULT *phrApplication,BSTR *pbstrApplicationMessage,DWORD *pdwReserved) {
    return This->lpVtbl->GetFailure(This,phr,phrApplication,pbstrApplicationMessage,pdwReserved);
}
#endif
#endif

#endif


#endif  /* __IVssComponentEx2_INTERFACE_DEFINED__ */

#ifndef __IXMLDOMDocument_FWD_DEFINED__
#define __IXMLDOMDocument_FWD_DEFINED__
typedef interface IXMLDOMDocument IXMLDOMDocument;
#ifdef __cplusplus
interface IXMLDOMDocument;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IVssCreateWriterMetadata interface
 */
#ifndef __IVssCreateWriterMetadata_INTERFACE_DEFINED__
#define __IVssCreateWriterMetadata_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssCreateWriterMetadata, 0x00000000, 0x0000, 0x0000, 0x00,0x00, 0x00,0x00,0x00,0x00,0x00,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000000-0000-0000-0000-000000000000")
IVssCreateWriterMetadata
{

    BEGIN_INTERFACE

    virtual HRESULT STDMETHODCALLTYPE AddIncludeFiles(
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive,
        LPCWSTR wszAlternateLocation) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddExcludeFiles(
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddComponent(
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszCaption,
        const BYTE *pbIcon,
        UINT cbIcon,
        boolean bRestoreMetadata,
        boolean bNotifyOnBackupComplete,
        boolean bSelectable,
        boolean bSelectableForRestore = 0,
        DWORD dwComponentFlags = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddDatabaseFiles(
        LPCWSTR wszLogicalPath,
        LPCWSTR wszDatabaseName,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        DWORD dwBackupTypeMask = VSS_FSBT_ALL_BACKUP_REQUIRED | VSS_FSBT_ALL_SNAPSHOT_REQUIRED) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddDatabaseLogFiles(
        LPCWSTR wszLogicalPath,
        LPCWSTR wszDatabaseName,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        DWORD dwBackupTypeMask = VSS_FSBT_ALL_BACKUP_REQUIRED | VSS_FSBT_ALL_SNAPSHOT_REQUIRED) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddFilesToFileGroup(
        LPCWSTR wszLogicalPath,
        LPCWSTR wszGroupName,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive,
        LPCWSTR wszAlternateLocation,
        DWORD dwBackupTypeMask = VSS_FSBT_ALL_BACKUP_REQUIRED | VSS_FSBT_ALL_SNAPSHOT_REQUIRED) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRestoreMethod(
        VSS_RESTOREMETHOD_ENUM method,
        LPCWSTR wszService,
        LPCWSTR wszUserProcedure,
        VSS_WRITERRESTORE_ENUM writerRestore,
        boolean bRebootRequired) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddAlternateLocationMapping(
        LPCWSTR wszSourcePath,
        LPCWSTR wszSourceFilespec,
        boolean bRecursive,
        LPCWSTR wszDestination) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddComponentDependency(
        LPCWSTR wszForLogicalPath,
        LPCWSTR wszForComponentName,
        VSS_ID onWriterId,
        LPCWSTR wszOnLogicalPath,
        LPCWSTR wszOnComponentName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBackupSchema(
        DWORD dwSchemaMask) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDocument(
        IXMLDOMDocument **pDoc) = 0;

    virtual HRESULT STDMETHODCALLTYPE SaveAsXML(
        BSTR *pbstrXML) = 0;

    END_INTERFACE

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssCreateWriterMetadata, 0x00000000, 0x0000, 0x0000, 0x00,0x00, 0x00,0x00,0x00,0x00,0x00,0x00)
#endif
#else
typedef struct IVssCreateWriterMetadataVtbl {
    BEGIN_INTERFACE

    /*** IVssCreateWriterMetadata methods ***/
    HRESULT (STDMETHODCALLTYPE *AddIncludeFiles)(
        IVssCreateWriterMetadata *This,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive,
        LPCWSTR wszAlternateLocation);

    HRESULT (STDMETHODCALLTYPE *AddExcludeFiles)(
        IVssCreateWriterMetadata *This,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive);

    HRESULT (STDMETHODCALLTYPE *AddComponent)(
        IVssCreateWriterMetadata *This,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszCaption,
        const BYTE *pbIcon,
        UINT cbIcon,
        boolean bRestoreMetadata,
        boolean bNotifyOnBackupComplete,
        boolean bSelectable,
        boolean bSelectableForRestore,
        DWORD dwComponentFlags);

    HRESULT (STDMETHODCALLTYPE *AddDatabaseFiles)(
        IVssCreateWriterMetadata *This,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszDatabaseName,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        DWORD dwBackupTypeMask);

    HRESULT (STDMETHODCALLTYPE *AddDatabaseLogFiles)(
        IVssCreateWriterMetadata *This,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszDatabaseName,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        DWORD dwBackupTypeMask);

    HRESULT (STDMETHODCALLTYPE *AddFilesToFileGroup)(
        IVssCreateWriterMetadata *This,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszGroupName,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive,
        LPCWSTR wszAlternateLocation,
        DWORD dwBackupTypeMask);

    HRESULT (STDMETHODCALLTYPE *SetRestoreMethod)(
        IVssCreateWriterMetadata *This,
        VSS_RESTOREMETHOD_ENUM method,
        LPCWSTR wszService,
        LPCWSTR wszUserProcedure,
        VSS_WRITERRESTORE_ENUM writerRestore,
        boolean bRebootRequired);

    HRESULT (STDMETHODCALLTYPE *AddAlternateLocationMapping)(
        IVssCreateWriterMetadata *This,
        LPCWSTR wszSourcePath,
        LPCWSTR wszSourceFilespec,
        boolean bRecursive,
        LPCWSTR wszDestination);

    HRESULT (STDMETHODCALLTYPE *AddComponentDependency)(
        IVssCreateWriterMetadata *This,
        LPCWSTR wszForLogicalPath,
        LPCWSTR wszForComponentName,
        VSS_ID onWriterId,
        LPCWSTR wszOnLogicalPath,
        LPCWSTR wszOnComponentName);

    HRESULT (STDMETHODCALLTYPE *SetBackupSchema)(
        IVssCreateWriterMetadata *This,
        DWORD dwSchemaMask);

    HRESULT (STDMETHODCALLTYPE *GetDocument)(
        IVssCreateWriterMetadata *This,
        IXMLDOMDocument **pDoc);

    HRESULT (STDMETHODCALLTYPE *SaveAsXML)(
        IVssCreateWriterMetadata *This,
        BSTR *pbstrXML);

    END_INTERFACE
} IVssCreateWriterMetadataVtbl;

interface IVssCreateWriterMetadata {
    CONST_VTBL IVssCreateWriterMetadataVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IVssCreateWriterMetadata methods ***/
#define IVssCreateWriterMetadata_AddIncludeFiles(This,wszPath,wszFilespec,bRecursive,wszAlternateLocation) (This)->lpVtbl->AddIncludeFiles(This,wszPath,wszFilespec,bRecursive,wszAlternateLocation)
#define IVssCreateWriterMetadata_AddExcludeFiles(This,wszPath,wszFilespec,bRecursive) (This)->lpVtbl->AddExcludeFiles(This,wszPath,wszFilespec,bRecursive)
#define IVssCreateWriterMetadata_AddComponent(This,ct,wszLogicalPath,wszComponentName,wszCaption,pbIcon,cbIcon,bRestoreMetadata,bNotifyOnBackupComplete,bSelectable,bSelectableForRestore,dwComponentFlags) (This)->lpVtbl->AddComponent(This,ct,wszLogicalPath,wszComponentName,wszCaption,pbIcon,cbIcon,bRestoreMetadata,bNotifyOnBackupComplete,bSelectable,bSelectableForRestore,dwComponentFlags)
#define IVssCreateWriterMetadata_AddDatabaseFiles(This,wszLogicalPath,wszDatabaseName,wszPath,wszFilespec,dwBackupTypeMask) (This)->lpVtbl->AddDatabaseFiles(This,wszLogicalPath,wszDatabaseName,wszPath,wszFilespec,dwBackupTypeMask)
#define IVssCreateWriterMetadata_AddDatabaseLogFiles(This,wszLogicalPath,wszDatabaseName,wszPath,wszFilespec,dwBackupTypeMask) (This)->lpVtbl->AddDatabaseLogFiles(This,wszLogicalPath,wszDatabaseName,wszPath,wszFilespec,dwBackupTypeMask)
#define IVssCreateWriterMetadata_AddFilesToFileGroup(This,wszLogicalPath,wszGroupName,wszPath,wszFilespec,bRecursive,wszAlternateLocation,dwBackupTypeMask) (This)->lpVtbl->AddFilesToFileGroup(This,wszLogicalPath,wszGroupName,wszPath,wszFilespec,bRecursive,wszAlternateLocation,dwBackupTypeMask)
#define IVssCreateWriterMetadata_SetRestoreMethod(This,method,wszService,wszUserProcedure,writerRestore,bRebootRequired) (This)->lpVtbl->SetRestoreMethod(This,method,wszService,wszUserProcedure,writerRestore,bRebootRequired)
#define IVssCreateWriterMetadata_AddAlternateLocationMapping(This,wszSourcePath,wszSourceFilespec,bRecursive,wszDestination) (This)->lpVtbl->AddAlternateLocationMapping(This,wszSourcePath,wszSourceFilespec,bRecursive,wszDestination)
#define IVssCreateWriterMetadata_AddComponentDependency(This,wszForLogicalPath,wszForComponentName,onWriterId,wszOnLogicalPath,wszOnComponentName) (This)->lpVtbl->AddComponentDependency(This,wszForLogicalPath,wszForComponentName,onWriterId,wszOnLogicalPath,wszOnComponentName)
#define IVssCreateWriterMetadata_SetBackupSchema(This,dwSchemaMask) (This)->lpVtbl->SetBackupSchema(This,dwSchemaMask)
#define IVssCreateWriterMetadata_GetDocument(This,pDoc) (This)->lpVtbl->GetDocument(This,pDoc)
#define IVssCreateWriterMetadata_SaveAsXML(This,pbstrXML) (This)->lpVtbl->SaveAsXML(This,pbstrXML)
#else
/*** IVssCreateWriterMetadata methods ***/
static inline HRESULT IVssCreateWriterMetadata_AddIncludeFiles(IVssCreateWriterMetadata* This,LPCWSTR wszPath,LPCWSTR wszFilespec,boolean bRecursive,LPCWSTR wszAlternateLocation) {
    return This->lpVtbl->AddIncludeFiles(This,wszPath,wszFilespec,bRecursive,wszAlternateLocation);
}
static inline HRESULT IVssCreateWriterMetadata_AddExcludeFiles(IVssCreateWriterMetadata* This,LPCWSTR wszPath,LPCWSTR wszFilespec,boolean bRecursive) {
    return This->lpVtbl->AddExcludeFiles(This,wszPath,wszFilespec,bRecursive);
}
static inline HRESULT IVssCreateWriterMetadata_AddComponent(IVssCreateWriterMetadata* This,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszCaption,const BYTE *pbIcon,UINT cbIcon,boolean bRestoreMetadata,boolean bNotifyOnBackupComplete,boolean bSelectable,boolean bSelectableForRestore,DWORD dwComponentFlags) {
    return This->lpVtbl->AddComponent(This,ct,wszLogicalPath,wszComponentName,wszCaption,pbIcon,cbIcon,bRestoreMetadata,bNotifyOnBackupComplete,bSelectable,bSelectableForRestore,dwComponentFlags);
}
static inline HRESULT IVssCreateWriterMetadata_AddDatabaseFiles(IVssCreateWriterMetadata* This,LPCWSTR wszLogicalPath,LPCWSTR wszDatabaseName,LPCWSTR wszPath,LPCWSTR wszFilespec,DWORD dwBackupTypeMask) {
    return This->lpVtbl->AddDatabaseFiles(This,wszLogicalPath,wszDatabaseName,wszPath,wszFilespec,dwBackupTypeMask);
}
static inline HRESULT IVssCreateWriterMetadata_AddDatabaseLogFiles(IVssCreateWriterMetadata* This,LPCWSTR wszLogicalPath,LPCWSTR wszDatabaseName,LPCWSTR wszPath,LPCWSTR wszFilespec,DWORD dwBackupTypeMask) {
    return This->lpVtbl->AddDatabaseLogFiles(This,wszLogicalPath,wszDatabaseName,wszPath,wszFilespec,dwBackupTypeMask);
}
static inline HRESULT IVssCreateWriterMetadata_AddFilesToFileGroup(IVssCreateWriterMetadata* This,LPCWSTR wszLogicalPath,LPCWSTR wszGroupName,LPCWSTR wszPath,LPCWSTR wszFilespec,boolean bRecursive,LPCWSTR wszAlternateLocation,DWORD dwBackupTypeMask) {
    return This->lpVtbl->AddFilesToFileGroup(This,wszLogicalPath,wszGroupName,wszPath,wszFilespec,bRecursive,wszAlternateLocation,dwBackupTypeMask);
}
static inline HRESULT IVssCreateWriterMetadata_SetRestoreMethod(IVssCreateWriterMetadata* This,VSS_RESTOREMETHOD_ENUM method,LPCWSTR wszService,LPCWSTR wszUserProcedure,VSS_WRITERRESTORE_ENUM writerRestore,boolean bRebootRequired) {
    return This->lpVtbl->SetRestoreMethod(This,method,wszService,wszUserProcedure,writerRestore,bRebootRequired);
}
static inline HRESULT IVssCreateWriterMetadata_AddAlternateLocationMapping(IVssCreateWriterMetadata* This,LPCWSTR wszSourcePath,LPCWSTR wszSourceFilespec,boolean bRecursive,LPCWSTR wszDestination) {
    return This->lpVtbl->AddAlternateLocationMapping(This,wszSourcePath,wszSourceFilespec,bRecursive,wszDestination);
}
static inline HRESULT IVssCreateWriterMetadata_AddComponentDependency(IVssCreateWriterMetadata* This,LPCWSTR wszForLogicalPath,LPCWSTR wszForComponentName,VSS_ID onWriterId,LPCWSTR wszOnLogicalPath,LPCWSTR wszOnComponentName) {
    return This->lpVtbl->AddComponentDependency(This,wszForLogicalPath,wszForComponentName,onWriterId,wszOnLogicalPath,wszOnComponentName);
}
static inline HRESULT IVssCreateWriterMetadata_SetBackupSchema(IVssCreateWriterMetadata* This,DWORD dwSchemaMask) {
    return This->lpVtbl->SetBackupSchema(This,dwSchemaMask);
}
static inline HRESULT IVssCreateWriterMetadata_GetDocument(IVssCreateWriterMetadata* This,IXMLDOMDocument **pDoc) {
    return This->lpVtbl->GetDocument(This,pDoc);
}
static inline HRESULT IVssCreateWriterMetadata_SaveAsXML(IVssCreateWriterMetadata* This,BSTR *pbstrXML) {
    return This->lpVtbl->SaveAsXML(This,pbstrXML);
}
#endif
#endif

#endif


#endif  /* __IVssCreateWriterMetadata_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssCreateWriterMetadataEx interface
 */
#ifndef __IVssCreateWriterMetadataEx_INTERFACE_DEFINED__
#define __IVssCreateWriterMetadataEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssCreateWriterMetadataEx, 0x9f21981d, 0xd469, 0x4349, 0xb8,0x07, 0x39,0xe6,0x4e,0x46,0x74,0xe1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9f21981d-d469-4349-b807-39e64e4674e1")
IVssCreateWriterMetadataEx : public IVssCreateWriterMetadata
{
    virtual HRESULT STDMETHODCALLTYPE AddExcludeFilesFromSnapshot(
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssCreateWriterMetadataEx, 0x9f21981d, 0xd469, 0x4349, 0xb8,0x07, 0x39,0xe6,0x4e,0x46,0x74,0xe1)
#endif
#else
typedef struct IVssCreateWriterMetadataExVtbl {
    BEGIN_INTERFACE

    /*** IVssCreateWriterMetadata methods ***/
    HRESULT (STDMETHODCALLTYPE *AddIncludeFiles)(
        IVssCreateWriterMetadataEx *This,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive,
        LPCWSTR wszAlternateLocation);

    HRESULT (STDMETHODCALLTYPE *AddExcludeFiles)(
        IVssCreateWriterMetadataEx *This,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive);

    HRESULT (STDMETHODCALLTYPE *AddComponent)(
        IVssCreateWriterMetadataEx *This,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszCaption,
        const BYTE *pbIcon,
        UINT cbIcon,
        boolean bRestoreMetadata,
        boolean bNotifyOnBackupComplete,
        boolean bSelectable,
        boolean bSelectableForRestore,
        DWORD dwComponentFlags);

    HRESULT (STDMETHODCALLTYPE *AddDatabaseFiles)(
        IVssCreateWriterMetadataEx *This,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszDatabaseName,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        DWORD dwBackupTypeMask);

    HRESULT (STDMETHODCALLTYPE *AddDatabaseLogFiles)(
        IVssCreateWriterMetadataEx *This,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszDatabaseName,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        DWORD dwBackupTypeMask);

    HRESULT (STDMETHODCALLTYPE *AddFilesToFileGroup)(
        IVssCreateWriterMetadataEx *This,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszGroupName,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive,
        LPCWSTR wszAlternateLocation,
        DWORD dwBackupTypeMask);

    HRESULT (STDMETHODCALLTYPE *SetRestoreMethod)(
        IVssCreateWriterMetadataEx *This,
        VSS_RESTOREMETHOD_ENUM method,
        LPCWSTR wszService,
        LPCWSTR wszUserProcedure,
        VSS_WRITERRESTORE_ENUM writerRestore,
        boolean bRebootRequired);

    HRESULT (STDMETHODCALLTYPE *AddAlternateLocationMapping)(
        IVssCreateWriterMetadataEx *This,
        LPCWSTR wszSourcePath,
        LPCWSTR wszSourceFilespec,
        boolean bRecursive,
        LPCWSTR wszDestination);

    HRESULT (STDMETHODCALLTYPE *AddComponentDependency)(
        IVssCreateWriterMetadataEx *This,
        LPCWSTR wszForLogicalPath,
        LPCWSTR wszForComponentName,
        VSS_ID onWriterId,
        LPCWSTR wszOnLogicalPath,
        LPCWSTR wszOnComponentName);

    HRESULT (STDMETHODCALLTYPE *SetBackupSchema)(
        IVssCreateWriterMetadataEx *This,
        DWORD dwSchemaMask);

    HRESULT (STDMETHODCALLTYPE *GetDocument)(
        IVssCreateWriterMetadataEx *This,
        IXMLDOMDocument **pDoc);

    HRESULT (STDMETHODCALLTYPE *SaveAsXML)(
        IVssCreateWriterMetadataEx *This,
        BSTR *pbstrXML);

    /*** IVssCreateWriterMetadataEx methods ***/
    HRESULT (STDMETHODCALLTYPE *AddExcludeFilesFromSnapshot)(
        IVssCreateWriterMetadataEx *This,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive);

    END_INTERFACE
} IVssCreateWriterMetadataExVtbl;

interface IVssCreateWriterMetadataEx {
    CONST_VTBL IVssCreateWriterMetadataExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IVssCreateWriterMetadata methods ***/
#define IVssCreateWriterMetadataEx_AddIncludeFiles(This,wszPath,wszFilespec,bRecursive,wszAlternateLocation) (This)->lpVtbl->AddIncludeFiles(This,wszPath,wszFilespec,bRecursive,wszAlternateLocation)
#define IVssCreateWriterMetadataEx_AddExcludeFiles(This,wszPath,wszFilespec,bRecursive) (This)->lpVtbl->AddExcludeFiles(This,wszPath,wszFilespec,bRecursive)
#define IVssCreateWriterMetadataEx_AddComponent(This,ct,wszLogicalPath,wszComponentName,wszCaption,pbIcon,cbIcon,bRestoreMetadata,bNotifyOnBackupComplete,bSelectable,bSelectableForRestore,dwComponentFlags) (This)->lpVtbl->AddComponent(This,ct,wszLogicalPath,wszComponentName,wszCaption,pbIcon,cbIcon,bRestoreMetadata,bNotifyOnBackupComplete,bSelectable,bSelectableForRestore,dwComponentFlags)
#define IVssCreateWriterMetadataEx_AddDatabaseFiles(This,wszLogicalPath,wszDatabaseName,wszPath,wszFilespec,dwBackupTypeMask) (This)->lpVtbl->AddDatabaseFiles(This,wszLogicalPath,wszDatabaseName,wszPath,wszFilespec,dwBackupTypeMask)
#define IVssCreateWriterMetadataEx_AddDatabaseLogFiles(This,wszLogicalPath,wszDatabaseName,wszPath,wszFilespec,dwBackupTypeMask) (This)->lpVtbl->AddDatabaseLogFiles(This,wszLogicalPath,wszDatabaseName,wszPath,wszFilespec,dwBackupTypeMask)
#define IVssCreateWriterMetadataEx_AddFilesToFileGroup(This,wszLogicalPath,wszGroupName,wszPath,wszFilespec,bRecursive,wszAlternateLocation,dwBackupTypeMask) (This)->lpVtbl->AddFilesToFileGroup(This,wszLogicalPath,wszGroupName,wszPath,wszFilespec,bRecursive,wszAlternateLocation,dwBackupTypeMask)
#define IVssCreateWriterMetadataEx_SetRestoreMethod(This,method,wszService,wszUserProcedure,writerRestore,bRebootRequired) (This)->lpVtbl->SetRestoreMethod(This,method,wszService,wszUserProcedure,writerRestore,bRebootRequired)
#define IVssCreateWriterMetadataEx_AddAlternateLocationMapping(This,wszSourcePath,wszSourceFilespec,bRecursive,wszDestination) (This)->lpVtbl->AddAlternateLocationMapping(This,wszSourcePath,wszSourceFilespec,bRecursive,wszDestination)
#define IVssCreateWriterMetadataEx_AddComponentDependency(This,wszForLogicalPath,wszForComponentName,onWriterId,wszOnLogicalPath,wszOnComponentName) (This)->lpVtbl->AddComponentDependency(This,wszForLogicalPath,wszForComponentName,onWriterId,wszOnLogicalPath,wszOnComponentName)
#define IVssCreateWriterMetadataEx_SetBackupSchema(This,dwSchemaMask) (This)->lpVtbl->SetBackupSchema(This,dwSchemaMask)
#define IVssCreateWriterMetadataEx_GetDocument(This,pDoc) (This)->lpVtbl->GetDocument(This,pDoc)
#define IVssCreateWriterMetadataEx_SaveAsXML(This,pbstrXML) (This)->lpVtbl->SaveAsXML(This,pbstrXML)
/*** IVssCreateWriterMetadataEx methods ***/
#define IVssCreateWriterMetadataEx_AddExcludeFilesFromSnapshot(This,wszPath,wszFilespec,bRecursive) (This)->lpVtbl->AddExcludeFilesFromSnapshot(This,wszPath,wszFilespec,bRecursive)
#else
/*** IVssCreateWriterMetadata methods ***/
static inline HRESULT IVssCreateWriterMetadataEx_AddIncludeFiles(IVssCreateWriterMetadataEx* This,LPCWSTR wszPath,LPCWSTR wszFilespec,boolean bRecursive,LPCWSTR wszAlternateLocation) {
    return This->lpVtbl->AddIncludeFiles(This,wszPath,wszFilespec,bRecursive,wszAlternateLocation);
}
static inline HRESULT IVssCreateWriterMetadataEx_AddExcludeFiles(IVssCreateWriterMetadataEx* This,LPCWSTR wszPath,LPCWSTR wszFilespec,boolean bRecursive) {
    return This->lpVtbl->AddExcludeFiles(This,wszPath,wszFilespec,bRecursive);
}
static inline HRESULT IVssCreateWriterMetadataEx_AddComponent(IVssCreateWriterMetadataEx* This,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszCaption,const BYTE *pbIcon,UINT cbIcon,boolean bRestoreMetadata,boolean bNotifyOnBackupComplete,boolean bSelectable,boolean bSelectableForRestore,DWORD dwComponentFlags) {
    return This->lpVtbl->AddComponent(This,ct,wszLogicalPath,wszComponentName,wszCaption,pbIcon,cbIcon,bRestoreMetadata,bNotifyOnBackupComplete,bSelectable,bSelectableForRestore,dwComponentFlags);
}
static inline HRESULT IVssCreateWriterMetadataEx_AddDatabaseFiles(IVssCreateWriterMetadataEx* This,LPCWSTR wszLogicalPath,LPCWSTR wszDatabaseName,LPCWSTR wszPath,LPCWSTR wszFilespec,DWORD dwBackupTypeMask) {
    return This->lpVtbl->AddDatabaseFiles(This,wszLogicalPath,wszDatabaseName,wszPath,wszFilespec,dwBackupTypeMask);
}
static inline HRESULT IVssCreateWriterMetadataEx_AddDatabaseLogFiles(IVssCreateWriterMetadataEx* This,LPCWSTR wszLogicalPath,LPCWSTR wszDatabaseName,LPCWSTR wszPath,LPCWSTR wszFilespec,DWORD dwBackupTypeMask) {
    return This->lpVtbl->AddDatabaseLogFiles(This,wszLogicalPath,wszDatabaseName,wszPath,wszFilespec,dwBackupTypeMask);
}
static inline HRESULT IVssCreateWriterMetadataEx_AddFilesToFileGroup(IVssCreateWriterMetadataEx* This,LPCWSTR wszLogicalPath,LPCWSTR wszGroupName,LPCWSTR wszPath,LPCWSTR wszFilespec,boolean bRecursive,LPCWSTR wszAlternateLocation,DWORD dwBackupTypeMask) {
    return This->lpVtbl->AddFilesToFileGroup(This,wszLogicalPath,wszGroupName,wszPath,wszFilespec,bRecursive,wszAlternateLocation,dwBackupTypeMask);
}
static inline HRESULT IVssCreateWriterMetadataEx_SetRestoreMethod(IVssCreateWriterMetadataEx* This,VSS_RESTOREMETHOD_ENUM method,LPCWSTR wszService,LPCWSTR wszUserProcedure,VSS_WRITERRESTORE_ENUM writerRestore,boolean bRebootRequired) {
    return This->lpVtbl->SetRestoreMethod(This,method,wszService,wszUserProcedure,writerRestore,bRebootRequired);
}
static inline HRESULT IVssCreateWriterMetadataEx_AddAlternateLocationMapping(IVssCreateWriterMetadataEx* This,LPCWSTR wszSourcePath,LPCWSTR wszSourceFilespec,boolean bRecursive,LPCWSTR wszDestination) {
    return This->lpVtbl->AddAlternateLocationMapping(This,wszSourcePath,wszSourceFilespec,bRecursive,wszDestination);
}
static inline HRESULT IVssCreateWriterMetadataEx_AddComponentDependency(IVssCreateWriterMetadataEx* This,LPCWSTR wszForLogicalPath,LPCWSTR wszForComponentName,VSS_ID onWriterId,LPCWSTR wszOnLogicalPath,LPCWSTR wszOnComponentName) {
    return This->lpVtbl->AddComponentDependency(This,wszForLogicalPath,wszForComponentName,onWriterId,wszOnLogicalPath,wszOnComponentName);
}
static inline HRESULT IVssCreateWriterMetadataEx_SetBackupSchema(IVssCreateWriterMetadataEx* This,DWORD dwSchemaMask) {
    return This->lpVtbl->SetBackupSchema(This,dwSchemaMask);
}
static inline HRESULT IVssCreateWriterMetadataEx_GetDocument(IVssCreateWriterMetadataEx* This,IXMLDOMDocument **pDoc) {
    return This->lpVtbl->GetDocument(This,pDoc);
}
static inline HRESULT IVssCreateWriterMetadataEx_SaveAsXML(IVssCreateWriterMetadataEx* This,BSTR *pbstrXML) {
    return This->lpVtbl->SaveAsXML(This,pbstrXML);
}
/*** IVssCreateWriterMetadataEx methods ***/
static inline HRESULT IVssCreateWriterMetadataEx_AddExcludeFilesFromSnapshot(IVssCreateWriterMetadataEx* This,LPCWSTR wszPath,LPCWSTR wszFilespec,boolean bRecursive) {
    return This->lpVtbl->AddExcludeFilesFromSnapshot(This,wszPath,wszFilespec,bRecursive);
}
#endif
#endif

#endif


#endif  /* __IVssCreateWriterMetadataEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssWriterImpl interface
 */
#ifndef __IVssWriterImpl_INTERFACE_DEFINED__
#define __IVssWriterImpl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssWriterImpl, 0x00000000, 0x0000, 0x0000, 0x00,0x00, 0x00,0x00,0x00,0x00,0x00,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000000-0000-0000-0000-000000000000")
IVssWriterImpl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        VSS_ID writerId,
        LPCWSTR wszWriterName,
        LPCWSTR wszWriterInstanceName,
        DWORD dwMajorVersion,
        DWORD dwMinorVersion,
        VSS_USAGE_TYPE ut,
        VSS_SOURCE_TYPE st,
        VSS_APPLICATION_LEVEL nLevel,
        DWORD dwTimeout,
        VSS_ALTERNATE_WRITER_STATE aws,
        boolean bIOThrottlingOnly) = 0;

    virtual HRESULT STDMETHODCALLTYPE Subscribe(
        DWORD dwSubscribeTimeout,
        DWORD dwEventFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unsubscribe(
        ) = 0;

    virtual void STDMETHODCALLTYPE Uninitialize(
        ) = 0;

    virtual LPCWSTR * STDMETHODCALLTYPE GetCurrentVolumeArray(
        ) = 0;

    virtual UINT STDMETHODCALLTYPE GetCurrentVolumeCount(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSnapshotDeviceName(
        LPCWSTR wszOriginalVolume,
        LPCWSTR *ppwszSnapshotDevice) = 0;

#ifdef WIDL_EXPLICIT_AGGREGATE_RETURNS
    virtual VSS_ID* STDMETHODCALLTYPE GetCurrentSnapshotSetId(
        VSS_ID *__ret) = 0;
    VSS_ID STDMETHODCALLTYPE GetCurrentSnapshotSetId(
        )
    {
        VSS_ID __ret;
        return *GetCurrentSnapshotSetId(&__ret);
    }
#else
    virtual VSS_ID STDMETHODCALLTYPE GetCurrentSnapshotSetId(
        ) = 0;
#endif

    virtual LONG STDMETHODCALLTYPE GetContext(
        ) = 0;

    virtual VSS_APPLICATION_LEVEL STDMETHODCALLTYPE GetCurrentLevel(
        ) = 0;

    virtual boolean STDMETHODCALLTYPE IsPathAffected(
        LPCWSTR wszPath) = 0;

    virtual boolean STDMETHODCALLTYPE IsBootableSystemStateBackedUp(
        ) = 0;

    virtual boolean STDMETHODCALLTYPE AreComponentsSelected(
        ) = 0;

    virtual VSS_BACKUP_TYPE STDMETHODCALLTYPE GetBackupType(
        ) = 0;

    virtual VSS_RESTORE_TYPE STDMETHODCALLTYPE GetRestoreType(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetWriterFailure(
        HRESULT hr) = 0;

    virtual boolean STDMETHODCALLTYPE IsPartialFileSupportEnabled(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE InstallAlternateWriter(
        VSS_ID idWriter,
        CLSID clsid) = 0;

    virtual IVssExamineWriterMetadata * STDMETHODCALLTYPE GetIdentityInformation(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetWriterFailureEx(
        HRESULT hr,
        HRESULT hrApplication,
        LPCWSTR wszApplicationMessage) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSessionId(
        VSS_ID *idSession) = 0;

    virtual boolean STDMETHODCALLTYPE IsWriterShuttingDown(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssWriterImpl, 0x00000000, 0x0000, 0x0000, 0x00,0x00, 0x00,0x00,0x00,0x00,0x00,0x00)
#endif
#else
typedef struct IVssWriterImplVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssWriterImpl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssWriterImpl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssWriterImpl *This);

    /*** IVssWriterImpl methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IVssWriterImpl *This,
        VSS_ID writerId,
        LPCWSTR wszWriterName,
        LPCWSTR wszWriterInstanceName,
        DWORD dwMajorVersion,
        DWORD dwMinorVersion,
        VSS_USAGE_TYPE ut,
        VSS_SOURCE_TYPE st,
        VSS_APPLICATION_LEVEL nLevel,
        DWORD dwTimeout,
        VSS_ALTERNATE_WRITER_STATE aws,
        boolean bIOThrottlingOnly);

    HRESULT (STDMETHODCALLTYPE *Subscribe)(
        IVssWriterImpl *This,
        DWORD dwSubscribeTimeout,
        DWORD dwEventFlags);

    HRESULT (STDMETHODCALLTYPE *Unsubscribe)(
        IVssWriterImpl *This);

    void (STDMETHODCALLTYPE *Uninitialize)(
        IVssWriterImpl *This);

    LPCWSTR * (STDMETHODCALLTYPE *GetCurrentVolumeArray)(
        IVssWriterImpl *This);

    UINT (STDMETHODCALLTYPE *GetCurrentVolumeCount)(
        IVssWriterImpl *This);

    HRESULT (STDMETHODCALLTYPE *GetSnapshotDeviceName)(
        IVssWriterImpl *This,
        LPCWSTR wszOriginalVolume,
        LPCWSTR *ppwszSnapshotDevice);

    VSS_ID * (STDMETHODCALLTYPE *GetCurrentSnapshotSetId)(
        IVssWriterImpl *This,
        VSS_ID *__ret);

    LONG (STDMETHODCALLTYPE *GetContext)(
        IVssWriterImpl *This);

    VSS_APPLICATION_LEVEL (STDMETHODCALLTYPE *GetCurrentLevel)(
        IVssWriterImpl *This);

    boolean (STDMETHODCALLTYPE *IsPathAffected)(
        IVssWriterImpl *This,
        LPCWSTR wszPath);

    boolean (STDMETHODCALLTYPE *IsBootableSystemStateBackedUp)(
        IVssWriterImpl *This);

    boolean (STDMETHODCALLTYPE *AreComponentsSelected)(
        IVssWriterImpl *This);

    VSS_BACKUP_TYPE (STDMETHODCALLTYPE *GetBackupType)(
        IVssWriterImpl *This);

    VSS_RESTORE_TYPE (STDMETHODCALLTYPE *GetRestoreType)(
        IVssWriterImpl *This);

    HRESULT (STDMETHODCALLTYPE *SetWriterFailure)(
        IVssWriterImpl *This,
        HRESULT hr);

    boolean (STDMETHODCALLTYPE *IsPartialFileSupportEnabled)(
        IVssWriterImpl *This);

    HRESULT (STDMETHODCALLTYPE *InstallAlternateWriter)(
        IVssWriterImpl *This,
        VSS_ID idWriter,
        CLSID clsid);

    IVssExamineWriterMetadata * (STDMETHODCALLTYPE *GetIdentityInformation)(
        IVssWriterImpl *This);

    HRESULT (STDMETHODCALLTYPE *SetWriterFailureEx)(
        IVssWriterImpl *This,
        HRESULT hr,
        HRESULT hrApplication,
        LPCWSTR wszApplicationMessage);

    HRESULT (STDMETHODCALLTYPE *GetSessionId)(
        IVssWriterImpl *This,
        VSS_ID *idSession);

    boolean (STDMETHODCALLTYPE *IsWriterShuttingDown)(
        IVssWriterImpl *This);

    END_INTERFACE
} IVssWriterImplVtbl;

interface IVssWriterImpl {
    CONST_VTBL IVssWriterImplVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssWriterImpl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssWriterImpl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssWriterImpl_Release(This) (This)->lpVtbl->Release(This)
/*** IVssWriterImpl methods ***/
#define IVssWriterImpl_Initialize(This,writerId,wszWriterName,wszWriterInstanceName,dwMajorVersion,dwMinorVersion,ut,st,nLevel,dwTimeout,aws,bIOThrottlingOnly) (This)->lpVtbl->Initialize(This,writerId,wszWriterName,wszWriterInstanceName,dwMajorVersion,dwMinorVersion,ut,st,nLevel,dwTimeout,aws,bIOThrottlingOnly)
#define IVssWriterImpl_Subscribe(This,dwSubscribeTimeout,dwEventFlags) (This)->lpVtbl->Subscribe(This,dwSubscribeTimeout,dwEventFlags)
#define IVssWriterImpl_Unsubscribe(This) (This)->lpVtbl->Unsubscribe(This)
#define IVssWriterImpl_Uninitialize(This) (This)->lpVtbl->Uninitialize(This)
#define IVssWriterImpl_GetCurrentVolumeArray(This) (This)->lpVtbl->GetCurrentVolumeArray(This)
#define IVssWriterImpl_GetCurrentVolumeCount(This) (This)->lpVtbl->GetCurrentVolumeCount(This)
#define IVssWriterImpl_GetSnapshotDeviceName(This,wszOriginalVolume,ppwszSnapshotDevice) (This)->lpVtbl->GetSnapshotDeviceName(This,wszOriginalVolume,ppwszSnapshotDevice)
#define IVssWriterImpl_GetCurrentSnapshotSetId(This) IVssWriterImpl_GetCurrentSnapshotSetId_define_WIDL_C_INLINE_WRAPPERS_for_aggregate_return_support
#define IVssWriterImpl_GetContext(This) (This)->lpVtbl->GetContext(This)
#define IVssWriterImpl_GetCurrentLevel(This) (This)->lpVtbl->GetCurrentLevel(This)
#define IVssWriterImpl_IsPathAffected(This,wszPath) (This)->lpVtbl->IsPathAffected(This,wszPath)
#define IVssWriterImpl_IsBootableSystemStateBackedUp(This) (This)->lpVtbl->IsBootableSystemStateBackedUp(This)
#define IVssWriterImpl_AreComponentsSelected(This) (This)->lpVtbl->AreComponentsSelected(This)
#define IVssWriterImpl_GetBackupType(This) (This)->lpVtbl->GetBackupType(This)
#define IVssWriterImpl_GetRestoreType(This) (This)->lpVtbl->GetRestoreType(This)
#define IVssWriterImpl_SetWriterFailure(This,hr) (This)->lpVtbl->SetWriterFailure(This,hr)
#define IVssWriterImpl_IsPartialFileSupportEnabled(This) (This)->lpVtbl->IsPartialFileSupportEnabled(This)
#define IVssWriterImpl_InstallAlternateWriter(This,idWriter,clsid) (This)->lpVtbl->InstallAlternateWriter(This,idWriter,clsid)
#define IVssWriterImpl_GetIdentityInformation(This) (This)->lpVtbl->GetIdentityInformation(This)
#define IVssWriterImpl_SetWriterFailureEx(This,hr,hrApplication,wszApplicationMessage) (This)->lpVtbl->SetWriterFailureEx(This,hr,hrApplication,wszApplicationMessage)
#define IVssWriterImpl_GetSessionId(This,idSession) (This)->lpVtbl->GetSessionId(This,idSession)
#define IVssWriterImpl_IsWriterShuttingDown(This) (This)->lpVtbl->IsWriterShuttingDown(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssWriterImpl_QueryInterface(IVssWriterImpl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssWriterImpl_AddRef(IVssWriterImpl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssWriterImpl_Release(IVssWriterImpl* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssWriterImpl methods ***/
static inline HRESULT IVssWriterImpl_Initialize(IVssWriterImpl* This,VSS_ID writerId,LPCWSTR wszWriterName,LPCWSTR wszWriterInstanceName,DWORD dwMajorVersion,DWORD dwMinorVersion,VSS_USAGE_TYPE ut,VSS_SOURCE_TYPE st,VSS_APPLICATION_LEVEL nLevel,DWORD dwTimeout,VSS_ALTERNATE_WRITER_STATE aws,boolean bIOThrottlingOnly) {
    return This->lpVtbl->Initialize(This,writerId,wszWriterName,wszWriterInstanceName,dwMajorVersion,dwMinorVersion,ut,st,nLevel,dwTimeout,aws,bIOThrottlingOnly);
}
static inline HRESULT IVssWriterImpl_Subscribe(IVssWriterImpl* This,DWORD dwSubscribeTimeout,DWORD dwEventFlags) {
    return This->lpVtbl->Subscribe(This,dwSubscribeTimeout,dwEventFlags);
}
static inline HRESULT IVssWriterImpl_Unsubscribe(IVssWriterImpl* This) {
    return This->lpVtbl->Unsubscribe(This);
}
static inline void IVssWriterImpl_Uninitialize(IVssWriterImpl* This) {
    This->lpVtbl->Uninitialize(This);
}
static inline LPCWSTR * IVssWriterImpl_GetCurrentVolumeArray(IVssWriterImpl* This) {
    return This->lpVtbl->GetCurrentVolumeArray(This);
}
static inline UINT IVssWriterImpl_GetCurrentVolumeCount(IVssWriterImpl* This) {
    return This->lpVtbl->GetCurrentVolumeCount(This);
}
static inline HRESULT IVssWriterImpl_GetSnapshotDeviceName(IVssWriterImpl* This,LPCWSTR wszOriginalVolume,LPCWSTR *ppwszSnapshotDevice) {
    return This->lpVtbl->GetSnapshotDeviceName(This,wszOriginalVolume,ppwszSnapshotDevice);
}
static inline VSS_ID IVssWriterImpl_GetCurrentSnapshotSetId(IVssWriterImpl* This) {
    VSS_ID __ret;
    return *This->lpVtbl->GetCurrentSnapshotSetId(This,&__ret);
}
static inline LONG IVssWriterImpl_GetContext(IVssWriterImpl* This) {
    return This->lpVtbl->GetContext(This);
}
static inline VSS_APPLICATION_LEVEL IVssWriterImpl_GetCurrentLevel(IVssWriterImpl* This) {
    return This->lpVtbl->GetCurrentLevel(This);
}
static inline boolean IVssWriterImpl_IsPathAffected(IVssWriterImpl* This,LPCWSTR wszPath) {
    return This->lpVtbl->IsPathAffected(This,wszPath);
}
static inline boolean IVssWriterImpl_IsBootableSystemStateBackedUp(IVssWriterImpl* This) {
    return This->lpVtbl->IsBootableSystemStateBackedUp(This);
}
static inline boolean IVssWriterImpl_AreComponentsSelected(IVssWriterImpl* This) {
    return This->lpVtbl->AreComponentsSelected(This);
}
static inline VSS_BACKUP_TYPE IVssWriterImpl_GetBackupType(IVssWriterImpl* This) {
    return This->lpVtbl->GetBackupType(This);
}
static inline VSS_RESTORE_TYPE IVssWriterImpl_GetRestoreType(IVssWriterImpl* This) {
    return This->lpVtbl->GetRestoreType(This);
}
static inline HRESULT IVssWriterImpl_SetWriterFailure(IVssWriterImpl* This,HRESULT hr) {
    return This->lpVtbl->SetWriterFailure(This,hr);
}
static inline boolean IVssWriterImpl_IsPartialFileSupportEnabled(IVssWriterImpl* This) {
    return This->lpVtbl->IsPartialFileSupportEnabled(This);
}
static inline HRESULT IVssWriterImpl_InstallAlternateWriter(IVssWriterImpl* This,VSS_ID idWriter,CLSID clsid) {
    return This->lpVtbl->InstallAlternateWriter(This,idWriter,clsid);
}
static inline IVssExamineWriterMetadata * IVssWriterImpl_GetIdentityInformation(IVssWriterImpl* This) {
    return This->lpVtbl->GetIdentityInformation(This);
}
static inline HRESULT IVssWriterImpl_SetWriterFailureEx(IVssWriterImpl* This,HRESULT hr,HRESULT hrApplication,LPCWSTR wszApplicationMessage) {
    return This->lpVtbl->SetWriterFailureEx(This,hr,hrApplication,wszApplicationMessage);
}
static inline HRESULT IVssWriterImpl_GetSessionId(IVssWriterImpl* This,VSS_ID *idSession) {
    return This->lpVtbl->GetSessionId(This,idSession);
}
static inline boolean IVssWriterImpl_IsWriterShuttingDown(IVssWriterImpl* This) {
    return This->lpVtbl->IsWriterShuttingDown(This);
}
#endif
#endif

#endif


#endif  /* __IVssWriterImpl_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssCreateExpressWriterMetadata interface
 */
#ifndef __IVssCreateExpressWriterMetadata_INTERFACE_DEFINED__
#define __IVssCreateExpressWriterMetadata_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssCreateExpressWriterMetadata, 0x9c772e77, 0xb26e, 0x427f, 0x92,0xdd, 0xc9,0x96,0xf4,0x1e,0xa5,0xe3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9c772e77-b26e-427f-92dd-c996f41ea5e3")
IVssCreateExpressWriterMetadata : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddExcludeFiles(
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddComponent(
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszCaption,
        const BYTE *pbIcon,
        UINT cbIcon,
        boolean bRestoreMetadata,
        boolean bNotifyOnBackupComplete,
        boolean bSelectable,
        boolean bSelectableForRestore = 0,
        DWORD dwComponentFlags = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddFilesToFileGroup(
        LPCWSTR wszLogicalPath,
        LPCWSTR wszGroupName,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive,
        LPCWSTR wszAlternateLocation,
        DWORD dwBackupTypeMask = VSS_FSBT_ALL_BACKUP_REQUIRED | VSS_FSBT_ALL_SNAPSHOT_REQUIRED) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRestoreMethod(
        VSS_RESTOREMETHOD_ENUM method,
        LPCWSTR wszService,
        LPCWSTR wszUserProcedure,
        VSS_WRITERRESTORE_ENUM writerRestore,
        boolean bRebootRequired) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddComponentDependency(
        LPCWSTR wszForLogicalPath,
        LPCWSTR wszForComponentName,
        VSS_ID onWriterId,
        LPCWSTR wszOnLogicalPath,
        LPCWSTR wszOnComponentName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBackupSchema(
        DWORD dwSchemaMask) = 0;

    virtual HRESULT STDMETHODCALLTYPE SaveAsXML(
        BSTR *pbstrXML) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssCreateExpressWriterMetadata, 0x9c772e77, 0xb26e, 0x427f, 0x92,0xdd, 0xc9,0x96,0xf4,0x1e,0xa5,0xe3)
#endif
#else
typedef struct IVssCreateExpressWriterMetadataVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssCreateExpressWriterMetadata *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssCreateExpressWriterMetadata *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssCreateExpressWriterMetadata *This);

    /*** IVssCreateExpressWriterMetadata methods ***/
    HRESULT (STDMETHODCALLTYPE *AddExcludeFiles)(
        IVssCreateExpressWriterMetadata *This,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive);

    HRESULT (STDMETHODCALLTYPE *AddComponent)(
        IVssCreateExpressWriterMetadata *This,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszCaption,
        const BYTE *pbIcon,
        UINT cbIcon,
        boolean bRestoreMetadata,
        boolean bNotifyOnBackupComplete,
        boolean bSelectable,
        boolean bSelectableForRestore,
        DWORD dwComponentFlags);

    HRESULT (STDMETHODCALLTYPE *AddFilesToFileGroup)(
        IVssCreateExpressWriterMetadata *This,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszGroupName,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive,
        LPCWSTR wszAlternateLocation,
        DWORD dwBackupTypeMask);

    HRESULT (STDMETHODCALLTYPE *SetRestoreMethod)(
        IVssCreateExpressWriterMetadata *This,
        VSS_RESTOREMETHOD_ENUM method,
        LPCWSTR wszService,
        LPCWSTR wszUserProcedure,
        VSS_WRITERRESTORE_ENUM writerRestore,
        boolean bRebootRequired);

    HRESULT (STDMETHODCALLTYPE *AddComponentDependency)(
        IVssCreateExpressWriterMetadata *This,
        LPCWSTR wszForLogicalPath,
        LPCWSTR wszForComponentName,
        VSS_ID onWriterId,
        LPCWSTR wszOnLogicalPath,
        LPCWSTR wszOnComponentName);

    HRESULT (STDMETHODCALLTYPE *SetBackupSchema)(
        IVssCreateExpressWriterMetadata *This,
        DWORD dwSchemaMask);

    HRESULT (STDMETHODCALLTYPE *SaveAsXML)(
        IVssCreateExpressWriterMetadata *This,
        BSTR *pbstrXML);

    END_INTERFACE
} IVssCreateExpressWriterMetadataVtbl;

interface IVssCreateExpressWriterMetadata {
    CONST_VTBL IVssCreateExpressWriterMetadataVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssCreateExpressWriterMetadata_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssCreateExpressWriterMetadata_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssCreateExpressWriterMetadata_Release(This) (This)->lpVtbl->Release(This)
/*** IVssCreateExpressWriterMetadata methods ***/
#define IVssCreateExpressWriterMetadata_AddExcludeFiles(This,wszPath,wszFilespec,bRecursive) (This)->lpVtbl->AddExcludeFiles(This,wszPath,wszFilespec,bRecursive)
#define IVssCreateExpressWriterMetadata_AddComponent(This,ct,wszLogicalPath,wszComponentName,wszCaption,pbIcon,cbIcon,bRestoreMetadata,bNotifyOnBackupComplete,bSelectable,bSelectableForRestore,dwComponentFlags) (This)->lpVtbl->AddComponent(This,ct,wszLogicalPath,wszComponentName,wszCaption,pbIcon,cbIcon,bRestoreMetadata,bNotifyOnBackupComplete,bSelectable,bSelectableForRestore,dwComponentFlags)
#define IVssCreateExpressWriterMetadata_AddFilesToFileGroup(This,wszLogicalPath,wszGroupName,wszPath,wszFilespec,bRecursive,wszAlternateLocation,dwBackupTypeMask) (This)->lpVtbl->AddFilesToFileGroup(This,wszLogicalPath,wszGroupName,wszPath,wszFilespec,bRecursive,wszAlternateLocation,dwBackupTypeMask)
#define IVssCreateExpressWriterMetadata_SetRestoreMethod(This,method,wszService,wszUserProcedure,writerRestore,bRebootRequired) (This)->lpVtbl->SetRestoreMethod(This,method,wszService,wszUserProcedure,writerRestore,bRebootRequired)
#define IVssCreateExpressWriterMetadata_AddComponentDependency(This,wszForLogicalPath,wszForComponentName,onWriterId,wszOnLogicalPath,wszOnComponentName) (This)->lpVtbl->AddComponentDependency(This,wszForLogicalPath,wszForComponentName,onWriterId,wszOnLogicalPath,wszOnComponentName)
#define IVssCreateExpressWriterMetadata_SetBackupSchema(This,dwSchemaMask) (This)->lpVtbl->SetBackupSchema(This,dwSchemaMask)
#define IVssCreateExpressWriterMetadata_SaveAsXML(This,pbstrXML) (This)->lpVtbl->SaveAsXML(This,pbstrXML)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssCreateExpressWriterMetadata_QueryInterface(IVssCreateExpressWriterMetadata* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssCreateExpressWriterMetadata_AddRef(IVssCreateExpressWriterMetadata* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssCreateExpressWriterMetadata_Release(IVssCreateExpressWriterMetadata* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssCreateExpressWriterMetadata methods ***/
static inline HRESULT IVssCreateExpressWriterMetadata_AddExcludeFiles(IVssCreateExpressWriterMetadata* This,LPCWSTR wszPath,LPCWSTR wszFilespec,boolean bRecursive) {
    return This->lpVtbl->AddExcludeFiles(This,wszPath,wszFilespec,bRecursive);
}
static inline HRESULT IVssCreateExpressWriterMetadata_AddComponent(IVssCreateExpressWriterMetadata* This,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszCaption,const BYTE *pbIcon,UINT cbIcon,boolean bRestoreMetadata,boolean bNotifyOnBackupComplete,boolean bSelectable,boolean bSelectableForRestore,DWORD dwComponentFlags) {
    return This->lpVtbl->AddComponent(This,ct,wszLogicalPath,wszComponentName,wszCaption,pbIcon,cbIcon,bRestoreMetadata,bNotifyOnBackupComplete,bSelectable,bSelectableForRestore,dwComponentFlags);
}
static inline HRESULT IVssCreateExpressWriterMetadata_AddFilesToFileGroup(IVssCreateExpressWriterMetadata* This,LPCWSTR wszLogicalPath,LPCWSTR wszGroupName,LPCWSTR wszPath,LPCWSTR wszFilespec,boolean bRecursive,LPCWSTR wszAlternateLocation,DWORD dwBackupTypeMask) {
    return This->lpVtbl->AddFilesToFileGroup(This,wszLogicalPath,wszGroupName,wszPath,wszFilespec,bRecursive,wszAlternateLocation,dwBackupTypeMask);
}
static inline HRESULT IVssCreateExpressWriterMetadata_SetRestoreMethod(IVssCreateExpressWriterMetadata* This,VSS_RESTOREMETHOD_ENUM method,LPCWSTR wszService,LPCWSTR wszUserProcedure,VSS_WRITERRESTORE_ENUM writerRestore,boolean bRebootRequired) {
    return This->lpVtbl->SetRestoreMethod(This,method,wszService,wszUserProcedure,writerRestore,bRebootRequired);
}
static inline HRESULT IVssCreateExpressWriterMetadata_AddComponentDependency(IVssCreateExpressWriterMetadata* This,LPCWSTR wszForLogicalPath,LPCWSTR wszForComponentName,VSS_ID onWriterId,LPCWSTR wszOnLogicalPath,LPCWSTR wszOnComponentName) {
    return This->lpVtbl->AddComponentDependency(This,wszForLogicalPath,wszForComponentName,onWriterId,wszOnLogicalPath,wszOnComponentName);
}
static inline HRESULT IVssCreateExpressWriterMetadata_SetBackupSchema(IVssCreateExpressWriterMetadata* This,DWORD dwSchemaMask) {
    return This->lpVtbl->SetBackupSchema(This,dwSchemaMask);
}
static inline HRESULT IVssCreateExpressWriterMetadata_SaveAsXML(IVssCreateExpressWriterMetadata* This,BSTR *pbstrXML) {
    return This->lpVtbl->SaveAsXML(This,pbstrXML);
}
#endif
#endif

#endif


#endif  /* __IVssCreateExpressWriterMetadata_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssExpressWriter interface
 */
#ifndef __IVssExpressWriter_INTERFACE_DEFINED__
#define __IVssExpressWriter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssExpressWriter, 0xe33affdc, 0x59c7, 0x47b1, 0x97,0xd5, 0x42,0x66,0x59,0x8f,0x62,0x35);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e33affdc-59c7-47b1-97d5-4266598f6235")
IVssExpressWriter : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateMetadata(
        VSS_ID writerId,
        LPCWSTR writerName,
        VSS_USAGE_TYPE usageType,
        DWORD versionMajor,
        DWORD versionMinor,
        DWORD reserved,
        IVssCreateExpressWriterMetadata **ppMetadata) = 0;

    virtual HRESULT STDMETHODCALLTYPE LoadMetadata(
        LPCWSTR metadata,
        DWORD reserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE Register(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unregister(
        VSS_ID writerId) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssExpressWriter, 0xe33affdc, 0x59c7, 0x47b1, 0x97,0xd5, 0x42,0x66,0x59,0x8f,0x62,0x35)
#endif
#else
typedef struct IVssExpressWriterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssExpressWriter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssExpressWriter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssExpressWriter *This);

    /*** IVssExpressWriter methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateMetadata)(
        IVssExpressWriter *This,
        VSS_ID writerId,
        LPCWSTR writerName,
        VSS_USAGE_TYPE usageType,
        DWORD versionMajor,
        DWORD versionMinor,
        DWORD reserved,
        IVssCreateExpressWriterMetadata **ppMetadata);

    HRESULT (STDMETHODCALLTYPE *LoadMetadata)(
        IVssExpressWriter *This,
        LPCWSTR metadata,
        DWORD reserved);

    HRESULT (STDMETHODCALLTYPE *Register)(
        IVssExpressWriter *This);

    HRESULT (STDMETHODCALLTYPE *Unregister)(
        IVssExpressWriter *This,
        VSS_ID writerId);

    END_INTERFACE
} IVssExpressWriterVtbl;

interface IVssExpressWriter {
    CONST_VTBL IVssExpressWriterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssExpressWriter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssExpressWriter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssExpressWriter_Release(This) (This)->lpVtbl->Release(This)
/*** IVssExpressWriter methods ***/
#define IVssExpressWriter_CreateMetadata(This,writerId,writerName,usageType,versionMajor,versionMinor,reserved,ppMetadata) (This)->lpVtbl->CreateMetadata(This,writerId,writerName,usageType,versionMajor,versionMinor,reserved,ppMetadata)
#define IVssExpressWriter_LoadMetadata(This,metadata,reserved) (This)->lpVtbl->LoadMetadata(This,metadata,reserved)
#define IVssExpressWriter_Register(This) (This)->lpVtbl->Register(This)
#define IVssExpressWriter_Unregister(This,writerId) (This)->lpVtbl->Unregister(This,writerId)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssExpressWriter_QueryInterface(IVssExpressWriter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssExpressWriter_AddRef(IVssExpressWriter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssExpressWriter_Release(IVssExpressWriter* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssExpressWriter methods ***/
static inline HRESULT IVssExpressWriter_CreateMetadata(IVssExpressWriter* This,VSS_ID writerId,LPCWSTR writerName,VSS_USAGE_TYPE usageType,DWORD versionMajor,DWORD versionMinor,DWORD reserved,IVssCreateExpressWriterMetadata **ppMetadata) {
    return This->lpVtbl->CreateMetadata(This,writerId,writerName,usageType,versionMajor,versionMinor,reserved,ppMetadata);
}
static inline HRESULT IVssExpressWriter_LoadMetadata(IVssExpressWriter* This,LPCWSTR metadata,DWORD reserved) {
    return This->lpVtbl->LoadMetadata(This,metadata,reserved);
}
static inline HRESULT IVssExpressWriter_Register(IVssExpressWriter* This) {
    return This->lpVtbl->Register(This);
}
static inline HRESULT IVssExpressWriter_Unregister(IVssExpressWriter* This,VSS_ID writerId) {
    return This->lpVtbl->Unregister(This,writerId);
}
#endif
#endif

#endif


#endif  /* __IVssExpressWriter_INTERFACE_DEFINED__ */

#endif /* WINAPI_PARTITION_DESKTOP */
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __vswriter_h__ */
