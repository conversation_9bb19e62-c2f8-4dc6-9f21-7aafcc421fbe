/*** Autogenerated by WIDL 10.8 from include/windows.ui.input.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_ui_input_h__
#define __windows_ui_input_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CUI_CInput_CIPointerPoint_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CInput_CIPointerPoint_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CInput_CIPointerPoint __x_ABI_CWindows_CUI_CInput_CIPointerPoint;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CInput_CIPointerPoint ABI::Windows::UI::Input::IPointerPoint
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                interface IPointerPoint;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties ABI::Windows::UI::Input::IPointerPointProperties
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                interface IPointerPointProperties;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2 __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2 ABI::Windows::UI::Input::IPointerPointProperties2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                interface IPointerPointProperties2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics ABI::Windows::UI::Input::IPointerPointStatics
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                interface IPointerPointStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform ABI::Windows::UI::Input::IPointerPointTransform
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                interface IPointerPointTransform;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CInput_CPointerPoint_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CInput_CPointerPoint_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                class PointerPoint;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CInput_CPointerPoint __x_ABI_CWindows_CUI_CInput_CPointerPoint;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CInput_CPointerPoint_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CInput_CPointerPointProperties_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CInput_CPointerPointProperties_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                class PointerPointProperties;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CInput_CPointerPointProperties __x_ABI_CWindows_CUI_CInput_CPointerPointProperties;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CInput_CPointerPointProperties_FWD_DEFINED__ */

#ifndef ____FIVectorView_1_Windows__CUI__CInput__CPointerPoint_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CUI__CInput__CPointerPoint_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CUI__CInput__CPointerPoint __FIVectorView_1_Windows__CUI__CInput__CPointerPoint;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::UI::Input::PointerPoint* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CUI__CInput__CPointerPoint_FWD_DEFINED__
#define ____FIVector_1_Windows__CUI__CInput__CPointerPoint_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CUI__CInput__CPointerPoint __FIVector_1_Windows__CUI__CInput__CPointerPoint;
#ifdef __cplusplus
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint ABI::Windows::Foundation::Collections::IVector<ABI::Windows::UI::Input::PointerPoint* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.devices.input.h>
#include <windows.system.h>
#include <windows.ui.core.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CUI_CInput_CPointerUpdateKind __x_ABI_CWindows_CUI_CInput_CPointerUpdateKind;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CUI_CInput_CIPointerPoint_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CInput_CIPointerPoint_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CInput_CIPointerPoint __x_ABI_CWindows_CUI_CInput_CIPointerPoint;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CInput_CIPointerPoint ABI::Windows::UI::Input::IPointerPoint
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                interface IPointerPoint;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties ABI::Windows::UI::Input::IPointerPointProperties
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                interface IPointerPointProperties;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2 __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2 ABI::Windows::UI::Input::IPointerPointProperties2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                interface IPointerPointProperties2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics ABI::Windows::UI::Input::IPointerPointStatics
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                interface IPointerPointStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform ABI::Windows::UI::Input::IPointerPointTransform
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                interface IPointerPointTransform;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CUI__CInput__CPointerPoint_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CUI__CInput__CPointerPoint_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CUI__CInput__CPointerPoint __FIVectorView_1_Windows__CUI__CInput__CPointerPoint;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::UI::Input::PointerPoint* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CUI__CInput__CPointerPoint_FWD_DEFINED__
#define ____FIVector_1_Windows__CUI__CInput__CPointerPoint_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CUI__CInput__CPointerPoint __FIVector_1_Windows__CUI__CInput__CPointerPoint;
#ifdef __cplusplus
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint ABI::Windows::Foundation::Collections::IVector<ABI::Windows::UI::Input::PointerPoint* >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                enum PointerUpdateKind {
                    PointerUpdateKind_Other = 0,
                    PointerUpdateKind_LeftButtonPressed = 1,
                    PointerUpdateKind_LeftButtonReleased = 2,
                    PointerUpdateKind_RightButtonPressed = 3,
                    PointerUpdateKind_RightButtonReleased = 4,
                    PointerUpdateKind_MiddleButtonPressed = 5,
                    PointerUpdateKind_MiddleButtonReleased = 6,
                    PointerUpdateKind_XButton1Pressed = 7,
                    PointerUpdateKind_XButton1Released = 8,
                    PointerUpdateKind_XButton2Pressed = 9,
                    PointerUpdateKind_XButton2Released = 10
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CUI_CInput_CPointerUpdateKind {
    PointerUpdateKind_Other = 0,
    PointerUpdateKind_LeftButtonPressed = 1,
    PointerUpdateKind_LeftButtonReleased = 2,
    PointerUpdateKind_RightButtonPressed = 3,
    PointerUpdateKind_RightButtonReleased = 4,
    PointerUpdateKind_MiddleButtonPressed = 5,
    PointerUpdateKind_MiddleButtonReleased = 6,
    PointerUpdateKind_XButton1Pressed = 7,
    PointerUpdateKind_XButton1Released = 8,
    PointerUpdateKind_XButton2Pressed = 9,
    PointerUpdateKind_XButton2Released = 10
};
#ifdef WIDL_using_Windows_UI_Input
#define PointerUpdateKind __x_ABI_CWindows_CUI_CInput_CPointerUpdateKind
#endif /* WIDL_using_Windows_UI_Input */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
/*****************************************************************************
 * IPointerPoint interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CInput_CIPointerPoint_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CInput_CIPointerPoint_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CInput_CIPointerPoint, 0xe995317d, 0x7296, 0x42d9, 0x82,0x33, 0xc5,0xbe,0x73,0xb7,0x4a,0x4a);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                MIDL_INTERFACE("e995317d-7296-42d9-8233-c5be73b74a4a")
                IPointerPoint : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_PointerDevice(
                        ABI::Windows::Devices::Input::IPointerDevice **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Position(
                        ABI::Windows::Foundation::Point *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_RawPosition(
                        ABI::Windows::Foundation::Point *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_pointer_id(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_FrameId(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Timestamp(
                        UINT64 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsInContact(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Properties(
                        ABI::Windows::UI::Input::IPointerPointProperties **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CInput_CIPointerPoint, 0xe995317d, 0x7296, 0x42d9, 0x82,0x33, 0xc5,0xbe,0x73,0xb7,0x4a,0x4a)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CInput_CIPointerPointVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *This,
        TrustLevel *trustLevel);

    /*** IPointerPoint methods ***/
    HRESULT (STDMETHODCALLTYPE *get_PointerDevice)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *This,
        __x_ABI_CWindows_CDevices_CInput_CIPointerDevice **value);

    HRESULT (STDMETHODCALLTYPE *get_Position)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *This,
        __x_ABI_CWindows_CFoundation_CPoint *value);

    HRESULT (STDMETHODCALLTYPE *get_RawPosition)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *This,
        __x_ABI_CWindows_CFoundation_CPoint *value);

    HRESULT (STDMETHODCALLTYPE *get_pointer_id)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_FrameId)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_Timestamp)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *This,
        UINT64 *value);

    HRESULT (STDMETHODCALLTYPE *get_IsInContact)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_Properties)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *This,
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties **value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CInput_CIPointerPointVtbl;

interface __x_ABI_CWindows_CUI_CInput_CIPointerPoint {
    CONST_VTBL __x_ABI_CWindows_CUI_CInput_CIPointerPointVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CInput_CIPointerPoint_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPoint_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPoint_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CInput_CIPointerPoint_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPoint_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPoint_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPointerPoint methods ***/
#define __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_PointerDevice(This,value) (This)->lpVtbl->get_PointerDevice(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_Position(This,value) (This)->lpVtbl->get_Position(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_RawPosition(This,value) (This)->lpVtbl->get_RawPosition(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_pointer_id(This,value) (This)->lpVtbl->get_pointer_id(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_FrameId(This,value) (This)->lpVtbl->get_FrameId(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_Timestamp(This,value) (This)->lpVtbl->get_Timestamp(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_IsInContact(This,value) (This)->lpVtbl->get_IsInContact(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_Properties(This,value) (This)->lpVtbl->get_Properties(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPoint_QueryInterface(__x_ABI_CWindows_CUI_CInput_CIPointerPoint* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CInput_CIPointerPoint_AddRef(__x_ABI_CWindows_CUI_CInput_CIPointerPoint* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CInput_CIPointerPoint_Release(__x_ABI_CWindows_CUI_CInput_CIPointerPoint* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPoint_GetIids(__x_ABI_CWindows_CUI_CInput_CIPointerPoint* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPoint_GetRuntimeClassName(__x_ABI_CWindows_CUI_CInput_CIPointerPoint* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPoint_GetTrustLevel(__x_ABI_CWindows_CUI_CInput_CIPointerPoint* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPointerPoint methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_PointerDevice(__x_ABI_CWindows_CUI_CInput_CIPointerPoint* This,__x_ABI_CWindows_CDevices_CInput_CIPointerDevice **value) {
    return This->lpVtbl->get_PointerDevice(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_Position(__x_ABI_CWindows_CUI_CInput_CIPointerPoint* This,__x_ABI_CWindows_CFoundation_CPoint *value) {
    return This->lpVtbl->get_Position(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_RawPosition(__x_ABI_CWindows_CUI_CInput_CIPointerPoint* This,__x_ABI_CWindows_CFoundation_CPoint *value) {
    return This->lpVtbl->get_RawPosition(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_pointer_id(__x_ABI_CWindows_CUI_CInput_CIPointerPoint* This,UINT32 *value) {
    return This->lpVtbl->get_pointer_id(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_FrameId(__x_ABI_CWindows_CUI_CInput_CIPointerPoint* This,UINT32 *value) {
    return This->lpVtbl->get_FrameId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_Timestamp(__x_ABI_CWindows_CUI_CInput_CIPointerPoint* This,UINT64 *value) {
    return This->lpVtbl->get_Timestamp(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_IsInContact(__x_ABI_CWindows_CUI_CInput_CIPointerPoint* This,boolean *value) {
    return This->lpVtbl->get_IsInContact(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_Properties(__x_ABI_CWindows_CUI_CInput_CIPointerPoint* This,__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties **value) {
    return This->lpVtbl->get_Properties(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Input
#define IID_IPointerPoint IID___x_ABI_CWindows_CUI_CInput_CIPointerPoint
#define IPointerPointVtbl __x_ABI_CWindows_CUI_CInput_CIPointerPointVtbl
#define IPointerPoint __x_ABI_CWindows_CUI_CInput_CIPointerPoint
#define IPointerPoint_QueryInterface __x_ABI_CWindows_CUI_CInput_CIPointerPoint_QueryInterface
#define IPointerPoint_AddRef __x_ABI_CWindows_CUI_CInput_CIPointerPoint_AddRef
#define IPointerPoint_Release __x_ABI_CWindows_CUI_CInput_CIPointerPoint_Release
#define IPointerPoint_GetIids __x_ABI_CWindows_CUI_CInput_CIPointerPoint_GetIids
#define IPointerPoint_GetRuntimeClassName __x_ABI_CWindows_CUI_CInput_CIPointerPoint_GetRuntimeClassName
#define IPointerPoint_GetTrustLevel __x_ABI_CWindows_CUI_CInput_CIPointerPoint_GetTrustLevel
#define IPointerPoint_get_PointerDevice __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_PointerDevice
#define IPointerPoint_get_Position __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_Position
#define IPointerPoint_get_RawPosition __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_RawPosition
#define IPointerPoint_get_pointer_id __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_pointer_id
#define IPointerPoint_get_FrameId __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_FrameId
#define IPointerPoint_get_Timestamp __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_Timestamp
#define IPointerPoint_get_IsInContact __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_IsInContact
#define IPointerPoint_get_Properties __x_ABI_CWindows_CUI_CInput_CIPointerPoint_get_Properties
#endif /* WIDL_using_Windows_UI_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CInput_CIPointerPoint_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPointerPointProperties interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CInput_CIPointerPointProperties, 0xc79d8a4b, 0xc163, 0x4ee7, 0x80,0x3f, 0x67,0xce,0x79,0xf9,0x97,0x2d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                MIDL_INTERFACE("c79d8a4b-c163-4ee7-803f-67ce79f9972d")
                IPointerPointProperties : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Pressure(
                        FLOAT *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsInverted(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsEraser(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Orientation(
                        FLOAT *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_XTilt(
                        FLOAT *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_YTilt(
                        FLOAT *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Twist(
                        FLOAT *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ContactRect(
                        ABI::Windows::Foundation::Rect *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ContactRectRaw(
                        ABI::Windows::Foundation::Rect *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_TouchConfidence(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsLeftButtonPressed(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsRightButtonPressed(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsMiddleButtonPressed(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_MouseWheelDelta(
                        INT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsHorizontalMouseWheel(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsPrimary(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsInRange(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsCanceled(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsBarrelButtonPressed(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsXButton1Pressed(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsXButton2Pressed(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_PointerUpdateKind(
                        ABI::Windows::UI::Input::PointerUpdateKind *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE HasUsage(
                        UINT32 usage_page,
                        UINT32 usage_id,
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetUsageValue(
                        UINT32 usage_page,
                        UINT32 usage_id,
                        INT32 *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties, 0xc79d8a4b, 0xc163, 0x4ee7, 0x80,0x3f, 0x67,0xce,0x79,0xf9,0x97,0x2d)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CInput_CIPointerPointPropertiesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        TrustLevel *trustLevel);

    /*** IPointerPointProperties methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Pressure)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        FLOAT *value);

    HRESULT (STDMETHODCALLTYPE *get_IsInverted)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsEraser)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_Orientation)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        FLOAT *value);

    HRESULT (STDMETHODCALLTYPE *get_XTilt)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        FLOAT *value);

    HRESULT (STDMETHODCALLTYPE *get_YTilt)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        FLOAT *value);

    HRESULT (STDMETHODCALLTYPE *get_Twist)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        FLOAT *value);

    HRESULT (STDMETHODCALLTYPE *get_ContactRect)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        __x_ABI_CWindows_CFoundation_CRect *value);

    HRESULT (STDMETHODCALLTYPE *get_ContactRectRaw)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        __x_ABI_CWindows_CFoundation_CRect *value);

    HRESULT (STDMETHODCALLTYPE *get_TouchConfidence)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsLeftButtonPressed)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsRightButtonPressed)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsMiddleButtonPressed)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_MouseWheelDelta)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_IsHorizontalMouseWheel)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsPrimary)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsInRange)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsCanceled)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsBarrelButtonPressed)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsXButton1Pressed)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsXButton2Pressed)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_PointerUpdateKind)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        __x_ABI_CWindows_CUI_CInput_CPointerUpdateKind *value);

    HRESULT (STDMETHODCALLTYPE *HasUsage)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        UINT32 usage_page,
        UINT32 usage_id,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetUsageValue)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties *This,
        UINT32 usage_page,
        UINT32 usage_id,
        INT32 *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CInput_CIPointerPointPropertiesVtbl;

interface __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties {
    CONST_VTBL __x_ABI_CWindows_CUI_CInput_CIPointerPointPropertiesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPointerPointProperties methods ***/
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_Pressure(This,value) (This)->lpVtbl->get_Pressure(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsInverted(This,value) (This)->lpVtbl->get_IsInverted(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsEraser(This,value) (This)->lpVtbl->get_IsEraser(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_Orientation(This,value) (This)->lpVtbl->get_Orientation(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_XTilt(This,value) (This)->lpVtbl->get_XTilt(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_YTilt(This,value) (This)->lpVtbl->get_YTilt(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_Twist(This,value) (This)->lpVtbl->get_Twist(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_ContactRect(This,value) (This)->lpVtbl->get_ContactRect(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_ContactRectRaw(This,value) (This)->lpVtbl->get_ContactRectRaw(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_TouchConfidence(This,value) (This)->lpVtbl->get_TouchConfidence(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsLeftButtonPressed(This,value) (This)->lpVtbl->get_IsLeftButtonPressed(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsRightButtonPressed(This,value) (This)->lpVtbl->get_IsRightButtonPressed(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsMiddleButtonPressed(This,value) (This)->lpVtbl->get_IsMiddleButtonPressed(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_MouseWheelDelta(This,value) (This)->lpVtbl->get_MouseWheelDelta(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsHorizontalMouseWheel(This,value) (This)->lpVtbl->get_IsHorizontalMouseWheel(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsPrimary(This,value) (This)->lpVtbl->get_IsPrimary(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsInRange(This,value) (This)->lpVtbl->get_IsInRange(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsCanceled(This,value) (This)->lpVtbl->get_IsCanceled(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsBarrelButtonPressed(This,value) (This)->lpVtbl->get_IsBarrelButtonPressed(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsXButton1Pressed(This,value) (This)->lpVtbl->get_IsXButton1Pressed(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsXButton2Pressed(This,value) (This)->lpVtbl->get_IsXButton2Pressed(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_PointerUpdateKind(This,value) (This)->lpVtbl->get_PointerUpdateKind(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_HasUsage(This,usage_page,usage_id,value) (This)->lpVtbl->HasUsage(This,usage_page,usage_id,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_GetUsageValue(This,usage_page,usage_id,value) (This)->lpVtbl->GetUsageValue(This,usage_page,usage_id,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_QueryInterface(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_AddRef(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_Release(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_GetIids(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_GetRuntimeClassName(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_GetTrustLevel(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPointerPointProperties methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_Pressure(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,FLOAT *value) {
    return This->lpVtbl->get_Pressure(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsInverted(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,boolean *value) {
    return This->lpVtbl->get_IsInverted(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsEraser(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,boolean *value) {
    return This->lpVtbl->get_IsEraser(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_Orientation(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,FLOAT *value) {
    return This->lpVtbl->get_Orientation(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_XTilt(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,FLOAT *value) {
    return This->lpVtbl->get_XTilt(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_YTilt(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,FLOAT *value) {
    return This->lpVtbl->get_YTilt(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_Twist(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,FLOAT *value) {
    return This->lpVtbl->get_Twist(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_ContactRect(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,__x_ABI_CWindows_CFoundation_CRect *value) {
    return This->lpVtbl->get_ContactRect(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_ContactRectRaw(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,__x_ABI_CWindows_CFoundation_CRect *value) {
    return This->lpVtbl->get_ContactRectRaw(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_TouchConfidence(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,boolean *value) {
    return This->lpVtbl->get_TouchConfidence(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsLeftButtonPressed(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,boolean *value) {
    return This->lpVtbl->get_IsLeftButtonPressed(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsRightButtonPressed(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,boolean *value) {
    return This->lpVtbl->get_IsRightButtonPressed(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsMiddleButtonPressed(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,boolean *value) {
    return This->lpVtbl->get_IsMiddleButtonPressed(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_MouseWheelDelta(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,INT32 *value) {
    return This->lpVtbl->get_MouseWheelDelta(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsHorizontalMouseWheel(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,boolean *value) {
    return This->lpVtbl->get_IsHorizontalMouseWheel(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsPrimary(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,boolean *value) {
    return This->lpVtbl->get_IsPrimary(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsInRange(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,boolean *value) {
    return This->lpVtbl->get_IsInRange(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsCanceled(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,boolean *value) {
    return This->lpVtbl->get_IsCanceled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsBarrelButtonPressed(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,boolean *value) {
    return This->lpVtbl->get_IsBarrelButtonPressed(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsXButton1Pressed(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,boolean *value) {
    return This->lpVtbl->get_IsXButton1Pressed(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsXButton2Pressed(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,boolean *value) {
    return This->lpVtbl->get_IsXButton2Pressed(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_PointerUpdateKind(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,__x_ABI_CWindows_CUI_CInput_CPointerUpdateKind *value) {
    return This->lpVtbl->get_PointerUpdateKind(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_HasUsage(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,UINT32 usage_page,UINT32 usage_id,boolean *value) {
    return This->lpVtbl->HasUsage(This,usage_page,usage_id,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_GetUsageValue(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties* This,UINT32 usage_page,UINT32 usage_id,INT32 *value) {
    return This->lpVtbl->GetUsageValue(This,usage_page,usage_id,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Input
#define IID_IPointerPointProperties IID___x_ABI_CWindows_CUI_CInput_CIPointerPointProperties
#define IPointerPointPropertiesVtbl __x_ABI_CWindows_CUI_CInput_CIPointerPointPropertiesVtbl
#define IPointerPointProperties __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties
#define IPointerPointProperties_QueryInterface __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_QueryInterface
#define IPointerPointProperties_AddRef __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_AddRef
#define IPointerPointProperties_Release __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_Release
#define IPointerPointProperties_GetIids __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_GetIids
#define IPointerPointProperties_GetRuntimeClassName __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_GetRuntimeClassName
#define IPointerPointProperties_GetTrustLevel __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_GetTrustLevel
#define IPointerPointProperties_get_Pressure __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_Pressure
#define IPointerPointProperties_get_IsInverted __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsInverted
#define IPointerPointProperties_get_IsEraser __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsEraser
#define IPointerPointProperties_get_Orientation __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_Orientation
#define IPointerPointProperties_get_XTilt __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_XTilt
#define IPointerPointProperties_get_YTilt __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_YTilt
#define IPointerPointProperties_get_Twist __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_Twist
#define IPointerPointProperties_get_ContactRect __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_ContactRect
#define IPointerPointProperties_get_ContactRectRaw __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_ContactRectRaw
#define IPointerPointProperties_get_TouchConfidence __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_TouchConfidence
#define IPointerPointProperties_get_IsLeftButtonPressed __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsLeftButtonPressed
#define IPointerPointProperties_get_IsRightButtonPressed __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsRightButtonPressed
#define IPointerPointProperties_get_IsMiddleButtonPressed __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsMiddleButtonPressed
#define IPointerPointProperties_get_MouseWheelDelta __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_MouseWheelDelta
#define IPointerPointProperties_get_IsHorizontalMouseWheel __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsHorizontalMouseWheel
#define IPointerPointProperties_get_IsPrimary __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsPrimary
#define IPointerPointProperties_get_IsInRange __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsInRange
#define IPointerPointProperties_get_IsCanceled __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsCanceled
#define IPointerPointProperties_get_IsBarrelButtonPressed __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsBarrelButtonPressed
#define IPointerPointProperties_get_IsXButton1Pressed __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsXButton1Pressed
#define IPointerPointProperties_get_IsXButton2Pressed __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_IsXButton2Pressed
#define IPointerPointProperties_get_PointerUpdateKind __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_get_PointerUpdateKind
#define IPointerPointProperties_HasUsage __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_HasUsage
#define IPointerPointProperties_GetUsageValue __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_GetUsageValue
#endif /* WIDL_using_Windows_UI_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CInput_CIPointerPointProperties_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPointerPointProperties2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2, 0x22c3433a, 0xc83b, 0x41c0, 0xa2,0x96, 0x5e,0x23,0x2d,0x64,0xd6,0xaf);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                MIDL_INTERFACE("22c3433a-c83b-41c0-a296-5e232d64d6af")
                IPointerPointProperties2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_ZDistance(
                        ABI::Windows::Foundation::IReference<FLOAT > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2, 0x22c3433a, 0xc83b, 0x41c0, 0xa2,0x96, 0x5e,0x23,0x2d,0x64,0xd6,0xaf)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2 *This,
        TrustLevel *trustLevel);

    /*** IPointerPointProperties2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ZDistance)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2 *This,
        __FIReference_1_FLOAT **value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2Vtbl;

interface __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2 {
    CONST_VTBL __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPointerPointProperties2 methods ***/
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_get_ZDistance(This,value) (This)->lpVtbl->get_ZDistance(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_QueryInterface(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_AddRef(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_Release(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_GetIids(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_GetRuntimeClassName(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_GetTrustLevel(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPointerPointProperties2 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_get_ZDistance(__x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2* This,__FIReference_1_FLOAT **value) {
    return This->lpVtbl->get_ZDistance(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Input
#define IID_IPointerPointProperties2 IID___x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2
#define IPointerPointProperties2Vtbl __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2Vtbl
#define IPointerPointProperties2 __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2
#define IPointerPointProperties2_QueryInterface __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_QueryInterface
#define IPointerPointProperties2_AddRef __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_AddRef
#define IPointerPointProperties2_Release __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_Release
#define IPointerPointProperties2_GetIids __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_GetIids
#define IPointerPointProperties2_GetRuntimeClassName __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_GetRuntimeClassName
#define IPointerPointProperties2_GetTrustLevel __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_GetTrustLevel
#define IPointerPointProperties2_get_ZDistance __x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_get_ZDistance
#endif /* WIDL_using_Windows_UI_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CInput_CIPointerPointProperties2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPointerPointStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CInput_CIPointerPointStatics, 0xa506638d, 0x2a1a, 0x413e, 0xbc,0x75, 0x9f,0x38,0x38,0x1c,0xc0,0x69);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                MIDL_INTERFACE("a506638d-2a1a-413e-bc75-9f38381cc069")
                IPointerPointStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetCurrentPoint(
                        UINT32 pointer_id,
                        ABI::Windows::UI::Input::IPointerPoint **point) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetIntermediatePoints(
                        UINT32 pointer_id,
                        ABI::Windows::Foundation::Collections::IVector<ABI::Windows::UI::Input::PointerPoint* > **points) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetCurrentPointTransformed(
                        UINT32 pointer_id,
                        ABI::Windows::UI::Input::IPointerPointTransform *transform,
                        ABI::Windows::UI::Input::IPointerPoint **point) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetIntermediatePointsTransformed(
                        UINT32 pointer_id,
                        ABI::Windows::UI::Input::IPointerPointTransform *transform,
                        ABI::Windows::Foundation::Collections::IVector<ABI::Windows::UI::Input::PointerPoint* > **points) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CInput_CIPointerPointStatics, 0xa506638d, 0x2a1a, 0x413e, 0xbc,0x75, 0x9f,0x38,0x38,0x1c,0xc0,0x69)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CInput_CIPointerPointStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics *This,
        TrustLevel *trustLevel);

    /*** IPointerPointStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCurrentPoint)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics *This,
        UINT32 pointer_id,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint **point);

    HRESULT (STDMETHODCALLTYPE *GetIntermediatePoints)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics *This,
        UINT32 pointer_id,
        __FIVector_1_Windows__CUI__CInput__CPointerPoint **points);

    HRESULT (STDMETHODCALLTYPE *GetCurrentPointTransformed)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics *This,
        UINT32 pointer_id,
        __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform *transform,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint **point);

    HRESULT (STDMETHODCALLTYPE *GetIntermediatePointsTransformed)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics *This,
        UINT32 pointer_id,
        __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform *transform,
        __FIVector_1_Windows__CUI__CInput__CPointerPoint **points);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CInput_CIPointerPointStaticsVtbl;

interface __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics {
    CONST_VTBL __x_ABI_CWindows_CUI_CInput_CIPointerPointStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPointerPointStatics methods ***/
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetCurrentPoint(This,pointer_id,point) (This)->lpVtbl->GetCurrentPoint(This,pointer_id,point)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetIntermediatePoints(This,pointer_id,points) (This)->lpVtbl->GetIntermediatePoints(This,pointer_id,points)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetCurrentPointTransformed(This,pointer_id,transform,point) (This)->lpVtbl->GetCurrentPointTransformed(This,pointer_id,transform,point)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetIntermediatePointsTransformed(This,pointer_id,transform,points) (This)->lpVtbl->GetIntermediatePointsTransformed(This,pointer_id,transform,points)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_QueryInterface(__x_ABI_CWindows_CUI_CInput_CIPointerPointStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_AddRef(__x_ABI_CWindows_CUI_CInput_CIPointerPointStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_Release(__x_ABI_CWindows_CUI_CInput_CIPointerPointStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetIids(__x_ABI_CWindows_CUI_CInput_CIPointerPointStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetRuntimeClassName(__x_ABI_CWindows_CUI_CInput_CIPointerPointStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetTrustLevel(__x_ABI_CWindows_CUI_CInput_CIPointerPointStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPointerPointStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetCurrentPoint(__x_ABI_CWindows_CUI_CInput_CIPointerPointStatics* This,UINT32 pointer_id,__x_ABI_CWindows_CUI_CInput_CIPointerPoint **point) {
    return This->lpVtbl->GetCurrentPoint(This,pointer_id,point);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetIntermediatePoints(__x_ABI_CWindows_CUI_CInput_CIPointerPointStatics* This,UINT32 pointer_id,__FIVector_1_Windows__CUI__CInput__CPointerPoint **points) {
    return This->lpVtbl->GetIntermediatePoints(This,pointer_id,points);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetCurrentPointTransformed(__x_ABI_CWindows_CUI_CInput_CIPointerPointStatics* This,UINT32 pointer_id,__x_ABI_CWindows_CUI_CInput_CIPointerPointTransform *transform,__x_ABI_CWindows_CUI_CInput_CIPointerPoint **point) {
    return This->lpVtbl->GetCurrentPointTransformed(This,pointer_id,transform,point);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetIntermediatePointsTransformed(__x_ABI_CWindows_CUI_CInput_CIPointerPointStatics* This,UINT32 pointer_id,__x_ABI_CWindows_CUI_CInput_CIPointerPointTransform *transform,__FIVector_1_Windows__CUI__CInput__CPointerPoint **points) {
    return This->lpVtbl->GetIntermediatePointsTransformed(This,pointer_id,transform,points);
}
#endif
#ifdef WIDL_using_Windows_UI_Input
#define IID_IPointerPointStatics IID___x_ABI_CWindows_CUI_CInput_CIPointerPointStatics
#define IPointerPointStaticsVtbl __x_ABI_CWindows_CUI_CInput_CIPointerPointStaticsVtbl
#define IPointerPointStatics __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics
#define IPointerPointStatics_QueryInterface __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_QueryInterface
#define IPointerPointStatics_AddRef __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_AddRef
#define IPointerPointStatics_Release __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_Release
#define IPointerPointStatics_GetIids __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetIids
#define IPointerPointStatics_GetRuntimeClassName __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetRuntimeClassName
#define IPointerPointStatics_GetTrustLevel __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetTrustLevel
#define IPointerPointStatics_GetCurrentPoint __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetCurrentPoint
#define IPointerPointStatics_GetIntermediatePoints __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetIntermediatePoints
#define IPointerPointStatics_GetCurrentPointTransformed __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetCurrentPointTransformed
#define IPointerPointStatics_GetIntermediatePointsTransformed __x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_GetIntermediatePointsTransformed
#endif /* WIDL_using_Windows_UI_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CInput_CIPointerPointStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPointerPointTransform interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CInput_CIPointerPointTransform, 0x4d5fe14f, 0xb87c, 0x4028, 0xbc,0x9c, 0x59,0xe9,0x94,0x7f,0xb0,0x56);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Input {
                MIDL_INTERFACE("4d5fe14f-b87c-4028-bc9c-59e9947fb056")
                IPointerPointTransform : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Inverse(
                        ABI::Windows::UI::Input::IPointerPointTransform **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE TryTransform(
                        ABI::Windows::Foundation::Point in_point,
                        ABI::Windows::Foundation::Point *out_point,
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE TransformBounds(
                        ABI::Windows::Foundation::Rect rect,
                        ABI::Windows::Foundation::Rect *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CInput_CIPointerPointTransform, 0x4d5fe14f, 0xb87c, 0x4028, 0xbc,0x9c, 0x59,0xe9,0x94,0x7f,0xb0,0x56)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CInput_CIPointerPointTransformVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform *This,
        TrustLevel *trustLevel);

    /*** IPointerPointTransform methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Inverse)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform *This,
        __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform **value);

    HRESULT (STDMETHODCALLTYPE *TryTransform)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform *This,
        __x_ABI_CWindows_CFoundation_CPoint in_point,
        __x_ABI_CWindows_CFoundation_CPoint *out_point,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *TransformBounds)(
        __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform *This,
        __x_ABI_CWindows_CFoundation_CRect rect,
        __x_ABI_CWindows_CFoundation_CRect *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CInput_CIPointerPointTransformVtbl;

interface __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform {
    CONST_VTBL __x_ABI_CWindows_CUI_CInput_CIPointerPointTransformVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPointerPointTransform methods ***/
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_get_Inverse(This,value) (This)->lpVtbl->get_Inverse(This,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_TryTransform(This,in_point,out_point,value) (This)->lpVtbl->TryTransform(This,in_point,out_point,value)
#define __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_TransformBounds(This,rect,value) (This)->lpVtbl->TransformBounds(This,rect,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_QueryInterface(__x_ABI_CWindows_CUI_CInput_CIPointerPointTransform* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_AddRef(__x_ABI_CWindows_CUI_CInput_CIPointerPointTransform* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_Release(__x_ABI_CWindows_CUI_CInput_CIPointerPointTransform* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_GetIids(__x_ABI_CWindows_CUI_CInput_CIPointerPointTransform* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_GetRuntimeClassName(__x_ABI_CWindows_CUI_CInput_CIPointerPointTransform* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_GetTrustLevel(__x_ABI_CWindows_CUI_CInput_CIPointerPointTransform* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPointerPointTransform methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_get_Inverse(__x_ABI_CWindows_CUI_CInput_CIPointerPointTransform* This,__x_ABI_CWindows_CUI_CInput_CIPointerPointTransform **value) {
    return This->lpVtbl->get_Inverse(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_TryTransform(__x_ABI_CWindows_CUI_CInput_CIPointerPointTransform* This,__x_ABI_CWindows_CFoundation_CPoint in_point,__x_ABI_CWindows_CFoundation_CPoint *out_point,boolean *value) {
    return This->lpVtbl->TryTransform(This,in_point,out_point,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_TransformBounds(__x_ABI_CWindows_CUI_CInput_CIPointerPointTransform* This,__x_ABI_CWindows_CFoundation_CRect rect,__x_ABI_CWindows_CFoundation_CRect *value) {
    return This->lpVtbl->TransformBounds(This,rect,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Input
#define IID_IPointerPointTransform IID___x_ABI_CWindows_CUI_CInput_CIPointerPointTransform
#define IPointerPointTransformVtbl __x_ABI_CWindows_CUI_CInput_CIPointerPointTransformVtbl
#define IPointerPointTransform __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform
#define IPointerPointTransform_QueryInterface __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_QueryInterface
#define IPointerPointTransform_AddRef __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_AddRef
#define IPointerPointTransform_Release __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_Release
#define IPointerPointTransform_GetIids __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_GetIids
#define IPointerPointTransform_GetRuntimeClassName __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_GetRuntimeClassName
#define IPointerPointTransform_GetTrustLevel __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_GetTrustLevel
#define IPointerPointTransform_get_Inverse __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_get_Inverse
#define IPointerPointTransform_TryTransform __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_TryTransform
#define IPointerPointTransform_TransformBounds __x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_TransformBounds
#endif /* WIDL_using_Windows_UI_Input */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CInput_CIPointerPointTransform_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Input.PointerPoint
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Input_PointerPoint_DEFINED
#define RUNTIMECLASS_Windows_UI_Input_PointerPoint_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Input_PointerPoint[] = {'W','i','n','d','o','w','s','.','U','I','.','I','n','p','u','t','.','P','o','i','n','t','e','r','P','o','i','n','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Input_PointerPoint[] = L"Windows.UI.Input.PointerPoint";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Input_PointerPoint[] = {'W','i','n','d','o','w','s','.','U','I','.','I','n','p','u','t','.','P','o','i','n','t','e','r','P','o','i','n','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Input_PointerPoint_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Input.PointerPointProperties
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Input_PointerPointProperties_DEFINED
#define RUNTIMECLASS_Windows_UI_Input_PointerPointProperties_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Input_PointerPointProperties[] = {'W','i','n','d','o','w','s','.','U','I','.','I','n','p','u','t','.','P','o','i','n','t','e','r','P','o','i','n','t','P','r','o','p','e','r','t','i','e','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Input_PointerPointProperties[] = L"Windows.UI.Input.PointerPointProperties";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Input_PointerPointProperties[] = {'W','i','n','d','o','w','s','.','U','I','.','I','n','p','u','t','.','P','o','i','n','t','e','r','P','o','i','n','t','P','r','o','p','e','r','t','i','e','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Input_PointerPointProperties_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IVectorView<ABI::Windows::UI::Input::PointerPoint* > interface
 */
#ifndef ____FIVectorView_1_Windows__CUI__CInput__CPointerPoint_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CUI__CInput__CPointerPoint_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CUI__CInput__CPointerPoint, 0xf0f57411, 0x7786, 0x5174, 0x87,0x52, 0x4c,0x5e,0x83,0x4b,0x6d,0xa2);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("f0f57411-**************-4c5e834b6da2")
                IVectorView<ABI::Windows::UI::Input::PointerPoint* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Input::PointerPoint*, ABI::Windows::UI::Input::IPointerPoint* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint, 0xf0f57411, 0x7786, 0x5174, 0x87,0x52, 0x4c,0x5e,0x83,0x4b,0x6d,0xa2)
#endif
#else
typedef struct __FIVectorView_1_Windows__CUI__CInput__CPointerPointVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::UI::Input::PointerPoint* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 index,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CUI__CInput__CPointerPointVtbl;

interface __FIVectorView_1_Windows__CUI__CInput__CPointerPoint {
    CONST_VTBL __FIVectorView_1_Windows__CUI__CInput__CPointerPointVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::UI::Input::PointerPoint* > methods ***/
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_QueryInterface(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_AddRef(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_Release(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetIids(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetRuntimeClassName(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetTrustLevel(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::UI::Input::PointerPoint* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetAt(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 index,__x_ABI_CWindows_CUI_CInput_CIPointerPoint **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_get_Size(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_IndexOf(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This,__x_ABI_CWindows_CUI_CInput_CIPointerPoint *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetMany(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CUI_CInput_CIPointerPoint **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_PointerPoint IID___FIVectorView_1_Windows__CUI__CInput__CPointerPoint
#define IVectorView_PointerPointVtbl __FIVectorView_1_Windows__CUI__CInput__CPointerPointVtbl
#define IVectorView_PointerPoint __FIVectorView_1_Windows__CUI__CInput__CPointerPoint
#define IVectorView_PointerPoint_QueryInterface __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_QueryInterface
#define IVectorView_PointerPoint_AddRef __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_AddRef
#define IVectorView_PointerPoint_Release __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_Release
#define IVectorView_PointerPoint_GetIids __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetIids
#define IVectorView_PointerPoint_GetRuntimeClassName __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetRuntimeClassName
#define IVectorView_PointerPoint_GetTrustLevel __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetTrustLevel
#define IVectorView_PointerPoint_GetAt __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetAt
#define IVectorView_PointerPoint_get_Size __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_get_Size
#define IVectorView_PointerPoint_IndexOf __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_IndexOf
#define IVectorView_PointerPoint_GetMany __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CUI__CInput__CPointerPoint_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVector<ABI::Windows::UI::Input::PointerPoint* > interface
 */
#ifndef ____FIVector_1_Windows__CUI__CInput__CPointerPoint_INTERFACE_DEFINED__
#define ____FIVector_1_Windows__CUI__CInput__CPointerPoint_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVector_1_Windows__CUI__CInput__CPointerPoint, 0xdfa655cf, 0xfde7, 0x5048, 0xb4,0xbf, 0xc9,0x09,0x23,0x1b,0x7e,0xdb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("dfa655cf-fde7-5048-b4bf-c909231b7edb")
                IVector<ABI::Windows::UI::Input::PointerPoint* > : IVector_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Input::PointerPoint*, ABI::Windows::UI::Input::IPointerPoint* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVector_1_Windows__CUI__CInput__CPointerPoint, 0xdfa655cf, 0xfde7, 0x5048, 0xb4,0xbf, 0xc9,0x09,0x23,0x1b,0x7e,0xdb)
#endif
#else
typedef struct __FIVector_1_Windows__CUI__CInput__CPointerPointVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        TrustLevel *trustLevel);

    /*** IVector<ABI::Windows::UI::Input::PointerPoint* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 index,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *GetView)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint **value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 index,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *value);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 index,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *Append)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAtEnd)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint **items,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *ReplaceAll)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 count,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint **items);

    END_INTERFACE
} __FIVector_1_Windows__CUI__CInput__CPointerPointVtbl;

interface __FIVector_1_Windows__CUI__CInput__CPointerPoint {
    CONST_VTBL __FIVector_1_Windows__CUI__CInput__CPointerPointVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVector<ABI::Windows::UI::Input::PointerPoint* > methods ***/
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetView(This,value) (This)->lpVtbl->GetView(This,value)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_SetAt(This,index,value) (This)->lpVtbl->SetAt(This,index,value)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_InsertAt(This,index,value) (This)->lpVtbl->InsertAt(This,index,value)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_Append(This,value) (This)->lpVtbl->Append(This,value)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_RemoveAtEnd(This) (This)->lpVtbl->RemoveAtEnd(This)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_Clear(This) (This)->lpVtbl->Clear(This)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_ReplaceAll(This,count,items) (This)->lpVtbl->ReplaceAll(This,count,items)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_QueryInterface(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVector_1_Windows__CUI__CInput__CPointerPoint_AddRef(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVector_1_Windows__CUI__CInput__CPointerPoint_Release(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetIids(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetRuntimeClassName(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetTrustLevel(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVector<ABI::Windows::UI::Input::PointerPoint* > methods ***/
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetAt(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 index,__x_ABI_CWindows_CUI_CInput_CIPointerPoint **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_get_Size(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetView(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,__FIVectorView_1_Windows__CUI__CInput__CPointerPoint **value) {
    return This->lpVtbl->GetView(This,value);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_IndexOf(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,__x_ABI_CWindows_CUI_CInput_CIPointerPoint *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_SetAt(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 index,__x_ABI_CWindows_CUI_CInput_CIPointerPoint *value) {
    return This->lpVtbl->SetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_InsertAt(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 index,__x_ABI_CWindows_CUI_CInput_CIPointerPoint *value) {
    return This->lpVtbl->InsertAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_RemoveAt(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_Append(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,__x_ABI_CWindows_CUI_CInput_CIPointerPoint *value) {
    return This->lpVtbl->Append(This,value);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_RemoveAtEnd(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This) {
    return This->lpVtbl->RemoveAtEnd(This);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_Clear(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetMany(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CUI_CInput_CIPointerPoint **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_ReplaceAll(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 count,__x_ABI_CWindows_CUI_CInput_CIPointerPoint **items) {
    return This->lpVtbl->ReplaceAll(This,count,items);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVector_PointerPoint IID___FIVector_1_Windows__CUI__CInput__CPointerPoint
#define IVector_PointerPointVtbl __FIVector_1_Windows__CUI__CInput__CPointerPointVtbl
#define IVector_PointerPoint __FIVector_1_Windows__CUI__CInput__CPointerPoint
#define IVector_PointerPoint_QueryInterface __FIVector_1_Windows__CUI__CInput__CPointerPoint_QueryInterface
#define IVector_PointerPoint_AddRef __FIVector_1_Windows__CUI__CInput__CPointerPoint_AddRef
#define IVector_PointerPoint_Release __FIVector_1_Windows__CUI__CInput__CPointerPoint_Release
#define IVector_PointerPoint_GetIids __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetIids
#define IVector_PointerPoint_GetRuntimeClassName __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetRuntimeClassName
#define IVector_PointerPoint_GetTrustLevel __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetTrustLevel
#define IVector_PointerPoint_GetAt __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetAt
#define IVector_PointerPoint_get_Size __FIVector_1_Windows__CUI__CInput__CPointerPoint_get_Size
#define IVector_PointerPoint_GetView __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetView
#define IVector_PointerPoint_IndexOf __FIVector_1_Windows__CUI__CInput__CPointerPoint_IndexOf
#define IVector_PointerPoint_SetAt __FIVector_1_Windows__CUI__CInput__CPointerPoint_SetAt
#define IVector_PointerPoint_InsertAt __FIVector_1_Windows__CUI__CInput__CPointerPoint_InsertAt
#define IVector_PointerPoint_RemoveAt __FIVector_1_Windows__CUI__CInput__CPointerPoint_RemoveAt
#define IVector_PointerPoint_Append __FIVector_1_Windows__CUI__CInput__CPointerPoint_Append
#define IVector_PointerPoint_RemoveAtEnd __FIVector_1_Windows__CUI__CInput__CPointerPoint_RemoveAtEnd
#define IVector_PointerPoint_Clear __FIVector_1_Windows__CUI__CInput__CPointerPoint_Clear
#define IVector_PointerPoint_GetMany __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetMany
#define IVector_PointerPoint_ReplaceAll __FIVector_1_Windows__CUI__CInput__CPointerPoint_ReplaceAll
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVector_1_Windows__CUI__CInput__CPointerPoint_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_ui_input_h__ */
