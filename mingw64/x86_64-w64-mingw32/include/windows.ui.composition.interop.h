/*** Autogenerated by WIDL 10.8 from include/windows.ui.composition.interop.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_ui_composition_interop_h__
#define __windows_ui_composition_interop_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CUI_CComposition_CICompositorInterop_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CComposition_CICompositorInterop_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CComposition_CICompositorInterop __x_ABI_CWindows_CUI_CComposition_CICompositorInterop;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CComposition_CICompositorInterop ABI::Windows::UI::Composition::ICompositorInterop
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Composition {
                interface ICompositorInterop;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <windows.ui.composition.h>
#include <sdkddkver.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef ____x_ABI_CWindows_CUI_CComposition_CICompositorInterop_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CComposition_CICompositorInterop_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CComposition_CICompositorInterop __x_ABI_CWindows_CUI_CComposition_CICompositorInterop;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CComposition_CICompositorInterop ABI::Windows::UI::Composition::ICompositorInterop
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Composition {
                interface ICompositorInterop;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * ICompositorInterop interface
 */
#ifndef ____x_ABI_CWindows_CUI_CComposition_CICompositorInterop_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CComposition_CICompositorInterop_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CComposition_CICompositorInterop, 0x25297d5c, 0x3ad4, 0x4c9c, 0xb5,0xcf, 0xe3,0x6a,0x38,0x51,0x23,0x30);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Composition {
                MIDL_INTERFACE("25297d5c-3ad4-4c9c-b5cf-e36a38512330")
                ICompositorInterop : public IUnknown
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateCompositionSurfaceForHandle(
                        HANDLE swapchain,
                        ABI::Windows::UI::Composition::ICompositionSurface **result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateCompositionSurfaceForSwapChain(
                        IUnknown *swapchain,
                        ABI::Windows::UI::Composition::ICompositionSurface **result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateGraphicsDevice(
                        IUnknown *device,
                        ABI::Windows::UI::Composition::ICompositionGraphicsDevice **result) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CComposition_CICompositorInterop, 0x25297d5c, 0x3ad4, 0x4c9c, 0xb5,0xcf, 0xe3,0x6a,0x38,0x51,0x23,0x30)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CComposition_CICompositorInteropVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CComposition_CICompositorInterop *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CComposition_CICompositorInterop *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CComposition_CICompositorInterop *This);

    /*** ICompositorInterop methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateCompositionSurfaceForHandle)(
        __x_ABI_CWindows_CUI_CComposition_CICompositorInterop *This,
        HANDLE swapchain,
        __x_ABI_CWindows_CUI_CComposition_CICompositionSurface **result);

    HRESULT (STDMETHODCALLTYPE *CreateCompositionSurfaceForSwapChain)(
        __x_ABI_CWindows_CUI_CComposition_CICompositorInterop *This,
        IUnknown *swapchain,
        __x_ABI_CWindows_CUI_CComposition_CICompositionSurface **result);

    HRESULT (STDMETHODCALLTYPE *CreateGraphicsDevice)(
        __x_ABI_CWindows_CUI_CComposition_CICompositorInterop *This,
        IUnknown *device,
        __x_ABI_CWindows_CUI_CComposition_CICompositionGraphicsDevice **result);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CComposition_CICompositorInteropVtbl;

interface __x_ABI_CWindows_CUI_CComposition_CICompositorInterop {
    CONST_VTBL __x_ABI_CWindows_CUI_CComposition_CICompositorInteropVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_Release(This) (This)->lpVtbl->Release(This)
/*** ICompositorInterop methods ***/
#define __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_CreateCompositionSurfaceForHandle(This,swapchain,result) (This)->lpVtbl->CreateCompositionSurfaceForHandle(This,swapchain,result)
#define __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_CreateCompositionSurfaceForSwapChain(This,swapchain,result) (This)->lpVtbl->CreateCompositionSurfaceForSwapChain(This,swapchain,result)
#define __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_CreateGraphicsDevice(This,device,result) (This)->lpVtbl->CreateGraphicsDevice(This,device,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_QueryInterface(__x_ABI_CWindows_CUI_CComposition_CICompositorInterop* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_AddRef(__x_ABI_CWindows_CUI_CComposition_CICompositorInterop* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_Release(__x_ABI_CWindows_CUI_CComposition_CICompositorInterop* This) {
    return This->lpVtbl->Release(This);
}
/*** ICompositorInterop methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_CreateCompositionSurfaceForHandle(__x_ABI_CWindows_CUI_CComposition_CICompositorInterop* This,HANDLE swapchain,__x_ABI_CWindows_CUI_CComposition_CICompositionSurface **result) {
    return This->lpVtbl->CreateCompositionSurfaceForHandle(This,swapchain,result);
}
static inline HRESULT __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_CreateCompositionSurfaceForSwapChain(__x_ABI_CWindows_CUI_CComposition_CICompositorInterop* This,IUnknown *swapchain,__x_ABI_CWindows_CUI_CComposition_CICompositionSurface **result) {
    return This->lpVtbl->CreateCompositionSurfaceForSwapChain(This,swapchain,result);
}
static inline HRESULT __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_CreateGraphicsDevice(__x_ABI_CWindows_CUI_CComposition_CICompositorInterop* This,IUnknown *device,__x_ABI_CWindows_CUI_CComposition_CICompositionGraphicsDevice **result) {
    return This->lpVtbl->CreateGraphicsDevice(This,device,result);
}
#endif
#ifdef WIDL_using_Windows_UI_Composition
#define IID_ICompositorInterop IID___x_ABI_CWindows_CUI_CComposition_CICompositorInterop
#define ICompositorInteropVtbl __x_ABI_CWindows_CUI_CComposition_CICompositorInteropVtbl
#define ICompositorInterop __x_ABI_CWindows_CUI_CComposition_CICompositorInterop
#define ICompositorInterop_QueryInterface __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_QueryInterface
#define ICompositorInterop_AddRef __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_AddRef
#define ICompositorInterop_Release __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_Release
#define ICompositorInterop_CreateCompositionSurfaceForHandle __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_CreateCompositionSurfaceForHandle
#define ICompositorInterop_CreateCompositionSurfaceForSwapChain __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_CreateCompositionSurfaceForSwapChain
#define ICompositorInterop_CreateGraphicsDevice __x_ABI_CWindows_CUI_CComposition_CICompositorInterop_CreateGraphicsDevice
#endif /* WIDL_using_Windows_UI_Composition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CComposition_CICompositorInterop_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_ui_composition_interop_h__ */
