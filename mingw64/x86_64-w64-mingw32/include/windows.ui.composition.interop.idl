/*
 * Copyright (C) 2023 Mohamad Al-Jaf
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "windows.ui.composition.idl";
import "sdkddkver.h";

namespace Windows.UI.Composition {
    interface ICompositorInterop;

    [
        object,
        uuid(25297d5c-3ad4-4c9c-b5cf-e36a38512330),
        pointer_default(unique)
    ]
    interface ICompositorInterop : IUnknown
    {
        HRESULT CreateCompositionSurfaceForHandle([in] HANDLE swapchain, [out, retval] ICompositionSurface **result);
        HRESULT CreateCompositionSurfaceForSwapChain([in] IUnknown *swapchain, [out, retval] ICompositionSurface **result);
        HRESULT CreateGraphicsDevice([in] IUnknown *device, [out, retval] ICompositionGraphicsDevice **result);
    };
}
