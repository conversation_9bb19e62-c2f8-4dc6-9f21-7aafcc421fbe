/*** Autogenerated by WIDL 10.8 from include/windows.media.faceanalysis.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_media_faceanalysis_h__
#define __windows_media_faceanalysis_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace ABI::Windows::Media::FaceAnalysis::IDetectedFace
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace FaceAnalysis {
                interface IDetectedFace;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector ABI::Windows::Media::FaceAnalysis::IFaceDetector
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace FaceAnalysis {
                interface IFaceDetector;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics ABI::Windows::Media::FaceAnalysis::IFaceDetectorStatics
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace FaceAnalysis {
                interface IFaceDetectorStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CFaceAnalysis_CDetectedFace_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CFaceAnalysis_CDetectedFace_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace FaceAnalysis {
                class DetectedFace;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CFaceAnalysis_CDetectedFace __x_ABI_CWindows_CMedia_CFaceAnalysis_CDetectedFace;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CFaceAnalysis_CDetectedFace_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CFaceAnalysis_CFaceDetector_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CFaceAnalysis_CFaceDetector_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace FaceAnalysis {
                class FaceDetector;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CFaceAnalysis_CFaceDetector __x_ABI_CWindows_CMedia_CFaceAnalysis_CFaceDetector;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CFaceAnalysis_CFaceDetector_FWD_DEFINED__ */

#ifndef ____FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Media::FaceAnalysis::DetectedFace* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Media::FaceAnalysis::DetectedFace* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Media::FaceAnalysis::DetectedFace* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
#define ____FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace;
#ifdef __cplusplus
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Media::FaceAnalysis::FaceDetector* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Media::FaceAnalysis::FaceDetector* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.graphics.imaging.h>
#include <windows.media.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace ABI::Windows::Media::FaceAnalysis::IDetectedFace
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace FaceAnalysis {
                interface IDetectedFace;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector ABI::Windows::Media::FaceAnalysis::IFaceDetector
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace FaceAnalysis {
                interface IFaceDetector;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics ABI::Windows::Media::FaceAnalysis::IFaceDetectorStatics
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace FaceAnalysis {
                interface IFaceDetectorStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Media::FaceAnalysis::DetectedFace* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Media::FaceAnalysis::DetectedFace* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Media::FaceAnalysis::DetectedFace* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
#define ____FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace;
#ifdef __cplusplus
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Media::FaceAnalysis::FaceDetector* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IDetectedFace interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace, 0x8200d454, 0x66bc, 0x34df, 0x94,0x10, 0xe8,0x94,0x00,0x19,0x54,0x14);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace FaceAnalysis {
                MIDL_INTERFACE("8200d454-66bc-34df-9410-e89400195414")
                IDetectedFace : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_FaceBox(
                        ABI::Windows::Graphics::Imaging::BitmapBounds *return_value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace, 0x8200d454, 0x66bc, 0x34df, 0x94,0x10, 0xe8,0x94,0x00,0x19,0x54,0x14)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace *This,
        TrustLevel *trustLevel);

    /*** IDetectedFace methods ***/
    HRESULT (STDMETHODCALLTYPE *get_FaceBox)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace *This,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapBounds *return_value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFaceVtbl;

interface __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace {
    CONST_VTBL __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDetectedFace methods ***/
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_get_FaceBox(This,return_value) (This)->lpVtbl->get_FaceBox(This,return_value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_QueryInterface(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_AddRef(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_Release(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_GetIids(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_GetTrustLevel(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDetectedFace methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_get_FaceBox(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace* This,__x_ABI_CWindows_CGraphics_CImaging_CBitmapBounds *return_value) {
    return This->lpVtbl->get_FaceBox(This,return_value);
}
#endif
#ifdef WIDL_using_Windows_Media_FaceAnalysis
#define IID_IDetectedFace IID___x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace
#define IDetectedFaceVtbl __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFaceVtbl
#define IDetectedFace __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace
#define IDetectedFace_QueryInterface __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_QueryInterface
#define IDetectedFace_AddRef __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_AddRef
#define IDetectedFace_Release __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_Release
#define IDetectedFace_GetIids __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_GetIids
#define IDetectedFace_GetRuntimeClassName __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_GetRuntimeClassName
#define IDetectedFace_GetTrustLevel __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_GetTrustLevel
#define IDetectedFace_get_FaceBox __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_get_FaceBox
#endif /* WIDL_using_Windows_Media_FaceAnalysis */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IFaceDetector interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector, 0x16b672dc, 0xfe6f, 0x3117, 0x8d,0x95, 0xc3,0xf0,0x4d,0x51,0x63,0x0c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace FaceAnalysis {
                MIDL_INTERFACE("16b672dc-fe6f-3117-8d95-c3f04d51630c")
                IFaceDetector : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE DetectFacesAsync(
                        ABI::Windows::Graphics::Imaging::ISoftwareBitmap *image,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >* > **return_value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE DetectFacesWithSearchAreaAsync(
                        ABI::Windows::Graphics::Imaging::ISoftwareBitmap *image,
                        ABI::Windows::Graphics::Imaging::BitmapBounds search_area,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >* > **return_value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_MinDetectableFaceSize(
                        ABI::Windows::Graphics::Imaging::BitmapSize *return_value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_MinDetectableFaceSize(
                        ABI::Windows::Graphics::Imaging::BitmapSize value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_MaxDetectableFaceSize(
                        ABI::Windows::Graphics::Imaging::BitmapSize *return_value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_MaxDetectableFaceSize(
                        ABI::Windows::Graphics::Imaging::BitmapSize value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector, 0x16b672dc, 0xfe6f, 0x3117, 0x8d,0x95, 0xc3,0xf0,0x4d,0x51,0x63,0x0c)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector *This,
        TrustLevel *trustLevel);

    /*** IFaceDetector methods ***/
    HRESULT (STDMETHODCALLTYPE *DetectFacesAsync)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector *This,
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *image,
        __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace **return_value);

    HRESULT (STDMETHODCALLTYPE *DetectFacesWithSearchAreaAsync)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector *This,
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *image,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapBounds search_area,
        __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace **return_value);

    HRESULT (STDMETHODCALLTYPE *get_MinDetectableFaceSize)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector *This,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapSize *return_value);

    HRESULT (STDMETHODCALLTYPE *put_MinDetectableFaceSize)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector *This,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapSize value);

    HRESULT (STDMETHODCALLTYPE *get_MaxDetectableFaceSize)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector *This,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapSize *return_value);

    HRESULT (STDMETHODCALLTYPE *put_MaxDetectableFaceSize)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector *This,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapSize value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorVtbl;

interface __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector {
    CONST_VTBL __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IFaceDetector methods ***/
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_DetectFacesAsync(This,image,return_value) (This)->lpVtbl->DetectFacesAsync(This,image,return_value)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_DetectFacesWithSearchAreaAsync(This,image,search_area,return_value) (This)->lpVtbl->DetectFacesWithSearchAreaAsync(This,image,search_area,return_value)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_get_MinDetectableFaceSize(This,return_value) (This)->lpVtbl->get_MinDetectableFaceSize(This,return_value)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_put_MinDetectableFaceSize(This,value) (This)->lpVtbl->put_MinDetectableFaceSize(This,value)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_get_MaxDetectableFaceSize(This,return_value) (This)->lpVtbl->get_MaxDetectableFaceSize(This,return_value)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_put_MaxDetectableFaceSize(This,value) (This)->lpVtbl->put_MaxDetectableFaceSize(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_QueryInterface(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_AddRef(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_Release(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_GetIids(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_GetTrustLevel(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IFaceDetector methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_DetectFacesAsync(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector* This,__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *image,__FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace **return_value) {
    return This->lpVtbl->DetectFacesAsync(This,image,return_value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_DetectFacesWithSearchAreaAsync(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector* This,__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *image,__x_ABI_CWindows_CGraphics_CImaging_CBitmapBounds search_area,__FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace **return_value) {
    return This->lpVtbl->DetectFacesWithSearchAreaAsync(This,image,search_area,return_value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_get_MinDetectableFaceSize(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector* This,__x_ABI_CWindows_CGraphics_CImaging_CBitmapSize *return_value) {
    return This->lpVtbl->get_MinDetectableFaceSize(This,return_value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_put_MinDetectableFaceSize(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector* This,__x_ABI_CWindows_CGraphics_CImaging_CBitmapSize value) {
    return This->lpVtbl->put_MinDetectableFaceSize(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_get_MaxDetectableFaceSize(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector* This,__x_ABI_CWindows_CGraphics_CImaging_CBitmapSize *return_value) {
    return This->lpVtbl->get_MaxDetectableFaceSize(This,return_value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_put_MaxDetectableFaceSize(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector* This,__x_ABI_CWindows_CGraphics_CImaging_CBitmapSize value) {
    return This->lpVtbl->put_MaxDetectableFaceSize(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_FaceAnalysis
#define IID_IFaceDetector IID___x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector
#define IFaceDetectorVtbl __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorVtbl
#define IFaceDetector __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector
#define IFaceDetector_QueryInterface __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_QueryInterface
#define IFaceDetector_AddRef __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_AddRef
#define IFaceDetector_Release __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_Release
#define IFaceDetector_GetIids __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_GetIids
#define IFaceDetector_GetRuntimeClassName __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_GetRuntimeClassName
#define IFaceDetector_GetTrustLevel __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_GetTrustLevel
#define IFaceDetector_DetectFacesAsync __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_DetectFacesAsync
#define IFaceDetector_DetectFacesWithSearchAreaAsync __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_DetectFacesWithSearchAreaAsync
#define IFaceDetector_get_MinDetectableFaceSize __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_get_MinDetectableFaceSize
#define IFaceDetector_put_MinDetectableFaceSize __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_put_MinDetectableFaceSize
#define IFaceDetector_get_MaxDetectableFaceSize __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_get_MaxDetectableFaceSize
#define IFaceDetector_put_MaxDetectableFaceSize __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_put_MaxDetectableFaceSize
#endif /* WIDL_using_Windows_Media_FaceAnalysis */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IFaceDetectorStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics, 0xbc042d67, 0x9047, 0x33f6, 0x88,0x1b, 0x67,0x46,0xc1,0xb2,0x18,0xb8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace FaceAnalysis {
                MIDL_INTERFACE("bc042d67-9047-33f6-881b-6746c1b218b8")
                IFaceDetectorStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateAsync(
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Media::FaceAnalysis::FaceDetector* > **return_value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetSupportedBitmapPixelFormats(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Graphics::Imaging::BitmapPixelFormat > **result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE IsBitmapPixelFormatSupported(
                        ABI::Windows::Graphics::Imaging::BitmapPixelFormat bitmap_pixel_format,
                        boolean *result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsSupported(
                        boolean *return_value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics, 0xbc042d67, 0x9047, 0x33f6, 0x88,0x1b, 0x67,0x46,0xc1,0xb2,0x18,0xb8)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics *This,
        TrustLevel *trustLevel);

    /*** IFaceDetectorStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateAsync)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics *This,
        __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector **return_value);

    HRESULT (STDMETHODCALLTYPE *GetSupportedBitmapPixelFormats)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics *This,
        __FIVectorView_1_BitmapPixelFormat **result);

    HRESULT (STDMETHODCALLTYPE *IsBitmapPixelFormatSupported)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics *This,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat bitmap_pixel_format,
        boolean *result);

    HRESULT (STDMETHODCALLTYPE *get_IsSupported)(
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics *This,
        boolean *return_value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStaticsVtbl;

interface __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics {
    CONST_VTBL __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IFaceDetectorStatics methods ***/
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_CreateAsync(This,return_value) (This)->lpVtbl->CreateAsync(This,return_value)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_GetSupportedBitmapPixelFormats(This,result) (This)->lpVtbl->GetSupportedBitmapPixelFormats(This,result)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_IsBitmapPixelFormatSupported(This,bitmap_pixel_format,result) (This)->lpVtbl->IsBitmapPixelFormatSupported(This,bitmap_pixel_format,result)
#define __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_get_IsSupported(This,return_value) (This)->lpVtbl->get_IsSupported(This,return_value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_QueryInterface(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_AddRef(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_Release(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_GetIids(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_GetTrustLevel(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IFaceDetectorStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_CreateAsync(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics* This,__FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector **return_value) {
    return This->lpVtbl->CreateAsync(This,return_value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_GetSupportedBitmapPixelFormats(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics* This,__FIVectorView_1_BitmapPixelFormat **result) {
    return This->lpVtbl->GetSupportedBitmapPixelFormats(This,result);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_IsBitmapPixelFormatSupported(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics* This,__x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat bitmap_pixel_format,boolean *result) {
    return This->lpVtbl->IsBitmapPixelFormatSupported(This,bitmap_pixel_format,result);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_get_IsSupported(__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics* This,boolean *return_value) {
    return This->lpVtbl->get_IsSupported(This,return_value);
}
#endif
#ifdef WIDL_using_Windows_Media_FaceAnalysis
#define IID_IFaceDetectorStatics IID___x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics
#define IFaceDetectorStaticsVtbl __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStaticsVtbl
#define IFaceDetectorStatics __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics
#define IFaceDetectorStatics_QueryInterface __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_QueryInterface
#define IFaceDetectorStatics_AddRef __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_AddRef
#define IFaceDetectorStatics_Release __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_Release
#define IFaceDetectorStatics_GetIids __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_GetIids
#define IFaceDetectorStatics_GetRuntimeClassName __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_GetRuntimeClassName
#define IFaceDetectorStatics_GetTrustLevel __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_GetTrustLevel
#define IFaceDetectorStatics_CreateAsync __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_CreateAsync
#define IFaceDetectorStatics_GetSupportedBitmapPixelFormats __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_GetSupportedBitmapPixelFormats
#define IFaceDetectorStatics_IsBitmapPixelFormatSupported __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_IsBitmapPixelFormatSupported
#define IFaceDetectorStatics_get_IsSupported __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_get_IsSupported
#endif /* WIDL_using_Windows_Media_FaceAnalysis */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetectorStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.FaceAnalysis.DetectedFace
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_FaceAnalysis_DetectedFace_DEFINED
#define RUNTIMECLASS_Windows_Media_FaceAnalysis_DetectedFace_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_FaceAnalysis_DetectedFace[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','F','a','c','e','A','n','a','l','y','s','i','s','.','D','e','t','e','c','t','e','d','F','a','c','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_FaceAnalysis_DetectedFace[] = L"Windows.Media.FaceAnalysis.DetectedFace";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_FaceAnalysis_DetectedFace[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','F','a','c','e','A','n','a','l','y','s','i','s','.','D','e','t','e','c','t','e','d','F','a','c','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_FaceAnalysis_DetectedFace_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.FaceAnalysis.FaceDetector
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_FaceAnalysis_FaceDetector_DEFINED
#define RUNTIMECLASS_Windows_Media_FaceAnalysis_FaceDetector_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_FaceAnalysis_FaceDetector[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','F','a','c','e','A','n','a','l','y','s','i','s','.','F','a','c','e','D','e','t','e','c','t','o','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_FaceAnalysis_FaceDetector[] = L"Windows.Media.FaceAnalysis.FaceDetector";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_FaceAnalysis_FaceDetector[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','F','a','c','e','A','n','a','l','y','s','i','s','.','F','a','c','e','D','e','t','e','c','t','o','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_FaceAnalysis_FaceDetector_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IIterable<ABI::Windows::Media::FaceAnalysis::DetectedFace* > interface
 */
#ifndef ____FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace, 0x25347323, 0x3556, 0x5cbb, 0x98,0x55, 0x2b,0x58,0x56,0x43,0x7f,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("*************-5cbb-9855-2b5856437f4d")
                IIterable<ABI::Windows::Media::FaceAnalysis::DetectedFace* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::FaceAnalysis::DetectedFace*, ABI::Windows::Media::FaceAnalysis::IDetectedFace* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace, 0x25347323, 0x3556, 0x5cbb, 0x98,0x55, 0x2b,0x58,0x56,0x43,0x7f,0x4d)
#endif
#else
typedef struct __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Media::FaceAnalysis::DetectedFace* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace **value);

    END_INTERFACE
} __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl;

interface __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace {
    CONST_VTBL __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Media::FaceAnalysis::DetectedFace* > methods ***/
#define __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface(__FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef(__FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release(__FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetIids(__FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetRuntimeClassName(__FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetTrustLevel(__FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Media::FaceAnalysis::DetectedFace* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_First(__FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,__FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_DetectedFace IID___FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace
#define IIterable_DetectedFaceVtbl __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl
#define IIterable_DetectedFace __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace
#define IIterable_DetectedFace_QueryInterface __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface
#define IIterable_DetectedFace_AddRef __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef
#define IIterable_DetectedFace_Release __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release
#define IIterable_DetectedFace_GetIids __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetIids
#define IIterable_DetectedFace_GetRuntimeClassName __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetRuntimeClassName
#define IIterable_DetectedFace_GetTrustLevel __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetTrustLevel
#define IIterable_DetectedFace_First __FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Media::FaceAnalysis::DetectedFace* > interface
 */
#ifndef ____FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace, 0xa0cf090e, 0x0340, 0x531f, 0x89,0x8b, 0xc2,0x1e,0xaf,0xb9,0xf4,0xae);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("a0cf090e-0340-531f-898b-c21eafb9f4ae")
                IIterator<ABI::Windows::Media::FaceAnalysis::DetectedFace* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::FaceAnalysis::DetectedFace*, ABI::Windows::Media::FaceAnalysis::IDetectedFace* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace, 0xa0cf090e, 0x0340, 0x531f, 0x89,0x8b, 0xc2,0x1e,0xaf,0xb9,0xf4,0xae)
#endif
#else
typedef struct __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Media::FaceAnalysis::DetectedFace* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        UINT32 items_size,
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl;

interface __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace {
    CONST_VTBL __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Media::FaceAnalysis::DetectedFace* > methods ***/
#define __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface(__FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef(__FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release(__FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetIids(__FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetRuntimeClassName(__FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetTrustLevel(__FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Media::FaceAnalysis::DetectedFace* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_get_Current(__FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_get_HasCurrent(__FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_MoveNext(__FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetMany(__FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,UINT32 items_size,__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_DetectedFace IID___FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace
#define IIterator_DetectedFaceVtbl __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl
#define IIterator_DetectedFace __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace
#define IIterator_DetectedFace_QueryInterface __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface
#define IIterator_DetectedFace_AddRef __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef
#define IIterator_DetectedFace_Release __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release
#define IIterator_DetectedFace_GetIids __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetIids
#define IIterator_DetectedFace_GetRuntimeClassName __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetRuntimeClassName
#define IIterator_DetectedFace_GetTrustLevel __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetTrustLevel
#define IIterator_DetectedFace_get_Current __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_get_Current
#define IIterator_DetectedFace_get_HasCurrent __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_get_HasCurrent
#define IIterator_DetectedFace_MoveNext __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_MoveNext
#define IIterator_DetectedFace_GetMany __FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Media::FaceAnalysis::DetectedFace* > interface
 */
#ifndef ____FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace, 0x39ef4411, 0x0618, 0x5b8d, 0x8e,0xa2, 0x81,0xc6,0x37,0xf8,0x23,0xf8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("39ef4411-0618-5b8d-8ea2-81c637f823f8")
                IVectorView<ABI::Windows::Media::FaceAnalysis::DetectedFace* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::FaceAnalysis::DetectedFace*, ABI::Windows::Media::FaceAnalysis::IDetectedFace* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace, 0x39ef4411, 0x0618, 0x5b8d, 0x8e,0xa2, 0x81,0xc6,0x37,0xf8,0x23,0xf8)
#endif
#else
typedef struct __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Media::FaceAnalysis::DetectedFace* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        UINT32 index,
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl;

interface __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace {
    CONST_VTBL __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Media::FaceAnalysis::DetectedFace* > methods ***/
#define __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface(__FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef(__FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release(__FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetIids(__FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetRuntimeClassName(__FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetTrustLevel(__FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Media::FaceAnalysis::DetectedFace* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetAt(__FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,UINT32 index,__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_get_Size(__FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_IndexOf(__FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetMany(__FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_DetectedFace IID___FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace
#define IVectorView_DetectedFaceVtbl __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl
#define IVectorView_DetectedFace __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace
#define IVectorView_DetectedFace_QueryInterface __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface
#define IVectorView_DetectedFace_AddRef __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef
#define IVectorView_DetectedFace_Release __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release
#define IVectorView_DetectedFace_GetIids __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetIids
#define IVectorView_DetectedFace_GetRuntimeClassName __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetRuntimeClassName
#define IVectorView_DetectedFace_GetTrustLevel __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetTrustLevel
#define IVectorView_DetectedFace_GetAt __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetAt
#define IVectorView_DetectedFace_get_Size __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_get_Size
#define IVectorView_DetectedFace_IndexOf __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_IndexOf
#define IVectorView_DetectedFace_GetMany __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* > interface
 */
#ifndef ____FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__
#define ____FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace, 0x0dfd8e09, 0x73f2, 0x505c, 0xa7,0x96, 0xa8,0xf0,0x31,0xb4,0xed,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("0dfd8e09-73f2-505c-a796-a8f031b4ede0")
                IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* > : IVector_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::FaceAnalysis::DetectedFace*, ABI::Windows::Media::FaceAnalysis::IDetectedFace* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace, 0x0dfd8e09, 0x73f2, 0x505c, 0xa7,0x96, 0xa8,0xf0,0x31,0xb4,0xed,0xe0)
#endif
#else
typedef struct __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        TrustLevel *trustLevel);

    /*** IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        UINT32 index,
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *GetView)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        __FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace **value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        UINT32 index,
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace *value);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        UINT32 index,
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *Append)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAtEnd)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace **items,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *ReplaceAll)(
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        UINT32 count,
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace **items);

    END_INTERFACE
} __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl;

interface __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace {
    CONST_VTBL __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* > methods ***/
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetView(This,value) (This)->lpVtbl->GetView(This,value)
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_SetAt(This,index,value) (This)->lpVtbl->SetAt(This,index,value)
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_InsertAt(This,index,value) (This)->lpVtbl->InsertAt(This,index,value)
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Append(This,value) (This)->lpVtbl->Append(This,value)
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_RemoveAtEnd(This) (This)->lpVtbl->RemoveAtEnd(This)
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Clear(This) (This)->lpVtbl->Clear(This)
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#define __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_ReplaceAll(This,count,items) (This)->lpVtbl->ReplaceAll(This,count,items)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetIids(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetRuntimeClassName(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetTrustLevel(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* > methods ***/
static inline HRESULT __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetAt(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,UINT32 index,__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_get_Size(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetView(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,__FIVectorView_1_Windows__CMedia__CFaceAnalysis__CDetectedFace **value) {
    return This->lpVtbl->GetView(This,value);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_IndexOf(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_SetAt(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,UINT32 index,__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace *value) {
    return This->lpVtbl->SetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_InsertAt(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,UINT32 index,__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace *value) {
    return This->lpVtbl->InsertAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_RemoveAt(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Append(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace *value) {
    return This->lpVtbl->Append(This,value);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_RemoveAtEnd(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This) {
    return This->lpVtbl->RemoveAtEnd(This);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Clear(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetMany(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_ReplaceAll(__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,UINT32 count,__x_ABI_CWindows_CMedia_CFaceAnalysis_CIDetectedFace **items) {
    return This->lpVtbl->ReplaceAll(This,count,items);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVector_DetectedFace IID___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace
#define IVector_DetectedFaceVtbl __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl
#define IVector_DetectedFace __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace
#define IVector_DetectedFace_QueryInterface __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface
#define IVector_DetectedFace_AddRef __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef
#define IVector_DetectedFace_Release __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release
#define IVector_DetectedFace_GetIids __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetIids
#define IVector_DetectedFace_GetRuntimeClassName __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetRuntimeClassName
#define IVector_DetectedFace_GetTrustLevel __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetTrustLevel
#define IVector_DetectedFace_GetAt __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetAt
#define IVector_DetectedFace_get_Size __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_get_Size
#define IVector_DetectedFace_GetView __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetView
#define IVector_DetectedFace_IndexOf __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_IndexOf
#define IVector_DetectedFace_SetAt __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_SetAt
#define IVector_DetectedFace_InsertAt __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_InsertAt
#define IVector_DetectedFace_RemoveAt __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_RemoveAt
#define IVector_DetectedFace_Append __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Append
#define IVector_DetectedFace_RemoveAtEnd __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_RemoveAtEnd
#define IVector_DetectedFace_Clear __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Clear
#define IVector_DetectedFace_GetMany __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetMany
#define IVector_DetectedFace_ReplaceAll __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_ReplaceAll
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Media::FaceAnalysis::FaceDetector* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector, 0xc0141cd2, 0x7a65, 0x514c, 0xbf,0xc4, 0xb4,0x9e,0x99,0x1f,0x03,0xeb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("c0141cd2-7a65-514c-bfc4-b49e991f03eb")
            IAsyncOperation<ABI::Windows::Media::FaceAnalysis::FaceDetector* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::FaceAnalysis::FaceDetector*, ABI::Windows::Media::FaceAnalysis::IFaceDetector* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector, 0xc0141cd2, 0x7a65, 0x514c, 0xbf,0xc4, 0xb4,0x9e,0x99,0x1f,0x03,0xeb)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetectorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Media::FaceAnalysis::FaceDetector* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector *This,
        __x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetectorVtbl;

interface __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector {
    CONST_VTBL __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetectorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Media::FaceAnalysis::FaceDetector* > methods ***/
#define __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_QueryInterface(__FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_AddRef(__FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_Release(__FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_GetIids(__FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_GetTrustLevel(__FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Media::FaceAnalysis::FaceDetector* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_put_Completed(__FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector* This,__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_get_Completed(__FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector* This,__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_GetResults(__FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector* This,__x_ABI_CWindows_CMedia_CFaceAnalysis_CIFaceDetector **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_FaceDetector IID___FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector
#define IAsyncOperation_FaceDetectorVtbl __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetectorVtbl
#define IAsyncOperation_FaceDetector __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector
#define IAsyncOperation_FaceDetector_QueryInterface __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_QueryInterface
#define IAsyncOperation_FaceDetector_AddRef __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_AddRef
#define IAsyncOperation_FaceDetector_Release __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_Release
#define IAsyncOperation_FaceDetector_GetIids __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_GetIids
#define IAsyncOperation_FaceDetector_GetRuntimeClassName __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_GetRuntimeClassName
#define IAsyncOperation_FaceDetector_GetTrustLevel __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_GetTrustLevel
#define IAsyncOperation_FaceDetector_put_Completed __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_put_Completed
#define IAsyncOperation_FaceDetector_get_Completed __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_get_Completed
#define IAsyncOperation_FaceDetector_GetResults __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Media::FaceAnalysis::FaceDetector* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector, 0x3224aec6, 0xe785, 0x5066, 0x97,0x6f, 0x79,0xdd,0x08,0x1b,0x75,0xa9);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("3224aec6-e785-5066-976f-79dd081b75a9")
            IAsyncOperationCompletedHandler<ABI::Windows::Media::FaceAnalysis::FaceDetector* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::FaceAnalysis::FaceDetector*, ABI::Windows::Media::FaceAnalysis::IFaceDetector* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector, 0x3224aec6, 0xe785, 0x5066, 0x97,0x6f, 0x79,0xdd,0x08,0x1b,0x75,0xa9)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetectorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Media::FaceAnalysis::FaceDetector* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector *This,
        __FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetectorVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetectorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Media::FaceAnalysis::FaceDetector* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_Release(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Media::FaceAnalysis::FaceDetector* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector* This,__FIAsyncOperation_1_Windows__CMedia__CFaceAnalysis__CFaceDetector *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_FaceDetector IID___FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector
#define IAsyncOperationCompletedHandler_FaceDetectorVtbl __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetectorVtbl
#define IAsyncOperationCompletedHandler_FaceDetector __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector
#define IAsyncOperationCompletedHandler_FaceDetector_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_QueryInterface
#define IAsyncOperationCompletedHandler_FaceDetector_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_AddRef
#define IAsyncOperationCompletedHandler_FaceDetector_Release __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_Release
#define IAsyncOperationCompletedHandler_FaceDetector_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CFaceAnalysis__CFaceDetector_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >* > interface
 */
#ifndef ____FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace, 0x37f1d7dc, 0xa1a4, 0x5a94, 0xb3,0x3b, 0x74,0x20,0x5a,0x65,0xa1,0xed);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("37f1d7dc-a1a4-5a94-b33b-74205a65a1ed")
            IAsyncOperation<ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >* > : IAsyncOperation_impl<ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace, 0x37f1d7dc, 0xa1a4, 0x5a94, 0xb3,0x3b, 0x74,0x20,0x5a,0x65,0xa1,0xed)
#endif
#else
typedef struct __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        __FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace **results);

    END_INTERFACE
} __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl;

interface __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace {
    CONST_VTBL __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >* > methods ***/
#define __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface(__FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef(__FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release(__FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetIids(__FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetRuntimeClassName(__FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetTrustLevel(__FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >* > methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_put_Completed(__FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,__FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_get_Completed(__FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,__FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetResults(__FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,__FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IVector_DetectedFace IID___FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace
#define IAsyncOperation_IVector_DetectedFaceVtbl __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl
#define IAsyncOperation_IVector_DetectedFace __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace
#define IAsyncOperation_IVector_DetectedFace_QueryInterface __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface
#define IAsyncOperation_IVector_DetectedFace_AddRef __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef
#define IAsyncOperation_IVector_DetectedFace_Release __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release
#define IAsyncOperation_IVector_DetectedFace_GetIids __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetIids
#define IAsyncOperation_IVector_DetectedFace_GetRuntimeClassName __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetRuntimeClassName
#define IAsyncOperation_IVector_DetectedFace_GetTrustLevel __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetTrustLevel
#define IAsyncOperation_IVector_DetectedFace_put_Completed __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_put_Completed
#define IAsyncOperation_IVector_DetectedFace_get_Completed __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_get_Completed
#define IAsyncOperation_IVector_DetectedFace_GetResults __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace, 0xb0a53153, 0x2015, 0x58b3, 0x9d,0xd0, 0xbd,0xf2,0x91,0xb8,0x56,0xb2);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("b0a53153-2015-58b3-9dd0-bdf291b856b2")
            IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace, 0xb0a53153, 0x2015, 0x58b3, 0x9d,0xd0, 0xbd,0xf2,0x91,0xb8,0x56,0xb2)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *This,
        __FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl;

interface __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >* > methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface(__FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef(__FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release(__FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::FaceAnalysis::DetectedFace* >* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Invoke(__FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace* This,__FIAsyncOperation_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IVector_DetectedFace IID___FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace
#define IAsyncOperationCompletedHandler_IVector_DetectedFaceVtbl __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFaceVtbl
#define IAsyncOperationCompletedHandler_IVector_DetectedFace __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace
#define IAsyncOperationCompletedHandler_IVector_DetectedFace_QueryInterface __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_QueryInterface
#define IAsyncOperationCompletedHandler_IVector_DetectedFace_AddRef __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_AddRef
#define IAsyncOperationCompletedHandler_IVector_DetectedFace_Release __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Release
#define IAsyncOperationCompletedHandler_IVector_DetectedFace_Invoke __FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1___FIVector_1_Windows__CMedia__CFaceAnalysis__CDetectedFace_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_media_faceanalysis_h__ */
