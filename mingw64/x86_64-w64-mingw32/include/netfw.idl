/*
 * Copyright 2007 <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 *
 *****************************************************************************
 *
 * The firewall management interface
 *
 */

import "icftypes.idl";
import "oaidl.idl";

[
   object,
   uuid(A6207B2E-7CDD-426A-951E-5E1CBC5AFEAD),
   dual
]
interface INetFwIcmpSettings : IDispatch
{
    [id(1), propget]
    HRESULT AllowOutboundDestinationUnreachable([out, retval] VARIANT_BOOL* allow);

    [id(1), propput]
    HRESULT AllowOutboundDestinationUnreachable( [in] VARIANT_BOOL allow );

    [id(2), propget]
    HRESULT AllowRedirect( [out, retval] VARIANT_BOOL* allow );

    [id(2), propput]
    HRESULT AllowRedirect( [in] VARIANT_BOOL allow );

    [id(3), propget]
    HRESULT AllowInboundEchoRequest( [out, retval] VARIANT_BOOL* allow );

    [id(3), propput]
    HRESULT AllowInboundEchoRequest( [in] VARIANT_BOOL allow );

    [id(4), propget]
    HRESULT AllowOutboundTimeExceeded( [out, retval] VARIANT_BOOL* allow );

    [id(4), propput]
    HRESULT AllowOutboundTimeExceeded( [in] VARIANT_BOOL allow );

    [id(5), propget]
    HRESULT AllowOutboundParameterProblem( [out, retval] VARIANT_BOOL* allow );

    [id(5), propput]
    HRESULT AllowOutboundParameterProblem( [in] VARIANT_BOOL allow );

    [id(6), propget]
    HRESULT AllowOutboundSourceQuench( [out, retval] VARIANT_BOOL* allow );

    [id(6), propput]
    HRESULT AllowOutboundSourceQuench( [in] VARIANT_BOOL allow );

    [id(7), propget]
    HRESULT AllowInboundRouterRequest( [out, retval] VARIANT_BOOL* allow );

    [id(7), propput]
    HRESULT AllowInboundRouterRequest( [in] VARIANT_BOOL allow );

    [id(8), propget]
    HRESULT AllowInboundTimestampRequest( [out, retval] VARIANT_BOOL* allow );

    [id(8), propput]
    HRESULT AllowInboundTimestampRequest( [in] VARIANT_BOOL allow );

    [id(9), propget]
    HRESULT AllowInboundMaskRequest( [out, retval] VARIANT_BOOL* allow );

    [id(9), propput]
    HRESULT AllowInboundMaskRequest( [in] VARIANT_BOOL allow );

    [id(10), propget]
    HRESULT AllowOutboundPacketTooBig( [out, retval] VARIANT_BOOL* allow );

    [id(10), propput]
    HRESULT AllowOutboundPacketTooBig( [in] VARIANT_BOOL allow );
}

[
    object,
    uuid(E0483BA0-47FF-4D9C-A6D6-7741D0B195F7),
    dual
]
interface INetFwOpenPort : IDispatch
{
    [id(1), propget]
    HRESULT Name( [out, retval] BSTR* name );

    [id(1), propput]
    HRESULT Name( [in] BSTR name );

    [id(2), propget]
    HRESULT IpVersion( [out, retval] NET_FW_IP_VERSION* ipVersion );

    [id(2), propput]
    HRESULT IpVersion( [in] NET_FW_IP_VERSION ipVersion );

    [id(3), propget]
    HRESULT Protocol( [out, retval] NET_FW_IP_PROTOCOL* ipProtocol );

    [id(3), propput]
    HRESULT Protocol( [in] NET_FW_IP_PROTOCOL ipProtocol );

    [id(4), propget]
    HRESULT Port( [out, retval] LONG* portNumber );

    [id(4), propput]
    HRESULT Port( [in] LONG portNumber );

    [id(5), propget]
    HRESULT Scope( [out, retval] NET_FW_SCOPE* scope );

    [id(5), propput]
    HRESULT Scope( [in] NET_FW_SCOPE scope );

    [id(6), propget]
    HRESULT RemoteAddresses( [out, retval] BSTR* remoteAddrs );

    [id(6), propput]
    HRESULT RemoteAddresses( [in] BSTR remoteAddrs );

    [id(7), propget]
    HRESULT Enabled( [out, retval] VARIANT_BOOL* enabled );

    [id(7), propput]
    HRESULT Enabled( [in] VARIANT_BOOL enabled );

    [id(8), propget]
    HRESULT BuiltIn( [out, retval] VARIANT_BOOL* builtIn );
}

[
    object,
    uuid(C0E9D7FA-E07E-430A-B19A-090CE82D92E2),
    dual
]
interface INetFwOpenPorts : IDispatch
{
    [id(1), propget]
    HRESULT Count( [out, retval] long* count );

    [id(2)]
    HRESULT Add( [in] INetFwOpenPort* port );

    [id(3)]
    HRESULT Remove( [in] LONG portNumber, [in] NET_FW_IP_PROTOCOL ipProtocol );

    [id(4)]
    HRESULT Item( [in] LONG portNumber, [in] NET_FW_IP_PROTOCOL ipProtocol,
                  [out, retval] INetFwOpenPort** openPort );

    [id(DISPID_NEWENUM), propget, restricted]
    HRESULT _NewEnum( [out, retval] IUnknown** newEnum );
}

[
    object,
    uuid(79FD57C8-908E-4A36-9888-D5B3F0A444CF),
    dual
]
interface INetFwService : IDispatch
{
    [id(1), propget]
    HRESULT Name( [out, retval] BSTR* name );

    [id(2), propget]
    HRESULT Type( [out, retval] NET_FW_SERVICE_TYPE* type );

    [id(3), propget]
    HRESULT Customized( [out, retval] VARIANT_BOOL* customized );

    [id(4), propget]
    HRESULT IpVersion( [out, retval] NET_FW_IP_VERSION* ipVersion );

    [id(4), propput]
    HRESULT IpVersion( [in] NET_FW_IP_VERSION ipVersion );

    [id(5), propget]
    HRESULT Scope( [out, retval] NET_FW_SCOPE* scope );

    [id(5), propput]
    HRESULT Scope( [in] NET_FW_SCOPE scope );

    [id(6), propget]
    HRESULT RemoteAddresses( [out, retval] BSTR* remoteAddrs );

    [id(6), propput]
    HRESULT RemoteAddresses( [in] BSTR remoteAddrs );

    [id(7), propget]
    HRESULT Enabled( [out, retval] VARIANT_BOOL* enabled );

    [id(7), propput]
    HRESULT Enabled( [in] VARIANT_BOOL enabled );

    [id(8), propget]
    HRESULT GloballyOpenPorts( [out, retval] INetFwOpenPorts** openPorts );
}

[
    object,
    uuid(79649BB4-903E-421B-94C9-79848E79F6EE),
    dual
]
interface INetFwServices : IDispatch
{
    [id(1), propget]
    HRESULT Count( [out, retval] long* count );

     [id(2)]
    HRESULT Item( [in] NET_FW_SERVICE_TYPE svcType,
                  [out, retval] INetFwService** service );

    [id(DISPID_NEWENUM), propget, restricted]
    HRESULT _NewEnum( [out, retval] IUnknown** newEnum );
}

[
    object,
    uuid(B5E64FFA-C2C5-444E-A301-FB5E00018050),
    dual
]
interface INetFwAuthorizedApplication : IDispatch
{
    [id(1), propget]
    HRESULT Name( [out, retval] BSTR* name );

    [id(1), propput]
    HRESULT Name( [in] BSTR name );

    [id(2), propget]
    HRESULT ProcessImageFileName( [out, retval] BSTR* imageFileName );

    [id(2), propput]
    HRESULT ProcessImageFileName( [in] BSTR imageFileName );

    [id(3), propget]
    HRESULT IpVersion( [out, retval] NET_FW_IP_VERSION* ipVersion );

    [id(3), propput]
    HRESULT IpVersion( [in] NET_FW_IP_VERSION ipVersion );

    [id(4), propget]
    HRESULT Scope( [out, retval] NET_FW_SCOPE* scope );

    [id(4), propput]
    HRESULT Scope( [in] NET_FW_SCOPE scope );

    [id(5), propget]
    HRESULT RemoteAddresses( [out, retval] BSTR* remoteAddrs );

    [id(5), propput]
    HRESULT RemoteAddresses( [in] BSTR remoteAddrs );

    [id(6), propget]
    HRESULT Enabled( [out, retval] VARIANT_BOOL* enabled );

    [id(6), propput]
    HRESULT Enabled( [in] VARIANT_BOOL enabled );
}

[
    object,
    uuid(D4BECDDF-6F73-4A83-B832-9C66874CD20E),
    dual
]
interface INetFwRemoteAdminSettings : IDispatch
{
    [id(1), propget]
    HRESULT IpVersion( [out, retval] NET_FW_IP_VERSION* ipVersion );

    [id(1), propput]
    HRESULT IpVersion( [in] NET_FW_IP_VERSION ipVersion );

    [id(2), propget]
    HRESULT Scope( [out, retval] NET_FW_SCOPE* scope );

    [id(2), propput]
    HRESULT Scope( [in] NET_FW_SCOPE scope );

    [id(3), propget]
    HRESULT RemoteAddresses( [out, retval] BSTR* remoteAddrs );

    [id(3), propput]
    HRESULT RemoteAddresses( [in] BSTR remoteAddrs );

    [id(4), propget]
    HRESULT Enabled( [out, retval] VARIANT_BOOL* enabled );

    [id(4), propput]
    HRESULT Enabled( [in] VARIANT_BOOL enabled );
}


[
    object,
    uuid(644EFD52-CCF9-486C-97A2-39F352570B30),
    dual
]
interface INetFwAuthorizedApplications : IDispatch
{
    [id(1), propget]
    HRESULT Count( [out, retval] long* count );

    [id(2)]
    HRESULT Add( [in] INetFwAuthorizedApplication* app );

    [id(3)]
    HRESULT Remove( [in] BSTR imageFileName );

    [id(4)]
    HRESULT Item( [in] BSTR imageFileName,
                  [out, retval] INetFwAuthorizedApplication** app );

    [id(DISPID_NEWENUM), propget, restricted]
    HRESULT _NewEnum( [out, retval] IUnknown** newEnum );
}

[
   local,
   object,
   uuid(af230d27-baba-4e42-aced-f524f22cfce2),
   dual
]
interface INetFwRule : IDispatch
{
   [id(1), propget]
   HRESULT Name( [out, retval] BSTR *name );
   [id(1), propput]
   HRESULT Name( [in] BSTR name );

   [id(2), propget]
   HRESULT Description( [out, retval] BSTR* desc );
   [id(2), propput]
   HRESULT Description( [in] BSTR desc );

   [id(3), propget]
   HRESULT ApplicationName( [out, retval] BSTR *imagename );
   [id(3), propput]
   HRESULT ApplicationName( [in] BSTR imagename );

   [id(4), propget]
   HRESULT ServiceName( [out, retval] BSTR *service );
   [id(4), propput]
   HRESULT ServiceName( [in] BSTR service );

   [id(5), propget]
   HRESULT Protocol( [out, retval] long* protocol );
   [id(5), propput]
   HRESULT Protocol( [in] long protocol );

   [id(6), propget]
   HRESULT LocalPorts( [out, retval] BSTR* ports );
   [id(6), propput]
   HRESULT LocalPorts( [in] BSTR ports );

   [id(7), propget]
   HRESULT RemotePorts( [out, retval] BSTR *ports );
   [id(7), propput]
   HRESULT RemotePorts( [in] BSTR ports );

   [id(8), propget]
   HRESULT LocalAddresses([out, retval] BSTR* address );
   [id(8), propput]
   HRESULT LocalAddresses([in] BSTR address);

   [id(9), propget]
   HRESULT RemoteAddresses( [out, retval] BSTR* address );
   [id(9), propput]
   HRESULT RemoteAddresses( [in] BSTR address );

   [id(10), propget]
   HRESULT IcmpTypesAndCodes( [out, retval] BSTR *codes );
   [id(10), propput]
   HRESULT IcmpTypesAndCodes( [in] BSTR codes );

   [id(11), propget]
   HRESULT Direction( [out, retval] NET_FW_RULE_DIRECTION* dir );
   [id(11), propput]
   HRESULT Direction( [in] NET_FW_RULE_DIRECTION dir );

   [id(12), propget]
   HRESULT Interfaces( [out, retval] VARIANT* interfaces );
   [id(12), propput]
   HRESULT Interfaces( [in] VARIANT interfaces );

   [id(13), propget]
   HRESULT InterfaceTypes( [out, retval] BSTR* types );
   [id(13), propput]
   HRESULT InterfaceTypes( [in] BSTR types );

   [id(14), propget]
   HRESULT Enabled( [out, retval] VARIANT_BOOL* enabled );
   [id(14), propput]
   HRESULT Enabled( [in] VARIANT_BOOL enabled );

   [id(15), propget]
   HRESULT Grouping( [out, retval] BSTR* context );
   [id(15), propput]
   HRESULT Grouping( [in] BSTR context );

   [id(16), propget]
   HRESULT Profiles( [out, retval] long* profiles );
   [id(16), propput]
   HRESULT Profiles( [in] long  profiles );

   [id(17), propget]
   HRESULT EdgeTraversal( [out, retval] VARIANT_BOOL* enabled );
   [id(17), propput]
   HRESULT EdgeTraversal( [in] VARIANT_BOOL enabled );

   [id(18), propget]
   HRESULT Action( [out, retval] NET_FW_ACTION* action );
   [id(18), propput]
   HRESULT Action( [in] NET_FW_ACTION action );
}

[
   local,
   object,
   uuid(9C27C8DA-189B-4DDE-89F7-8B39A316782C),
   dual
]
interface INetFwRule2 : INetFwRule
{
   [id(19), propget]
   HRESULT EdgeTraversalOptions( [out, retval] long* lOptions );
   [id(19), propput]
   HRESULT EdgeTraversalOptions( [in] long lOptions );
}

[
   local,
   object,
   uuid(B21563FF-D696-4222-AB46-4E89B73AB34A),
   dual
]
interface INetFwRule3 : INetFwRule2
{
   [id(20), propget]
   HRESULT LocalAppPackageId( [out, retval] BSTR* wszPackageId );
   [id(20), propput]
   HRESULT LocalAppPackageId( [in] BSTR wszPackageId );

   [id(21), propget]
   HRESULT LocalUserOwner( [out, retval] BSTR* wszUserOwner );
   [id(21), propput]
   HRESULT LocalUserOwner( [in] BSTR wszUserOwner );

   [id(22), propget]
   HRESULT LocalUserAuthorizedList( [out, retval] BSTR* wszUserAuthList );
   [id(22), propput]
   HRESULT LocalUserAuthorizedList( [in] BSTR wszUserAuthList );

   [id(23), propget]
   HRESULT RemoteUserAuthorizedList( [out, retval] BSTR* wszUserAuthList );
   [id(23), propput]
   HRESULT RemoteUserAuthorizedList( [in] BSTR wszUserAuthList );

   [id(24), propget]
   HRESULT RemoteMachineAuthorizedList( [out, retval] BSTR* wszUserAuthList );
   [id(24), propput]
   HRESULT RemoteMachineAuthorizedList( [in] BSTR wszUserAuthList );

   [id(25), propget]
   HRESULT SecureFlags( [out, retval] long* lOptions );
   [id(25), propput]
   HRESULT SecureFlags( [in] long lOptions );
}

[
   local,
   object,
   uuid(9c4c6277-5027-441e-afae-ca1f542da009),
   dual
]
interface INetFwRules : IDispatch
{
   [id(1), propget]
   HRESULT Count( [out, retval] long* count );

   [id(2)]
   HRESULT Add( [in] INetFwRule* rule );

   [id(3)]
   HRESULT Remove( [in] BSTR name );

   [id(4)]
   HRESULT Item( [in] BSTR name, [out, retval] INetFwRule** rule );

   [id(DISPID_NEWENUM), propget, restricted]
   HRESULT _NewEnum( [out, retval] IUnknown** newEnum );
}

[
    local,
    object,
    uuid(8267bbe3-f890-491c-b7b6-2db1ef0e5d2b),
    dual
]
interface INetFwServiceRestriction : IDispatch
{
   [id(1)]
   HRESULT RestrictService( [in] BSTR serviceName, [in] BSTR appName,
              [in] VARIANT_BOOL restrictService, [in] VARIANT_BOOL serviceSidRestricted );

   [id(2)]
   HRESULT ServiceRestricted( [in] BSTR serviceName, [in] BSTR appName,
              [out, retval] VARIANT_BOOL* serviceRestricted );

   [id(3), propget]
   HRESULT Rules( [out, retval] INetFwRules** rules );
}

[
     object,
     uuid(174A0DDA-E9F9-449D-993B-21AB667CA456),
     dual
]
interface INetFwProfile : IDispatch
{
    [id(1), propget]
    HRESULT Type( [out, retval] NET_FW_PROFILE_TYPE* type );

    [id(2), propget]
    HRESULT FirewallEnabled( [out, retval] VARIANT_BOOL* enabled );

    [id(2), propput]
    HRESULT FirewallEnabled( [in] VARIANT_BOOL enabled );

    [id(3), propget]
    HRESULT ExceptionsNotAllowed( [out, retval] VARIANT_BOOL* notAllowed );

    [id(3), propput]
    HRESULT ExceptionsNotAllowed( [in] VARIANT_BOOL notAllowed );

    [id(4), propget]
    HRESULT NotificationsDisabled( [out, retval] VARIANT_BOOL* disabled );

    [id(4), propput]
    HRESULT NotificationsDisabled( [in] VARIANT_BOOL disabled );

    [id(5), propget]
    HRESULT UnicastResponsesToMulticastBroadcastDisabled( [out, retval] VARIANT_BOOL* disabled );

    [id(5), propput]
    HRESULT UnicastResponsesToMulticastBroadcastDisabled( [in] VARIANT_BOOL disabled );

    [id(6), propget]
    HRESULT RemoteAdminSettings( [out, retval] INetFwRemoteAdminSettings** remoteAdminSettings );

    [id(7), propget]
    HRESULT IcmpSettings( [out, retval] INetFwIcmpSettings** icmpSettings );

    [id(8), propget]
    HRESULT GloballyOpenPorts( [out, retval] INetFwOpenPorts** openPorts );

    [id(9), propget]
    HRESULT Services( [out, retval] INetFwServices** services );

    [id(10), propget]
    HRESULT AuthorizedApplications( [out, retval] INetFwAuthorizedApplications** apps );
}

[
     object,
     uuid(D46D2478-9AC9-4008-9DC7-5563CE5536CC),
     dual
]
interface INetFwPolicy : IDispatch
{
    [id(1), propget]
    HRESULT CurrentProfile( [out, retval] INetFwProfile** profile );

    [id(2)]
    HRESULT GetProfileByType( [in] NET_FW_PROFILE_TYPE profileType,
                              [out, retval] INetFwProfile** profile );
}

[
    local,
    object,
    uuid(98325047-c671-4174-8d81-defcd3f03186),
    dual
]
interface INetFwPolicy2 : IDispatch
{
   [id(1), propget]
   HRESULT CurrentProfileTypes( [out, retval] long *profile );

   [id(2), propget]
   HRESULT FirewallEnabled( [in] NET_FW_PROFILE_TYPE2 profileType, [out, retval] VARIANT_BOOL* enabled );

   [id(2), propput]
   HRESULT FirewallEnabled( [in] NET_FW_PROFILE_TYPE2 profileType, [in] VARIANT_BOOL enabled );

   [id(3), propget]
   HRESULT ExcludedInterfaces( [in] NET_FW_PROFILE_TYPE2 profileType, [out, retval] VARIANT *interfaces );

   [id(3), propput]
   HRESULT ExcludedInterfaces( [in] NET_FW_PROFILE_TYPE2 profileType, [in] VARIANT interfaces );

   [id(4), propget]
   HRESULT BlockAllInboundTraffic( [in] NET_FW_PROFILE_TYPE2 profileType, [out, retval] VARIANT_BOOL* block );

   [id(4), propput]
   HRESULT BlockAllInboundTraffic( [in] NET_FW_PROFILE_TYPE2 profileType, [in] VARIANT_BOOL block );

   [id(5), propget]
   HRESULT NotificationsDisabled( [in] NET_FW_PROFILE_TYPE2 profileType, [out, retval] VARIANT_BOOL* disabled );
   [id(5), propput]
   HRESULT NotificationsDisabled( [in] NET_FW_PROFILE_TYPE2 profileType, [in] VARIANT_BOOL disabled );

   [id(6), propget]
   HRESULT UnicastResponsesToMulticastBroadcastDisabled( [in] NET_FW_PROFILE_TYPE2 profileType,
       [out, retval] VARIANT_BOOL* disabled );
   [id(6), propput]
   HRESULT UnicastResponsesToMulticastBroadcastDisabled( [in] NET_FW_PROFILE_TYPE2 profileType,
       [in] VARIANT_BOOL disabled );

   [id(7), propget]
   HRESULT Rules( [out, retval] INetFwRules** rules );

   [id(8), propget]
   HRESULT ServiceRestriction( [out, retval] INetFwServiceRestriction **ServiceRestriction );

   [id(9)]
   HRESULT EnableRuleGroup( [in] long profileTypesBitmask, [in] BSTR group, [in] VARIANT_BOOL enable );

   [id(10)]
   HRESULT IsRuleGroupEnabled( [in] long profileTypesBitmask, [in] BSTR group, [out, retval] VARIANT_BOOL *enabled );

   [id(11)]
   HRESULT RestoreLocalFirewallDefaults();

   [id(12), propget]
   HRESULT DefaultInboundAction( [in] NET_FW_PROFILE_TYPE2 profileType, [out, retval] NET_FW_ACTION* action );

   [id(12), propput]
   HRESULT DefaultInboundAction( [in] NET_FW_PROFILE_TYPE2 profileType, [in] NET_FW_ACTION action );

   [id(13), propget]
   HRESULT DefaultOutboundAction( [in] NET_FW_PROFILE_TYPE2 profileType, [out, retval] NET_FW_ACTION* action );

   [id(13), propput]
   HRESULT DefaultOutboundAction( [in] NET_FW_PROFILE_TYPE2 profileType, [in] NET_FW_ACTION action );

   [id(14), propget]
   HRESULT IsRuleGroupCurrentlyEnabled( [in] BSTR group, [out, retval] VARIANT_BOOL* enabled );

   [id(15), propget]
   HRESULT LocalPolicyModifyState( [out, retval] NET_FW_MODIFY_STATE* modifyState );
}

[
    object,
    uuid(F7898AF5-CAC4-4632-A2EC-DA06E5111AF2),
    dual
]
interface INetFwMgr : IDispatch
{
    [id(1), propget]
    HRESULT LocalPolicy( [out, retval] INetFwPolicy** localPolicy );

    [id(2), propget]
    HRESULT CurrentProfileType( [out, retval] NET_FW_PROFILE_TYPE* profileType );

    [id(3)]
    HRESULT RestoreDefaults();

    [id(4)]
    HRESULT IsPortAllowed( [in] BSTR imageFileName, [in] NET_FW_IP_VERSION ipVersion,
                           [in] LONG portNumber, [in] BSTR localAddress,
                           [in] NET_FW_IP_PROTOCOL ipProtocol, [out] VARIANT* allowed,
                           [out] VARIANT* restricted );

    [id(5)]
    HRESULT IsIcmpTypeAllowed( [in] NET_FW_IP_VERSION ipVersion, [in] BSTR localAddress,
                               [in] BYTE type, [out] VARIANT* allowed,
                               [out] VARIANT* restricted );
}

[
    local,
    object,
    uuid(71881699-18f4-458b-b892-3ffce5e07f75),
    dual
]
interface INetFwProduct : IDispatch
{
    [id(1), propget]
    HRESULT RuleCategories([out, retval] VARIANT *ruleCategories);

    [id(1), propput]
    HRESULT RuleCategories([in] VARIANT ruleCategories);

    [id(2), propget]
    HRESULT DisplayName([out, retval] BSTR *displayName);

    [id(2), propput]
    HRESULT DisplayName([in] BSTR displayName);

    [id(3), propget]
    HRESULT PathToSignedProductExe([out, retval] BSTR *path);
}

[
    local,
    object,
    uuid(39eb36e0-2097-40bd-8af2-63a13b525362),
    dual
]
interface INetFwProducts : IDispatch
{
    [id(1), propget]
    HRESULT Count([out, retval] LONG *count);

    [id(2)]
    HRESULT Register([in] INetFwProduct *product, [out, retval] IUnknown **registration);

    [id(3)]
    HRESULT Item([in] LONG index, [out, retval] INetFwProduct **product);

    [id(DISPID_NEWENUM), propget, restricted]
    HRESULT _NewEnum([out, retval] IUnknown **newEnum);
}

[
    uuid(DB4F3345-3EF8-45ED-B976-25A6D3B81B71),
    version(1.0)
]
library NetFwPublicTypeLib
{
    importlib("stdole2.tlb");
    interface INetFwRemoteAdminSettings;
    interface INetFwIcmpSettings;
    interface INetFwOpenPort;
    interface INetFwOpenPorts;
    interface INetFwService;
    interface INetFwServices;
    interface INetFwAuthorizedApplication;
    interface INetFwAuthorizedApplications;
    interface INetFwServiceRestriction;
    interface INetFwRule;
    interface INetFwRules;
    interface INetFwProfile;
    interface INetFwPolicy;
    interface INetFwPolicy2;
    interface INetFwMgr;
    interface INetFwProduct;
    interface INetFwProducts;

    [
        uuid(0CA545C6-37AD-4A6C-BF92-9F7610067EF5)
    ]
    coclass NetFwOpenPort
    {
        [default] interface INetFwOpenPort;
    }

    [
        uuid(EC9846B3-2762-4A6B-A214-6ACB603462D2)
    ]
    coclass NetFwAuthorizedApplication
    {
        [default] interface INetFwAuthorizedApplication;
    }

    [
        uuid(304CE942-6E39-40D8-943A-B913C40C9CD4)
    ]
    coclass NetFwMgr
    {
        [default] interface INetFwMgr;
    }

    [
        uuid(e2b3c97f-6ae1-41ac-817a-f6f92166d7dd)
    ]
    coclass NetFwPolicy2
    {
        [default] interface INetFwPolicy2;
    }

    [
        uuid(2c5bc43e-3369-4c33-ab0c-be9469677af4)
    ]
    coclass NetFwRule
    {
        [default] interface INetFwRule;
    }

    [
        uuid(9d745ed8-c514-4d1d-bf42-751fed2d5ac7)
    ]
    coclass NetFwProduct
    {
        [default] interface INetFwProduct;
    }

    [
        uuid(cc19079b-8272-4d73-bb70-cdb533527b61)
    ]
    coclass NetFwProducts
    {
        [default] interface INetFwProducts;
    }
}
