/*
 * Copyright (C) 2023 Mo<PERSON><PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";
import "windows.foundation.numerics.idl";
import "windows.graphics.directx.idl";
import "windows.perception.spatial.idl";
import "windows.storage.streams.idl";

namespace Windows.Perception.Spatial.Surfaces {
    interface ISpatialSurfaceInfo;
    interface ISpatialSurfaceMesh;
    interface ISpatialSurfaceMeshBuffer;
    interface ISpatialSurfaceMeshOptions;
    interface ISpatialSurfaceMeshOptionsStatics;
    interface ISpatialSurfaceObserver;
    interface ISpatialSurfaceObserverStatics;
    interface ISpatialSurfaceObserverStatics2;

    runtimeclass SpatialSurfaceInfo;
    runtimeclass SpatialSurfaceMesh;
    runtimeclass SpatialSurfaceMeshBuffer;
    runtimeclass SpatialSurfaceMeshOptions;
    runtimeclass SpatialSurfaceObserver;

    declare {
        interface Windows.Foundation.Collections.IMapView<GUID, Windows.Perception.Spatial.Surfaces.SpatialSurfaceInfo *>;
        interface Windows.Foundation.Collections.IKeyValuePair<GUID, Windows.Perception.Spatial.Surfaces.SpatialSurfaceInfo *>;
        interface Windows.Foundation.Collections.IIterable<Windows.Foundation.Collections.IKeyValuePair<GUID, Windows.Perception.Spatial.Surfaces.SpatialSurfaceInfo *> *>;
        interface Windows.Foundation.Collections.IIterator<Windows.Foundation.Collections.IKeyValuePair<GUID, Windows.Perception.Spatial.Surfaces.SpatialSurfaceInfo *> *>;
        interface Windows.Foundation.AsyncOperationCompletedHandler<Windows.Perception.Spatial.Surfaces.SpatialSurfaceMesh *>;
        interface Windows.Foundation.IAsyncOperation<Windows.Perception.Spatial.Surfaces.SpatialSurfaceMesh *>;
        interface Windows.Foundation.TypedEventHandler<Windows.Perception.Spatial.Surfaces.SpatialSurfaceObserver *, IInspectable *>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.Perception.Spatial.Surfaces.SpatialSurfaceInfo),
        uuid(f8e9ebe7-39b7-3962-bb03-57f56e1fb0a1)
    ]
    interface ISpatialSurfaceInfo : IInspectable
    {
        [propget] HRESULT Id([out, retval] GUID *value);
        [propget] HRESULT UpdateTime([out, retval] Windows.Foundation.DateTime *value);
        HRESULT TryGetBounds(
            [in] Windows.Perception.Spatial.SpatialCoordinateSystem *system,
            [out, retval] Windows.Foundation.IReference<Windows.Perception.Spatial.SpatialBoundingOrientedBox> **value
        );
        [overload("TryComputeLatestMeshAsync")] HRESULT TryComputeLatestMeshAsync(
            [in] DOUBLE triangles,
            [out, retval] Windows.Foundation.IAsyncOperation<Windows.Perception.Spatial.Surfaces.SpatialSurfaceMesh *> **value
        );
        [overload("TryComputeLatestMeshAsync")] HRESULT TryComputeLatestMeshWithOptionsAsync(
            [in] DOUBLE triangles,
            [in] Windows.Perception.Spatial.Surfaces.SpatialSurfaceMeshOptions *options,
            [out, retval] Windows.Foundation.IAsyncOperation<Windows.Perception.Spatial.Surfaces.SpatialSurfaceMesh *> **value
        );
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.Perception.Spatial.Surfaces.SpatialSurfaceMesh),
        uuid(108f57d9-df0d-3950-a0fd-f972c77c27b4)
    ]
    interface ISpatialSurfaceMesh : IInspectable
    {
        [propget] HRESULT SurfaceInfo([out, retval] Windows.Perception.Spatial.Surfaces.SpatialSurfaceInfo **value);
        [propget] HRESULT CoordinateSystem([out, retval] Windows.Perception.Spatial.SpatialCoordinateSystem **value);
        [propget] HRESULT TriangleIndices([out, retval] Windows.Perception.Spatial.Surfaces.SpatialSurfaceMeshBuffer **value);
        [propget] HRESULT VertexPositions([out, retval] Windows.Perception.Spatial.Surfaces.SpatialSurfaceMeshBuffer **value);
        [propget] HRESULT VertexPositionScale([out, retval] Windows.Foundation.Numerics.Vector3 *value);
        [propget] HRESULT VertexNormals([out, retval] Windows.Perception.Spatial.Surfaces.SpatialSurfaceMeshBuffer **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.Perception.Spatial.Surfaces.SpatialSurfaceMeshBuffer),
        uuid(93cf59e0-871f-33f8-98b2-03d101458f6f)
    ]
    interface ISpatialSurfaceMeshBuffer : IInspectable
    {
        [propget] HRESULT Format([out, retval] Windows.Graphics.DirectX.DirectXPixelFormat *value);
        [propget] HRESULT Stride([out, retval] UINT32 *value);
        [propget] HRESULT ElementCount([out, retval] UINT32 *value);
        [propget] HRESULT Data([out, retval] Windows.Storage.Streams.IBuffer **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.Perception.Spatial.Surfaces.SpatialSurfaceMeshOptions),
        uuid(d2759f89-3572-3d2d-a10d-5fee9394aa37)
    ]
    interface ISpatialSurfaceMeshOptions : IInspectable
    {
        [propget] HRESULT VertexPositionFormat([out, retval] Windows.Graphics.DirectX.DirectXPixelFormat *value);
        [propput] HRESULT VertexPositionFormat([in] Windows.Graphics.DirectX.DirectXPixelFormat value);
        [propget] HRESULT TriangleIndexFormat([out, retval] Windows.Graphics.DirectX.DirectXPixelFormat *value);
        [propput] HRESULT TriangleIndexFormat([in] Windows.Graphics.DirectX.DirectXPixelFormat value);
        [propget] HRESULT VertexNormalFormat([out, retval] Windows.Graphics.DirectX.DirectXPixelFormat *value);
        [propput] HRESULT VertexNormalFormat([in] Windows.Graphics.DirectX.DirectXPixelFormat value);
        [propget] HRESULT IncludeVertexNormals([out, retval] boolean *value);
        [propput] HRESULT IncludeVertexNormals([in] boolean value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.Perception.Spatial.Surfaces.SpatialSurfaceMeshOptions),
        uuid(9b340abf-**************-013575caae5e)
    ]
    interface ISpatialSurfaceMeshOptionsStatics : IInspectable
    {
        [propget] HRESULT SupportedVertexPositionFormats([out, retval] Windows.Foundation.Collections.IVectorView<Windows.Graphics.DirectX.DirectXPixelFormat> **value);
        [propget] HRESULT SupportedTriangleIndexFormats([out, retval] Windows.Foundation.Collections.IVectorView<Windows.Graphics.DirectX.DirectXPixelFormat> **value);
        [propget] HRESULT SupportedVertexNormalFormats([out, retval] Windows.Foundation.Collections.IVectorView<Windows.Graphics.DirectX.DirectXPixelFormat> **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.Perception.Spatial.Surfaces.SpatialSurfaceObserver),
        uuid(10b69819-ddca-3483-ac3a-748fe8c86df5)
    ]
    interface ISpatialSurfaceObserver : IInspectable
    {
        HRESULT GetObservedSurfaces([out, retval] Windows.Foundation.Collections.IMapView<GUID, Windows.Perception.Spatial.Surfaces.SpatialSurfaceInfo *> **value);
        HRESULT SetBoundingVolume([in] Windows.Perception.Spatial.SpatialBoundingVolume *bounds);
        HRESULT SetBoundingVolumes([in] Windows.Foundation.Collections.IIterable<Windows.Perception.Spatial.SpatialBoundingVolume *> *bounds);
        [eventadd] HRESULT ObservedSurfacesChanged(
            [in] Windows.Foundation.TypedEventHandler<Windows.Perception.Spatial.Surfaces.SpatialSurfaceObserver *, IInspectable *> *handler,
            [out, retval] EventRegistrationToken *token
        );
        [eventremove] HRESULT ObservedSurfacesChanged([in] EventRegistrationToken token);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        exclusiveto(Windows.Perception.Spatial.Surfaces.SpatialSurfaceObserver),
        uuid(165951ed-**************-87e027bc9285)
    ]
    interface ISpatialSurfaceObserverStatics : IInspectable
    {
        HRESULT RequestAccessAsync([out, retval] Windows.Foundation.IAsyncOperation<Windows.Perception.Spatial.SpatialPerceptionAccessStatus> **result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.Perception.Spatial.Surfaces.SpatialSurfaceObserver),
        uuid(0f534261-c55d-4e6b-a895-a19de69a42e3)
    ]
    interface ISpatialSurfaceObserverStatics2 : IInspectable
        requires Windows.Perception.Spatial.Surfaces.ISpatialSurfaceObserverStatics
    {
        HRESULT IsSupported([out, retval] boolean *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass SpatialSurfaceInfo
    {
        [default] interface Windows.Perception.Spatial.Surfaces.ISpatialSurfaceInfo;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass SpatialSurfaceMesh
    {
        [default] interface Windows.Perception.Spatial.Surfaces.ISpatialSurfaceMesh;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass SpatialSurfaceMeshBuffer
    {
        [default] interface Windows.Perception.Spatial.Surfaces.ISpatialSurfaceMeshBuffer;
    }

    [
        activatable(Windows.Foundation.UniversalApiContract, 2.0),
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        static(Windows.Perception.Spatial.Surfaces.ISpatialSurfaceMeshOptionsStatics, Windows.Foundation.UniversalApiContract, 2.0),
        threading(both)
    ]
    runtimeclass SpatialSurfaceMeshOptions
    {
        [default] interface Windows.Perception.Spatial.Surfaces.ISpatialSurfaceMeshOptions;
    }

    [
        activatable(Windows.Foundation.UniversalApiContract, 2.0),
        contract(Windows.Foundation.UniversalApiContract, 2.0),
        marshaling_behavior(agile),
        static(Windows.Perception.Spatial.Surfaces.ISpatialSurfaceObserverStatics, Windows.Foundation.UniversalApiContract, 2.0),
        static(Windows.Perception.Spatial.Surfaces.ISpatialSurfaceObserverStatics2, Windows.Foundation.UniversalApiContract, 4.0),
        threading(both)
    ]
    runtimeclass SpatialSurfaceObserver
    {
        [default] interface Windows.Perception.Spatial.Surfaces.ISpatialSurfaceObserver;
    }
}
