/*** Autogenerated by WIDL 10.8 from include/windows.system.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_system_h__
#define __windows_system_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueueHandler_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueueHandler_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler ABI::Windows::System::IDispatcherQueueHandler
namespace ABI {
    namespace Windows {
        namespace System {
            interface IDispatcherQueueHandler;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueue_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueue_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIDispatcherQueue __x_ABI_CWindows_CSystem_CIDispatcherQueue;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue ABI::Windows::System::IDispatcherQueue
namespace ABI {
    namespace Windows {
        namespace System {
            interface IDispatcherQueue;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueue2_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueue2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIDispatcherQueue2 __x_ABI_CWindows_CSystem_CIDispatcherQueue2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue2 ABI::Windows::System::IDispatcherQueue2
namespace ABI {
    namespace Windows {
        namespace System {
            interface IDispatcherQueue2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueueController_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueueController_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIDispatcherQueueController __x_ABI_CWindows_CSystem_CIDispatcherQueueController;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueController ABI::Windows::System::IDispatcherQueueController
namespace ABI {
    namespace Windows {
        namespace System {
            interface IDispatcherQueueController;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics ABI::Windows::System::IDispatcherQueueControllerStatics
namespace ABI {
    namespace Windows {
        namespace System {
            interface IDispatcherQueueControllerStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs ABI::Windows::System::IDispatcherQueueShutdownStartingEventArgs
namespace ABI {
    namespace Windows {
        namespace System {
            interface IDispatcherQueueShutdownStartingEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics ABI::Windows::System::IDispatcherQueueStatics
namespace ABI {
    namespace Windows {
        namespace System {
            interface IDispatcherQueueStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer ABI::Windows::System::IDispatcherQueueTimer
namespace ABI {
    namespace Windows {
        namespace System {
            interface IDispatcherQueueTimer;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIUserChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIUserChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIUserChangedEventArgs __x_ABI_CWindows_CSystem_CIUserChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIUserChangedEventArgs ABI::Windows::System::IUserChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace System {
            interface IUserChangedEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CDispatcherQueue_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CDispatcherQueue_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace System {
            class DispatcherQueue;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CSystem_CDispatcherQueue __x_ABI_CWindows_CSystem_CDispatcherQueue;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CSystem_CDispatcherQueue_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CSystem_CDispatcherQueueController_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CDispatcherQueueController_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace System {
            class DispatcherQueueController;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CSystem_CDispatcherQueueController __x_ABI_CWindows_CSystem_CDispatcherQueueController;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CSystem_CDispatcherQueueController_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CSystem_CDispatcherQueueShutdownStartingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CDispatcherQueueShutdownStartingEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace System {
            class DispatcherQueueShutdownStartingEventArgs;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CSystem_CDispatcherQueueShutdownStartingEventArgs __x_ABI_CWindows_CSystem_CDispatcherQueueShutdownStartingEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CSystem_CDispatcherQueueShutdownStartingEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CSystem_CDispatcherQueueTimer_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CDispatcherQueueTimer_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace System {
            class DispatcherQueueTimer;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CSystem_CDispatcherQueueTimer __x_ABI_CWindows_CSystem_CDispatcherQueueTimer;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CSystem_CDispatcherQueueTimer_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CSystem_CUser_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CUser_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace System {
            class User;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CSystem_CUser __x_ABI_CWindows_CSystem_CUser;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CSystem_CUser_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CSystem_CUserChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CUserChangedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace System {
            class UserChangedEventArgs;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CSystem_CUserChangedEventArgs __x_ABI_CWindows_CSystem_CUserChangedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CSystem_CUserChangedEventArgs_FWD_DEFINED__ */

#ifndef ____FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::System::DispatcherQueue*,IInspectable* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::System::DispatcherQueue*,ABI::Windows::System::DispatcherQueueShutdownStartingEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::System::DispatcherQueueTimer*,IInspectable* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CSystem_CDispatcherQueuePriority __x_ABI_CWindows_CSystem_CDispatcherQueuePriority;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CSystem_CProcessorArchitecture __x_ABI_CWindows_CSystem_CProcessorArchitecture;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CSystem_CVirtualKey __x_ABI_CWindows_CSystem_CVirtualKey;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CSystem_CVirtualKeyModifiers __x_ABI_CWindows_CSystem_CVirtualKeyModifiers;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueue_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueue_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIDispatcherQueue __x_ABI_CWindows_CSystem_CIDispatcherQueue;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue ABI::Windows::System::IDispatcherQueue
namespace ABI {
    namespace Windows {
        namespace System {
            interface IDispatcherQueue;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueue2_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueue2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIDispatcherQueue2 __x_ABI_CWindows_CSystem_CIDispatcherQueue2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue2 ABI::Windows::System::IDispatcherQueue2
namespace ABI {
    namespace Windows {
        namespace System {
            interface IDispatcherQueue2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueueController_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueueController_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIDispatcherQueueController __x_ABI_CWindows_CSystem_CIDispatcherQueueController;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueController ABI::Windows::System::IDispatcherQueueController
namespace ABI {
    namespace Windows {
        namespace System {
            interface IDispatcherQueueController;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics ABI::Windows::System::IDispatcherQueueControllerStatics
namespace ABI {
    namespace Windows {
        namespace System {
            interface IDispatcherQueueControllerStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs ABI::Windows::System::IDispatcherQueueShutdownStartingEventArgs
namespace ABI {
    namespace Windows {
        namespace System {
            interface IDispatcherQueueShutdownStartingEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics ABI::Windows::System::IDispatcherQueueStatics
namespace ABI {
    namespace Windows {
        namespace System {
            interface IDispatcherQueueStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer ABI::Windows::System::IDispatcherQueueTimer
namespace ABI {
    namespace Windows {
        namespace System {
            interface IDispatcherQueueTimer;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIUser_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIUser_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIUser __x_ABI_CWindows_CSystem_CIUser;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIUser ABI::Windows::System::IUser
namespace ABI {
    namespace Windows {
        namespace System {
            interface IUser;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIUserStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIUserStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIUserStatics __x_ABI_CWindows_CSystem_CIUserStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIUserStatics ABI::Windows::System::IUserStatics
namespace ABI {
    namespace Windows {
        namespace System {
            interface IUserStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIUserChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIUserChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIUserChangedEventArgs __x_ABI_CWindows_CSystem_CIUserChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIUserChangedEventArgs ABI::Windows::System::IUserChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace System {
            interface IUserChangedEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSystem_CIUserChangedEventArgs2_FWD_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIUserChangedEventArgs2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSystem_CIUserChangedEventArgs2 __x_ABI_CWindows_CSystem_CIUserChangedEventArgs2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSystem_CIUserChangedEventArgs2 ABI::Windows::System::IUserChangedEventArgs2
namespace ABI {
    namespace Windows {
        namespace System {
            interface IUserChangedEventArgs2;
        }
    }
}
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace System {
            enum DispatcherQueuePriority {
                DispatcherQueuePriority_Low = -10,
                DispatcherQueuePriority_Normal = 0,
                DispatcherQueuePriority_High = 10
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CSystem_CDispatcherQueuePriority {
    DispatcherQueuePriority_Low = -10,
    DispatcherQueuePriority_Normal = 0,
    DispatcherQueuePriority_High = 10
};
#ifdef WIDL_using_Windows_System
#define DispatcherQueuePriority __x_ABI_CWindows_CSystem_CDispatcherQueuePriority
#endif /* WIDL_using_Windows_System */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace System {
            enum ProcessorArchitecture {
                ProcessorArchitecture_X86 = 0,
                ProcessorArchitecture_Arm = 5,
                ProcessorArchitecture_X64 = 9,
                ProcessorArchitecture_Neutral = 11,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
                ProcessorArchitecture_Arm64 = 12,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
                ProcessorArchitecture_X86OnArm64 = 14,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */
                ProcessorArchitecture_Unknown = 65535
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CSystem_CProcessorArchitecture {
    ProcessorArchitecture_X86 = 0,
    ProcessorArchitecture_Arm = 5,
    ProcessorArchitecture_X64 = 9,
    ProcessorArchitecture_Neutral = 11,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
    ProcessorArchitecture_Arm64 = 12,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
    ProcessorArchitecture_X86OnArm64 = 14,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */
    ProcessorArchitecture_Unknown = 65535
};
#ifdef WIDL_using_Windows_System
#define ProcessorArchitecture __x_ABI_CWindows_CSystem_CProcessorArchitecture
#endif /* WIDL_using_Windows_System */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace System {
            enum VirtualKey {
                VirtualKey_None = 0,
                VirtualKey_LeftButton = 1,
                VirtualKey_RightButton = 2,
                VirtualKey_Cancel = 3,
                VirtualKey_MiddleButton = 4,
                VirtualKey_XButton1 = 5,
                VirtualKey_XButton2 = 6,
                VirtualKey_Back = 8,
                VirtualKey_Tab = 9,
                VirtualKey_Clear = 12,
                VirtualKey_Enter = 13,
                VirtualKey_Shift = 16,
                VirtualKey_Control = 17,
                VirtualKey_Menu = 18,
                VirtualKey_Pause = 19,
                VirtualKey_CapitalLock = 20,
                VirtualKey_Kana = 21,
                VirtualKey_Hangul = 21,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
                VirtualKey_ImeOn = 22,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */
                VirtualKey_Junja = 23,
                VirtualKey_Final = 24,
                VirtualKey_Hanja = 25,
                VirtualKey_Kanji = 25,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
                VirtualKey_ImeOff = 26,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */
                VirtualKey_Escape = 27,
                VirtualKey_Convert = 28,
                VirtualKey_NonConvert = 29,
                VirtualKey_Accept = 30,
                VirtualKey_ModeChange = 31,
                VirtualKey_Space = 32,
                VirtualKey_PageUp = 33,
                VirtualKey_PageDown = 34,
                VirtualKey_End = 35,
                VirtualKey_Home = 36,
                VirtualKey_Left = 37,
                VirtualKey_Up = 38,
                VirtualKey_Right = 39,
                VirtualKey_Down = 40,
                VirtualKey_Select = 41,
                VirtualKey_Print = 42,
                VirtualKey_Execute = 43,
                VirtualKey_Snapshot = 44,
                VirtualKey_Insert = 45,
                VirtualKey_Delete = 46,
                VirtualKey_Help = 47,
                VirtualKey_Number0 = 48,
                VirtualKey_Number1 = 49,
                VirtualKey_Number2 = 50,
                VirtualKey_Number3 = 51,
                VirtualKey_Number4 = 52,
                VirtualKey_Number5 = 53,
                VirtualKey_Number6 = 54,
                VirtualKey_Number7 = 55,
                VirtualKey_Number8 = 56,
                VirtualKey_Number9 = 57,
                VirtualKey_A = 65,
                VirtualKey_B = 66,
                VirtualKey_C = 67,
                VirtualKey_D = 68,
                VirtualKey_E = 69,
                VirtualKey_F = 70,
                VirtualKey_G = 71,
                VirtualKey_H = 72,
                VirtualKey_I = 73,
                VirtualKey_J = 74,
                VirtualKey_K = 75,
                VirtualKey_L = 76,
                VirtualKey_M = 77,
                VirtualKey_N = 78,
                VirtualKey_O = 79,
                VirtualKey_P = 80,
                VirtualKey_Q = 81,
                VirtualKey_R = 82,
                VirtualKey_S = 83,
                VirtualKey_T = 84,
                VirtualKey_U = 85,
                VirtualKey_V = 86,
                VirtualKey_W = 87,
                VirtualKey_X = 88,
                VirtualKey_Y = 89,
                VirtualKey_Z = 90,
                VirtualKey_LeftWindows = 91,
                VirtualKey_RightWindows = 92,
                VirtualKey_Application = 93,
                VirtualKey_Sleep = 95,
                VirtualKey_NumberPad0 = 96,
                VirtualKey_NumberPad1 = 97,
                VirtualKey_NumberPad2 = 98,
                VirtualKey_NumberPad3 = 99,
                VirtualKey_NumberPad4 = 100,
                VirtualKey_NumberPad5 = 101,
                VirtualKey_NumberPad6 = 102,
                VirtualKey_NumberPad7 = 103,
                VirtualKey_NumberPad8 = 104,
                VirtualKey_NumberPad9 = 105,
                VirtualKey_Multiply = 106,
                VirtualKey_Add = 107,
                VirtualKey_Separator = 108,
                VirtualKey_Subtract = 109,
                VirtualKey_Decimal = 110,
                VirtualKey_Divide = 111,
                VirtualKey_F1 = 112,
                VirtualKey_F2 = 113,
                VirtualKey_F3 = 114,
                VirtualKey_F4 = 115,
                VirtualKey_F5 = 116,
                VirtualKey_F6 = 117,
                VirtualKey_F7 = 118,
                VirtualKey_F8 = 119,
                VirtualKey_F9 = 120,
                VirtualKey_F10 = 121,
                VirtualKey_F11 = 122,
                VirtualKey_F12 = 123,
                VirtualKey_F13 = 124,
                VirtualKey_F14 = 125,
                VirtualKey_F15 = 126,
                VirtualKey_F16 = 127,
                VirtualKey_F17 = 128,
                VirtualKey_F18 = 129,
                VirtualKey_F19 = 130,
                VirtualKey_F20 = 131,
                VirtualKey_F21 = 132,
                VirtualKey_F22 = 133,
                VirtualKey_F23 = 134,
                VirtualKey_F24 = 135,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_NavigationView = 136,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_NavigationMenu = 137,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_NavigationUp = 138,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_NavigationDown = 139,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_NavigationLeft = 140,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_NavigationRight = 141,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_NavigationAccept = 142,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_NavigationCancel = 143,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
                VirtualKey_NumberKeyLock = 144,
                VirtualKey_Scroll = 145,
                VirtualKey_LeftShift = 160,
                VirtualKey_RightShift = 161,
                VirtualKey_LeftControl = 162,
                VirtualKey_RightControl = 163,
                VirtualKey_LeftMenu = 164,
                VirtualKey_RightMenu = 165,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GoBack = 166,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GoForward = 167,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_Refresh = 168,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_Stop = 169,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_Search = 170,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_Favorites = 171,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GoHome = 172,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadA = 195,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadB = 196,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadX = 197,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadY = 198,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadRightShoulder = 199,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadLeftShoulder = 200,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadLeftTrigger = 201,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadRightTrigger = 202,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadDPadUp = 203,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadDPadDown = 204,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadDPadLeft = 205,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadDPadRight = 206,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadMenu = 207,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadView = 208,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadLeftThumbstickButton = 209,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadRightThumbstickButton = 210,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadLeftThumbstickUp = 211,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadLeftThumbstickDown = 212,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadLeftThumbstickRight = 213,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadLeftThumbstickLeft = 214,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadRightThumbstickUp = 215,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadRightThumbstickDown = 216,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadRightThumbstickRight = 217,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                VirtualKey_GamepadRightThumbstickLeft = 218
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CSystem_CVirtualKey {
    VirtualKey_None = 0,
    VirtualKey_LeftButton = 1,
    VirtualKey_RightButton = 2,
    VirtualKey_Cancel = 3,
    VirtualKey_MiddleButton = 4,
    VirtualKey_XButton1 = 5,
    VirtualKey_XButton2 = 6,
    VirtualKey_Back = 8,
    VirtualKey_Tab = 9,
    VirtualKey_Clear = 12,
    VirtualKey_Enter = 13,
    VirtualKey_Shift = 16,
    VirtualKey_Control = 17,
    VirtualKey_Menu = 18,
    VirtualKey_Pause = 19,
    VirtualKey_CapitalLock = 20,
    VirtualKey_Kana = 21,
    VirtualKey_Hangul = 21,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
    VirtualKey_ImeOn = 22,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */
    VirtualKey_Junja = 23,
    VirtualKey_Final = 24,
    VirtualKey_Hanja = 25,
    VirtualKey_Kanji = 25,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
    VirtualKey_ImeOff = 26,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */
    VirtualKey_Escape = 27,
    VirtualKey_Convert = 28,
    VirtualKey_NonConvert = 29,
    VirtualKey_Accept = 30,
    VirtualKey_ModeChange = 31,
    VirtualKey_Space = 32,
    VirtualKey_PageUp = 33,
    VirtualKey_PageDown = 34,
    VirtualKey_End = 35,
    VirtualKey_Home = 36,
    VirtualKey_Left = 37,
    VirtualKey_Up = 38,
    VirtualKey_Right = 39,
    VirtualKey_Down = 40,
    VirtualKey_Select = 41,
    VirtualKey_Print = 42,
    VirtualKey_Execute = 43,
    VirtualKey_Snapshot = 44,
    VirtualKey_Insert = 45,
    VirtualKey_Delete = 46,
    VirtualKey_Help = 47,
    VirtualKey_Number0 = 48,
    VirtualKey_Number1 = 49,
    VirtualKey_Number2 = 50,
    VirtualKey_Number3 = 51,
    VirtualKey_Number4 = 52,
    VirtualKey_Number5 = 53,
    VirtualKey_Number6 = 54,
    VirtualKey_Number7 = 55,
    VirtualKey_Number8 = 56,
    VirtualKey_Number9 = 57,
    VirtualKey_A = 65,
    VirtualKey_B = 66,
    VirtualKey_C = 67,
    VirtualKey_D = 68,
    VirtualKey_E = 69,
    VirtualKey_F = 70,
    VirtualKey_G = 71,
    VirtualKey_H = 72,
    VirtualKey_I = 73,
    VirtualKey_J = 74,
    VirtualKey_K = 75,
    VirtualKey_L = 76,
    VirtualKey_M = 77,
    VirtualKey_N = 78,
    VirtualKey_O = 79,
    VirtualKey_P = 80,
    VirtualKey_Q = 81,
    VirtualKey_R = 82,
    VirtualKey_S = 83,
    VirtualKey_T = 84,
    VirtualKey_U = 85,
    VirtualKey_V = 86,
    VirtualKey_W = 87,
    VirtualKey_X = 88,
    VirtualKey_Y = 89,
    VirtualKey_Z = 90,
    VirtualKey_LeftWindows = 91,
    VirtualKey_RightWindows = 92,
    VirtualKey_Application = 93,
    VirtualKey_Sleep = 95,
    VirtualKey_NumberPad0 = 96,
    VirtualKey_NumberPad1 = 97,
    VirtualKey_NumberPad2 = 98,
    VirtualKey_NumberPad3 = 99,
    VirtualKey_NumberPad4 = 100,
    VirtualKey_NumberPad5 = 101,
    VirtualKey_NumberPad6 = 102,
    VirtualKey_NumberPad7 = 103,
    VirtualKey_NumberPad8 = 104,
    VirtualKey_NumberPad9 = 105,
    VirtualKey_Multiply = 106,
    VirtualKey_Add = 107,
    VirtualKey_Separator = 108,
    VirtualKey_Subtract = 109,
    VirtualKey_Decimal = 110,
    VirtualKey_Divide = 111,
    VirtualKey_F1 = 112,
    VirtualKey_F2 = 113,
    VirtualKey_F3 = 114,
    VirtualKey_F4 = 115,
    VirtualKey_F5 = 116,
    VirtualKey_F6 = 117,
    VirtualKey_F7 = 118,
    VirtualKey_F8 = 119,
    VirtualKey_F9 = 120,
    VirtualKey_F10 = 121,
    VirtualKey_F11 = 122,
    VirtualKey_F12 = 123,
    VirtualKey_F13 = 124,
    VirtualKey_F14 = 125,
    VirtualKey_F15 = 126,
    VirtualKey_F16 = 127,
    VirtualKey_F17 = 128,
    VirtualKey_F18 = 129,
    VirtualKey_F19 = 130,
    VirtualKey_F20 = 131,
    VirtualKey_F21 = 132,
    VirtualKey_F22 = 133,
    VirtualKey_F23 = 134,
    VirtualKey_F24 = 135,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_NavigationView = 136,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_NavigationMenu = 137,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_NavigationUp = 138,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_NavigationDown = 139,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_NavigationLeft = 140,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_NavigationRight = 141,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_NavigationAccept = 142,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_NavigationCancel = 143,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
    VirtualKey_NumberKeyLock = 144,
    VirtualKey_Scroll = 145,
    VirtualKey_LeftShift = 160,
    VirtualKey_RightShift = 161,
    VirtualKey_LeftControl = 162,
    VirtualKey_RightControl = 163,
    VirtualKey_LeftMenu = 164,
    VirtualKey_RightMenu = 165,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GoBack = 166,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GoForward = 167,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_Refresh = 168,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_Stop = 169,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_Search = 170,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_Favorites = 171,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GoHome = 172,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadA = 195,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadB = 196,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadX = 197,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadY = 198,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadRightShoulder = 199,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadLeftShoulder = 200,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadLeftTrigger = 201,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadRightTrigger = 202,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadDPadUp = 203,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadDPadDown = 204,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadDPadLeft = 205,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadDPadRight = 206,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadMenu = 207,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadView = 208,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadLeftThumbstickButton = 209,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadRightThumbstickButton = 210,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadLeftThumbstickUp = 211,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadLeftThumbstickDown = 212,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadLeftThumbstickRight = 213,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadLeftThumbstickLeft = 214,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadRightThumbstickUp = 215,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadRightThumbstickDown = 216,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadRightThumbstickRight = 217,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    VirtualKey_GamepadRightThumbstickLeft = 218
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
};
#ifdef WIDL_using_Windows_System
#define VirtualKey __x_ABI_CWindows_CSystem_CVirtualKey
#endif /* WIDL_using_Windows_System */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace System {
            enum VirtualKeyModifiers {
                VirtualKeyModifiers_None = 0x0,
                VirtualKeyModifiers_Control = 0x1,
                VirtualKeyModifiers_Menu = 0x2,
                VirtualKeyModifiers_Shift = 0x4,
                VirtualKeyModifiers_Windows = 0x8
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CSystem_CVirtualKeyModifiers {
    VirtualKeyModifiers_None = 0x0,
    VirtualKeyModifiers_Control = 0x1,
    VirtualKeyModifiers_Menu = 0x2,
    VirtualKeyModifiers_Shift = 0x4,
    VirtualKeyModifiers_Windows = 0x8
};
#ifdef WIDL_using_Windows_System
#define VirtualKeyModifiers __x_ABI_CWindows_CSystem_CVirtualKeyModifiers
#endif /* WIDL_using_Windows_System */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
/*****************************************************************************
 * IDispatcherQueueHandler interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueueHandler_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueueHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSystem_CIDispatcherQueueHandler, 0xdfa2dc9c, 0x1a2d, 0x4917, 0x98,0xf2, 0x93,0x9a,0xf1,0xd6,0xe0,0xc8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace System {
            MIDL_INTERFACE("dfa2dc9c-1a2d-4917-98f2-939af1d6e0c8")
            IDispatcherQueueHandler : public IUnknown
            {
                virtual HRESULT STDMETHODCALLTYPE Invoke(
                    ) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSystem_CIDispatcherQueueHandler, 0xdfa2dc9c, 0x1a2d, 0x4917, 0x98,0xf2, 0x93,0x9a,0xf1,0xd6,0xe0,0xc8)
#endif
#else
typedef struct __x_ABI_CWindows_CSystem_CIDispatcherQueueHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler *This);

    /*** IDispatcherQueueHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler *This);

    END_INTERFACE
} __x_ABI_CWindows_CSystem_CIDispatcherQueueHandlerVtbl;

interface __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler {
    CONST_VTBL __x_ABI_CWindows_CSystem_CIDispatcherQueueHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatcherQueueHandler methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler_Invoke(This) (This)->lpVtbl->Invoke(This)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler_QueryInterface(__x_ABI_CWindows_CSystem_CIDispatcherQueueHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler_AddRef(__x_ABI_CWindows_CSystem_CIDispatcherQueueHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler_Release(__x_ABI_CWindows_CSystem_CIDispatcherQueueHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatcherQueueHandler methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler_Invoke(__x_ABI_CWindows_CSystem_CIDispatcherQueueHandler* This) {
    return This->lpVtbl->Invoke(This);
}
#endif
#ifdef WIDL_using_Windows_System
#define IID_IDispatcherQueueHandler IID___x_ABI_CWindows_CSystem_CIDispatcherQueueHandler
#define IDispatcherQueueHandlerVtbl __x_ABI_CWindows_CSystem_CIDispatcherQueueHandlerVtbl
#define IDispatcherQueueHandler __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler
#define IDispatcherQueueHandler_QueryInterface __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler_QueryInterface
#define IDispatcherQueueHandler_AddRef __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler_AddRef
#define IDispatcherQueueHandler_Release __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler_Release
#define IDispatcherQueueHandler_Invoke __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler_Invoke
#endif /* WIDL_using_Windows_System */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSystem_CIDispatcherQueueHandler_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * IDispatcherQueue interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueue_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueue_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSystem_CIDispatcherQueue, 0x603e88e4, 0xa338, 0x4ffe, 0xa4,0x57, 0xa5,0xcf,0xb9,0xce,0xb8,0x99);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace System {
            MIDL_INTERFACE("603e88e4-a338-4ffe-a457-a5cfb9ceb899")
            IDispatcherQueue : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE CreateTimer(
                    ABI::Windows::System::IDispatcherQueueTimer **result) = 0;

                virtual HRESULT STDMETHODCALLTYPE TryEnqueue(
                    ABI::Windows::System::IDispatcherQueueHandler *callback,
                    boolean *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE TryEnqueueWithPriority(
                    ABI::Windows::System::DispatcherQueuePriority priority,
                    ABI::Windows::System::IDispatcherQueueHandler *callback,
                    boolean *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE add_ShutdownStarting(
                    ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::System::DispatcherQueue*,ABI::Windows::System::DispatcherQueueShutdownStartingEventArgs* > *handler,
                    EventRegistrationToken *token) = 0;

                virtual HRESULT STDMETHODCALLTYPE remove_ShutdownStarting(
                    EventRegistrationToken token) = 0;

                virtual HRESULT STDMETHODCALLTYPE add_ShutdownCompleted(
                    ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::System::DispatcherQueue*,IInspectable* > *handler,
                    EventRegistrationToken *token) = 0;

                virtual HRESULT STDMETHODCALLTYPE remove_ShutdownCompleted(
                    EventRegistrationToken token) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSystem_CIDispatcherQueue, 0x603e88e4, 0xa338, 0x4ffe, 0xa4,0x57, 0xa5,0xcf,0xb9,0xce,0xb8,0x99)
#endif
#else
typedef struct __x_ABI_CWindows_CSystem_CIDispatcherQueueVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue *This,
        TrustLevel *trustLevel);

    /*** IDispatcherQueue methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateTimer)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue *This,
        __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer **result);

    HRESULT (STDMETHODCALLTYPE *TryEnqueue)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue *This,
        __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler *callback,
        boolean *result);

    HRESULT (STDMETHODCALLTYPE *TryEnqueueWithPriority)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue *This,
        __x_ABI_CWindows_CSystem_CDispatcherQueuePriority priority,
        __x_ABI_CWindows_CSystem_CIDispatcherQueueHandler *callback,
        boolean *result);

    HRESULT (STDMETHODCALLTYPE *add_ShutdownStarting)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue *This,
        __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_ShutdownStarting)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_ShutdownCompleted)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue *This,
        __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_ShutdownCompleted)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CSystem_CIDispatcherQueueVtbl;

interface __x_ABI_CWindows_CSystem_CIDispatcherQueue {
    CONST_VTBL __x_ABI_CWindows_CSystem_CIDispatcherQueueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDispatcherQueue methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue_CreateTimer(This,result) (This)->lpVtbl->CreateTimer(This,result)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue_TryEnqueue(This,callback,result) (This)->lpVtbl->TryEnqueue(This,callback,result)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue_TryEnqueueWithPriority(This,priority,callback,result) (This)->lpVtbl->TryEnqueueWithPriority(This,priority,callback,result)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue_add_ShutdownStarting(This,handler,token) (This)->lpVtbl->add_ShutdownStarting(This,handler,token)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue_remove_ShutdownStarting(This,token) (This)->lpVtbl->remove_ShutdownStarting(This,token)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue_add_ShutdownCompleted(This,handler,token) (This)->lpVtbl->add_ShutdownCompleted(This,handler,token)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue_remove_ShutdownCompleted(This,token) (This)->lpVtbl->remove_ShutdownCompleted(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueue_QueryInterface(__x_ABI_CWindows_CSystem_CIDispatcherQueue* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIDispatcherQueue_AddRef(__x_ABI_CWindows_CSystem_CIDispatcherQueue* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIDispatcherQueue_Release(__x_ABI_CWindows_CSystem_CIDispatcherQueue* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueue_GetIids(__x_ABI_CWindows_CSystem_CIDispatcherQueue* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueue_GetRuntimeClassName(__x_ABI_CWindows_CSystem_CIDispatcherQueue* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueue_GetTrustLevel(__x_ABI_CWindows_CSystem_CIDispatcherQueue* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDispatcherQueue methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueue_CreateTimer(__x_ABI_CWindows_CSystem_CIDispatcherQueue* This,__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer **result) {
    return This->lpVtbl->CreateTimer(This,result);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueue_TryEnqueue(__x_ABI_CWindows_CSystem_CIDispatcherQueue* This,__x_ABI_CWindows_CSystem_CIDispatcherQueueHandler *callback,boolean *result) {
    return This->lpVtbl->TryEnqueue(This,callback,result);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueue_TryEnqueueWithPriority(__x_ABI_CWindows_CSystem_CIDispatcherQueue* This,__x_ABI_CWindows_CSystem_CDispatcherQueuePriority priority,__x_ABI_CWindows_CSystem_CIDispatcherQueueHandler *callback,boolean *result) {
    return This->lpVtbl->TryEnqueueWithPriority(This,priority,callback,result);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueue_add_ShutdownStarting(__x_ABI_CWindows_CSystem_CIDispatcherQueue* This,__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_ShutdownStarting(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueue_remove_ShutdownStarting(__x_ABI_CWindows_CSystem_CIDispatcherQueue* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_ShutdownStarting(This,token);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueue_add_ShutdownCompleted(__x_ABI_CWindows_CSystem_CIDispatcherQueue* This,__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_ShutdownCompleted(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueue_remove_ShutdownCompleted(__x_ABI_CWindows_CSystem_CIDispatcherQueue* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_ShutdownCompleted(This,token);
}
#endif
#ifdef WIDL_using_Windows_System
#define IID_IDispatcherQueue IID___x_ABI_CWindows_CSystem_CIDispatcherQueue
#define IDispatcherQueueVtbl __x_ABI_CWindows_CSystem_CIDispatcherQueueVtbl
#define IDispatcherQueue __x_ABI_CWindows_CSystem_CIDispatcherQueue
#define IDispatcherQueue_QueryInterface __x_ABI_CWindows_CSystem_CIDispatcherQueue_QueryInterface
#define IDispatcherQueue_AddRef __x_ABI_CWindows_CSystem_CIDispatcherQueue_AddRef
#define IDispatcherQueue_Release __x_ABI_CWindows_CSystem_CIDispatcherQueue_Release
#define IDispatcherQueue_GetIids __x_ABI_CWindows_CSystem_CIDispatcherQueue_GetIids
#define IDispatcherQueue_GetRuntimeClassName __x_ABI_CWindows_CSystem_CIDispatcherQueue_GetRuntimeClassName
#define IDispatcherQueue_GetTrustLevel __x_ABI_CWindows_CSystem_CIDispatcherQueue_GetTrustLevel
#define IDispatcherQueue_CreateTimer __x_ABI_CWindows_CSystem_CIDispatcherQueue_CreateTimer
#define IDispatcherQueue_TryEnqueue __x_ABI_CWindows_CSystem_CIDispatcherQueue_TryEnqueue
#define IDispatcherQueue_TryEnqueueWithPriority __x_ABI_CWindows_CSystem_CIDispatcherQueue_TryEnqueueWithPriority
#define IDispatcherQueue_add_ShutdownStarting __x_ABI_CWindows_CSystem_CIDispatcherQueue_add_ShutdownStarting
#define IDispatcherQueue_remove_ShutdownStarting __x_ABI_CWindows_CSystem_CIDispatcherQueue_remove_ShutdownStarting
#define IDispatcherQueue_add_ShutdownCompleted __x_ABI_CWindows_CSystem_CIDispatcherQueue_add_ShutdownCompleted
#define IDispatcherQueue_remove_ShutdownCompleted __x_ABI_CWindows_CSystem_CIDispatcherQueue_remove_ShutdownCompleted
#endif /* WIDL_using_Windows_System */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSystem_CIDispatcherQueue_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * IDispatcherQueue2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueue2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueue2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSystem_CIDispatcherQueue2, 0xc822c647, 0x30ef, 0x506e, 0xbd,0x1e, 0xa6,0x47,0xae,0x66,0x75,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace System {
            MIDL_INTERFACE("c822c647-30ef-506e-bd1e-a647ae6675ff")
            IDispatcherQueue2 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_HasThreadAccess(
                    boolean *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSystem_CIDispatcherQueue2, 0xc822c647, 0x30ef, 0x506e, 0xbd,0x1e, 0xa6,0x47,0xae,0x66,0x75,0xff)
#endif
#else
typedef struct __x_ABI_CWindows_CSystem_CIDispatcherQueue2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue2 *This,
        TrustLevel *trustLevel);

    /*** IDispatcherQueue2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_HasThreadAccess)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueue2 *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CSystem_CIDispatcherQueue2Vtbl;

interface __x_ABI_CWindows_CSystem_CIDispatcherQueue2 {
    CONST_VTBL __x_ABI_CWindows_CSystem_CIDispatcherQueue2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDispatcherQueue2 methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueue2_get_HasThreadAccess(This,value) (This)->lpVtbl->get_HasThreadAccess(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueue2_QueryInterface(__x_ABI_CWindows_CSystem_CIDispatcherQueue2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIDispatcherQueue2_AddRef(__x_ABI_CWindows_CSystem_CIDispatcherQueue2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIDispatcherQueue2_Release(__x_ABI_CWindows_CSystem_CIDispatcherQueue2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueue2_GetIids(__x_ABI_CWindows_CSystem_CIDispatcherQueue2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueue2_GetRuntimeClassName(__x_ABI_CWindows_CSystem_CIDispatcherQueue2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueue2_GetTrustLevel(__x_ABI_CWindows_CSystem_CIDispatcherQueue2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDispatcherQueue2 methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueue2_get_HasThreadAccess(__x_ABI_CWindows_CSystem_CIDispatcherQueue2* This,boolean *value) {
    return This->lpVtbl->get_HasThreadAccess(This,value);
}
#endif
#ifdef WIDL_using_Windows_System
#define IID_IDispatcherQueue2 IID___x_ABI_CWindows_CSystem_CIDispatcherQueue2
#define IDispatcherQueue2Vtbl __x_ABI_CWindows_CSystem_CIDispatcherQueue2Vtbl
#define IDispatcherQueue2 __x_ABI_CWindows_CSystem_CIDispatcherQueue2
#define IDispatcherQueue2_QueryInterface __x_ABI_CWindows_CSystem_CIDispatcherQueue2_QueryInterface
#define IDispatcherQueue2_AddRef __x_ABI_CWindows_CSystem_CIDispatcherQueue2_AddRef
#define IDispatcherQueue2_Release __x_ABI_CWindows_CSystem_CIDispatcherQueue2_Release
#define IDispatcherQueue2_GetIids __x_ABI_CWindows_CSystem_CIDispatcherQueue2_GetIids
#define IDispatcherQueue2_GetRuntimeClassName __x_ABI_CWindows_CSystem_CIDispatcherQueue2_GetRuntimeClassName
#define IDispatcherQueue2_GetTrustLevel __x_ABI_CWindows_CSystem_CIDispatcherQueue2_GetTrustLevel
#define IDispatcherQueue2_get_HasThreadAccess __x_ABI_CWindows_CSystem_CIDispatcherQueue2_get_HasThreadAccess
#endif /* WIDL_using_Windows_System */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSystem_CIDispatcherQueue2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */

/*****************************************************************************
 * IDispatcherQueueController interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueueController_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueueController_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSystem_CIDispatcherQueueController, 0x22f34e66, 0x50db, 0x4e36, 0xa9,0x8d, 0x61,0xc0,0x1b,0x38,0x4d,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace System {
            MIDL_INTERFACE("22f34e66-50db-4e36-a98d-61c01b384d20")
            IDispatcherQueueController : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_DispatcherQueue(
                    ABI::Windows::System::IDispatcherQueue **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE ShutdownQueueAsync(
                    ABI::Windows::Foundation::IAsyncAction **operation) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSystem_CIDispatcherQueueController, 0x22f34e66, 0x50db, 0x4e36, 0xa9,0x8d, 0x61,0xc0,0x1b,0x38,0x4d,0x20)
#endif
#else
typedef struct __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueController *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueController *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueController *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueController *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueController *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueController *This,
        TrustLevel *trustLevel);

    /*** IDispatcherQueueController methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DispatcherQueue)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueController *This,
        __x_ABI_CWindows_CSystem_CIDispatcherQueue **value);

    HRESULT (STDMETHODCALLTYPE *ShutdownQueueAsync)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueController *This,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **operation);

    END_INTERFACE
} __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerVtbl;

interface __x_ABI_CWindows_CSystem_CIDispatcherQueueController {
    CONST_VTBL __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueController_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueController_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueController_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueController_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueController_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueController_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDispatcherQueueController methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueController_get_DispatcherQueue(This,value) (This)->lpVtbl->get_DispatcherQueue(This,value)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueController_ShutdownQueueAsync(This,operation) (This)->lpVtbl->ShutdownQueueAsync(This,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueController_QueryInterface(__x_ABI_CWindows_CSystem_CIDispatcherQueueController* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIDispatcherQueueController_AddRef(__x_ABI_CWindows_CSystem_CIDispatcherQueueController* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIDispatcherQueueController_Release(__x_ABI_CWindows_CSystem_CIDispatcherQueueController* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueController_GetIids(__x_ABI_CWindows_CSystem_CIDispatcherQueueController* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueController_GetRuntimeClassName(__x_ABI_CWindows_CSystem_CIDispatcherQueueController* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueController_GetTrustLevel(__x_ABI_CWindows_CSystem_CIDispatcherQueueController* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDispatcherQueueController methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueController_get_DispatcherQueue(__x_ABI_CWindows_CSystem_CIDispatcherQueueController* This,__x_ABI_CWindows_CSystem_CIDispatcherQueue **value) {
    return This->lpVtbl->get_DispatcherQueue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueController_ShutdownQueueAsync(__x_ABI_CWindows_CSystem_CIDispatcherQueueController* This,__x_ABI_CWindows_CFoundation_CIAsyncAction **operation) {
    return This->lpVtbl->ShutdownQueueAsync(This,operation);
}
#endif
#ifdef WIDL_using_Windows_System
#define IID_IDispatcherQueueController IID___x_ABI_CWindows_CSystem_CIDispatcherQueueController
#define IDispatcherQueueControllerVtbl __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerVtbl
#define IDispatcherQueueController __x_ABI_CWindows_CSystem_CIDispatcherQueueController
#define IDispatcherQueueController_QueryInterface __x_ABI_CWindows_CSystem_CIDispatcherQueueController_QueryInterface
#define IDispatcherQueueController_AddRef __x_ABI_CWindows_CSystem_CIDispatcherQueueController_AddRef
#define IDispatcherQueueController_Release __x_ABI_CWindows_CSystem_CIDispatcherQueueController_Release
#define IDispatcherQueueController_GetIids __x_ABI_CWindows_CSystem_CIDispatcherQueueController_GetIids
#define IDispatcherQueueController_GetRuntimeClassName __x_ABI_CWindows_CSystem_CIDispatcherQueueController_GetRuntimeClassName
#define IDispatcherQueueController_GetTrustLevel __x_ABI_CWindows_CSystem_CIDispatcherQueueController_GetTrustLevel
#define IDispatcherQueueController_get_DispatcherQueue __x_ABI_CWindows_CSystem_CIDispatcherQueueController_get_DispatcherQueue
#define IDispatcherQueueController_ShutdownQueueAsync __x_ABI_CWindows_CSystem_CIDispatcherQueueController_ShutdownQueueAsync
#endif /* WIDL_using_Windows_System */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSystem_CIDispatcherQueueController_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * IDispatcherQueueControllerStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics, 0x0a6c98e0, 0x5198, 0x49a2, 0xa3,0x13, 0x3f,0x70,0xd1,0xf1,0x3c,0x27);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace System {
            MIDL_INTERFACE("0a6c98e0-5198-49a2-a313-3f70d1f13c27")
            IDispatcherQueueControllerStatics : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE CreateOnDedicatedThread(
                    ABI::Windows::System::IDispatcherQueueController **result) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics, 0x0a6c98e0, 0x5198, 0x49a2, 0xa3,0x13, 0x3f,0x70,0xd1,0xf1,0x3c,0x27)
#endif
#else
typedef struct __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics *This,
        TrustLevel *trustLevel);

    /*** IDispatcherQueueControllerStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateOnDedicatedThread)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics *This,
        __x_ABI_CWindows_CSystem_CIDispatcherQueueController **result);

    END_INTERFACE
} __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStaticsVtbl;

interface __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics {
    CONST_VTBL __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDispatcherQueueControllerStatics methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_CreateOnDedicatedThread(This,result) (This)->lpVtbl->CreateOnDedicatedThread(This,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_QueryInterface(__x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_AddRef(__x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_Release(__x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_GetIids(__x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_GetRuntimeClassName(__x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_GetTrustLevel(__x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDispatcherQueueControllerStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_CreateOnDedicatedThread(__x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics* This,__x_ABI_CWindows_CSystem_CIDispatcherQueueController **result) {
    return This->lpVtbl->CreateOnDedicatedThread(This,result);
}
#endif
#ifdef WIDL_using_Windows_System
#define IID_IDispatcherQueueControllerStatics IID___x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics
#define IDispatcherQueueControllerStaticsVtbl __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStaticsVtbl
#define IDispatcherQueueControllerStatics __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics
#define IDispatcherQueueControllerStatics_QueryInterface __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_QueryInterface
#define IDispatcherQueueControllerStatics_AddRef __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_AddRef
#define IDispatcherQueueControllerStatics_Release __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_Release
#define IDispatcherQueueControllerStatics_GetIids __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_GetIids
#define IDispatcherQueueControllerStatics_GetRuntimeClassName __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_GetRuntimeClassName
#define IDispatcherQueueControllerStatics_GetTrustLevel __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_GetTrustLevel
#define IDispatcherQueueControllerStatics_CreateOnDedicatedThread __x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_CreateOnDedicatedThread
#endif /* WIDL_using_Windows_System */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSystem_CIDispatcherQueueControllerStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * IDispatcherQueueShutdownStartingEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs, 0xc4724c4c, 0xff97, 0x40c0, 0xa2,0x26, 0xcc,0x0a,0xaa,0x54,0x5e,0x89);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace System {
            MIDL_INTERFACE("c4724c4c-ff97-40c0-a226-cc0aaa545e89")
            IDispatcherQueueShutdownStartingEventArgs : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE GetDeferral(
                    ABI::Windows::Foundation::IDeferral **result) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs, 0xc4724c4c, 0xff97, 0x40c0, 0xa2,0x26, 0xcc,0x0a,0xaa,0x54,0x5e,0x89)
#endif
#else
typedef struct __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs *This,
        TrustLevel *trustLevel);

    /*** IDispatcherQueueShutdownStartingEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDeferral)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs *This,
        __x_ABI_CWindows_CFoundation_CIDeferral **result);

    END_INTERFACE
} __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgsVtbl;

interface __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs {
    CONST_VTBL __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDispatcherQueueShutdownStartingEventArgs methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_GetDeferral(This,result) (This)->lpVtbl->GetDeferral(This,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_QueryInterface(__x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_AddRef(__x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_Release(__x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_GetIids(__x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_GetTrustLevel(__x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDispatcherQueueShutdownStartingEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_GetDeferral(__x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs* This,__x_ABI_CWindows_CFoundation_CIDeferral **result) {
    return This->lpVtbl->GetDeferral(This,result);
}
#endif
#ifdef WIDL_using_Windows_System
#define IID_IDispatcherQueueShutdownStartingEventArgs IID___x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs
#define IDispatcherQueueShutdownStartingEventArgsVtbl __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgsVtbl
#define IDispatcherQueueShutdownStartingEventArgs __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs
#define IDispatcherQueueShutdownStartingEventArgs_QueryInterface __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_QueryInterface
#define IDispatcherQueueShutdownStartingEventArgs_AddRef __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_AddRef
#define IDispatcherQueueShutdownStartingEventArgs_Release __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_Release
#define IDispatcherQueueShutdownStartingEventArgs_GetIids __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_GetIids
#define IDispatcherQueueShutdownStartingEventArgs_GetRuntimeClassName __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_GetRuntimeClassName
#define IDispatcherQueueShutdownStartingEventArgs_GetTrustLevel __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_GetTrustLevel
#define IDispatcherQueueShutdownStartingEventArgs_GetDeferral __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_GetDeferral
#endif /* WIDL_using_Windows_System */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * IDispatcherQueueStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSystem_CIDispatcherQueueStatics, 0xa96d83d7, 0x9371, 0x4517, 0x92,0x45, 0xd0,0x82,0x4a,0xc1,0x2c,0x74);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace System {
            MIDL_INTERFACE("a96d83d7-**************-d0824ac12c74")
            IDispatcherQueueStatics : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE GetForCurrentThread(
                    ABI::Windows::System::IDispatcherQueue **result) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSystem_CIDispatcherQueueStatics, 0xa96d83d7, 0x9371, 0x4517, 0x92,0x45, 0xd0,0x82,0x4a,0xc1,0x2c,0x74)
#endif
#else
typedef struct __x_ABI_CWindows_CSystem_CIDispatcherQueueStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics *This,
        TrustLevel *trustLevel);

    /*** IDispatcherQueueStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *GetForCurrentThread)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics *This,
        __x_ABI_CWindows_CSystem_CIDispatcherQueue **result);

    END_INTERFACE
} __x_ABI_CWindows_CSystem_CIDispatcherQueueStaticsVtbl;

interface __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics {
    CONST_VTBL __x_ABI_CWindows_CSystem_CIDispatcherQueueStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDispatcherQueueStatics methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_GetForCurrentThread(This,result) (This)->lpVtbl->GetForCurrentThread(This,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_QueryInterface(__x_ABI_CWindows_CSystem_CIDispatcherQueueStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_AddRef(__x_ABI_CWindows_CSystem_CIDispatcherQueueStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_Release(__x_ABI_CWindows_CSystem_CIDispatcherQueueStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_GetIids(__x_ABI_CWindows_CSystem_CIDispatcherQueueStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_GetRuntimeClassName(__x_ABI_CWindows_CSystem_CIDispatcherQueueStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_GetTrustLevel(__x_ABI_CWindows_CSystem_CIDispatcherQueueStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDispatcherQueueStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_GetForCurrentThread(__x_ABI_CWindows_CSystem_CIDispatcherQueueStatics* This,__x_ABI_CWindows_CSystem_CIDispatcherQueue **result) {
    return This->lpVtbl->GetForCurrentThread(This,result);
}
#endif
#ifdef WIDL_using_Windows_System
#define IID_IDispatcherQueueStatics IID___x_ABI_CWindows_CSystem_CIDispatcherQueueStatics
#define IDispatcherQueueStaticsVtbl __x_ABI_CWindows_CSystem_CIDispatcherQueueStaticsVtbl
#define IDispatcherQueueStatics __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics
#define IDispatcherQueueStatics_QueryInterface __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_QueryInterface
#define IDispatcherQueueStatics_AddRef __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_AddRef
#define IDispatcherQueueStatics_Release __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_Release
#define IDispatcherQueueStatics_GetIids __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_GetIids
#define IDispatcherQueueStatics_GetRuntimeClassName __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_GetRuntimeClassName
#define IDispatcherQueueStatics_GetTrustLevel __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_GetTrustLevel
#define IDispatcherQueueStatics_GetForCurrentThread __x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_GetForCurrentThread
#endif /* WIDL_using_Windows_System */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSystem_CIDispatcherQueueStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * IDispatcherQueueTimer interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSystem_CIDispatcherQueueTimer, 0x5feabb1d, 0xa31c, 0x4727, 0xb1,0xac, 0x37,0x45,0x46,0x49,0xd5,0x6a);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace System {
            MIDL_INTERFACE("5feabb1d-a31c-4727-b1ac-37454649d56a")
            IDispatcherQueueTimer : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Interval(
                    ABI::Windows::Foundation::TimeSpan *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_Interval(
                    ABI::Windows::Foundation::TimeSpan value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsRunning(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsRepeating(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_IsRepeating(
                    boolean value) = 0;

                virtual HRESULT STDMETHODCALLTYPE Start(
                    ) = 0;

                virtual HRESULT STDMETHODCALLTYPE Stop(
                    ) = 0;

                virtual HRESULT STDMETHODCALLTYPE add_Tick(
                    ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::System::DispatcherQueueTimer*,IInspectable* > *handler,
                    EventRegistrationToken *token) = 0;

                virtual HRESULT STDMETHODCALLTYPE remove_Tick(
                    EventRegistrationToken token) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer, 0x5feabb1d, 0xa31c, 0x4727, 0xb1,0xac, 0x37,0x45,0x46,0x49,0xd5,0x6a)
#endif
#else
typedef struct __x_ABI_CWindows_CSystem_CIDispatcherQueueTimerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer *This,
        TrustLevel *trustLevel);

    /*** IDispatcherQueueTimer methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Interval)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan *value);

    HRESULT (STDMETHODCALLTYPE *put_Interval)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan value);

    HRESULT (STDMETHODCALLTYPE *get_IsRunning)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsRepeating)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IsRepeating)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *Start)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer *This);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer *This);

    HRESULT (STDMETHODCALLTYPE *add_Tick)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer *This,
        __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_Tick)(
        __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CSystem_CIDispatcherQueueTimerVtbl;

interface __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer {
    CONST_VTBL __x_ABI_CWindows_CSystem_CIDispatcherQueueTimerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDispatcherQueueTimer methods ***/
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_get_Interval(This,value) (This)->lpVtbl->get_Interval(This,value)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_put_Interval(This,value) (This)->lpVtbl->put_Interval(This,value)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_get_IsRunning(This,value) (This)->lpVtbl->get_IsRunning(This,value)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_get_IsRepeating(This,value) (This)->lpVtbl->get_IsRepeating(This,value)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_put_IsRepeating(This,value) (This)->lpVtbl->put_IsRepeating(This,value)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_Start(This) (This)->lpVtbl->Start(This)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_Stop(This) (This)->lpVtbl->Stop(This)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_add_Tick(This,handler,token) (This)->lpVtbl->add_Tick(This,handler,token)
#define __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_remove_Tick(This,token) (This)->lpVtbl->remove_Tick(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_QueryInterface(__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_AddRef(__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_Release(__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_GetIids(__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_GetRuntimeClassName(__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_GetTrustLevel(__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDispatcherQueueTimer methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_get_Interval(__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer* This,__x_ABI_CWindows_CFoundation_CTimeSpan *value) {
    return This->lpVtbl->get_Interval(This,value);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_put_Interval(__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer* This,__x_ABI_CWindows_CFoundation_CTimeSpan value) {
    return This->lpVtbl->put_Interval(This,value);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_get_IsRunning(__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer* This,boolean *value) {
    return This->lpVtbl->get_IsRunning(This,value);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_get_IsRepeating(__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer* This,boolean *value) {
    return This->lpVtbl->get_IsRepeating(This,value);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_put_IsRepeating(__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer* This,boolean value) {
    return This->lpVtbl->put_IsRepeating(This,value);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_Start(__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer* This) {
    return This->lpVtbl->Start(This);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_Stop(__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_add_Tick(__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer* This,__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_Tick(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_remove_Tick(__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_Tick(This,token);
}
#endif
#ifdef WIDL_using_Windows_System
#define IID_IDispatcherQueueTimer IID___x_ABI_CWindows_CSystem_CIDispatcherQueueTimer
#define IDispatcherQueueTimerVtbl __x_ABI_CWindows_CSystem_CIDispatcherQueueTimerVtbl
#define IDispatcherQueueTimer __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer
#define IDispatcherQueueTimer_QueryInterface __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_QueryInterface
#define IDispatcherQueueTimer_AddRef __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_AddRef
#define IDispatcherQueueTimer_Release __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_Release
#define IDispatcherQueueTimer_GetIids __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_GetIids
#define IDispatcherQueueTimer_GetRuntimeClassName __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_GetRuntimeClassName
#define IDispatcherQueueTimer_GetTrustLevel __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_GetTrustLevel
#define IDispatcherQueueTimer_get_Interval __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_get_Interval
#define IDispatcherQueueTimer_put_Interval __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_put_Interval
#define IDispatcherQueueTimer_get_IsRunning __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_get_IsRunning
#define IDispatcherQueueTimer_get_IsRepeating __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_get_IsRepeating
#define IDispatcherQueueTimer_put_IsRepeating __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_put_IsRepeating
#define IDispatcherQueueTimer_Start __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_Start
#define IDispatcherQueueTimer_Stop __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_Stop
#define IDispatcherQueueTimer_add_Tick __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_add_Tick
#define IDispatcherQueueTimer_remove_Tick __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_remove_Tick
#endif /* WIDL_using_Windows_System */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSystem_CIDispatcherQueueTimer_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * IUserChangedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CSystem_CIUserChangedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSystem_CIUserChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSystem_CIUserChangedEventArgs, 0x086459dc, 0x18c6, 0x48db, 0xbc,0x99, 0x72,0x4f,0xb9,0x20,0x3c,0xcc);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace System {
            MIDL_INTERFACE("086459dc-18c6-48db-bc99-724fb9203ccc")
            IUserChangedEventArgs : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_User(
                    ABI::Windows::System::IUser **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSystem_CIUserChangedEventArgs, 0x086459dc, 0x18c6, 0x48db, 0xbc,0x99, 0x72,0x4f,0xb9,0x20,0x3c,0xcc)
#endif
#else
typedef struct __x_ABI_CWindows_CSystem_CIUserChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSystem_CIUserChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSystem_CIUserChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSystem_CIUserChangedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSystem_CIUserChangedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSystem_CIUserChangedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSystem_CIUserChangedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IUserChangedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_User)(
        __x_ABI_CWindows_CSystem_CIUserChangedEventArgs *This,
        __x_ABI_CWindows_CSystem_CIUser **value);

    END_INTERFACE
} __x_ABI_CWindows_CSystem_CIUserChangedEventArgsVtbl;

interface __x_ABI_CWindows_CSystem_CIUserChangedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CSystem_CIUserChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IUserChangedEventArgs methods ***/
#define __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_get_User(This,value) (This)->lpVtbl->get_User(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_QueryInterface(__x_ABI_CWindows_CSystem_CIUserChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_AddRef(__x_ABI_CWindows_CSystem_CIUserChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_Release(__x_ABI_CWindows_CSystem_CIUserChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_GetIids(__x_ABI_CWindows_CSystem_CIUserChangedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CSystem_CIUserChangedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_GetTrustLevel(__x_ABI_CWindows_CSystem_CIUserChangedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IUserChangedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_get_User(__x_ABI_CWindows_CSystem_CIUserChangedEventArgs* This,__x_ABI_CWindows_CSystem_CIUser **value) {
    return This->lpVtbl->get_User(This,value);
}
#endif
#ifdef WIDL_using_Windows_System
#define IID_IUserChangedEventArgs IID___x_ABI_CWindows_CSystem_CIUserChangedEventArgs
#define IUserChangedEventArgsVtbl __x_ABI_CWindows_CSystem_CIUserChangedEventArgsVtbl
#define IUserChangedEventArgs __x_ABI_CWindows_CSystem_CIUserChangedEventArgs
#define IUserChangedEventArgs_QueryInterface __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_QueryInterface
#define IUserChangedEventArgs_AddRef __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_AddRef
#define IUserChangedEventArgs_Release __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_Release
#define IUserChangedEventArgs_GetIids __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_GetIids
#define IUserChangedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_GetRuntimeClassName
#define IUserChangedEventArgs_GetTrustLevel __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_GetTrustLevel
#define IUserChangedEventArgs_get_User __x_ABI_CWindows_CSystem_CIUserChangedEventArgs_get_User
#endif /* WIDL_using_Windows_System */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSystem_CIUserChangedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.System.DispatcherQueue
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef RUNTIMECLASS_Windows_System_DispatcherQueue_DEFINED
#define RUNTIMECLASS_Windows_System_DispatcherQueue_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_System_DispatcherQueue[] = {'W','i','n','d','o','w','s','.','S','y','s','t','e','m','.','D','i','s','p','a','t','c','h','e','r','Q','u','e','u','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_System_DispatcherQueue[] = L"Windows.System.DispatcherQueue";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_System_DispatcherQueue[] = {'W','i','n','d','o','w','s','.','S','y','s','t','e','m','.','D','i','s','p','a','t','c','h','e','r','Q','u','e','u','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_System_DispatcherQueue_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*
 * Class Windows.System.DispatcherQueueController
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef RUNTIMECLASS_Windows_System_DispatcherQueueController_DEFINED
#define RUNTIMECLASS_Windows_System_DispatcherQueueController_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_System_DispatcherQueueController[] = {'W','i','n','d','o','w','s','.','S','y','s','t','e','m','.','D','i','s','p','a','t','c','h','e','r','Q','u','e','u','e','C','o','n','t','r','o','l','l','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_System_DispatcherQueueController[] = L"Windows.System.DispatcherQueueController";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_System_DispatcherQueueController[] = {'W','i','n','d','o','w','s','.','S','y','s','t','e','m','.','D','i','s','p','a','t','c','h','e','r','Q','u','e','u','e','C','o','n','t','r','o','l','l','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_System_DispatcherQueueController_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*
 * Class Windows.System.DispatcherQueueShutdownStartingEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef RUNTIMECLASS_Windows_System_DispatcherQueueShutdownStartingEventArgs_DEFINED
#define RUNTIMECLASS_Windows_System_DispatcherQueueShutdownStartingEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_System_DispatcherQueueShutdownStartingEventArgs[] = {'W','i','n','d','o','w','s','.','S','y','s','t','e','m','.','D','i','s','p','a','t','c','h','e','r','Q','u','e','u','e','S','h','u','t','d','o','w','n','S','t','a','r','t','i','n','g','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_System_DispatcherQueueShutdownStartingEventArgs[] = L"Windows.System.DispatcherQueueShutdownStartingEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_System_DispatcherQueueShutdownStartingEventArgs[] = {'W','i','n','d','o','w','s','.','S','y','s','t','e','m','.','D','i','s','p','a','t','c','h','e','r','Q','u','e','u','e','S','h','u','t','d','o','w','n','S','t','a','r','t','i','n','g','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_System_DispatcherQueueShutdownStartingEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*
 * Class Windows.System.DispatcherQueueTimer
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef RUNTIMECLASS_Windows_System_DispatcherQueueTimer_DEFINED
#define RUNTIMECLASS_Windows_System_DispatcherQueueTimer_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_System_DispatcherQueueTimer[] = {'W','i','n','d','o','w','s','.','S','y','s','t','e','m','.','D','i','s','p','a','t','c','h','e','r','Q','u','e','u','e','T','i','m','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_System_DispatcherQueueTimer[] = L"Windows.System.DispatcherQueueTimer";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_System_DispatcherQueueTimer[] = {'W','i','n','d','o','w','s','.','S','y','s','t','e','m','.','D','i','s','p','a','t','c','h','e','r','Q','u','e','u','e','T','i','m','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_System_DispatcherQueueTimer_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*
 * Class Windows.System.User
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_System_User_DEFINED
#define RUNTIMECLASS_Windows_System_User_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_System_User[] = {'W','i','n','d','o','w','s','.','S','y','s','t','e','m','.','U','s','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_System_User[] = L"Windows.System.User";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_System_User[] = {'W','i','n','d','o','w','s','.','S','y','s','t','e','m','.','U','s','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_System_User_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.System.UserChangedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_System_UserChangedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_System_UserChangedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_System_UserChangedEventArgs[] = {'W','i','n','d','o','w','s','.','S','y','s','t','e','m','.','U','s','e','r','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_System_UserChangedEventArgs[] = L"Windows.System.UserChangedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_System_UserChangedEventArgs[] = {'W','i','n','d','o','w','s','.','S','y','s','t','e','m','.','U','s','e','r','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_System_UserChangedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::System::DispatcherQueue*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable, 0xfe79f855, 0x2f40, 0x5b88, 0xa0,0xc3, 0x4c,0x04,0x2a,0x05,0xdd,0x05);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("fe79f855-2f40-5b88-a0c3-4c042a05dd05")
            ITypedEventHandler<ABI::Windows::System::DispatcherQueue*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::System::DispatcherQueue*, ABI::Windows::System::IDispatcherQueue* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable, 0xfe79f855, 0x2f40, 0x5b88, 0xa0,0xc3, 0x4c,0x04,0x2a,0x05,0xdd,0x05)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::System::DispatcherQueue*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable *This,
        __x_ABI_CWindows_CSystem_CIDispatcherQueue *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::System::DispatcherQueue*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable_Release(__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::System::DispatcherQueue*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable* This,__x_ABI_CWindows_CSystem_CIDispatcherQueue *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_DispatcherQueue_IInspectable IID___FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable
#define ITypedEventHandler_DispatcherQueue_IInspectableVtbl __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectableVtbl
#define ITypedEventHandler_DispatcherQueue_IInspectable __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable
#define ITypedEventHandler_DispatcherQueue_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable_QueryInterface
#define ITypedEventHandler_DispatcherQueue_IInspectable_AddRef __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable_AddRef
#define ITypedEventHandler_DispatcherQueue_IInspectable_Release __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable_Release
#define ITypedEventHandler_DispatcherQueue_IInspectable_Invoke __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_IInspectable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::System::DispatcherQueue*,ABI::Windows::System::DispatcherQueueShutdownStartingEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs, 0xb58b5e24, 0xe1c6, 0x528e, 0x9d,0x99, 0x07,0xec,0x88,0x29,0xde,0xa5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("b58b5e24-e1c6-528e-9d99-07ec8829dea5")
            ITypedEventHandler<ABI::Windows::System::DispatcherQueue*,ABI::Windows::System::DispatcherQueueShutdownStartingEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::System::DispatcherQueue*, ABI::Windows::System::IDispatcherQueue* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::System::DispatcherQueueShutdownStartingEventArgs*, ABI::Windows::System::IDispatcherQueueShutdownStartingEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs, 0xb58b5e24, 0xe1c6, 0x528e, 0x9d,0x99, 0x07,0xec,0x88,0x29,0xde,0xa5)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::System::DispatcherQueue*,ABI::Windows::System::DispatcherQueueShutdownStartingEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs *This,
        __x_ABI_CWindows_CSystem_CIDispatcherQueue *sender,
        __x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::System::DispatcherQueue*,ABI::Windows::System::DispatcherQueueShutdownStartingEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs_AddRef(__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs_Release(__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::System::DispatcherQueue*,ABI::Windows::System::DispatcherQueueShutdownStartingEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs_Invoke(__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs* This,__x_ABI_CWindows_CSystem_CIDispatcherQueue *sender,__x_ABI_CWindows_CSystem_CIDispatcherQueueShutdownStartingEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_DispatcherQueue_DispatcherQueueShutdownStartingEventArgs IID___FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs
#define ITypedEventHandler_DispatcherQueue_DispatcherQueueShutdownStartingEventArgsVtbl __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgsVtbl
#define ITypedEventHandler_DispatcherQueue_DispatcherQueueShutdownStartingEventArgs __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs
#define ITypedEventHandler_DispatcherQueue_DispatcherQueueShutdownStartingEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs_QueryInterface
#define ITypedEventHandler_DispatcherQueue_DispatcherQueueShutdownStartingEventArgs_AddRef __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs_AddRef
#define ITypedEventHandler_DispatcherQueue_DispatcherQueueShutdownStartingEventArgs_Release __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs_Release
#define ITypedEventHandler_DispatcherQueue_DispatcherQueueShutdownStartingEventArgs_Invoke __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CSystem__CDispatcherQueue_Windows__CSystem__CDispatcherQueueShutdownStartingEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::System::DispatcherQueueTimer*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable, 0x8b5644c8, 0x8b57, 0x50ce, 0x89,0x33, 0x7a,0xb2,0xcc,0x5a,0x14,0xef);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("8b5644c8-8b57-50ce-8933-7ab2cc5a14ef")
            ITypedEventHandler<ABI::Windows::System::DispatcherQueueTimer*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::System::DispatcherQueueTimer*, ABI::Windows::System::IDispatcherQueueTimer* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable, 0x8b5644c8, 0x8b57, 0x50ce, 0x89,0x33, 0x7a,0xb2,0xcc,0x5a,0x14,0xef)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::System::DispatcherQueueTimer*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable *This,
        __x_ABI_CWindows_CSystem_CIDispatcherQueueTimer *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::System::DispatcherQueueTimer*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable_Release(__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::System::DispatcherQueueTimer*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable* This,__x_ABI_CWindows_CSystem_CIDispatcherQueueTimer *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_DispatcherQueueTimer_IInspectable IID___FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable
#define ITypedEventHandler_DispatcherQueueTimer_IInspectableVtbl __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectableVtbl
#define ITypedEventHandler_DispatcherQueueTimer_IInspectable __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable
#define ITypedEventHandler_DispatcherQueueTimer_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable_QueryInterface
#define ITypedEventHandler_DispatcherQueueTimer_IInspectable_AddRef __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable_AddRef
#define ITypedEventHandler_DispatcherQueueTimer_IInspectable_Release __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable_Release
#define ITypedEventHandler_DispatcherQueueTimer_IInspectable_Invoke __FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CSystem__CDispatcherQueueTimer_IInspectable_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_system_h__ */
