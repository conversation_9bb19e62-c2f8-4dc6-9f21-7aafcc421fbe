/*
 * Copyright (C) 2010 Nikolay Sivov
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "unknwn.idl";
import "objidl.idl";
import "oaidl.idl";

typedef enum XmlNodeType {
    XmlNodeType_None                  = 0,
    XmlNodeType_Element               = 1,
    XmlNodeType_Attribute             = 2,
    XmlNodeType_Text                  = 3,
    XmlNodeType_CDATA                 = 4,
    XmlNodeType_ProcessingInstruction = 7,
    XmlNodeType_Comment               = 8,
    XmlNodeType_DocumentType          = 10,
    XmlNodeType_Whitespace            = 13,
    XmlNodeType_EndElement            = 15,
    XmlNodeType_XmlDeclaration        = 17,
    _XmlNodeType_Last                 = 17
} XmlNodeType;

/* IXmlReader */
[
  local,
  object,
  uuid(7279fc81-709d-4095-b63d-69fe4b0d9030),
  pointer_default(unique)
]
interface IXmlReader : IUnknown
{
    HRESULT SetInput( [in] IUnknown *input);
    HRESULT GetProperty( [in] UINT property, [out] LONG_PTR *value);
    HRESULT SetProperty( [in] UINT property, [in] LONG_PTR value);
    HRESULT Read( [out] XmlNodeType *node_type);
    HRESULT GetNodeType( [out] XmlNodeType *node_type);
    HRESULT MoveToFirstAttribute(void);
    HRESULT MoveToNextAttribute(void);
    HRESULT MoveToAttributeByName( [in] LPCWSTR local_name,
                                   [in] LPCWSTR namespaceUri);
    HRESULT MoveToElement(void);
    HRESULT GetQualifiedName( [out] LPCWSTR *qualifiedName,
                              [out] UINT *qualifiedName_length);
    HRESULT GetNamespaceUri( [out] LPCWSTR *namespaceUri,
                             [out] UINT *nnamespaceUri_length);
    HRESULT GetLocalName( [out] LPCWSTR *local_name,
                          [out] UINT *locale_name_length);
    HRESULT GetPrefix( [out] LPCWSTR *prefix,
                       [out] UINT *prefix_length);
    HRESULT GetValue( [out] LPCWSTR *value,
                      [out] UINT *value_length);
    HRESULT ReadValueChunk( [out] WCHAR *buffer,
                            [in]  UINT chunk_size,
                            [in,out] UINT *read);
    HRESULT GetBaseUri( [out] LPCWSTR *baseUri,
                        [out] UINT *baseUri_length);
    BOOL IsDefault(void);
    BOOL IsEmptyElement(void);
    HRESULT GetLineNumber( [out] UINT *lineNumber);
    HRESULT GetLinePosition( [out] UINT *linePosition);
    HRESULT GetAttributeCount( [out] UINT *attributeCount);
    HRESULT GetDepth( [out] UINT *depth);
    BOOL IsEOF(void);
}

/* IXmlResolver */
[
  local,
  object,
  uuid(7279fc82-709d-4095-b63d-69fe4b0d9030),
  pointer_default(unique)
]
interface IXmlResolver : IUnknown
{
    HRESULT ResolveUri([in] LPCWSTR base_uri,
                       [in] LPCWSTR public_id,
                       [in] LPCWSTR system_id,
                       [out] IUnknown **input);
}

/* IXmlReader state */
typedef enum XmlReadState
{
    XmlReadState_Initial,
    XmlReadState_Interactive,
    XmlReadState_Error,
    XmlReadState_EndOfFile,
    XmlReadState_Closed
} XmlReadState;

/* conformance levels */
typedef enum XmlConformanceLevel
{
    XmlConformanceLevel_Auto,
    XmlConformanceLevel_Fragment,
    XmlConformanceLevel_Document,
    _XmlConformanceLevel_Last = XmlConformanceLevel_Document
} XmlConformanceLevel;

/* DTD processing mode */
typedef enum DtdProcessing
{
    DtdProcessing_Prohibit,
    DtdProcessing_Parse,
    _DtdProcessing_Last = DtdProcessing_Parse
} DtdProcessing;

/* IXmlReader properties */
typedef enum XmlReaderProperty
{
    XmlReaderProperty_MultiLanguage,
    XmlReaderProperty_ConformanceLevel,
    XmlReaderProperty_RandomAccess,
    XmlReaderProperty_XmlResolver,
    XmlReaderProperty_DtdProcessing,
    XmlReaderProperty_ReadState,
    XmlReaderProperty_MaxElementDepth,
    XmlReaderProperty_MaxEntityExpansion,
    _XmlReaderProperty_Last = XmlReaderProperty_MaxEntityExpansion
} XmlReaderProperty;

/* reader error codes */
typedef enum XmlError
{
    MX_E_MX = 0xc00cee00,
    MX_E_INPUTEND,
    MX_E_ENCODING,
    MX_E_ENCODINGSWITCH,
    MX_E_ENCODINGSIGNATURE,
    WC_E_WC = 0xc00cee20,
    WC_E_WHITESPACE,
    WC_E_SEMICOLON,
    WC_E_GREATERTHAN,
    WC_E_QUOTE,
    WC_E_EQUAL,
    WC_E_LESSTHAN,
    WC_E_HEXDIGIT,
    WC_E_DIGIT,
    WC_E_LEFTBRACKET,
    WC_E_LEFTPAREN,
    WC_E_XMLCHARACTER,
    WC_E_NAMECHARACTER,
    WC_E_SYNTAX,
    WC_E_CDSECT,
    WC_E_COMMENT,
    WC_E_CONDSECT,
    WC_E_DECLATTLIST,
    WC_E_DECLDOCTYPE,
    WC_E_DECLELEMENT,
    WC_E_DECLENTITY,
    WC_E_DECLNOTATION,
    WC_E_NDATA,
    WC_E_PUBLIC,
    WC_E_SYSTEM,
    WC_E_NAME,
    WC_E_ROOTELEMENT,
    WC_E_ELEMENTMATCH,
    WC_E_UNIQUEATTRIBUTE,
    WC_E_TEXTXMLDECL,
    WC_E_LEADINGXML,
    WC_E_TEXTDECL,
    WC_E_XMLDECL,
    WC_E_ENCNAME,
    WC_E_PUBLICID,
    WC_E_PESINTERNALSUBSET,
    WC_E_PESBETWEENDECLS,
    WC_E_NORECURSION,
    WC_E_ENTITYCONTENT,
    WC_E_UNDECLAREDENTITY,
    WC_E_PARSEDENTITY,
    WC_E_NOEXTERNALENTITYREF,
    WC_E_PI,
    WC_E_SYSTEMID,
    WC_E_QUESTIONMARK,
    WC_E_CDSECTEND,
    WC_E_MOREDATA,
    WC_E_DTDPROHIBITED,
    WC_E_INVALIDXMLSPACE,
    NC_E_NC = 0xc00cee60,
    NC_E_QNAMECHARACTER,
    NC_E_QNAMECOLON,
    NC_E_NAMECOLON,
    NC_E_DECLAREDPREFIX,
    NC_E_UNDECLAREDPREFIX,
    NC_E_EMPTYURI,
    NC_E_XMLPREFIXRESERVED,
    NC_E_XMLNSPREFIXRESERVED,
    NC_E_XMLURIRESERVED,
    NC_E_XMLNSURIRESERVED,
    SC_E_SC = 0xc00cee80,
    SC_E_MAXELEMENTDEPTH,
    SC_E_MAXENTITYEXPANSION,
    WR_E_WR = 0xc00cef00,
    WR_E_NONWHITESPACE,
    WR_E_NSPREFIXDECLARED,
    WR_E_NSPREFIXWITHEMPTYNSURI,
    WR_E_DUPLICATEATTRIBUTE,
    WR_E_XMLNSPREFIXDECLARATION,
    WR_E_XMLPREFIXDECLARATION,
    WR_E_XMLURIDECLARATION,
    WR_E_XMLNSURIDECLARATION,
    WR_E_NAMESPACEUNDECLARED,
    WR_E_INVALIDXMLSPACE,
    WR_E_INVALIDACTION,
    WR_E_INVALIDSURROGATEPAIR,
    XML_E_INVALID_DECIMAL = 0xc00ce01d,
    XML_E_INVALID_HEXIDECIMAL,
    XML_E_INVALID_UNICODE,
    XML_E_INVALIDENCODING = 0xc00ce06e
} XmlError;

/* IXmlReader construction */
cpp_quote("STDAPI CreateXmlReader(REFIID riid, void **ppvObject, IMalloc *pMalloc);")

cpp_quote("typedef IUnknown IXmlReaderInput;")
cpp_quote("STDAPI CreateXmlReaderInputWithEncodingCodePage(IUnknown *stream, IMalloc *pMalloc,")
cpp_quote("        UINT encoding_codepage, WINBOOL hint, const WCHAR *base_uri, IXmlReaderInput **pInput);")
cpp_quote("STDAPI CreateXmlReaderInputWithEncodingName(IUnknown *stream, IMalloc *pMalloc,")
cpp_quote("                                            LPCWSTR encoding, WINBOOL hint,")
cpp_quote("                                            LPCWSTR base_uri, IXmlReaderInput **ppInput);")

typedef enum XmlStandalone
{
    XmlStandalone_Omit,
    XmlStandalone_Yes,
    XmlStandalone_No,
    _XmlStandalone_Last = XmlStandalone_No
} XmlStandalone;

typedef enum XmlWriterProperty
{
    XmlWriterProperty_MultiLanguage,
    XmlWriterProperty_Indent,
    XmlWriterProperty_ByteOrderMark,
    XmlWriterProperty_OmitXmlDeclaration,
    XmlWriterProperty_ConformanceLevel,
    _XmlWriterProperty_Last = XmlWriterProperty_OmitXmlDeclaration
} XmlWriterProperty;

/* IXmlWriter */
[
  local,
  object,
  uuid(7279FC88-709D-4095-B63D-69FE4B0D9030),
  pointer_default(unique)
]
interface IXmlWriter : IUnknown
{
    HRESULT SetOutput([in] IUnknown *pOutput);
    HRESULT GetProperty([in] UINT nProperty, [out] LONG_PTR *ppValue);
    HRESULT SetProperty([in] UINT nProperty, [in] LONG_PTR pValue);
    HRESULT WriteAttributes([in] IXmlReader *pReader, [in] BOOL fWriteDefaultAttributes);
    HRESULT WriteAttributeString([in] LPCWSTR pwszPrefix, [in] LPCWSTR pwszLocalName,
                                 [in] LPCWSTR pwszNamespaceUri, [in] LPCWSTR pwszValue);
    HRESULT WriteCData([in] LPCWSTR pwszText);
    HRESULT WriteCharEntity([in] WCHAR wch);
    HRESULT WriteChars([in] const WCHAR *pwch, [in] UINT cwch);
    HRESULT WriteComment([in] LPCWSTR pwszComment);
    HRESULT WriteDocType([in] LPCWSTR pwszName, [in] LPCWSTR pwszPublicId,
                         [in] LPCWSTR pwszSystemId, [in] LPCWSTR pwszSubset);
    HRESULT WriteElementString([in] LPCWSTR pwszPrefix, [in] LPCWSTR pwszLocalName,
                               [in] LPCWSTR pwszNamespaceUri, [in] LPCWSTR pwszValue);
    HRESULT WriteEndDocument();
    HRESULT WriteEndElement();
    HRESULT WriteEntityRef([in] LPCWSTR pwszName);
    HRESULT WriteFullEndElement();
    HRESULT WriteName([in] LPCWSTR pwszName);
    HRESULT WriteNmToken([in] LPCWSTR pwszNmToken);
    HRESULT WriteNode([in] IXmlReader *pReader, [in] BOOL fWriteDefaultAttributes);
    HRESULT WriteNodeShallow([in] IXmlReader *pReader, [in] BOOL fWriteDefaultAttributes);
    HRESULT WriteProcessingInstruction([in] LPCWSTR pwszName, [in] LPCWSTR pwszText);
    HRESULT WriteQualifiedName([in] LPCWSTR pwszLocalName, [in] LPCWSTR pwszNamespaceUri);
    HRESULT WriteRaw([in] LPCWSTR pwszData);
    HRESULT WriteRawChars([in] const WCHAR *pwch, [in] UINT cwch);
    HRESULT WriteStartDocument([in] XmlStandalone standalone);
    HRESULT WriteStartElement([in] LPCWSTR pwszPrefix, [in] LPCWSTR pwszLocalName,
                              [in]  LPCWSTR pwszNamespaceUri);
    HRESULT WriteString([in] LPCWSTR pwszText);
    HRESULT WriteSurrogateCharEntity([in] WCHAR wchLow, [in] WCHAR wchHigh);
    HRESULT WriteWhitespace([in] LPCWSTR pwszWhitespace);
    HRESULT Flush();
}

/* IXmlWriter construction */
cpp_quote("STDAPI CreateXmlWriter(REFIID riid, void **ppvObject, IMalloc *pMalloc);")

cpp_quote("typedef IUnknown IXmlWriterOutput;")
cpp_quote("STDAPI CreateXmlWriterOutputWithEncodingName(IUnknown *stream, IMalloc *pMalloc,")
cpp_quote("                                             LPCWSTR encoding, IXmlWriterOutput **output);")
cpp_quote("STDAPI CreateXmlWriterOutputWithEncodingCodePage(IUnknown *stream, IMalloc *pMalloc,")
cpp_quote("    UINT codepage, IXmlWriterOutput **output);")
