/*** Autogenerated by WIDL 10.8 from include/windows.devices.usb.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_devices_usb_h__
#define __windows_devices_usb_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice ABI::Windows::Devices::Usb::IUsbDevice
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                interface IUsbDevice;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass ABI::Windows::Devices::Usb::IUsbDeviceClass
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                interface IUsbDeviceClass;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics ABI::Windows::Devices::Usb::IUsbDeviceStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                interface IUsbDeviceStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CUsb_CUsbConfiguration_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CUsbConfiguration_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                class UsbConfiguration;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CUsb_CUsbConfiguration __x_ABI_CWindows_CDevices_CUsb_CUsbConfiguration;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CUsb_CUsbConfiguration_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CUsb_CUsbDevice_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CUsbDevice_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                class UsbDevice;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CUsb_CUsbDevice __x_ABI_CWindows_CDevices_CUsb_CUsbDevice;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CUsb_CUsbDevice_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CUsb_CUsbDeviceClass_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CUsbDeviceClass_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                class UsbDeviceClass;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CUsb_CUsbDeviceClass __x_ABI_CWindows_CDevices_CUsb_CUsbDeviceClass;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CUsb_CUsbDeviceClass_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CUsb_CUsbDeviceDescriptor_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CUsbDeviceDescriptor_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                class UsbDeviceDescriptor;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CUsb_CUsbDeviceDescriptor __x_ABI_CWindows_CDevices_CUsb_CUsbDeviceDescriptor;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CUsb_CUsbDeviceDescriptor_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CUsb_CUsbInterface_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CUsbInterface_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                class UsbInterface;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CUsb_CUsbInterface __x_ABI_CWindows_CDevices_CUsb_CUsbInterface;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CUsb_CUsbInterface_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CUsb_CUsbSetupPacket_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CUsbSetupPacket_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                class UsbSetupPacket;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CUsb_CUsbSetupPacket __x_ABI_CWindows_CDevices_CUsb_CUsbSetupPacket;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CUsb_CUsbSetupPacket_FWD_DEFINED__ */

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Devices::Usb::UsbDevice* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Usb::UsbDevice* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.storage.streams.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef ____x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice ABI::Windows::Devices::Usb::IUsbDevice
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                interface IUsbDevice;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass ABI::Windows::Devices::Usb::IUsbDeviceClass
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                interface IUsbDeviceClass;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CUsb_CIUsbConfiguration_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CIUsbConfiguration_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CUsb_CIUsbConfiguration __x_ABI_CWindows_CDevices_CUsb_CIUsbConfiguration;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbConfiguration ABI::Windows::Devices::Usb::IUsbConfiguration
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                interface IUsbConfiguration;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceDescriptor_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceDescriptor_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceDescriptor __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceDescriptor;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceDescriptor ABI::Windows::Devices::Usb::IUsbDeviceDescriptor
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                interface IUsbDeviceDescriptor;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics ABI::Windows::Devices::Usb::IUsbDeviceStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                interface IUsbDeviceStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CUsb_CIUsbInterface_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CIUsbInterface_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CUsb_CIUsbInterface __x_ABI_CWindows_CDevices_CUsb_CIUsbInterface;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbInterface ABI::Windows::Devices::Usb::IUsbInterface
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                interface IUsbInterface;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacket_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacket_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacket __x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacket;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacket ABI::Windows::Devices::Usb::IUsbSetupPacket
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                interface IUsbSetupPacket;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacketFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacketFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacketFactory __x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacketFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacketFactory ABI::Windows::Devices::Usb::IUsbSetupPacketFactory
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                interface IUsbSetupPacketFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Usb::UsbDevice* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IUsbDevice interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CUsb_CIUsbDevice, 0x5249b992, 0xc456, 0x44d5, 0xad,0x5e, 0x24,0xf5,0xa0,0x89,0xf6,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                MIDL_INTERFACE("5249b992-c456-44d5-ad5e-24f5a089f63b")
                IUsbDevice : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE SendControlOutTransferAsync(
                        ABI::Windows::Devices::Usb::IUsbSetupPacket *packet,
                        ABI::Windows::Storage::Streams::IBuffer *buffer,
                        ABI::Windows::Foundation::IAsyncOperation<UINT32 > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE SendControlOutTransferAsyncNoBuffer(
                        ABI::Windows::Devices::Usb::IUsbSetupPacket *packet,
                        ABI::Windows::Foundation::IAsyncOperation<UINT32 > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE SendControlInTransferAsync(
                        ABI::Windows::Devices::Usb::IUsbSetupPacket *packet,
                        ABI::Windows::Storage::Streams::IBuffer *buffer,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::Streams::IBuffer* > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE SendControlInTransferAsyncNoBuffer(
                        ABI::Windows::Devices::Usb::IUsbSetupPacket *packet,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::Streams::IBuffer* > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_DefaultInterface(
                        ABI::Windows::Devices::Usb::IUsbInterface **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_DeviceDescriptor(
                        ABI::Windows::Devices::Usb::IUsbDeviceDescriptor **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Configuration(
                        ABI::Windows::Devices::Usb::IUsbConfiguration **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CUsb_CIUsbDevice, 0x5249b992, 0xc456, 0x44d5, 0xad,0x5e, 0x24,0xf5,0xa0,0x89,0xf6,0x3b)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice *This,
        TrustLevel *trustLevel);

    /*** IUsbDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *SendControlOutTransferAsync)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice *This,
        __x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacket *packet,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer,
        __FIAsyncOperation_1_UINT32 **operation);

    HRESULT (STDMETHODCALLTYPE *SendControlOutTransferAsyncNoBuffer)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice *This,
        __x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacket *packet,
        __FIAsyncOperation_1_UINT32 **operation);

    HRESULT (STDMETHODCALLTYPE *SendControlInTransferAsync)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice *This,
        __x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacket *packet,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer,
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer **operation);

    HRESULT (STDMETHODCALLTYPE *SendControlInTransferAsyncNoBuffer)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice *This,
        __x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacket *packet,
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer **operation);

    HRESULT (STDMETHODCALLTYPE *get_DefaultInterface)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice *This,
        __x_ABI_CWindows_CDevices_CUsb_CIUsbInterface **value);

    HRESULT (STDMETHODCALLTYPE *get_DeviceDescriptor)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice *This,
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceDescriptor **value);

    HRESULT (STDMETHODCALLTYPE *get_Configuration)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice *This,
        __x_ABI_CWindows_CDevices_CUsb_CIUsbConfiguration **value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceVtbl;

interface __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice {
    CONST_VTBL __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IUsbDevice methods ***/
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_SendControlOutTransferAsync(This,packet,buffer,operation) (This)->lpVtbl->SendControlOutTransferAsync(This,packet,buffer,operation)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_SendControlOutTransferAsyncNoBuffer(This,packet,operation) (This)->lpVtbl->SendControlOutTransferAsyncNoBuffer(This,packet,operation)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_SendControlInTransferAsync(This,packet,buffer,operation) (This)->lpVtbl->SendControlInTransferAsync(This,packet,buffer,operation)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_SendControlInTransferAsyncNoBuffer(This,packet,operation) (This)->lpVtbl->SendControlInTransferAsyncNoBuffer(This,packet,operation)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_get_DefaultInterface(This,value) (This)->lpVtbl->get_DefaultInterface(This,value)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_get_DeviceDescriptor(This,value) (This)->lpVtbl->get_DeviceDescriptor(This,value)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_get_Configuration(This,value) (This)->lpVtbl->get_Configuration(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_QueryInterface(__x_ABI_CWindows_CDevices_CUsb_CIUsbDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_AddRef(__x_ABI_CWindows_CDevices_CUsb_CIUsbDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_Release(__x_ABI_CWindows_CDevices_CUsb_CIUsbDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_GetIids(__x_ABI_CWindows_CDevices_CUsb_CIUsbDevice* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CUsb_CIUsbDevice* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_GetTrustLevel(__x_ABI_CWindows_CDevices_CUsb_CIUsbDevice* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IUsbDevice methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_SendControlOutTransferAsync(__x_ABI_CWindows_CDevices_CUsb_CIUsbDevice* This,__x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacket *packet,__x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer,__FIAsyncOperation_1_UINT32 **operation) {
    return This->lpVtbl->SendControlOutTransferAsync(This,packet,buffer,operation);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_SendControlOutTransferAsyncNoBuffer(__x_ABI_CWindows_CDevices_CUsb_CIUsbDevice* This,__x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacket *packet,__FIAsyncOperation_1_UINT32 **operation) {
    return This->lpVtbl->SendControlOutTransferAsyncNoBuffer(This,packet,operation);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_SendControlInTransferAsync(__x_ABI_CWindows_CDevices_CUsb_CIUsbDevice* This,__x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacket *packet,__x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer,__FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer **operation) {
    return This->lpVtbl->SendControlInTransferAsync(This,packet,buffer,operation);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_SendControlInTransferAsyncNoBuffer(__x_ABI_CWindows_CDevices_CUsb_CIUsbDevice* This,__x_ABI_CWindows_CDevices_CUsb_CIUsbSetupPacket *packet,__FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer **operation) {
    return This->lpVtbl->SendControlInTransferAsyncNoBuffer(This,packet,operation);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_get_DefaultInterface(__x_ABI_CWindows_CDevices_CUsb_CIUsbDevice* This,__x_ABI_CWindows_CDevices_CUsb_CIUsbInterface **value) {
    return This->lpVtbl->get_DefaultInterface(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_get_DeviceDescriptor(__x_ABI_CWindows_CDevices_CUsb_CIUsbDevice* This,__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceDescriptor **value) {
    return This->lpVtbl->get_DeviceDescriptor(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_get_Configuration(__x_ABI_CWindows_CDevices_CUsb_CIUsbDevice* This,__x_ABI_CWindows_CDevices_CUsb_CIUsbConfiguration **value) {
    return This->lpVtbl->get_Configuration(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Usb
#define IID_IUsbDevice IID___x_ABI_CWindows_CDevices_CUsb_CIUsbDevice
#define IUsbDeviceVtbl __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceVtbl
#define IUsbDevice __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice
#define IUsbDevice_QueryInterface __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_QueryInterface
#define IUsbDevice_AddRef __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_AddRef
#define IUsbDevice_Release __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_Release
#define IUsbDevice_GetIids __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_GetIids
#define IUsbDevice_GetRuntimeClassName __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_GetRuntimeClassName
#define IUsbDevice_GetTrustLevel __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_GetTrustLevel
#define IUsbDevice_SendControlOutTransferAsync __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_SendControlOutTransferAsync
#define IUsbDevice_SendControlOutTransferAsyncNoBuffer __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_SendControlOutTransferAsyncNoBuffer
#define IUsbDevice_SendControlInTransferAsync __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_SendControlInTransferAsync
#define IUsbDevice_SendControlInTransferAsyncNoBuffer __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_SendControlInTransferAsyncNoBuffer
#define IUsbDevice_get_DefaultInterface __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_get_DefaultInterface
#define IUsbDevice_get_DeviceDescriptor __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_get_DeviceDescriptor
#define IUsbDevice_get_Configuration __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_get_Configuration
#endif /* WIDL_using_Windows_Devices_Usb */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CUsb_CIUsbDevice_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IUsbDeviceClass interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass, 0x051942f9, 0x845e, 0x47eb, 0xb1,0x2a, 0x38,0xf2,0xf6,0x17,0xaf,0xe7);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                MIDL_INTERFACE("051942f9-845e-47eb-b12a-38f2f617afe7")
                IUsbDeviceClass : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_ClassCode(
                        BYTE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_ClassCode(
                        BYTE value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_SubclassCode(
                        ABI::Windows::Foundation::IReference<BYTE > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_SubclassCode(
                        ABI::Windows::Foundation::IReference<BYTE > *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ProtocolCode(
                        ABI::Windows::Foundation::IReference<BYTE > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_ProtocolCode(
                        ABI::Windows::Foundation::IReference<BYTE > *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass, 0x051942f9, 0x845e, 0x47eb, 0xb1,0x2a, 0x38,0xf2,0xf6,0x17,0xaf,0xe7)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClassVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass *This,
        TrustLevel *trustLevel);

    /*** IUsbDeviceClass methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ClassCode)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass *This,
        BYTE *value);

    HRESULT (STDMETHODCALLTYPE *put_ClassCode)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass *This,
        BYTE value);

    HRESULT (STDMETHODCALLTYPE *get_SubclassCode)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass *This,
        __FIReference_1_BYTE **value);

    HRESULT (STDMETHODCALLTYPE *put_SubclassCode)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass *This,
        __FIReference_1_BYTE *value);

    HRESULT (STDMETHODCALLTYPE *get_ProtocolCode)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass *This,
        __FIReference_1_BYTE **value);

    HRESULT (STDMETHODCALLTYPE *put_ProtocolCode)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass *This,
        __FIReference_1_BYTE *value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClassVtbl;

interface __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass {
    CONST_VTBL __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClassVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IUsbDeviceClass methods ***/
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_get_ClassCode(This,value) (This)->lpVtbl->get_ClassCode(This,value)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_put_ClassCode(This,value) (This)->lpVtbl->put_ClassCode(This,value)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_get_SubclassCode(This,value) (This)->lpVtbl->get_SubclassCode(This,value)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_put_SubclassCode(This,value) (This)->lpVtbl->put_SubclassCode(This,value)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_get_ProtocolCode(This,value) (This)->lpVtbl->get_ProtocolCode(This,value)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_put_ProtocolCode(This,value) (This)->lpVtbl->put_ProtocolCode(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_QueryInterface(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_AddRef(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_Release(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_GetIids(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_GetTrustLevel(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IUsbDeviceClass methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_get_ClassCode(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass* This,BYTE *value) {
    return This->lpVtbl->get_ClassCode(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_put_ClassCode(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass* This,BYTE value) {
    return This->lpVtbl->put_ClassCode(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_get_SubclassCode(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass* This,__FIReference_1_BYTE **value) {
    return This->lpVtbl->get_SubclassCode(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_put_SubclassCode(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass* This,__FIReference_1_BYTE *value) {
    return This->lpVtbl->put_SubclassCode(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_get_ProtocolCode(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass* This,__FIReference_1_BYTE **value) {
    return This->lpVtbl->get_ProtocolCode(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_put_ProtocolCode(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass* This,__FIReference_1_BYTE *value) {
    return This->lpVtbl->put_ProtocolCode(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Usb
#define IID_IUsbDeviceClass IID___x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass
#define IUsbDeviceClassVtbl __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClassVtbl
#define IUsbDeviceClass __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass
#define IUsbDeviceClass_QueryInterface __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_QueryInterface
#define IUsbDeviceClass_AddRef __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_AddRef
#define IUsbDeviceClass_Release __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_Release
#define IUsbDeviceClass_GetIids __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_GetIids
#define IUsbDeviceClass_GetRuntimeClassName __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_GetRuntimeClassName
#define IUsbDeviceClass_GetTrustLevel __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_GetTrustLevel
#define IUsbDeviceClass_get_ClassCode __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_get_ClassCode
#define IUsbDeviceClass_put_ClassCode __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_put_ClassCode
#define IUsbDeviceClass_get_SubclassCode __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_get_SubclassCode
#define IUsbDeviceClass_put_SubclassCode __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_put_SubclassCode
#define IUsbDeviceClass_get_ProtocolCode __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_get_ProtocolCode
#define IUsbDeviceClass_put_ProtocolCode __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_put_ProtocolCode
#endif /* WIDL_using_Windows_Devices_Usb */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IUsbDeviceStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics, 0x066b85a2, 0x09b7, 0x4446, 0x85,0x02, 0x6f,0xe6,0xdc,0xaa,0x73,0x09);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Usb {
                MIDL_INTERFACE("066b85a2-09b7-4446-8502-6fe6dcaa7309")
                IUsbDeviceStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetDeviceSelector(
                        UINT32 vendor,
                        UINT32 product,
                        GUID class,
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetDeviceSelectorGuidOnly(
                        GUID class,
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetDeviceSelectorVidPidOnly(
                        UINT32 vendor,
                        UINT32 product,
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetDeviceClassSelector(
                        ABI::Windows::Devices::Usb::IUsbDeviceClass *class,
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FromIdAsync(
                        HSTRING id,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Usb::UsbDevice* > **operation) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics, 0x066b85a2, 0x09b7, 0x4446, 0x85,0x02, 0x6f,0xe6,0xdc,0xaa,0x73,0x09)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics *This,
        TrustLevel *trustLevel);

    /*** IUsbDeviceStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDeviceSelector)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics *This,
        UINT32 vendor,
        UINT32 product,
        GUID class,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *GetDeviceSelectorGuidOnly)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics *This,
        GUID class,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *GetDeviceSelectorVidPidOnly)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics *This,
        UINT32 vendor,
        UINT32 product,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *GetDeviceClassSelector)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics *This,
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass *class,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *FromIdAsync)(
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics *This,
        HSTRING id,
        __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice **operation);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStaticsVtbl;

interface __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics {
    CONST_VTBL __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IUsbDeviceStatics methods ***/
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetDeviceSelector(This,vendor,product,class,value) (This)->lpVtbl->GetDeviceSelector(This,vendor,product,class,value)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetDeviceSelectorGuidOnly(This,class,value) (This)->lpVtbl->GetDeviceSelectorGuidOnly(This,class,value)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetDeviceSelectorVidPidOnly(This,vendor,product,value) (This)->lpVtbl->GetDeviceSelectorVidPidOnly(This,vendor,product,value)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetDeviceClassSelector(This,class,value) (This)->lpVtbl->GetDeviceClassSelector(This,class,value)
#define __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_FromIdAsync(This,id,operation) (This)->lpVtbl->FromIdAsync(This,id,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_QueryInterface(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_AddRef(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_Release(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetIids(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetTrustLevel(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IUsbDeviceStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetDeviceSelector(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics* This,UINT32 vendor,UINT32 product,GUID class,HSTRING *value) {
    return This->lpVtbl->GetDeviceSelector(This,vendor,product,class,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetDeviceSelectorGuidOnly(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics* This,GUID class,HSTRING *value) {
    return This->lpVtbl->GetDeviceSelectorGuidOnly(This,class,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetDeviceSelectorVidPidOnly(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics* This,UINT32 vendor,UINT32 product,HSTRING *value) {
    return This->lpVtbl->GetDeviceSelectorVidPidOnly(This,vendor,product,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetDeviceClassSelector(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics* This,__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceClass *class,HSTRING *value) {
    return This->lpVtbl->GetDeviceClassSelector(This,class,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_FromIdAsync(__x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics* This,HSTRING id,__FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice **operation) {
    return This->lpVtbl->FromIdAsync(This,id,operation);
}
#endif
#ifdef WIDL_using_Windows_Devices_Usb
#define IID_IUsbDeviceStatics IID___x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics
#define IUsbDeviceStaticsVtbl __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStaticsVtbl
#define IUsbDeviceStatics __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics
#define IUsbDeviceStatics_QueryInterface __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_QueryInterface
#define IUsbDeviceStatics_AddRef __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_AddRef
#define IUsbDeviceStatics_Release __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_Release
#define IUsbDeviceStatics_GetIids __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetIids
#define IUsbDeviceStatics_GetRuntimeClassName __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetRuntimeClassName
#define IUsbDeviceStatics_GetTrustLevel __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetTrustLevel
#define IUsbDeviceStatics_GetDeviceSelector __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetDeviceSelector
#define IUsbDeviceStatics_GetDeviceSelectorGuidOnly __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetDeviceSelectorGuidOnly
#define IUsbDeviceStatics_GetDeviceSelectorVidPidOnly __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetDeviceSelectorVidPidOnly
#define IUsbDeviceStatics_GetDeviceClassSelector __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_GetDeviceClassSelector
#define IUsbDeviceStatics_FromIdAsync __x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_FromIdAsync
#endif /* WIDL_using_Windows_Devices_Usb */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CUsb_CIUsbDeviceStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Usb.UsbConfiguration
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Usb_UsbConfiguration_DEFINED
#define RUNTIMECLASS_Windows_Devices_Usb_UsbConfiguration_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Usb_UsbConfiguration[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','U','s','b','.','U','s','b','C','o','n','f','i','g','u','r','a','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Usb_UsbConfiguration[] = L"Windows.Devices.Usb.UsbConfiguration";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Usb_UsbConfiguration[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','U','s','b','.','U','s','b','C','o','n','f','i','g','u','r','a','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Usb_UsbConfiguration_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Usb.UsbDevice
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Usb_UsbDevice_DEFINED
#define RUNTIMECLASS_Windows_Devices_Usb_UsbDevice_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Usb_UsbDevice[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','U','s','b','.','U','s','b','D','e','v','i','c','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Usb_UsbDevice[] = L"Windows.Devices.Usb.UsbDevice";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Usb_UsbDevice[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','U','s','b','.','U','s','b','D','e','v','i','c','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Usb_UsbDevice_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Usb.UsbDeviceClass
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Usb_UsbDeviceClass_DEFINED
#define RUNTIMECLASS_Windows_Devices_Usb_UsbDeviceClass_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Usb_UsbDeviceClass[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','U','s','b','.','U','s','b','D','e','v','i','c','e','C','l','a','s','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Usb_UsbDeviceClass[] = L"Windows.Devices.Usb.UsbDeviceClass";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Usb_UsbDeviceClass[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','U','s','b','.','U','s','b','D','e','v','i','c','e','C','l','a','s','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Usb_UsbDeviceClass_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Usb.UsbDeviceDescriptor
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Usb_UsbDeviceDescriptor_DEFINED
#define RUNTIMECLASS_Windows_Devices_Usb_UsbDeviceDescriptor_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Usb_UsbDeviceDescriptor[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','U','s','b','.','U','s','b','D','e','v','i','c','e','D','e','s','c','r','i','p','t','o','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Usb_UsbDeviceDescriptor[] = L"Windows.Devices.Usb.UsbDeviceDescriptor";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Usb_UsbDeviceDescriptor[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','U','s','b','.','U','s','b','D','e','v','i','c','e','D','e','s','c','r','i','p','t','o','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Usb_UsbDeviceDescriptor_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Usb.UsbInterface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Usb_UsbInterface_DEFINED
#define RUNTIMECLASS_Windows_Devices_Usb_UsbInterface_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Usb_UsbInterface[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','U','s','b','.','U','s','b','I','n','t','e','r','f','a','c','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Usb_UsbInterface[] = L"Windows.Devices.Usb.UsbInterface";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Usb_UsbInterface[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','U','s','b','.','U','s','b','I','n','t','e','r','f','a','c','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Usb_UsbInterface_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Usb.UsbSetupPacket
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Usb_UsbSetupPacket_DEFINED
#define RUNTIMECLASS_Windows_Devices_Usb_UsbSetupPacket_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Usb_UsbSetupPacket[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','U','s','b','.','U','s','b','S','e','t','u','p','P','a','c','k','e','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Usb_UsbSetupPacket[] = L"Windows.Devices.Usb.UsbSetupPacket";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Usb_UsbSetupPacket[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','U','s','b','.','U','s','b','S','e','t','u','p','P','a','c','k','e','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Usb_UsbSetupPacket_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Devices::Usb::UsbDevice* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice, 0x7331254f, 0x6caf, 0x587d, 0x9c,0x2a, 0x01,0x8c,0x66,0xd3,0x12,0xdb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("7331254f-6caf-587d-9c2a-018c66d312db")
            IAsyncOperationCompletedHandler<ABI::Windows::Devices::Usb::UsbDevice* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Usb::UsbDevice*, ABI::Windows::Devices::Usb::IUsbDevice* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice, 0x7331254f, 0x6caf, 0x587d, 0x9c,0x2a, 0x01,0x8c,0x66,0xd3,0x12,0xdb)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Usb::UsbDevice* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice *This,
        __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDeviceVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Usb::UsbDevice* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice_Release(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Usb::UsbDevice* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice* This,__FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_UsbDevice IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice
#define IAsyncOperationCompletedHandler_UsbDeviceVtbl __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDeviceVtbl
#define IAsyncOperationCompletedHandler_UsbDevice __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice
#define IAsyncOperationCompletedHandler_UsbDevice_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice_QueryInterface
#define IAsyncOperationCompletedHandler_UsbDevice_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice_AddRef
#define IAsyncOperationCompletedHandler_UsbDevice_Release __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice_Release
#define IAsyncOperationCompletedHandler_UsbDevice_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Devices::Usb::UsbDevice* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice, 0x2138c5ed, 0xb71a, 0x5166, 0x99,0x48, 0xd5,0x57,0x92,0x74,0x8f,0x5c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("2138c5ed-b71a-5166-9948-d55792748f5c")
            IAsyncOperation<ABI::Windows::Devices::Usb::UsbDevice* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Usb::UsbDevice*, ABI::Windows::Devices::Usb::IUsbDevice* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice, 0x2138c5ed, 0xb71a, 0x5166, 0x99,0x48, 0xd5,0x57,0x92,0x74,0x8f,0x5c)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Devices::Usb::UsbDevice* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice *This,
        __x_ABI_CWindows_CDevices_CUsb_CIUsbDevice **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDeviceVtbl;

interface __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice {
    CONST_VTBL __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Devices::Usb::UsbDevice* > methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_QueryInterface(__FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_AddRef(__FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_Release(__FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_GetIids(__FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_GetTrustLevel(__FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Devices::Usb::UsbDevice* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_put_Completed(__FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_get_Completed(__FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CUsb__CUsbDevice **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_GetResults(__FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice* This,__x_ABI_CWindows_CDevices_CUsb_CIUsbDevice **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_UsbDevice IID___FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice
#define IAsyncOperation_UsbDeviceVtbl __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDeviceVtbl
#define IAsyncOperation_UsbDevice __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice
#define IAsyncOperation_UsbDevice_QueryInterface __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_QueryInterface
#define IAsyncOperation_UsbDevice_AddRef __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_AddRef
#define IAsyncOperation_UsbDevice_Release __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_Release
#define IAsyncOperation_UsbDevice_GetIids __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_GetIids
#define IAsyncOperation_UsbDevice_GetRuntimeClassName __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_GetRuntimeClassName
#define IAsyncOperation_UsbDevice_GetTrustLevel __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_GetTrustLevel
#define IAsyncOperation_UsbDevice_put_Completed __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_put_Completed
#define IAsyncOperation_UsbDevice_get_Completed __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_get_Completed
#define IAsyncOperation_UsbDevice_GetResults __FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CDevices__CUsb__CUsbDevice_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_devices_usb_h__ */
