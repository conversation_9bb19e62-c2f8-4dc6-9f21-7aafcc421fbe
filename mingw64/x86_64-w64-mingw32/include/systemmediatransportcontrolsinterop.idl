/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

import "inspectable.idl";

[
    uuid(ddb0472d-c911-4a1f-86d9-dc3d71a95f5a),
]
interface ISystemMediaTransportControlsInterop : IInspectable
{
    HRESULT GetForWindow([in] HWND appWindow, [in] REFIID riid, [out, retval, iid_is(riid)] void **mediaTransportControl);
}
