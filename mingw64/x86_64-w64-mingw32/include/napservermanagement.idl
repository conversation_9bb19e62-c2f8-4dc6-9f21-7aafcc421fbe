/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

import "naptypes.idl";
import "unknwn.idl";

cpp_quote("#include <winapifamily.h>")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

interface INapServerInfo;
interface INapServerManagement;

cpp_quote("EXTERN_C const CLSID CLSID_NapServerManagement;")
cpp_quote("EXTERN_C const CLSID CLSID_NapServerInfo;")

[object, uuid (9de543e7-0f23-47e0-a8bc-971a894f86d4), pointer_default (unique)]
interface INapServerManagement : IUnknown {
  HRESULT RegisterSystemHealthValidator ([in] const NapComponentRegistrationInfo *validator,[in] const CLSID *validatorClsid);
  HRESULT UnregisterSystemHealthValidator ([in] SystemHealthEntityId id);
  HRESULT SetFailureCategoryMappings ([in] SystemHealthEntityId id,[in] const FailureCategoryMapping mapping);
};

[object, uuid (599f9021-**************-e88975efff0e), pointer_default (unique)]
interface INapServerInfo : IUnknown {
  HRESULT GetNapServerInfo ([out] CountedString **serverName,[out] CountedString **serverDescription,[out] CountedString **protocolVersion);
  HRESULT GetRegisteredSystemHealthValidators ([out] SystemHealthEntityCount *count,[out, size_is (,*count)] NapComponentRegistrationInfo **validators,[out, size_is (,*count)] CLSID **validatorClsids);
  HRESULT GetFailureCategoryMappings ([in] SystemHealthEntityId id,[out] FailureCategoryMapping *mapping);
};
cpp_quote("#endif")
