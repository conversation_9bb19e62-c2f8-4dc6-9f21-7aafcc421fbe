/*
 * Copyright (C) 2024 Biswap<PERSON>yo <PERSON>h
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "inspectable.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";

namespace Windows.Graphics {
    typedef struct DisplayAdapterId DisplayAdapterId;
    typedef struct DisplayId DisplayId;
    typedef struct PointInt32 PointInt32;
    typedef struct RectInt32 RectInt32;
    typedef struct SizeInt32 SizeInt32;

    interface IGeometrySource2D;

    declare {
        interface Windows.Foundation.Collections.IIterable<Windows.Graphics.RectInt32>;
        interface Windows.Foundation.Collections.IIterator<Windows.Graphics.RectInt32>;
        interface Windows.Foundation.Collections.IVectorView<Windows.Graphics.RectInt32>;
        interface Windows.Foundation.IReference<Windows.Graphics.SizeInt32>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 6.0)
    ]
    struct DisplayAdapterId
    {
        UINT32 LowPart;
        INT32 HighPart;
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 12.0)
    ]
    struct DisplayId
    {
        UINT64 Value;
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0)
    ]
    struct PointInt32
    {
        INT32 X;
        INT32 Y;
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0)
    ]
    struct RectInt32
    {
        INT32 X;
        INT32 Y;
        INT32 Width;
        INT32 Height;
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0)
    ]
    struct SizeInt32
    {
        INT32 Width;
        INT32 Height;
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 6.0),
        uuid(caff7902-670c-4181-a624-da977203b845)
    ]
    interface IGeometrySource2D : IInspectable
    {
    }
}
