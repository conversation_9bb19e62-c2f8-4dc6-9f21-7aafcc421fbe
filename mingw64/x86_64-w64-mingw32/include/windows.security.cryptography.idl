/*
 * Copyright 2022 Nikolay Sivov for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "inspectable.idl";
import "asyncinfo.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";
import "windows.storage.streams.idl";

namespace Windows.Security.Cryptography
{
    typedef enum BinaryStringEncoding BinaryStringEncoding;
    runtimeclass CryptographicBuffer;

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum BinaryStringEncoding
    {
        Utf8 = 0,
        Utf16LE = 1,
        Utf16BE = 2,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Security.Cryptography.CryptographicBuffer),
        uuid(320b7e22-3cb0-4cdf-8663-1d28910065eb)
    ]
    interface ICryptographicBufferStatics : IInspectable
    {
        HRESULT Compare(
                [in] Windows.Storage.Streams.IBuffer *object1,
                [in] Windows.Storage.Streams.IBuffer *object2,
                [out, retval] boolean *is_equal
        );
        HRESULT GenerateRandom(
                [in] UINT32 length,
                [out, retval] Windows.Storage.Streams.IBuffer **buffer
        );
        HRESULT GenerateRandomNumber(
                [out, retval] UINT32 *value
        );
        HRESULT CreateFromByteArray(
                [in] UINT32 value_size,
                [in, size_is(value_size)] BYTE *value,
                [out, retval] Windows.Storage.Streams.IBuffer **buffer
        );
        HRESULT CopyToByteArray(
                [in] Windows.Storage.Streams.IBuffer *buffer,
                [out] UINT32 *value_size,
                [out, size_is(, *value_size)] BYTE **value
        );
        HRESULT DecodeFromHexString(
                [in] HSTRING value,
                [out, retval] Windows.Storage.Streams.IBuffer **buffer
        );
        HRESULT EncodeToHexString(
                [in] Windows.Storage.Streams.IBuffer *buffer,
                [out, retval] HSTRING *value
        );
        HRESULT DecodeFromBase64String(
                [in] HSTRING value,
                [out, retval] Windows.Storage.Streams.IBuffer **buffer
        );
        HRESULT EncodeToBase64String(
                [in] Windows.Storage.Streams.IBuffer *buffer,
                [out, retval] HSTRING *value
        );
        HRESULT ConvertStringToBinary(
                [in] HSTRING value,
                [in] Windows.Security.Cryptography.BinaryStringEncoding encoding,
                [out, retval] Windows.Storage.Streams.IBuffer **buffer
        );
        HRESULT ConvertBinaryToString(
                [in] Windows.Security.Cryptography.BinaryStringEncoding encoding,
                [in] Windows.Storage.Streams.IBuffer *buffer,
                [out, retval] HSTRING *value
        );
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        static(Windows.Security.Cryptography.ICryptographicBufferStatics, Windows.Foundation.UniversalApiContract, 1.0),
        threading(both)
    ]
    runtimeclass CryptographicBuffer
    {
    }
}
