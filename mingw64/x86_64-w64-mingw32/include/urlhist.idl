/*
 * Copyright 2006 <PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "objidl.idl";
import "oleidl.idl";
import "oaidl.idl";
import "docobj.idl";

cpp_quote("#define STATURL_QUERYFLAG_ISCACHED  0x010000")
cpp_quote("#define STATURL_QUERYFLAG_NOURL     0x020000")
cpp_quote("#define STATURL_QUERYFLAG_NOTITLE   0x040000")
cpp_quote("#define STATURL_QUERYFLAG_TOPLEVEL  0x080000")

cpp_quote("#define STATURLFLAG_ISCACHED    0x0001")
cpp_quote("#define STATURLFLAG_ISTOPLEVEL  0x0002")

typedef enum _ADDURL_FLAG
{
    ADDURL_FIRST                = 0,
    ADDURL_ADDTOHISTORYANDCACHE = 0,
    ADDURL_ADDTOCACHE           = 1,
    ADDURL_Max                  = 0x7fffffff
} ADDURL_FLAG;

/*****************************************************************************
 *    IEnumSTATURL interface
 */
[
    object,
    uuid(3c374a42-bae4-11cf-bf7d-00aa006946ee),
    pointer_default(unique)
]
interface IEnumSTATURL : IUnknown
{
    typedef [unique] IEnumSTATURL *LPENUMSTATURL;

    typedef struct _STATURL
    {
        DWORD cbSize;
        LPWSTR pwcsUrl;
        LPWSTR pwcsTitle;
        FILETIME ftLastVisited;
        FILETIME ftLastUpdated;
        FILETIME ftExpires;
        DWORD dwFlags;
    } STATURL, *LPSTATURL;

    HRESULT Next(
        [in] ULONG celt,
        [in, out] LPSTATURL rgelt,
        [in, out] ULONG *pceltFetched);

    HRESULT Skip([in] ULONG celt);
    HRESULT Reset();
    HRESULT Clone([out] IEnumSTATURL **ppenum);

    HRESULT SetFilter(
        [in] LPCOLESTR poszFilter,
        [in] DWORD dwFlags);
}

/*****************************************************************************
 *    IUrlHistoryStg interface
 */
[
    object,
    uuid(3c374a41-bae4-11cf-bf7d-00aa006946ee),
    pointer_default(unique)
]
interface IUrlHistoryStg : IUnknown
{
    typedef [unique] IUrlHistoryStg *LPURLHISTORYSTG;

    HRESULT AddUrl(
        [in] LPCOLESTR pocsUrl,
        [in, unique] LPCOLESTR pocsTitle,
        [in] DWORD dwFlags);

    HRESULT DeleteUrl(
        [in] LPCOLESTR pocsUrl,
        [in] DWORD dwFlags);

    HRESULT QueryUrl(
        [in] LPCOLESTR pocsUrl,
        [in] DWORD dwFlags,
        [in, out, unique] LPSTATURL lpSTATURL);

    HRESULT BindToObject(
        [in] LPCOLESTR pocsUrl,
        [in] REFIID riid,
        [out, iid_is(riid)] void **ppvOut);

    HRESULT EnumUrls(
        [out] IEnumSTATURL **ppEnum);
}

/*****************************************************************************
 *    IUrlHistoryStg2 interface
 */
[
    object,
    uuid(afa0dc11-c313-11d0-831a-00c04fd5ae38),
    pointer_default(unique)
]
interface IUrlHistoryStg2 : IUrlHistoryStg
{
    typedef [unique] IUrlHistoryStg2 *LPURLHISTORYSTG2;

    HRESULT AddUrlAndNotify(
        [in] LPCOLESTR pocsUrl,
        [in,unique] LPCOLESTR pocsTitle,
        [in] DWORD dwFlags,
        [in] BOOL fWriteHistory,
        [in] IOleCommandTarget *poctNotify,
        [in, unique] IUnknown *punkISFolder);

    HRESULT ClearHistory();
}

/*****************************************************************************
 *    IUrlHistoryNotify interface
 */
[
    object,
    uuid(bc40bec1-c493-11d0-831b-00C04fd5ae38),
    pointer_default(unique)
]
interface IUrlHistoryNotify : IOleCommandTarget
{
    typedef [unique] IUrlHistoryNotify *LPURLHISTORYNOTIFY;
}
