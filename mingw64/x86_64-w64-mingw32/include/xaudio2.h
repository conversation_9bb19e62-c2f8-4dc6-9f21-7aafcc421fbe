/*** Autogenerated by WIDL 10.8 from include/xaudio2.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __xaudio2_h__
#define __xaudio2_h__

/* Forward declarations */

#ifndef __IXAudio2EngineCallback_FWD_DEFINED__
#define __IXAudio2EngineCallback_FWD_DEFINED__
typedef interface IXAudio2EngineCallback IXAudio2EngineCallback;
#ifdef __cplusplus
interface IXAudio2EngineCallback;
#endif /* __cplusplus */
#endif

#ifndef __IXAudio2Voice_FWD_DEFINED__
#define __IXAudio2Voice_FWD_DEFINED__
typedef interface IXAudio2Voice IXAudio2Voice;
#ifdef __cplusplus
interface IXAudio2Voice;
#endif /* __cplusplus */
#endif

#ifndef __IXAudio2SourceVoice_FWD_DEFINED__
#define __IXAudio2SourceVoice_FWD_DEFINED__
typedef interface IXAudio2SourceVoice IXAudio2SourceVoice;
#ifdef __cplusplus
interface IXAudio2SourceVoice;
#endif /* __cplusplus */
#endif

#ifndef __IXAudio2SubmixVoice_FWD_DEFINED__
#define __IXAudio2SubmixVoice_FWD_DEFINED__
typedef interface IXAudio2SubmixVoice IXAudio2SubmixVoice;
#ifdef __cplusplus
interface IXAudio2SubmixVoice;
#endif /* __cplusplus */
#endif

#ifndef __IXAudio2MasteringVoice_FWD_DEFINED__
#define __IXAudio2MasteringVoice_FWD_DEFINED__
typedef interface IXAudio2MasteringVoice IXAudio2MasteringVoice;
#ifdef __cplusplus
interface IXAudio2MasteringVoice;
#endif /* __cplusplus */
#endif

#ifndef __IXAudio2VoiceCallback_FWD_DEFINED__
#define __IXAudio2VoiceCallback_FWD_DEFINED__
typedef interface IXAudio2VoiceCallback IXAudio2VoiceCallback;
#ifdef __cplusplus
interface IXAudio2VoiceCallback;
#endif /* __cplusplus */
#endif

#ifndef __IXAudio2_FWD_DEFINED__
#define __IXAudio2_FWD_DEFINED__
typedef interface IXAudio2 IXAudio2;
#ifdef __cplusplus
interface IXAudio2;
#endif /* __cplusplus */
#endif

#ifndef __IXAudio2Extension_FWD_DEFINED__
#define __IXAudio2Extension_FWD_DEFINED__
typedef interface IXAudio2Extension IXAudio2Extension;
#ifdef __cplusplus
interface IXAudio2Extension;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <unknwn.h>
#include <mmdeviceapi.h>
#include <audiosessiontypes.h>
#include <mmreg.h>

#ifdef __cplusplus
extern "C" {
#endif

#pragma pack(push,1)
#ifndef __IXAudio2Voice_FWD_DEFINED__
#define __IXAudio2Voice_FWD_DEFINED__
typedef interface IXAudio2Voice IXAudio2Voice;
#ifdef __cplusplus
interface IXAudio2Voice;
#endif /* __cplusplus */
#endif

typedef enum XAUDIO2_WINDOWS_PROCESSOR_SPECIFIER {
    Processor1 = 0x1,
    Processor2 = 0x2,
    Processor3 = 0x4,
    Processor4 = 0x8,
    Processor5 = 0x10,
    Processor6 = 0x20,
    Processor7 = 0x40,
    Processor8 = 0x80,
    Processor9 = 0x100,
    Processor10 = 0x200,
    Processor11 = 0x400,
    Processor12 = 0x800,
    Processor13 = 0x1000,
    Processor14 = 0x2000,
    Processor15 = 0x4000,
    Processor16 = 0x8000,
    Processor17 = 0x10000,
    Processor18 = 0x20000,
    Processor19 = 0x40000,
    Processor20 = 0x80000,
    Processor21 = 0x100000,
    Processor22 = 0x200000,
    Processor23 = 0x400000,
    Processor24 = 0x800000,
    Processor25 = 0x1000000,
    Processor26 = 0x2000000,
    Processor27 = 0x4000000,
    Processor28 = 0x8000000,
    Processor29 = 0x10000000,
    Processor30 = 0x20000000,
    Processor31 = 0x40000000,
    Processor32 = 0x80000000,
    XAUDIO2_ANY_PROCESSOR = 0xffffffff,
    XAUDIO2_DEFAULT_PROCESSOR = XAUDIO2_ANY_PROCESSOR
} XAUDIO2_WINDOWS_PROCESSOR_SPECIFIER;
typedef enum XAUDIO2_WINDOWS_PROCESSOR_SPECIFIER XAUDIO2_PROCESSOR;
typedef struct XAUDIO2_PERFORMANCE_DATA {
    UINT64 AudioCyclesSinceLastQuery;
    UINT64 TotalCyclesSinceLastQuery;
    UINT32 MinimumCyclesPerQuantum;
    UINT32 MaximumCyclesPerQuantum;
    UINT32 MemoryUsageInBytes;
    UINT32 CurrentLatencyInSamples;
    UINT32 GlitchesSinceEngineStarted;
    UINT32 ActiveSourceVoiceCount;
    UINT32 TotalSourceVoiceCount;
    UINT32 ActiveSubmixVoiceCount;
    UINT32 ActiveResamplerCount;
    UINT32 ActiveMatrixMixCount;
    UINT32 ActiveXmaSourceVoices;
    UINT32 ActiveXmaStreams;
} XAUDIO2_PERFORMANCE_DATA;
typedef enum XAUDIO2_DEVICE_ROLE {
    NotDefaultDevice = 0x0,
    DefaultConsoleDevice = 0x1,
    DefaultMultimediaDevice = 0x2,
    DefaultCommunicationsDevice = 0x4,
    DefaultGameDevice = 0x8,
    GlobalDefaultDevice = 0xf,
    InvalidDeviceRole = ~GlobalDefaultDevice
} XAUDIO2_DEVICE_ROLE;
typedef struct XAUDIO2_VOICE_DETAILS {
    UINT32 CreationFlags;
    UINT32 ActiveFlags;
    UINT32 InputChannels;
    UINT32 InputSampleRate;
} XAUDIO2_VOICE_DETAILS;
typedef struct XAUDIO2_SEND_DESCRIPTOR {
    UINT32 Flags;
    IXAudio2Voice *pOutputVoice;
} XAUDIO2_SEND_DESCRIPTOR;
typedef struct XAUDIO2_VOICE_SENDS {
    UINT32 SendCount;
    XAUDIO2_SEND_DESCRIPTOR *pSends;
} XAUDIO2_VOICE_SENDS;
typedef struct XAUDIO2_EFFECT_DESCRIPTOR {
    IUnknown *pEffect;
    WINBOOL InitialState;
    UINT32 OutputChannels;
} XAUDIO2_EFFECT_DESCRIPTOR;
typedef struct XAUDIO2_EFFECT_CHAIN {
    UINT32 EffectCount;
    XAUDIO2_EFFECT_DESCRIPTOR *pEffectDescriptors;
} XAUDIO2_EFFECT_CHAIN;
#define XAUDIO2_MAX_BUFFER_BYTES (0x80000000)

#define XAUDIO2_MAX_QUEUED_BUFFERS (64)

#define XAUDIO2_MAX_BUFFERS_SYSTEM (2)

#define XAUDIO2_MAX_AUDIO_CHANNELS (64)

#define XAUDIO2_MIN_SAMPLE_RATE (1000)

#define XAUDIO2_MAX_SAMPLE_RATE (200000)

#define XAUDIO2_MAX_VOLUME_LEVEL (16777216.0000000)

#define XAUDIO2_MIN_FREQ_RATIO (1 / 1024.00000000000)

#define XAUDIO2_MAX_FREQ_RATIO (1024.00000000000)

#define XAUDIO2_DEFAULT_FREQ_RATIO (2.00000000000000)

#define XAUDIO2_MAX_FILTER_ONEOVERQ (1.50000000000000)

#define XAUDIO2_MAX_FILTER_FREQUENCY (1.00000000000000)

#define XAUDIO2_MAX_LOOP_COUNT (254)

#define XAUDIO2_COMMIT_NOW (0)

#define XAUDIO2_COMMIT_ALL (0)

#define XAUDIO2_INVALID_OPSET (0xffffffff)

#define XAUDIO2_NO_LOOP_REGION (0)

#define XAUDIO2_LOOP_INFINITE (255)

#define XAUDIO2_DEFAULT_CHANNELS (0)

#define XAUDIO2_DEFAULT_SAMPLERATE (0)

/*****************************************************************************
 * IXAudio2EngineCallback interface
 */
#ifndef __IXAudio2EngineCallback_INTERFACE_DEFINED__
#define __IXAudio2EngineCallback_INTERFACE_DEFINED__

#if defined(__cplusplus) && !defined(CINTERFACE)
interface IXAudio2EngineCallback
{

    BEGIN_INTERFACE

    virtual void STDMETHODCALLTYPE OnProcessingPassStart(
        ) = 0;

    virtual void STDMETHODCALLTYPE OnProcessingPassEnd(
        ) = 0;

    virtual void STDMETHODCALLTYPE OnCriticalError(
        HRESULT Error) = 0;

    END_INTERFACE

};
#else
typedef struct IXAudio2EngineCallbackVtbl {
    BEGIN_INTERFACE

    /*** IXAudio2EngineCallback methods ***/
    void (STDMETHODCALLTYPE *OnProcessingPassStart)(
        IXAudio2EngineCallback *This);

    void (STDMETHODCALLTYPE *OnProcessingPassEnd)(
        IXAudio2EngineCallback *This);

    void (STDMETHODCALLTYPE *OnCriticalError)(
        IXAudio2EngineCallback *This,
        HRESULT Error);

    END_INTERFACE
} IXAudio2EngineCallbackVtbl;

interface IXAudio2EngineCallback {
    CONST_VTBL IXAudio2EngineCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IXAudio2EngineCallback methods ***/
#define IXAudio2EngineCallback_OnProcessingPassStart(This) (This)->lpVtbl->OnProcessingPassStart(This)
#define IXAudio2EngineCallback_OnProcessingPassEnd(This) (This)->lpVtbl->OnProcessingPassEnd(This)
#define IXAudio2EngineCallback_OnCriticalError(This,Error) (This)->lpVtbl->OnCriticalError(This,Error)
#else
/*** IXAudio2EngineCallback methods ***/
static inline void IXAudio2EngineCallback_OnProcessingPassStart(IXAudio2EngineCallback* This) {
    This->lpVtbl->OnProcessingPassStart(This);
}
static inline void IXAudio2EngineCallback_OnProcessingPassEnd(IXAudio2EngineCallback* This) {
    This->lpVtbl->OnProcessingPassEnd(This);
}
static inline void IXAudio2EngineCallback_OnCriticalError(IXAudio2EngineCallback* This,HRESULT Error) {
    This->lpVtbl->OnCriticalError(This,Error);
}
#endif
#endif

#endif


#endif  /* __IXAudio2EngineCallback_INTERFACE_DEFINED__ */

typedef enum XAUDIO2_FILTER_TYPE {
    LowPassFilter = 0,
    BandPassFilter = 1,
    HighPassFilter = 2,
    NotchFilter = 3
} XAUDIO2_FILTER_TYPE;
typedef struct XAUDIO2_FILTER_PARAMETERS {
    XAUDIO2_FILTER_TYPE Type;
    float Frequency;
    float OneOverQ;
} XAUDIO2_FILTER_PARAMETERS;
/*****************************************************************************
 * IXAudio2Voice interface
 */
#ifndef __IXAudio2Voice_INTERFACE_DEFINED__
#define __IXAudio2Voice_INTERFACE_DEFINED__

#if defined(__cplusplus) && !defined(CINTERFACE)
interface IXAudio2Voice
{

    BEGIN_INTERFACE

    virtual void STDMETHODCALLTYPE GetVoiceDetails(
        XAUDIO2_VOICE_DETAILS *pVoiceDetails) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutputVoices(
        const XAUDIO2_VOICE_SENDS *pSendList) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEffectChain(
        const XAUDIO2_EFFECT_CHAIN *pEffectChain) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableEffect(
        UINT32 EffectIndex,
        UINT32 OperationSet = XAUDIO2_COMMIT_NOW) = 0;

    virtual HRESULT STDMETHODCALLTYPE DisableEffect(
        UINT32 EffectIndex,
        UINT32 OperationSet = XAUDIO2_COMMIT_NOW) = 0;

    virtual void STDMETHODCALLTYPE GetEffectState(
        UINT32 EffectIndex,
        WINBOOL *pEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEffectParameters(
        UINT32 EffectIndex,
        const void *pParameters,
        UINT32 ParametersByteSize,
        UINT32 OperationSet = XAUDIO2_COMMIT_NOW) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEffectParameters(
        UINT32 EffectIndex,
        void *pParameters,
        UINT32 ParametersByteSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFilterParameters(
        const XAUDIO2_FILTER_PARAMETERS *pParameters,
        UINT32 OperationSet = XAUDIO2_COMMIT_NOW) = 0;

    virtual void STDMETHODCALLTYPE GetFilterParameters(
        XAUDIO2_FILTER_PARAMETERS *pParameters) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutputFilterParameters(
        IXAudio2Voice *pDestinationVoice,
        const XAUDIO2_FILTER_PARAMETERS *pParameters,
        UINT32 OperationSet = XAUDIO2_COMMIT_NOW) = 0;

    virtual void STDMETHODCALLTYPE GetOutputFilterParameters(
        IXAudio2Voice *pDestinationVoice,
        XAUDIO2_FILTER_PARAMETERS *pParameters) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVolume(
        float Volume,
        UINT32 OperationSet = XAUDIO2_COMMIT_NOW) = 0;

    virtual void STDMETHODCALLTYPE GetVolume(
        float *pVolume) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetChannelVolumes(
        UINT32 Channels,
        const float *pVolumes,
        UINT32 OperationSet = XAUDIO2_COMMIT_NOW) = 0;

    virtual void STDMETHODCALLTYPE GetChannelVolumes(
        UINT32 Channels,
        float *pVolumes) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutputMatrix(
        IXAudio2Voice *pDestinationVoice,
        UINT32 SourceChannels,
        UINT32 DestinationChannels,
        const float *pLevelMatrix,
        UINT32 OperationSet = XAUDIO2_COMMIT_NOW) = 0;

    virtual void STDMETHODCALLTYPE GetOutputMatrix(
        IXAudio2Voice *pDestinationVoice,
        UINT32 SourceChannels,
        UINT32 DestinationChannels,
        float *pLevelMatrix) = 0;

    virtual void STDMETHODCALLTYPE DestroyVoice(
        ) = 0;

    END_INTERFACE

};
#else
typedef struct IXAudio2VoiceVtbl {
    BEGIN_INTERFACE

    /*** IXAudio2Voice methods ***/
    void (STDMETHODCALLTYPE *GetVoiceDetails)(
        IXAudio2Voice *This,
        XAUDIO2_VOICE_DETAILS *pVoiceDetails);

    HRESULT (STDMETHODCALLTYPE *SetOutputVoices)(
        IXAudio2Voice *This,
        const XAUDIO2_VOICE_SENDS *pSendList);

    HRESULT (STDMETHODCALLTYPE *SetEffectChain)(
        IXAudio2Voice *This,
        const XAUDIO2_EFFECT_CHAIN *pEffectChain);

    HRESULT (STDMETHODCALLTYPE *EnableEffect)(
        IXAudio2Voice *This,
        UINT32 EffectIndex,
        UINT32 OperationSet);

    HRESULT (STDMETHODCALLTYPE *DisableEffect)(
        IXAudio2Voice *This,
        UINT32 EffectIndex,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetEffectState)(
        IXAudio2Voice *This,
        UINT32 EffectIndex,
        WINBOOL *pEnabled);

    HRESULT (STDMETHODCALLTYPE *SetEffectParameters)(
        IXAudio2Voice *This,
        UINT32 EffectIndex,
        const void *pParameters,
        UINT32 ParametersByteSize,
        UINT32 OperationSet);

    HRESULT (STDMETHODCALLTYPE *GetEffectParameters)(
        IXAudio2Voice *This,
        UINT32 EffectIndex,
        void *pParameters,
        UINT32 ParametersByteSize);

    HRESULT (STDMETHODCALLTYPE *SetFilterParameters)(
        IXAudio2Voice *This,
        const XAUDIO2_FILTER_PARAMETERS *pParameters,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetFilterParameters)(
        IXAudio2Voice *This,
        XAUDIO2_FILTER_PARAMETERS *pParameters);

    HRESULT (STDMETHODCALLTYPE *SetOutputFilterParameters)(
        IXAudio2Voice *This,
        IXAudio2Voice *pDestinationVoice,
        const XAUDIO2_FILTER_PARAMETERS *pParameters,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetOutputFilterParameters)(
        IXAudio2Voice *This,
        IXAudio2Voice *pDestinationVoice,
        XAUDIO2_FILTER_PARAMETERS *pParameters);

    HRESULT (STDMETHODCALLTYPE *SetVolume)(
        IXAudio2Voice *This,
        float Volume,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetVolume)(
        IXAudio2Voice *This,
        float *pVolume);

    HRESULT (STDMETHODCALLTYPE *SetChannelVolumes)(
        IXAudio2Voice *This,
        UINT32 Channels,
        const float *pVolumes,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetChannelVolumes)(
        IXAudio2Voice *This,
        UINT32 Channels,
        float *pVolumes);

    HRESULT (STDMETHODCALLTYPE *SetOutputMatrix)(
        IXAudio2Voice *This,
        IXAudio2Voice *pDestinationVoice,
        UINT32 SourceChannels,
        UINT32 DestinationChannels,
        const float *pLevelMatrix,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetOutputMatrix)(
        IXAudio2Voice *This,
        IXAudio2Voice *pDestinationVoice,
        UINT32 SourceChannels,
        UINT32 DestinationChannels,
        float *pLevelMatrix);

    void (STDMETHODCALLTYPE *DestroyVoice)(
        IXAudio2Voice *This);

    END_INTERFACE
} IXAudio2VoiceVtbl;

interface IXAudio2Voice {
    CONST_VTBL IXAudio2VoiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IXAudio2Voice methods ***/
#define IXAudio2Voice_GetVoiceDetails(This,pVoiceDetails) (This)->lpVtbl->GetVoiceDetails(This,pVoiceDetails)
#define IXAudio2Voice_SetOutputVoices(This,pSendList) (This)->lpVtbl->SetOutputVoices(This,pSendList)
#define IXAudio2Voice_SetEffectChain(This,pEffectChain) (This)->lpVtbl->SetEffectChain(This,pEffectChain)
#define IXAudio2Voice_EnableEffect(This,EffectIndex,OperationSet) (This)->lpVtbl->EnableEffect(This,EffectIndex,OperationSet)
#define IXAudio2Voice_DisableEffect(This,EffectIndex,OperationSet) (This)->lpVtbl->DisableEffect(This,EffectIndex,OperationSet)
#define IXAudio2Voice_GetEffectState(This,EffectIndex,pEnabled) (This)->lpVtbl->GetEffectState(This,EffectIndex,pEnabled)
#define IXAudio2Voice_SetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize,OperationSet) (This)->lpVtbl->SetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize,OperationSet)
#define IXAudio2Voice_GetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize) (This)->lpVtbl->GetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize)
#define IXAudio2Voice_SetFilterParameters(This,pParameters,OperationSet) (This)->lpVtbl->SetFilterParameters(This,pParameters,OperationSet)
#define IXAudio2Voice_GetFilterParameters(This,pParameters) (This)->lpVtbl->GetFilterParameters(This,pParameters)
#define IXAudio2Voice_SetOutputFilterParameters(This,pDestinationVoice,pParameters,OperationSet) (This)->lpVtbl->SetOutputFilterParameters(This,pDestinationVoice,pParameters,OperationSet)
#define IXAudio2Voice_GetOutputFilterParameters(This,pDestinationVoice,pParameters) (This)->lpVtbl->GetOutputFilterParameters(This,pDestinationVoice,pParameters)
#define IXAudio2Voice_SetVolume(This,Volume,OperationSet) (This)->lpVtbl->SetVolume(This,Volume,OperationSet)
#define IXAudio2Voice_GetVolume(This,pVolume) (This)->lpVtbl->GetVolume(This,pVolume)
#define IXAudio2Voice_SetChannelVolumes(This,Channels,pVolumes,OperationSet) (This)->lpVtbl->SetChannelVolumes(This,Channels,pVolumes,OperationSet)
#define IXAudio2Voice_GetChannelVolumes(This,Channels,pVolumes) (This)->lpVtbl->GetChannelVolumes(This,Channels,pVolumes)
#define IXAudio2Voice_SetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix,OperationSet) (This)->lpVtbl->SetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix,OperationSet)
#define IXAudio2Voice_GetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix) (This)->lpVtbl->GetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix)
#define IXAudio2Voice_DestroyVoice(This) (This)->lpVtbl->DestroyVoice(This)
#else
/*** IXAudio2Voice methods ***/
static inline void IXAudio2Voice_GetVoiceDetails(IXAudio2Voice* This,XAUDIO2_VOICE_DETAILS *pVoiceDetails) {
    This->lpVtbl->GetVoiceDetails(This,pVoiceDetails);
}
static inline HRESULT IXAudio2Voice_SetOutputVoices(IXAudio2Voice* This,const XAUDIO2_VOICE_SENDS *pSendList) {
    return This->lpVtbl->SetOutputVoices(This,pSendList);
}
static inline HRESULT IXAudio2Voice_SetEffectChain(IXAudio2Voice* This,const XAUDIO2_EFFECT_CHAIN *pEffectChain) {
    return This->lpVtbl->SetEffectChain(This,pEffectChain);
}
static inline HRESULT IXAudio2Voice_EnableEffect(IXAudio2Voice* This,UINT32 EffectIndex,UINT32 OperationSet) {
    return This->lpVtbl->EnableEffect(This,EffectIndex,OperationSet);
}
static inline HRESULT IXAudio2Voice_DisableEffect(IXAudio2Voice* This,UINT32 EffectIndex,UINT32 OperationSet) {
    return This->lpVtbl->DisableEffect(This,EffectIndex,OperationSet);
}
static inline void IXAudio2Voice_GetEffectState(IXAudio2Voice* This,UINT32 EffectIndex,WINBOOL *pEnabled) {
    This->lpVtbl->GetEffectState(This,EffectIndex,pEnabled);
}
static inline HRESULT IXAudio2Voice_SetEffectParameters(IXAudio2Voice* This,UINT32 EffectIndex,const void *pParameters,UINT32 ParametersByteSize,UINT32 OperationSet) {
    return This->lpVtbl->SetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize,OperationSet);
}
static inline HRESULT IXAudio2Voice_GetEffectParameters(IXAudio2Voice* This,UINT32 EffectIndex,void *pParameters,UINT32 ParametersByteSize) {
    return This->lpVtbl->GetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize);
}
static inline HRESULT IXAudio2Voice_SetFilterParameters(IXAudio2Voice* This,const XAUDIO2_FILTER_PARAMETERS *pParameters,UINT32 OperationSet) {
    return This->lpVtbl->SetFilterParameters(This,pParameters,OperationSet);
}
static inline void IXAudio2Voice_GetFilterParameters(IXAudio2Voice* This,XAUDIO2_FILTER_PARAMETERS *pParameters) {
    This->lpVtbl->GetFilterParameters(This,pParameters);
}
static inline HRESULT IXAudio2Voice_SetOutputFilterParameters(IXAudio2Voice* This,IXAudio2Voice *pDestinationVoice,const XAUDIO2_FILTER_PARAMETERS *pParameters,UINT32 OperationSet) {
    return This->lpVtbl->SetOutputFilterParameters(This,pDestinationVoice,pParameters,OperationSet);
}
static inline void IXAudio2Voice_GetOutputFilterParameters(IXAudio2Voice* This,IXAudio2Voice *pDestinationVoice,XAUDIO2_FILTER_PARAMETERS *pParameters) {
    This->lpVtbl->GetOutputFilterParameters(This,pDestinationVoice,pParameters);
}
static inline HRESULT IXAudio2Voice_SetVolume(IXAudio2Voice* This,float Volume,UINT32 OperationSet) {
    return This->lpVtbl->SetVolume(This,Volume,OperationSet);
}
static inline void IXAudio2Voice_GetVolume(IXAudio2Voice* This,float *pVolume) {
    This->lpVtbl->GetVolume(This,pVolume);
}
static inline HRESULT IXAudio2Voice_SetChannelVolumes(IXAudio2Voice* This,UINT32 Channels,const float *pVolumes,UINT32 OperationSet) {
    return This->lpVtbl->SetChannelVolumes(This,Channels,pVolumes,OperationSet);
}
static inline void IXAudio2Voice_GetChannelVolumes(IXAudio2Voice* This,UINT32 Channels,float *pVolumes) {
    This->lpVtbl->GetChannelVolumes(This,Channels,pVolumes);
}
static inline HRESULT IXAudio2Voice_SetOutputMatrix(IXAudio2Voice* This,IXAudio2Voice *pDestinationVoice,UINT32 SourceChannels,UINT32 DestinationChannels,const float *pLevelMatrix,UINT32 OperationSet) {
    return This->lpVtbl->SetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix,OperationSet);
}
static inline void IXAudio2Voice_GetOutputMatrix(IXAudio2Voice* This,IXAudio2Voice *pDestinationVoice,UINT32 SourceChannels,UINT32 DestinationChannels,float *pLevelMatrix) {
    This->lpVtbl->GetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix);
}
static inline void IXAudio2Voice_DestroyVoice(IXAudio2Voice* This) {
    This->lpVtbl->DestroyVoice(This);
}
#endif
#endif

#endif


#endif  /* __IXAudio2Voice_INTERFACE_DEFINED__ */

typedef struct XAUDIO2_BUFFER {
    UINT32 Flags;
    UINT32 AudioBytes;
    const BYTE *pAudioData;
    UINT32 PlayBegin;
    UINT32 PlayLength;
    UINT32 LoopBegin;
    UINT32 LoopLength;
    UINT32 LoopCount;
    void *pContext;
} XAUDIO2_BUFFER;
typedef struct XAUDIO2_BUFFER_WMA {
    const UINT32 *pDecodedPacketCumulativeBytes;
    UINT32 PacketCount;
} XAUDIO2_BUFFER_WMA;
typedef struct XAUDIO2_VOICE_STATE {
    void *pCurrentBufferContext;
    UINT32 BuffersQueued;
    UINT64 SamplesPlayed;
} XAUDIO2_VOICE_STATE;
/*****************************************************************************
 * IXAudio2SourceVoice interface
 */
#ifndef __IXAudio2SourceVoice_INTERFACE_DEFINED__
#define __IXAudio2SourceVoice_INTERFACE_DEFINED__

#if defined(__cplusplus) && !defined(CINTERFACE)
interface IXAudio2SourceVoice : public IXAudio2Voice
{
    virtual HRESULT STDMETHODCALLTYPE Start(
        UINT32 Flags = 0,
        UINT32 OperationSet = XAUDIO2_COMMIT_NOW) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        UINT32 Flags = 0,
        UINT32 OperationSet = XAUDIO2_COMMIT_NOW) = 0;

    virtual HRESULT STDMETHODCALLTYPE SubmitSourceBuffer(
        const XAUDIO2_BUFFER *pBuffer,
        const XAUDIO2_BUFFER_WMA *pBufferWMA = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE FlushSourceBuffers(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Discontinuity(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExitLoop(
        UINT32 OperationSet = XAUDIO2_COMMIT_NOW) = 0;

    virtual void STDMETHODCALLTYPE GetState(
        XAUDIO2_VOICE_STATE *pVoiceState,
        UINT32 Flags = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFrequencyRatio(
        float Ratio,
        UINT32 OperationSet = XAUDIO2_COMMIT_NOW) = 0;

    virtual void STDMETHODCALLTYPE GetFrequencyRatio(
        float *pRatio) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSourceSampleRate(
        UINT32 NewSourceSampleRate) = 0;

};
#else
typedef struct IXAudio2SourceVoiceVtbl {
    BEGIN_INTERFACE

    /*** IXAudio2Voice methods ***/
    void (STDMETHODCALLTYPE *GetVoiceDetails)(
        IXAudio2SourceVoice *This,
        XAUDIO2_VOICE_DETAILS *pVoiceDetails);

    HRESULT (STDMETHODCALLTYPE *SetOutputVoices)(
        IXAudio2SourceVoice *This,
        const XAUDIO2_VOICE_SENDS *pSendList);

    HRESULT (STDMETHODCALLTYPE *SetEffectChain)(
        IXAudio2SourceVoice *This,
        const XAUDIO2_EFFECT_CHAIN *pEffectChain);

    HRESULT (STDMETHODCALLTYPE *EnableEffect)(
        IXAudio2SourceVoice *This,
        UINT32 EffectIndex,
        UINT32 OperationSet);

    HRESULT (STDMETHODCALLTYPE *DisableEffect)(
        IXAudio2SourceVoice *This,
        UINT32 EffectIndex,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetEffectState)(
        IXAudio2SourceVoice *This,
        UINT32 EffectIndex,
        WINBOOL *pEnabled);

    HRESULT (STDMETHODCALLTYPE *SetEffectParameters)(
        IXAudio2SourceVoice *This,
        UINT32 EffectIndex,
        const void *pParameters,
        UINT32 ParametersByteSize,
        UINT32 OperationSet);

    HRESULT (STDMETHODCALLTYPE *GetEffectParameters)(
        IXAudio2SourceVoice *This,
        UINT32 EffectIndex,
        void *pParameters,
        UINT32 ParametersByteSize);

    HRESULT (STDMETHODCALLTYPE *SetFilterParameters)(
        IXAudio2SourceVoice *This,
        const XAUDIO2_FILTER_PARAMETERS *pParameters,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetFilterParameters)(
        IXAudio2SourceVoice *This,
        XAUDIO2_FILTER_PARAMETERS *pParameters);

    HRESULT (STDMETHODCALLTYPE *SetOutputFilterParameters)(
        IXAudio2SourceVoice *This,
        IXAudio2Voice *pDestinationVoice,
        const XAUDIO2_FILTER_PARAMETERS *pParameters,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetOutputFilterParameters)(
        IXAudio2SourceVoice *This,
        IXAudio2Voice *pDestinationVoice,
        XAUDIO2_FILTER_PARAMETERS *pParameters);

    HRESULT (STDMETHODCALLTYPE *SetVolume)(
        IXAudio2SourceVoice *This,
        float Volume,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetVolume)(
        IXAudio2SourceVoice *This,
        float *pVolume);

    HRESULT (STDMETHODCALLTYPE *SetChannelVolumes)(
        IXAudio2SourceVoice *This,
        UINT32 Channels,
        const float *pVolumes,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetChannelVolumes)(
        IXAudio2SourceVoice *This,
        UINT32 Channels,
        float *pVolumes);

    HRESULT (STDMETHODCALLTYPE *SetOutputMatrix)(
        IXAudio2SourceVoice *This,
        IXAudio2Voice *pDestinationVoice,
        UINT32 SourceChannels,
        UINT32 DestinationChannels,
        const float *pLevelMatrix,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetOutputMatrix)(
        IXAudio2SourceVoice *This,
        IXAudio2Voice *pDestinationVoice,
        UINT32 SourceChannels,
        UINT32 DestinationChannels,
        float *pLevelMatrix);

    void (STDMETHODCALLTYPE *DestroyVoice)(
        IXAudio2SourceVoice *This);

    /*** IXAudio2SourceVoice methods ***/
    HRESULT (STDMETHODCALLTYPE *Start)(
        IXAudio2SourceVoice *This,
        UINT32 Flags,
        UINT32 OperationSet);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IXAudio2SourceVoice *This,
        UINT32 Flags,
        UINT32 OperationSet);

    HRESULT (STDMETHODCALLTYPE *SubmitSourceBuffer)(
        IXAudio2SourceVoice *This,
        const XAUDIO2_BUFFER *pBuffer,
        const XAUDIO2_BUFFER_WMA *pBufferWMA);

    HRESULT (STDMETHODCALLTYPE *FlushSourceBuffers)(
        IXAudio2SourceVoice *This);

    HRESULT (STDMETHODCALLTYPE *Discontinuity)(
        IXAudio2SourceVoice *This);

    HRESULT (STDMETHODCALLTYPE *ExitLoop)(
        IXAudio2SourceVoice *This,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetState)(
        IXAudio2SourceVoice *This,
        XAUDIO2_VOICE_STATE *pVoiceState,
        UINT32 Flags);

    HRESULT (STDMETHODCALLTYPE *SetFrequencyRatio)(
        IXAudio2SourceVoice *This,
        float Ratio,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetFrequencyRatio)(
        IXAudio2SourceVoice *This,
        float *pRatio);

    HRESULT (STDMETHODCALLTYPE *SetSourceSampleRate)(
        IXAudio2SourceVoice *This,
        UINT32 NewSourceSampleRate);

    END_INTERFACE
} IXAudio2SourceVoiceVtbl;

interface IXAudio2SourceVoice {
    CONST_VTBL IXAudio2SourceVoiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IXAudio2Voice methods ***/
#define IXAudio2SourceVoice_GetVoiceDetails(This,pVoiceDetails) (This)->lpVtbl->GetVoiceDetails(This,pVoiceDetails)
#define IXAudio2SourceVoice_SetOutputVoices(This,pSendList) (This)->lpVtbl->SetOutputVoices(This,pSendList)
#define IXAudio2SourceVoice_SetEffectChain(This,pEffectChain) (This)->lpVtbl->SetEffectChain(This,pEffectChain)
#define IXAudio2SourceVoice_EnableEffect(This,EffectIndex,OperationSet) (This)->lpVtbl->EnableEffect(This,EffectIndex,OperationSet)
#define IXAudio2SourceVoice_DisableEffect(This,EffectIndex,OperationSet) (This)->lpVtbl->DisableEffect(This,EffectIndex,OperationSet)
#define IXAudio2SourceVoice_GetEffectState(This,EffectIndex,pEnabled) (This)->lpVtbl->GetEffectState(This,EffectIndex,pEnabled)
#define IXAudio2SourceVoice_SetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize,OperationSet) (This)->lpVtbl->SetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize,OperationSet)
#define IXAudio2SourceVoice_GetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize) (This)->lpVtbl->GetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize)
#define IXAudio2SourceVoice_SetFilterParameters(This,pParameters,OperationSet) (This)->lpVtbl->SetFilterParameters(This,pParameters,OperationSet)
#define IXAudio2SourceVoice_GetFilterParameters(This,pParameters) (This)->lpVtbl->GetFilterParameters(This,pParameters)
#define IXAudio2SourceVoice_SetOutputFilterParameters(This,pDestinationVoice,pParameters,OperationSet) (This)->lpVtbl->SetOutputFilterParameters(This,pDestinationVoice,pParameters,OperationSet)
#define IXAudio2SourceVoice_GetOutputFilterParameters(This,pDestinationVoice,pParameters) (This)->lpVtbl->GetOutputFilterParameters(This,pDestinationVoice,pParameters)
#define IXAudio2SourceVoice_SetVolume(This,Volume,OperationSet) (This)->lpVtbl->SetVolume(This,Volume,OperationSet)
#define IXAudio2SourceVoice_GetVolume(This,pVolume) (This)->lpVtbl->GetVolume(This,pVolume)
#define IXAudio2SourceVoice_SetChannelVolumes(This,Channels,pVolumes,OperationSet) (This)->lpVtbl->SetChannelVolumes(This,Channels,pVolumes,OperationSet)
#define IXAudio2SourceVoice_GetChannelVolumes(This,Channels,pVolumes) (This)->lpVtbl->GetChannelVolumes(This,Channels,pVolumes)
#define IXAudio2SourceVoice_SetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix,OperationSet) (This)->lpVtbl->SetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix,OperationSet)
#define IXAudio2SourceVoice_GetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix) (This)->lpVtbl->GetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix)
#define IXAudio2SourceVoice_DestroyVoice(This) (This)->lpVtbl->DestroyVoice(This)
/*** IXAudio2SourceVoice methods ***/
#define IXAudio2SourceVoice_Start(This,Flags,OperationSet) (This)->lpVtbl->Start(This,Flags,OperationSet)
#define IXAudio2SourceVoice_Stop(This,Flags,OperationSet) (This)->lpVtbl->Stop(This,Flags,OperationSet)
#define IXAudio2SourceVoice_SubmitSourceBuffer(This,pBuffer,pBufferWMA) (This)->lpVtbl->SubmitSourceBuffer(This,pBuffer,pBufferWMA)
#define IXAudio2SourceVoice_FlushSourceBuffers(This) (This)->lpVtbl->FlushSourceBuffers(This)
#define IXAudio2SourceVoice_Discontinuity(This) (This)->lpVtbl->Discontinuity(This)
#define IXAudio2SourceVoice_ExitLoop(This,OperationSet) (This)->lpVtbl->ExitLoop(This,OperationSet)
#define IXAudio2SourceVoice_GetState(This,pVoiceState,Flags) (This)->lpVtbl->GetState(This,pVoiceState,Flags)
#define IXAudio2SourceVoice_SetFrequencyRatio(This,Ratio,OperationSet) (This)->lpVtbl->SetFrequencyRatio(This,Ratio,OperationSet)
#define IXAudio2SourceVoice_GetFrequencyRatio(This,pRatio) (This)->lpVtbl->GetFrequencyRatio(This,pRatio)
#define IXAudio2SourceVoice_SetSourceSampleRate(This,NewSourceSampleRate) (This)->lpVtbl->SetSourceSampleRate(This,NewSourceSampleRate)
#else
/*** IXAudio2Voice methods ***/
static inline void IXAudio2SourceVoice_GetVoiceDetails(IXAudio2SourceVoice* This,XAUDIO2_VOICE_DETAILS *pVoiceDetails) {
    This->lpVtbl->GetVoiceDetails(This,pVoiceDetails);
}
static inline HRESULT IXAudio2SourceVoice_SetOutputVoices(IXAudio2SourceVoice* This,const XAUDIO2_VOICE_SENDS *pSendList) {
    return This->lpVtbl->SetOutputVoices(This,pSendList);
}
static inline HRESULT IXAudio2SourceVoice_SetEffectChain(IXAudio2SourceVoice* This,const XAUDIO2_EFFECT_CHAIN *pEffectChain) {
    return This->lpVtbl->SetEffectChain(This,pEffectChain);
}
static inline HRESULT IXAudio2SourceVoice_EnableEffect(IXAudio2SourceVoice* This,UINT32 EffectIndex,UINT32 OperationSet) {
    return This->lpVtbl->EnableEffect(This,EffectIndex,OperationSet);
}
static inline HRESULT IXAudio2SourceVoice_DisableEffect(IXAudio2SourceVoice* This,UINT32 EffectIndex,UINT32 OperationSet) {
    return This->lpVtbl->DisableEffect(This,EffectIndex,OperationSet);
}
static inline void IXAudio2SourceVoice_GetEffectState(IXAudio2SourceVoice* This,UINT32 EffectIndex,WINBOOL *pEnabled) {
    This->lpVtbl->GetEffectState(This,EffectIndex,pEnabled);
}
static inline HRESULT IXAudio2SourceVoice_SetEffectParameters(IXAudio2SourceVoice* This,UINT32 EffectIndex,const void *pParameters,UINT32 ParametersByteSize,UINT32 OperationSet) {
    return This->lpVtbl->SetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize,OperationSet);
}
static inline HRESULT IXAudio2SourceVoice_GetEffectParameters(IXAudio2SourceVoice* This,UINT32 EffectIndex,void *pParameters,UINT32 ParametersByteSize) {
    return This->lpVtbl->GetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize);
}
static inline HRESULT IXAudio2SourceVoice_SetFilterParameters(IXAudio2SourceVoice* This,const XAUDIO2_FILTER_PARAMETERS *pParameters,UINT32 OperationSet) {
    return This->lpVtbl->SetFilterParameters(This,pParameters,OperationSet);
}
static inline void IXAudio2SourceVoice_GetFilterParameters(IXAudio2SourceVoice* This,XAUDIO2_FILTER_PARAMETERS *pParameters) {
    This->lpVtbl->GetFilterParameters(This,pParameters);
}
static inline HRESULT IXAudio2SourceVoice_SetOutputFilterParameters(IXAudio2SourceVoice* This,IXAudio2Voice *pDestinationVoice,const XAUDIO2_FILTER_PARAMETERS *pParameters,UINT32 OperationSet) {
    return This->lpVtbl->SetOutputFilterParameters(This,pDestinationVoice,pParameters,OperationSet);
}
static inline void IXAudio2SourceVoice_GetOutputFilterParameters(IXAudio2SourceVoice* This,IXAudio2Voice *pDestinationVoice,XAUDIO2_FILTER_PARAMETERS *pParameters) {
    This->lpVtbl->GetOutputFilterParameters(This,pDestinationVoice,pParameters);
}
static inline HRESULT IXAudio2SourceVoice_SetVolume(IXAudio2SourceVoice* This,float Volume,UINT32 OperationSet) {
    return This->lpVtbl->SetVolume(This,Volume,OperationSet);
}
static inline void IXAudio2SourceVoice_GetVolume(IXAudio2SourceVoice* This,float *pVolume) {
    This->lpVtbl->GetVolume(This,pVolume);
}
static inline HRESULT IXAudio2SourceVoice_SetChannelVolumes(IXAudio2SourceVoice* This,UINT32 Channels,const float *pVolumes,UINT32 OperationSet) {
    return This->lpVtbl->SetChannelVolumes(This,Channels,pVolumes,OperationSet);
}
static inline void IXAudio2SourceVoice_GetChannelVolumes(IXAudio2SourceVoice* This,UINT32 Channels,float *pVolumes) {
    This->lpVtbl->GetChannelVolumes(This,Channels,pVolumes);
}
static inline HRESULT IXAudio2SourceVoice_SetOutputMatrix(IXAudio2SourceVoice* This,IXAudio2Voice *pDestinationVoice,UINT32 SourceChannels,UINT32 DestinationChannels,const float *pLevelMatrix,UINT32 OperationSet) {
    return This->lpVtbl->SetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix,OperationSet);
}
static inline void IXAudio2SourceVoice_GetOutputMatrix(IXAudio2SourceVoice* This,IXAudio2Voice *pDestinationVoice,UINT32 SourceChannels,UINT32 DestinationChannels,float *pLevelMatrix) {
    This->lpVtbl->GetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix);
}
static inline void IXAudio2SourceVoice_DestroyVoice(IXAudio2SourceVoice* This) {
    This->lpVtbl->DestroyVoice(This);
}
/*** IXAudio2SourceVoice methods ***/
static inline HRESULT IXAudio2SourceVoice_Start(IXAudio2SourceVoice* This,UINT32 Flags,UINT32 OperationSet) {
    return This->lpVtbl->Start(This,Flags,OperationSet);
}
static inline HRESULT IXAudio2SourceVoice_Stop(IXAudio2SourceVoice* This,UINT32 Flags,UINT32 OperationSet) {
    return This->lpVtbl->Stop(This,Flags,OperationSet);
}
static inline HRESULT IXAudio2SourceVoice_SubmitSourceBuffer(IXAudio2SourceVoice* This,const XAUDIO2_BUFFER *pBuffer,const XAUDIO2_BUFFER_WMA *pBufferWMA) {
    return This->lpVtbl->SubmitSourceBuffer(This,pBuffer,pBufferWMA);
}
static inline HRESULT IXAudio2SourceVoice_FlushSourceBuffers(IXAudio2SourceVoice* This) {
    return This->lpVtbl->FlushSourceBuffers(This);
}
static inline HRESULT IXAudio2SourceVoice_Discontinuity(IXAudio2SourceVoice* This) {
    return This->lpVtbl->Discontinuity(This);
}
static inline HRESULT IXAudio2SourceVoice_ExitLoop(IXAudio2SourceVoice* This,UINT32 OperationSet) {
    return This->lpVtbl->ExitLoop(This,OperationSet);
}
static inline void IXAudio2SourceVoice_GetState(IXAudio2SourceVoice* This,XAUDIO2_VOICE_STATE *pVoiceState,UINT32 Flags) {
    This->lpVtbl->GetState(This,pVoiceState,Flags);
}
static inline HRESULT IXAudio2SourceVoice_SetFrequencyRatio(IXAudio2SourceVoice* This,float Ratio,UINT32 OperationSet) {
    return This->lpVtbl->SetFrequencyRatio(This,Ratio,OperationSet);
}
static inline void IXAudio2SourceVoice_GetFrequencyRatio(IXAudio2SourceVoice* This,float *pRatio) {
    This->lpVtbl->GetFrequencyRatio(This,pRatio);
}
static inline HRESULT IXAudio2SourceVoice_SetSourceSampleRate(IXAudio2SourceVoice* This,UINT32 NewSourceSampleRate) {
    return This->lpVtbl->SetSourceSampleRate(This,NewSourceSampleRate);
}
#endif
#endif

#endif


#endif  /* __IXAudio2SourceVoice_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXAudio2SubmixVoice interface
 */
#ifndef __IXAudio2SubmixVoice_INTERFACE_DEFINED__
#define __IXAudio2SubmixVoice_INTERFACE_DEFINED__

#if defined(__cplusplus) && !defined(CINTERFACE)
interface IXAudio2SubmixVoice : public IXAudio2Voice
{
};
#else
typedef struct IXAudio2SubmixVoiceVtbl {
    BEGIN_INTERFACE

    /*** IXAudio2Voice methods ***/
    void (STDMETHODCALLTYPE *GetVoiceDetails)(
        IXAudio2SubmixVoice *This,
        XAUDIO2_VOICE_DETAILS *pVoiceDetails);

    HRESULT (STDMETHODCALLTYPE *SetOutputVoices)(
        IXAudio2SubmixVoice *This,
        const XAUDIO2_VOICE_SENDS *pSendList);

    HRESULT (STDMETHODCALLTYPE *SetEffectChain)(
        IXAudio2SubmixVoice *This,
        const XAUDIO2_EFFECT_CHAIN *pEffectChain);

    HRESULT (STDMETHODCALLTYPE *EnableEffect)(
        IXAudio2SubmixVoice *This,
        UINT32 EffectIndex,
        UINT32 OperationSet);

    HRESULT (STDMETHODCALLTYPE *DisableEffect)(
        IXAudio2SubmixVoice *This,
        UINT32 EffectIndex,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetEffectState)(
        IXAudio2SubmixVoice *This,
        UINT32 EffectIndex,
        WINBOOL *pEnabled);

    HRESULT (STDMETHODCALLTYPE *SetEffectParameters)(
        IXAudio2SubmixVoice *This,
        UINT32 EffectIndex,
        const void *pParameters,
        UINT32 ParametersByteSize,
        UINT32 OperationSet);

    HRESULT (STDMETHODCALLTYPE *GetEffectParameters)(
        IXAudio2SubmixVoice *This,
        UINT32 EffectIndex,
        void *pParameters,
        UINT32 ParametersByteSize);

    HRESULT (STDMETHODCALLTYPE *SetFilterParameters)(
        IXAudio2SubmixVoice *This,
        const XAUDIO2_FILTER_PARAMETERS *pParameters,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetFilterParameters)(
        IXAudio2SubmixVoice *This,
        XAUDIO2_FILTER_PARAMETERS *pParameters);

    HRESULT (STDMETHODCALLTYPE *SetOutputFilterParameters)(
        IXAudio2SubmixVoice *This,
        IXAudio2Voice *pDestinationVoice,
        const XAUDIO2_FILTER_PARAMETERS *pParameters,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetOutputFilterParameters)(
        IXAudio2SubmixVoice *This,
        IXAudio2Voice *pDestinationVoice,
        XAUDIO2_FILTER_PARAMETERS *pParameters);

    HRESULT (STDMETHODCALLTYPE *SetVolume)(
        IXAudio2SubmixVoice *This,
        float Volume,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetVolume)(
        IXAudio2SubmixVoice *This,
        float *pVolume);

    HRESULT (STDMETHODCALLTYPE *SetChannelVolumes)(
        IXAudio2SubmixVoice *This,
        UINT32 Channels,
        const float *pVolumes,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetChannelVolumes)(
        IXAudio2SubmixVoice *This,
        UINT32 Channels,
        float *pVolumes);

    HRESULT (STDMETHODCALLTYPE *SetOutputMatrix)(
        IXAudio2SubmixVoice *This,
        IXAudio2Voice *pDestinationVoice,
        UINT32 SourceChannels,
        UINT32 DestinationChannels,
        const float *pLevelMatrix,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetOutputMatrix)(
        IXAudio2SubmixVoice *This,
        IXAudio2Voice *pDestinationVoice,
        UINT32 SourceChannels,
        UINT32 DestinationChannels,
        float *pLevelMatrix);

    void (STDMETHODCALLTYPE *DestroyVoice)(
        IXAudio2SubmixVoice *This);

    END_INTERFACE
} IXAudio2SubmixVoiceVtbl;

interface IXAudio2SubmixVoice {
    CONST_VTBL IXAudio2SubmixVoiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IXAudio2Voice methods ***/
#define IXAudio2SubmixVoice_GetVoiceDetails(This,pVoiceDetails) (This)->lpVtbl->GetVoiceDetails(This,pVoiceDetails)
#define IXAudio2SubmixVoice_SetOutputVoices(This,pSendList) (This)->lpVtbl->SetOutputVoices(This,pSendList)
#define IXAudio2SubmixVoice_SetEffectChain(This,pEffectChain) (This)->lpVtbl->SetEffectChain(This,pEffectChain)
#define IXAudio2SubmixVoice_EnableEffect(This,EffectIndex,OperationSet) (This)->lpVtbl->EnableEffect(This,EffectIndex,OperationSet)
#define IXAudio2SubmixVoice_DisableEffect(This,EffectIndex,OperationSet) (This)->lpVtbl->DisableEffect(This,EffectIndex,OperationSet)
#define IXAudio2SubmixVoice_GetEffectState(This,EffectIndex,pEnabled) (This)->lpVtbl->GetEffectState(This,EffectIndex,pEnabled)
#define IXAudio2SubmixVoice_SetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize,OperationSet) (This)->lpVtbl->SetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize,OperationSet)
#define IXAudio2SubmixVoice_GetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize) (This)->lpVtbl->GetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize)
#define IXAudio2SubmixVoice_SetFilterParameters(This,pParameters,OperationSet) (This)->lpVtbl->SetFilterParameters(This,pParameters,OperationSet)
#define IXAudio2SubmixVoice_GetFilterParameters(This,pParameters) (This)->lpVtbl->GetFilterParameters(This,pParameters)
#define IXAudio2SubmixVoice_SetOutputFilterParameters(This,pDestinationVoice,pParameters,OperationSet) (This)->lpVtbl->SetOutputFilterParameters(This,pDestinationVoice,pParameters,OperationSet)
#define IXAudio2SubmixVoice_GetOutputFilterParameters(This,pDestinationVoice,pParameters) (This)->lpVtbl->GetOutputFilterParameters(This,pDestinationVoice,pParameters)
#define IXAudio2SubmixVoice_SetVolume(This,Volume,OperationSet) (This)->lpVtbl->SetVolume(This,Volume,OperationSet)
#define IXAudio2SubmixVoice_GetVolume(This,pVolume) (This)->lpVtbl->GetVolume(This,pVolume)
#define IXAudio2SubmixVoice_SetChannelVolumes(This,Channels,pVolumes,OperationSet) (This)->lpVtbl->SetChannelVolumes(This,Channels,pVolumes,OperationSet)
#define IXAudio2SubmixVoice_GetChannelVolumes(This,Channels,pVolumes) (This)->lpVtbl->GetChannelVolumes(This,Channels,pVolumes)
#define IXAudio2SubmixVoice_SetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix,OperationSet) (This)->lpVtbl->SetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix,OperationSet)
#define IXAudio2SubmixVoice_GetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix) (This)->lpVtbl->GetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix)
#define IXAudio2SubmixVoice_DestroyVoice(This) (This)->lpVtbl->DestroyVoice(This)
#else
/*** IXAudio2Voice methods ***/
static inline void IXAudio2SubmixVoice_GetVoiceDetails(IXAudio2SubmixVoice* This,XAUDIO2_VOICE_DETAILS *pVoiceDetails) {
    This->lpVtbl->GetVoiceDetails(This,pVoiceDetails);
}
static inline HRESULT IXAudio2SubmixVoice_SetOutputVoices(IXAudio2SubmixVoice* This,const XAUDIO2_VOICE_SENDS *pSendList) {
    return This->lpVtbl->SetOutputVoices(This,pSendList);
}
static inline HRESULT IXAudio2SubmixVoice_SetEffectChain(IXAudio2SubmixVoice* This,const XAUDIO2_EFFECT_CHAIN *pEffectChain) {
    return This->lpVtbl->SetEffectChain(This,pEffectChain);
}
static inline HRESULT IXAudio2SubmixVoice_EnableEffect(IXAudio2SubmixVoice* This,UINT32 EffectIndex,UINT32 OperationSet) {
    return This->lpVtbl->EnableEffect(This,EffectIndex,OperationSet);
}
static inline HRESULT IXAudio2SubmixVoice_DisableEffect(IXAudio2SubmixVoice* This,UINT32 EffectIndex,UINT32 OperationSet) {
    return This->lpVtbl->DisableEffect(This,EffectIndex,OperationSet);
}
static inline void IXAudio2SubmixVoice_GetEffectState(IXAudio2SubmixVoice* This,UINT32 EffectIndex,WINBOOL *pEnabled) {
    This->lpVtbl->GetEffectState(This,EffectIndex,pEnabled);
}
static inline HRESULT IXAudio2SubmixVoice_SetEffectParameters(IXAudio2SubmixVoice* This,UINT32 EffectIndex,const void *pParameters,UINT32 ParametersByteSize,UINT32 OperationSet) {
    return This->lpVtbl->SetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize,OperationSet);
}
static inline HRESULT IXAudio2SubmixVoice_GetEffectParameters(IXAudio2SubmixVoice* This,UINT32 EffectIndex,void *pParameters,UINT32 ParametersByteSize) {
    return This->lpVtbl->GetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize);
}
static inline HRESULT IXAudio2SubmixVoice_SetFilterParameters(IXAudio2SubmixVoice* This,const XAUDIO2_FILTER_PARAMETERS *pParameters,UINT32 OperationSet) {
    return This->lpVtbl->SetFilterParameters(This,pParameters,OperationSet);
}
static inline void IXAudio2SubmixVoice_GetFilterParameters(IXAudio2SubmixVoice* This,XAUDIO2_FILTER_PARAMETERS *pParameters) {
    This->lpVtbl->GetFilterParameters(This,pParameters);
}
static inline HRESULT IXAudio2SubmixVoice_SetOutputFilterParameters(IXAudio2SubmixVoice* This,IXAudio2Voice *pDestinationVoice,const XAUDIO2_FILTER_PARAMETERS *pParameters,UINT32 OperationSet) {
    return This->lpVtbl->SetOutputFilterParameters(This,pDestinationVoice,pParameters,OperationSet);
}
static inline void IXAudio2SubmixVoice_GetOutputFilterParameters(IXAudio2SubmixVoice* This,IXAudio2Voice *pDestinationVoice,XAUDIO2_FILTER_PARAMETERS *pParameters) {
    This->lpVtbl->GetOutputFilterParameters(This,pDestinationVoice,pParameters);
}
static inline HRESULT IXAudio2SubmixVoice_SetVolume(IXAudio2SubmixVoice* This,float Volume,UINT32 OperationSet) {
    return This->lpVtbl->SetVolume(This,Volume,OperationSet);
}
static inline void IXAudio2SubmixVoice_GetVolume(IXAudio2SubmixVoice* This,float *pVolume) {
    This->lpVtbl->GetVolume(This,pVolume);
}
static inline HRESULT IXAudio2SubmixVoice_SetChannelVolumes(IXAudio2SubmixVoice* This,UINT32 Channels,const float *pVolumes,UINT32 OperationSet) {
    return This->lpVtbl->SetChannelVolumes(This,Channels,pVolumes,OperationSet);
}
static inline void IXAudio2SubmixVoice_GetChannelVolumes(IXAudio2SubmixVoice* This,UINT32 Channels,float *pVolumes) {
    This->lpVtbl->GetChannelVolumes(This,Channels,pVolumes);
}
static inline HRESULT IXAudio2SubmixVoice_SetOutputMatrix(IXAudio2SubmixVoice* This,IXAudio2Voice *pDestinationVoice,UINT32 SourceChannels,UINT32 DestinationChannels,const float *pLevelMatrix,UINT32 OperationSet) {
    return This->lpVtbl->SetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix,OperationSet);
}
static inline void IXAudio2SubmixVoice_GetOutputMatrix(IXAudio2SubmixVoice* This,IXAudio2Voice *pDestinationVoice,UINT32 SourceChannels,UINT32 DestinationChannels,float *pLevelMatrix) {
    This->lpVtbl->GetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix);
}
static inline void IXAudio2SubmixVoice_DestroyVoice(IXAudio2SubmixVoice* This) {
    This->lpVtbl->DestroyVoice(This);
}
#endif
#endif

#endif


#endif  /* __IXAudio2SubmixVoice_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXAudio2MasteringVoice interface
 */
#ifndef __IXAudio2MasteringVoice_INTERFACE_DEFINED__
#define __IXAudio2MasteringVoice_INTERFACE_DEFINED__

#if defined(__cplusplus) && !defined(CINTERFACE)
interface IXAudio2MasteringVoice : public IXAudio2Voice
{
    virtual HRESULT STDMETHODCALLTYPE GetChannelMask(
        DWORD *pChannelMask) = 0;

};
#else
typedef struct IXAudio2MasteringVoiceVtbl {
    BEGIN_INTERFACE

    /*** IXAudio2Voice methods ***/
    void (STDMETHODCALLTYPE *GetVoiceDetails)(
        IXAudio2MasteringVoice *This,
        XAUDIO2_VOICE_DETAILS *pVoiceDetails);

    HRESULT (STDMETHODCALLTYPE *SetOutputVoices)(
        IXAudio2MasteringVoice *This,
        const XAUDIO2_VOICE_SENDS *pSendList);

    HRESULT (STDMETHODCALLTYPE *SetEffectChain)(
        IXAudio2MasteringVoice *This,
        const XAUDIO2_EFFECT_CHAIN *pEffectChain);

    HRESULT (STDMETHODCALLTYPE *EnableEffect)(
        IXAudio2MasteringVoice *This,
        UINT32 EffectIndex,
        UINT32 OperationSet);

    HRESULT (STDMETHODCALLTYPE *DisableEffect)(
        IXAudio2MasteringVoice *This,
        UINT32 EffectIndex,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetEffectState)(
        IXAudio2MasteringVoice *This,
        UINT32 EffectIndex,
        WINBOOL *pEnabled);

    HRESULT (STDMETHODCALLTYPE *SetEffectParameters)(
        IXAudio2MasteringVoice *This,
        UINT32 EffectIndex,
        const void *pParameters,
        UINT32 ParametersByteSize,
        UINT32 OperationSet);

    HRESULT (STDMETHODCALLTYPE *GetEffectParameters)(
        IXAudio2MasteringVoice *This,
        UINT32 EffectIndex,
        void *pParameters,
        UINT32 ParametersByteSize);

    HRESULT (STDMETHODCALLTYPE *SetFilterParameters)(
        IXAudio2MasteringVoice *This,
        const XAUDIO2_FILTER_PARAMETERS *pParameters,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetFilterParameters)(
        IXAudio2MasteringVoice *This,
        XAUDIO2_FILTER_PARAMETERS *pParameters);

    HRESULT (STDMETHODCALLTYPE *SetOutputFilterParameters)(
        IXAudio2MasteringVoice *This,
        IXAudio2Voice *pDestinationVoice,
        const XAUDIO2_FILTER_PARAMETERS *pParameters,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetOutputFilterParameters)(
        IXAudio2MasteringVoice *This,
        IXAudio2Voice *pDestinationVoice,
        XAUDIO2_FILTER_PARAMETERS *pParameters);

    HRESULT (STDMETHODCALLTYPE *SetVolume)(
        IXAudio2MasteringVoice *This,
        float Volume,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetVolume)(
        IXAudio2MasteringVoice *This,
        float *pVolume);

    HRESULT (STDMETHODCALLTYPE *SetChannelVolumes)(
        IXAudio2MasteringVoice *This,
        UINT32 Channels,
        const float *pVolumes,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetChannelVolumes)(
        IXAudio2MasteringVoice *This,
        UINT32 Channels,
        float *pVolumes);

    HRESULT (STDMETHODCALLTYPE *SetOutputMatrix)(
        IXAudio2MasteringVoice *This,
        IXAudio2Voice *pDestinationVoice,
        UINT32 SourceChannels,
        UINT32 DestinationChannels,
        const float *pLevelMatrix,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetOutputMatrix)(
        IXAudio2MasteringVoice *This,
        IXAudio2Voice *pDestinationVoice,
        UINT32 SourceChannels,
        UINT32 DestinationChannels,
        float *pLevelMatrix);

    void (STDMETHODCALLTYPE *DestroyVoice)(
        IXAudio2MasteringVoice *This);

    /*** IXAudio2MasteringVoice methods ***/
    HRESULT (STDMETHODCALLTYPE *GetChannelMask)(
        IXAudio2MasteringVoice *This,
        DWORD *pChannelMask);

    END_INTERFACE
} IXAudio2MasteringVoiceVtbl;

interface IXAudio2MasteringVoice {
    CONST_VTBL IXAudio2MasteringVoiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IXAudio2Voice methods ***/
#define IXAudio2MasteringVoice_GetVoiceDetails(This,pVoiceDetails) (This)->lpVtbl->GetVoiceDetails(This,pVoiceDetails)
#define IXAudio2MasteringVoice_SetOutputVoices(This,pSendList) (This)->lpVtbl->SetOutputVoices(This,pSendList)
#define IXAudio2MasteringVoice_SetEffectChain(This,pEffectChain) (This)->lpVtbl->SetEffectChain(This,pEffectChain)
#define IXAudio2MasteringVoice_EnableEffect(This,EffectIndex,OperationSet) (This)->lpVtbl->EnableEffect(This,EffectIndex,OperationSet)
#define IXAudio2MasteringVoice_DisableEffect(This,EffectIndex,OperationSet) (This)->lpVtbl->DisableEffect(This,EffectIndex,OperationSet)
#define IXAudio2MasteringVoice_GetEffectState(This,EffectIndex,pEnabled) (This)->lpVtbl->GetEffectState(This,EffectIndex,pEnabled)
#define IXAudio2MasteringVoice_SetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize,OperationSet) (This)->lpVtbl->SetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize,OperationSet)
#define IXAudio2MasteringVoice_GetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize) (This)->lpVtbl->GetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize)
#define IXAudio2MasteringVoice_SetFilterParameters(This,pParameters,OperationSet) (This)->lpVtbl->SetFilterParameters(This,pParameters,OperationSet)
#define IXAudio2MasteringVoice_GetFilterParameters(This,pParameters) (This)->lpVtbl->GetFilterParameters(This,pParameters)
#define IXAudio2MasteringVoice_SetOutputFilterParameters(This,pDestinationVoice,pParameters,OperationSet) (This)->lpVtbl->SetOutputFilterParameters(This,pDestinationVoice,pParameters,OperationSet)
#define IXAudio2MasteringVoice_GetOutputFilterParameters(This,pDestinationVoice,pParameters) (This)->lpVtbl->GetOutputFilterParameters(This,pDestinationVoice,pParameters)
#define IXAudio2MasteringVoice_SetVolume(This,Volume,OperationSet) (This)->lpVtbl->SetVolume(This,Volume,OperationSet)
#define IXAudio2MasteringVoice_GetVolume(This,pVolume) (This)->lpVtbl->GetVolume(This,pVolume)
#define IXAudio2MasteringVoice_SetChannelVolumes(This,Channels,pVolumes,OperationSet) (This)->lpVtbl->SetChannelVolumes(This,Channels,pVolumes,OperationSet)
#define IXAudio2MasteringVoice_GetChannelVolumes(This,Channels,pVolumes) (This)->lpVtbl->GetChannelVolumes(This,Channels,pVolumes)
#define IXAudio2MasteringVoice_SetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix,OperationSet) (This)->lpVtbl->SetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix,OperationSet)
#define IXAudio2MasteringVoice_GetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix) (This)->lpVtbl->GetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix)
#define IXAudio2MasteringVoice_DestroyVoice(This) (This)->lpVtbl->DestroyVoice(This)
/*** IXAudio2MasteringVoice methods ***/
#define IXAudio2MasteringVoice_GetChannelMask(This,pChannelMask) (This)->lpVtbl->GetChannelMask(This,pChannelMask)
#else
/*** IXAudio2Voice methods ***/
static inline void IXAudio2MasteringVoice_GetVoiceDetails(IXAudio2MasteringVoice* This,XAUDIO2_VOICE_DETAILS *pVoiceDetails) {
    This->lpVtbl->GetVoiceDetails(This,pVoiceDetails);
}
static inline HRESULT IXAudio2MasteringVoice_SetOutputVoices(IXAudio2MasteringVoice* This,const XAUDIO2_VOICE_SENDS *pSendList) {
    return This->lpVtbl->SetOutputVoices(This,pSendList);
}
static inline HRESULT IXAudio2MasteringVoice_SetEffectChain(IXAudio2MasteringVoice* This,const XAUDIO2_EFFECT_CHAIN *pEffectChain) {
    return This->lpVtbl->SetEffectChain(This,pEffectChain);
}
static inline HRESULT IXAudio2MasteringVoice_EnableEffect(IXAudio2MasteringVoice* This,UINT32 EffectIndex,UINT32 OperationSet) {
    return This->lpVtbl->EnableEffect(This,EffectIndex,OperationSet);
}
static inline HRESULT IXAudio2MasteringVoice_DisableEffect(IXAudio2MasteringVoice* This,UINT32 EffectIndex,UINT32 OperationSet) {
    return This->lpVtbl->DisableEffect(This,EffectIndex,OperationSet);
}
static inline void IXAudio2MasteringVoice_GetEffectState(IXAudio2MasteringVoice* This,UINT32 EffectIndex,WINBOOL *pEnabled) {
    This->lpVtbl->GetEffectState(This,EffectIndex,pEnabled);
}
static inline HRESULT IXAudio2MasteringVoice_SetEffectParameters(IXAudio2MasteringVoice* This,UINT32 EffectIndex,const void *pParameters,UINT32 ParametersByteSize,UINT32 OperationSet) {
    return This->lpVtbl->SetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize,OperationSet);
}
static inline HRESULT IXAudio2MasteringVoice_GetEffectParameters(IXAudio2MasteringVoice* This,UINT32 EffectIndex,void *pParameters,UINT32 ParametersByteSize) {
    return This->lpVtbl->GetEffectParameters(This,EffectIndex,pParameters,ParametersByteSize);
}
static inline HRESULT IXAudio2MasteringVoice_SetFilterParameters(IXAudio2MasteringVoice* This,const XAUDIO2_FILTER_PARAMETERS *pParameters,UINT32 OperationSet) {
    return This->lpVtbl->SetFilterParameters(This,pParameters,OperationSet);
}
static inline void IXAudio2MasteringVoice_GetFilterParameters(IXAudio2MasteringVoice* This,XAUDIO2_FILTER_PARAMETERS *pParameters) {
    This->lpVtbl->GetFilterParameters(This,pParameters);
}
static inline HRESULT IXAudio2MasteringVoice_SetOutputFilterParameters(IXAudio2MasteringVoice* This,IXAudio2Voice *pDestinationVoice,const XAUDIO2_FILTER_PARAMETERS *pParameters,UINT32 OperationSet) {
    return This->lpVtbl->SetOutputFilterParameters(This,pDestinationVoice,pParameters,OperationSet);
}
static inline void IXAudio2MasteringVoice_GetOutputFilterParameters(IXAudio2MasteringVoice* This,IXAudio2Voice *pDestinationVoice,XAUDIO2_FILTER_PARAMETERS *pParameters) {
    This->lpVtbl->GetOutputFilterParameters(This,pDestinationVoice,pParameters);
}
static inline HRESULT IXAudio2MasteringVoice_SetVolume(IXAudio2MasteringVoice* This,float Volume,UINT32 OperationSet) {
    return This->lpVtbl->SetVolume(This,Volume,OperationSet);
}
static inline void IXAudio2MasteringVoice_GetVolume(IXAudio2MasteringVoice* This,float *pVolume) {
    This->lpVtbl->GetVolume(This,pVolume);
}
static inline HRESULT IXAudio2MasteringVoice_SetChannelVolumes(IXAudio2MasteringVoice* This,UINT32 Channels,const float *pVolumes,UINT32 OperationSet) {
    return This->lpVtbl->SetChannelVolumes(This,Channels,pVolumes,OperationSet);
}
static inline void IXAudio2MasteringVoice_GetChannelVolumes(IXAudio2MasteringVoice* This,UINT32 Channels,float *pVolumes) {
    This->lpVtbl->GetChannelVolumes(This,Channels,pVolumes);
}
static inline HRESULT IXAudio2MasteringVoice_SetOutputMatrix(IXAudio2MasteringVoice* This,IXAudio2Voice *pDestinationVoice,UINT32 SourceChannels,UINT32 DestinationChannels,const float *pLevelMatrix,UINT32 OperationSet) {
    return This->lpVtbl->SetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix,OperationSet);
}
static inline void IXAudio2MasteringVoice_GetOutputMatrix(IXAudio2MasteringVoice* This,IXAudio2Voice *pDestinationVoice,UINT32 SourceChannels,UINT32 DestinationChannels,float *pLevelMatrix) {
    This->lpVtbl->GetOutputMatrix(This,pDestinationVoice,SourceChannels,DestinationChannels,pLevelMatrix);
}
static inline void IXAudio2MasteringVoice_DestroyVoice(IXAudio2MasteringVoice* This) {
    This->lpVtbl->DestroyVoice(This);
}
/*** IXAudio2MasteringVoice methods ***/
static inline HRESULT IXAudio2MasteringVoice_GetChannelMask(IXAudio2MasteringVoice* This,DWORD *pChannelMask) {
    return This->lpVtbl->GetChannelMask(This,pChannelMask);
}
#endif
#endif

#endif


#endif  /* __IXAudio2MasteringVoice_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXAudio2VoiceCallback interface
 */
#ifndef __IXAudio2VoiceCallback_INTERFACE_DEFINED__
#define __IXAudio2VoiceCallback_INTERFACE_DEFINED__

#if defined(__cplusplus) && !defined(CINTERFACE)
interface IXAudio2VoiceCallback
{

    BEGIN_INTERFACE

    virtual void STDMETHODCALLTYPE OnVoiceProcessingPassStart(
        UINT32 BytesRequired) = 0;

    virtual void STDMETHODCALLTYPE OnVoiceProcessingPassEnd(
        ) = 0;

    virtual void STDMETHODCALLTYPE OnStreamEnd(
        ) = 0;

    virtual void STDMETHODCALLTYPE OnBufferStart(
        void *pBufferContext) = 0;

    virtual void STDMETHODCALLTYPE OnBufferEnd(
        void *pBufferContext) = 0;

    virtual void STDMETHODCALLTYPE OnLoopEnd(
        void *pBufferContext) = 0;

    virtual void STDMETHODCALLTYPE OnVoiceError(
        void *pBuffercontext,
        HRESULT Error) = 0;

    END_INTERFACE

};
#else
typedef struct IXAudio2VoiceCallbackVtbl {
    BEGIN_INTERFACE

    /*** IXAudio2VoiceCallback methods ***/
    void (STDMETHODCALLTYPE *OnVoiceProcessingPassStart)(
        IXAudio2VoiceCallback *This,
        UINT32 BytesRequired);

    void (STDMETHODCALLTYPE *OnVoiceProcessingPassEnd)(
        IXAudio2VoiceCallback *This);

    void (STDMETHODCALLTYPE *OnStreamEnd)(
        IXAudio2VoiceCallback *This);

    void (STDMETHODCALLTYPE *OnBufferStart)(
        IXAudio2VoiceCallback *This,
        void *pBufferContext);

    void (STDMETHODCALLTYPE *OnBufferEnd)(
        IXAudio2VoiceCallback *This,
        void *pBufferContext);

    void (STDMETHODCALLTYPE *OnLoopEnd)(
        IXAudio2VoiceCallback *This,
        void *pBufferContext);

    void (STDMETHODCALLTYPE *OnVoiceError)(
        IXAudio2VoiceCallback *This,
        void *pBuffercontext,
        HRESULT Error);

    END_INTERFACE
} IXAudio2VoiceCallbackVtbl;

interface IXAudio2VoiceCallback {
    CONST_VTBL IXAudio2VoiceCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IXAudio2VoiceCallback methods ***/
#define IXAudio2VoiceCallback_OnVoiceProcessingPassStart(This,BytesRequired) (This)->lpVtbl->OnVoiceProcessingPassStart(This,BytesRequired)
#define IXAudio2VoiceCallback_OnVoiceProcessingPassEnd(This) (This)->lpVtbl->OnVoiceProcessingPassEnd(This)
#define IXAudio2VoiceCallback_OnStreamEnd(This) (This)->lpVtbl->OnStreamEnd(This)
#define IXAudio2VoiceCallback_OnBufferStart(This,pBufferContext) (This)->lpVtbl->OnBufferStart(This,pBufferContext)
#define IXAudio2VoiceCallback_OnBufferEnd(This,pBufferContext) (This)->lpVtbl->OnBufferEnd(This,pBufferContext)
#define IXAudio2VoiceCallback_OnLoopEnd(This,pBufferContext) (This)->lpVtbl->OnLoopEnd(This,pBufferContext)
#define IXAudio2VoiceCallback_OnVoiceError(This,pBuffercontext,Error) (This)->lpVtbl->OnVoiceError(This,pBuffercontext,Error)
#else
/*** IXAudio2VoiceCallback methods ***/
static inline void IXAudio2VoiceCallback_OnVoiceProcessingPassStart(IXAudio2VoiceCallback* This,UINT32 BytesRequired) {
    This->lpVtbl->OnVoiceProcessingPassStart(This,BytesRequired);
}
static inline void IXAudio2VoiceCallback_OnVoiceProcessingPassEnd(IXAudio2VoiceCallback* This) {
    This->lpVtbl->OnVoiceProcessingPassEnd(This);
}
static inline void IXAudio2VoiceCallback_OnStreamEnd(IXAudio2VoiceCallback* This) {
    This->lpVtbl->OnStreamEnd(This);
}
static inline void IXAudio2VoiceCallback_OnBufferStart(IXAudio2VoiceCallback* This,void *pBufferContext) {
    This->lpVtbl->OnBufferStart(This,pBufferContext);
}
static inline void IXAudio2VoiceCallback_OnBufferEnd(IXAudio2VoiceCallback* This,void *pBufferContext) {
    This->lpVtbl->OnBufferEnd(This,pBufferContext);
}
static inline void IXAudio2VoiceCallback_OnLoopEnd(IXAudio2VoiceCallback* This,void *pBufferContext) {
    This->lpVtbl->OnLoopEnd(This,pBufferContext);
}
static inline void IXAudio2VoiceCallback_OnVoiceError(IXAudio2VoiceCallback* This,void *pBuffercontext,HRESULT Error) {
    This->lpVtbl->OnVoiceError(This,pBuffercontext,Error);
}
#endif
#endif

#endif


#endif  /* __IXAudio2VoiceCallback_INTERFACE_DEFINED__ */

typedef struct XAUDIO2_DEBUG_CONFIGURATION {
    UINT32 TraceMask;
    UINT32 BreakMask;
    WINBOOL LogThreadID;
    WINBOOL LogFileline;
    WINBOOL LogFunctionName;
    WINBOOL LogTiming;
} XAUDIO2_DEBUG_CONFIGURATION;
/*****************************************************************************
 * IXAudio2 interface
 */
#ifndef __IXAudio2_INTERFACE_DEFINED__
#define __IXAudio2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXAudio2, 0x2b02e3cf, 0x2e0b, 0x4ec3, 0xbe,0x45, 0x1b,0x2a,0x3f,0xe7,0x21,0x0d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2b02e3cf-2e0b-4ec3-be45-1b2a3fe7210d")
IXAudio2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE RegisterForCallbacks(
        IXAudio2EngineCallback *pCallback) = 0;

    virtual void STDMETHODCALLTYPE UnregisterForCallbacks(
        IXAudio2EngineCallback *pCallback) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSourceVoice(
        IXAudio2SourceVoice **ppSourceVoice,
        const WAVEFORMATEX *pSourceFormat,
        UINT32 Flags = 0,
        float MaxFrequencyRatio = XAUDIO2_DEFAULT_FREQ_RATIO,
        IXAudio2VoiceCallback *pCallback = 0,
        const XAUDIO2_VOICE_SENDS *pSendList = 0,
        const XAUDIO2_EFFECT_CHAIN *pEffectChain = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSubmixVoice(
        IXAudio2SubmixVoice **ppSubmixVoice,
        UINT32 InputChannels,
        UINT32 InputSampleRate,
        UINT32 Flags = 0,
        UINT32 ProcessingStage = 0,
        const XAUDIO2_VOICE_SENDS *pSendList = 0,
        const XAUDIO2_EFFECT_CHAIN *pEffectChain = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateMasteringVoice(
        IXAudio2MasteringVoice **ppMasteringVoice,
        UINT32 InputChannels = XAUDIO2_DEFAULT_CHANNELS,
        UINT32 InputSampleRate = XAUDIO2_DEFAULT_SAMPLERATE,
        UINT32 Flags = 0,
        LPCWSTR DeviceId = 0,
        const XAUDIO2_EFFECT_CHAIN *pEffectChain = 0,
        AUDIO_STREAM_CATEGORY category = AudioCategory_GameEffects) = 0;

    virtual HRESULT STDMETHODCALLTYPE StartEngine(
        ) = 0;

    virtual void STDMETHODCALLTYPE StopEngine(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CommitChanges(
        UINT32 OperationSet) = 0;

    virtual void STDMETHODCALLTYPE GetPerformanceData(
        XAUDIO2_PERFORMANCE_DATA *pPerfData) = 0;

    virtual void STDMETHODCALLTYPE SetDebugConfiguration(
        const XAUDIO2_DEBUG_CONFIGURATION *pDebugConfiguration,
        void *pReserved = 0) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXAudio2, 0x2b02e3cf, 0x2e0b, 0x4ec3, 0xbe,0x45, 0x1b,0x2a,0x3f,0xe7,0x21,0x0d)
#endif
#else
typedef struct IXAudio2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXAudio2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXAudio2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXAudio2 *This);

    /*** IXAudio2 methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterForCallbacks)(
        IXAudio2 *This,
        IXAudio2EngineCallback *pCallback);

    void (STDMETHODCALLTYPE *UnregisterForCallbacks)(
        IXAudio2 *This,
        IXAudio2EngineCallback *pCallback);

    HRESULT (STDMETHODCALLTYPE *CreateSourceVoice)(
        IXAudio2 *This,
        IXAudio2SourceVoice **ppSourceVoice,
        const WAVEFORMATEX *pSourceFormat,
        UINT32 Flags,
        float MaxFrequencyRatio,
        IXAudio2VoiceCallback *pCallback,
        const XAUDIO2_VOICE_SENDS *pSendList,
        const XAUDIO2_EFFECT_CHAIN *pEffectChain);

    HRESULT (STDMETHODCALLTYPE *CreateSubmixVoice)(
        IXAudio2 *This,
        IXAudio2SubmixVoice **ppSubmixVoice,
        UINT32 InputChannels,
        UINT32 InputSampleRate,
        UINT32 Flags,
        UINT32 ProcessingStage,
        const XAUDIO2_VOICE_SENDS *pSendList,
        const XAUDIO2_EFFECT_CHAIN *pEffectChain);

    HRESULT (STDMETHODCALLTYPE *CreateMasteringVoice)(
        IXAudio2 *This,
        IXAudio2MasteringVoice **ppMasteringVoice,
        UINT32 InputChannels,
        UINT32 InputSampleRate,
        UINT32 Flags,
        LPCWSTR DeviceId,
        const XAUDIO2_EFFECT_CHAIN *pEffectChain,
        AUDIO_STREAM_CATEGORY category);

    HRESULT (STDMETHODCALLTYPE *StartEngine)(
        IXAudio2 *This);

    void (STDMETHODCALLTYPE *StopEngine)(
        IXAudio2 *This);

    HRESULT (STDMETHODCALLTYPE *CommitChanges)(
        IXAudio2 *This,
        UINT32 OperationSet);

    void (STDMETHODCALLTYPE *GetPerformanceData)(
        IXAudio2 *This,
        XAUDIO2_PERFORMANCE_DATA *pPerfData);

    void (STDMETHODCALLTYPE *SetDebugConfiguration)(
        IXAudio2 *This,
        const XAUDIO2_DEBUG_CONFIGURATION *pDebugConfiguration,
        void *pReserved);

    END_INTERFACE
} IXAudio2Vtbl;

interface IXAudio2 {
    CONST_VTBL IXAudio2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXAudio2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXAudio2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXAudio2_Release(This) (This)->lpVtbl->Release(This)
/*** IXAudio2 methods ***/
#define IXAudio2_RegisterForCallbacks(This,pCallback) (This)->lpVtbl->RegisterForCallbacks(This,pCallback)
#define IXAudio2_UnregisterForCallbacks(This,pCallback) (This)->lpVtbl->UnregisterForCallbacks(This,pCallback)
#define IXAudio2_CreateSourceVoice(This,ppSourceVoice,pSourceFormat,Flags,MaxFrequencyRatio,pCallback,pSendList,pEffectChain) (This)->lpVtbl->CreateSourceVoice(This,ppSourceVoice,pSourceFormat,Flags,MaxFrequencyRatio,pCallback,pSendList,pEffectChain)
#define IXAudio2_CreateSubmixVoice(This,ppSubmixVoice,InputChannels,InputSampleRate,Flags,ProcessingStage,pSendList,pEffectChain) (This)->lpVtbl->CreateSubmixVoice(This,ppSubmixVoice,InputChannels,InputSampleRate,Flags,ProcessingStage,pSendList,pEffectChain)
#define IXAudio2_CreateMasteringVoice(This,ppMasteringVoice,InputChannels,InputSampleRate,Flags,DeviceId,pEffectChain,category) (This)->lpVtbl->CreateMasteringVoice(This,ppMasteringVoice,InputChannels,InputSampleRate,Flags,DeviceId,pEffectChain,category)
#define IXAudio2_StartEngine(This) (This)->lpVtbl->StartEngine(This)
#define IXAudio2_StopEngine(This) (This)->lpVtbl->StopEngine(This)
#define IXAudio2_CommitChanges(This,OperationSet) (This)->lpVtbl->CommitChanges(This,OperationSet)
#define IXAudio2_GetPerformanceData(This,pPerfData) (This)->lpVtbl->GetPerformanceData(This,pPerfData)
#define IXAudio2_SetDebugConfiguration(This,pDebugConfiguration,pReserved) (This)->lpVtbl->SetDebugConfiguration(This,pDebugConfiguration,pReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IXAudio2_QueryInterface(IXAudio2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXAudio2_AddRef(IXAudio2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXAudio2_Release(IXAudio2* This) {
    return This->lpVtbl->Release(This);
}
/*** IXAudio2 methods ***/
static inline HRESULT IXAudio2_RegisterForCallbacks(IXAudio2* This,IXAudio2EngineCallback *pCallback) {
    return This->lpVtbl->RegisterForCallbacks(This,pCallback);
}
static inline void IXAudio2_UnregisterForCallbacks(IXAudio2* This,IXAudio2EngineCallback *pCallback) {
    This->lpVtbl->UnregisterForCallbacks(This,pCallback);
}
static inline HRESULT IXAudio2_CreateSourceVoice(IXAudio2* This,IXAudio2SourceVoice **ppSourceVoice,const WAVEFORMATEX *pSourceFormat,UINT32 Flags,float MaxFrequencyRatio,IXAudio2VoiceCallback *pCallback,const XAUDIO2_VOICE_SENDS *pSendList,const XAUDIO2_EFFECT_CHAIN *pEffectChain) {
    return This->lpVtbl->CreateSourceVoice(This,ppSourceVoice,pSourceFormat,Flags,MaxFrequencyRatio,pCallback,pSendList,pEffectChain);
}
static inline HRESULT IXAudio2_CreateSubmixVoice(IXAudio2* This,IXAudio2SubmixVoice **ppSubmixVoice,UINT32 InputChannels,UINT32 InputSampleRate,UINT32 Flags,UINT32 ProcessingStage,const XAUDIO2_VOICE_SENDS *pSendList,const XAUDIO2_EFFECT_CHAIN *pEffectChain) {
    return This->lpVtbl->CreateSubmixVoice(This,ppSubmixVoice,InputChannels,InputSampleRate,Flags,ProcessingStage,pSendList,pEffectChain);
}
static inline HRESULT IXAudio2_CreateMasteringVoice(IXAudio2* This,IXAudio2MasteringVoice **ppMasteringVoice,UINT32 InputChannels,UINT32 InputSampleRate,UINT32 Flags,LPCWSTR DeviceId,const XAUDIO2_EFFECT_CHAIN *pEffectChain,AUDIO_STREAM_CATEGORY category) {
    return This->lpVtbl->CreateMasteringVoice(This,ppMasteringVoice,InputChannels,InputSampleRate,Flags,DeviceId,pEffectChain,category);
}
static inline HRESULT IXAudio2_StartEngine(IXAudio2* This) {
    return This->lpVtbl->StartEngine(This);
}
static inline void IXAudio2_StopEngine(IXAudio2* This) {
    This->lpVtbl->StopEngine(This);
}
static inline HRESULT IXAudio2_CommitChanges(IXAudio2* This,UINT32 OperationSet) {
    return This->lpVtbl->CommitChanges(This,OperationSet);
}
static inline void IXAudio2_GetPerformanceData(IXAudio2* This,XAUDIO2_PERFORMANCE_DATA *pPerfData) {
    This->lpVtbl->GetPerformanceData(This,pPerfData);
}
static inline void IXAudio2_SetDebugConfiguration(IXAudio2* This,const XAUDIO2_DEBUG_CONFIGURATION *pDebugConfiguration,void *pReserved) {
    This->lpVtbl->SetDebugConfiguration(This,pDebugConfiguration,pReserved);
}
#endif
#endif

#endif


#endif  /* __IXAudio2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXAudio2Extension interface
 */
#ifndef __IXAudio2Extension_INTERFACE_DEFINED__
#define __IXAudio2Extension_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXAudio2Extension, 0x84ac29bb, 0xd619, 0x44d2, 0xb1,0x97, 0xe4,0xac,0xf7,0xdf,0x3e,0xd6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("84ac29bb-d619-44d2-b197-e4acf7df3ed6")
IXAudio2Extension : public IUnknown
{
    virtual void STDMETHODCALLTYPE GetProcessingQuantum(
        UINT32 *quantum_numerator,
        UINT32 *quantum_denominator) = 0;

    virtual void STDMETHODCALLTYPE GetProcessor(
        XAUDIO2_PROCESSOR *processor) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXAudio2Extension, 0x84ac29bb, 0xd619, 0x44d2, 0xb1,0x97, 0xe4,0xac,0xf7,0xdf,0x3e,0xd6)
#endif
#else
typedef struct IXAudio2ExtensionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXAudio2Extension *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXAudio2Extension *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXAudio2Extension *This);

    /*** IXAudio2Extension methods ***/
    void (STDMETHODCALLTYPE *GetProcessingQuantum)(
        IXAudio2Extension *This,
        UINT32 *quantum_numerator,
        UINT32 *quantum_denominator);

    void (STDMETHODCALLTYPE *GetProcessor)(
        IXAudio2Extension *This,
        XAUDIO2_PROCESSOR *processor);

    END_INTERFACE
} IXAudio2ExtensionVtbl;

interface IXAudio2Extension {
    CONST_VTBL IXAudio2ExtensionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXAudio2Extension_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXAudio2Extension_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXAudio2Extension_Release(This) (This)->lpVtbl->Release(This)
/*** IXAudio2Extension methods ***/
#define IXAudio2Extension_GetProcessingQuantum(This,quantum_numerator,quantum_denominator) (This)->lpVtbl->GetProcessingQuantum(This,quantum_numerator,quantum_denominator)
#define IXAudio2Extension_GetProcessor(This,processor) (This)->lpVtbl->GetProcessor(This,processor)
#else
/*** IUnknown methods ***/
static inline HRESULT IXAudio2Extension_QueryInterface(IXAudio2Extension* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXAudio2Extension_AddRef(IXAudio2Extension* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXAudio2Extension_Release(IXAudio2Extension* This) {
    return This->lpVtbl->Release(This);
}
/*** IXAudio2Extension methods ***/
static inline void IXAudio2Extension_GetProcessingQuantum(IXAudio2Extension* This,UINT32 *quantum_numerator,UINT32 *quantum_denominator) {
    This->lpVtbl->GetProcessingQuantum(This,quantum_numerator,quantum_denominator);
}
static inline void IXAudio2Extension_GetProcessor(IXAudio2Extension* This,XAUDIO2_PROCESSOR *processor) {
    This->lpVtbl->GetProcessor(This,processor);
}
#endif
#endif

#endif


#endif  /* __IXAudio2Extension_INTERFACE_DEFINED__ */

#define XAUDIO2_DEBUG_ENGINE (0x1)

#define XAUDIO2_VOICE_NOPITCH (0x2)

#define XAUDIO2_VOICE_NOSRC (0x4)

#define XAUDIO2_VOICE_USEFILTER (0x8)

#define XAUDIO2_VOICE_MUSIC (0x10)

#define XAUDIO2_PLAY_TAILS (0x20)

#define XAUDIO2_END_OF_STREAM (0x40)

#define XAUDIO2_SEND_USEFILTER (0x80)

#define XAUDIO2_VOICE_NOSAMPLESPLAYED (0x100)

#define XAUDIO2_DEFAULT_FILTER_TYPE (LowPassFilter)

#define XAUDIO2_DEFAULT_FILTER_FREQUENCY (XAUDIO2_MAX_FILTER_FREQUENCY)

#define XAUDIO2_DEFAULT_FILTER_ONEOVERQ (1.00000000000000)

#define XAUDIO2_QUANTUM_NUMERATOR (1)

#define XAUDIO2_QUANTUM_DENOMINATOR (100)

#define XAUDIO2_QUANTUM_MS ((1000.00000000000 * XAUDIO2_QUANTUM_NUMERATOR) / XAUDIO2_QUANTUM_DENOMINATOR)

#define XAUDIO2_E_INVALID_CALL ((HRESULT)0x88960001)
#define XAUDIO2_E_XMA_DECODER_ERROR ((HRESULT)0x88960002)
#define XAUDIO2_E_XAPO_CREATION_FAILED ((HRESULT)0x88960003)
#define XAUDIO2_E_DEVICE_INVALIDATED ((HRESULT)0x88960004)
#ifdef XAUDIO2_HELPER_FUNCTIONS
#define _USE_MATH_DEFINES
#include <math.h>
inline static float XAudio2DecibelsToAmplitudeRatio(float decibels) { return powf(10.0f, decibels/20.0f); }
inline static float XAudio2AmplitudeRatioToDecibels(float volume) { if (volume == 0) { return -3.402823466e+38f; } return 20.0f * log10f(volume); }
inline static float XAudio2SemitonesToFrequencyRatio(float semitones) { return powf(2.0f, semitones/12.0f); }
inline static float XAudio2FrequencyRatioToSemitones(float freqratio) { return 39.86313713864835f * log10f(freqratio); }
inline static float XAudio2CutoffFrequencyToRadians(float cutofffreq, UINT32 samplerate) { if ((UINT32)(cutofffreq * 6.0f) >= samplerate) { return XAUDIO2_MAX_FILTER_FREQUENCY; } return 2.0f * sinf((float)M_PI * cutofffreq / samplerate); }
inline static float XAudio2RadiansToCutoffFrequency(float radians, float samplerate) { return samplerate * asinf(radians/2.0f) / (float)M_PI; }
#endif
#pragma pack(pop)
HRESULT WINAPI XAudio2Create(IXAudio2** pxaudio2, UINT32 flags, XAUDIO2_PROCESSOR processor);
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __xaudio2_h__ */
