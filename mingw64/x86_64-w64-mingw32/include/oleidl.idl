cpp_quote("/**")
cpp_quote(" * This file is part of the mingw-w64 runtime package.")
cpp_quote(" * No warranty is given; refer to the file DISCLAIMER within this package.")
cpp_quote(" */")
cpp_quote("")
#ifndef DO_NO_IMPORTS
import "objidl.idl";

cpp_quote("")
#endif

cpp_quote("#include <winapifamily.h>")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

interface IOleInPlaceActiveObject;
interface IEnumOLEVERB;

cpp_quote("")
[local, object, uuid (00000111-0000-0000-C000-000000000046)]
interface IOleAdviseHolder : IUnknown {
  typedef [unique] IOleAdviseHolder *LPOLEADVISEHOLDER;
cpp_quote("")
  HRESULT Advise ([in, unique] IAdviseSink *pAdvise,[out] DWORD *pdwConnection);
  HRESULT Unadvise ([in] DWORD dwConnection);
  HRESULT EnumAdvise ([out] IEnumSTATDATA **ppenumAdvise);
  HRESULT SendOnRename ([in, unique] IMoniker *pmk);
  HRESULT SendOnSave (void);
  HRESULT SendOnClose (void);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
[object, uuid (0000011e-0000-0000-C000-000000000046), pointer_default (unique)]
interface IOleCache : IUnknown {
  typedef [unique] IOleCache *LPOLECACHE;
cpp_quote("")
  HRESULT Cache ([in, unique] FORMATETC *pformatetc,[in] DWORD advf,[out] DWORD *pdwConnection);
  HRESULT Uncache ([in] DWORD dwConnection);
  HRESULT EnumCache ([out] IEnumSTATDATA **ppenumSTATDATA);
  HRESULT InitCache ([in, unique] IDataObject *pDataObject);
  HRESULT SetData ([in, unique] FORMATETC *pformatetc,[in, unique] STGMEDIUM *pmedium,[in] BOOL fRelease);
}

cpp_quote("")
[object, uuid (00000128-0000-0000-C000-000000000046), pointer_default (unique)]
interface IOleCache2 : IOleCache {
  typedef [unique] IOleCache2 *LPOLECACHE2;
cpp_quote("")
  const DWORD UPDFCACHE_NODATACACHE = 0x00000001;
  const DWORD UPDFCACHE_ONSAVECACHE = 0x00000002;
  const DWORD UPDFCACHE_ONSTOPCACHE = 0x00000004;
  const DWORD UPDFCACHE_NORMALCACHE = 0x00000008;
  const DWORD UPDFCACHE_IFBLANK = 0x00000010;
  const DWORD UPDFCACHE_ONLYIFBLANK = 0x80000000;
  const DWORD UPDFCACHE_IFBLANKORONSAVECACHE = (UPDFCACHE_IFBLANK | UPDFCACHE_ONSAVECACHE);
  const DWORD UPDFCACHE_ALL = ((DWORD) (~ (UPDFCACHE_ONLYIFBLANK)));
  const DWORD UPDFCACHE_ALLBUTNODATACACHE = (UPDFCACHE_ALL &((DWORD) (~UPDFCACHE_NODATACACHE)));
cpp_quote("")
  typedef [v1_enum] enum tagDISCARDCACHE {
    DISCARDCACHE_SAVEIFDIRTY = 0,
    DISCARDCACHE_NOSAVE = 1
  } DISCARDCACHE;
cpp_quote("")
  [local] HRESULT UpdateCache ([in] LPDATAOBJECT pDataObject,[in] DWORD grfUpdf,[in] LPVOID pReserved);
  [call_as (UpdateCache)] HRESULT RemoteUpdateCache ([in] LPDATAOBJECT pDataObject,[in] DWORD grfUpdf,[in] LONG_PTR pReserved);
  HRESULT DiscardCache ([in] DWORD dwDiscardOptions);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
[object, uuid (00000129-0000-0000-C000-000000000046)]
interface IOleCacheControl : IUnknown {
  typedef [unique] IOleCacheControl *LPOLECACHECONTROL;
cpp_quote("")
  HRESULT OnRun (LPDATAOBJECT pDataObject);
  HRESULT OnStop (void);
}

cpp_quote("")
[object, uuid (0000011a-0000-0000-C000-000000000046), pointer_default (unique)]
interface IParseDisplayName : IUnknown {
  typedef [unique] IParseDisplayName *LPPARSEDISPLAYNAME;
cpp_quote("")
  HRESULT ParseDisplayName ([in, unique] IBindCtx *pbc,[in] LPOLESTR pszDisplayName,[out] ULONG *pchEaten,[out] IMoniker **ppmkOut);
}

cpp_quote("")
[object, uuid (0000011b-0000-0000-C000-000000000046), pointer_default (unique)]
interface IOleContainer : IParseDisplayName {
  typedef [unique] IOleContainer *LPOLECONTAINER;
cpp_quote("")
  HRESULT EnumObjects ([in] DWORD grfFlags,[out] IEnumUnknown **ppenum);
  HRESULT LockContainer ([in] BOOL fLock);
}

cpp_quote("")
[object, uuid (00000118-0000-0000-C000-000000000046), pointer_default (unique)]
interface IOleClientSite : IUnknown {
  typedef [unique] IOleClientSite *LPOLECLIENTSITE;
cpp_quote("")
  HRESULT SaveObject (void);
  HRESULT GetMoniker ([in] DWORD dwAssign,[in] DWORD dwWhichMoniker,[out] IMoniker **ppmk);
  HRESULT GetContainer ([out] IOleContainer **ppContainer);
  HRESULT ShowObject (void);
  HRESULT OnShowWindow ([in] BOOL fShow);
  HRESULT RequestNewObjectLayout (void);
}

cpp_quote("")
[object, uuid (00000112-0000-0000-C000-000000000046), pointer_default (unique)]
interface IOleObject : IUnknown {
  typedef [unique] IOleObject *LPOLEOBJECT;
cpp_quote("")
  typedef enum tagOLEGETMONIKER {
    OLEGETMONIKER_ONLYIFTHERE = 1,
    OLEGETMONIKER_FORCEASSIGN = 2,
    OLEGETMONIKER_UNASSIGN = 3,
    OLEGETMONIKER_TEMPFORUSER = 4
  } OLEGETMONIKER;
cpp_quote("")
  typedef enum tagOLEWHICHMK {
    OLEWHICHMK_CONTAINER = 1,
    OLEWHICHMK_OBJREL = 2,
    OLEWHICHMK_OBJFULL = 3
  } OLEWHICHMK;
cpp_quote("")
  typedef enum tagUSERCLASSTYPE {
    USERCLASSTYPE_FULL = 1,
    USERCLASSTYPE_SHORT = 2,
    USERCLASSTYPE_APPNAME = 3,
  } USERCLASSTYPE;
cpp_quote("")
  typedef enum tagOLEMISC {
    OLEMISC_RECOMPOSEONRESIZE = 0x00000001,
    OLEMISC_ONLYICONIC = 0x00000002,
    OLEMISC_INSERTNOTREPLACE = 0x00000004,
    OLEMISC_STATIC = 0x00000008,
    OLEMISC_CANTLINKINSIDE = 0x00000010,
    OLEMISC_CANLINKBYOLE1 = 0x00000020,
    OLEMISC_ISLINKOBJECT = 0x00000040,
    OLEMISC_INSIDEOUT = 0x00000080,
    OLEMISC_ACTIVATEWHENVISIBLE = 0x00000100,
    OLEMISC_RENDERINGISDEVICEINDEPENDENT= 0x00000200,
    OLEMISC_INVISIBLEATRUNTIME = 0x00000400,
    OLEMISC_ALWAYSRUN = 0x00000800,
    OLEMISC_ACTSLIKEBUTTON = 0x00001000,
    OLEMISC_ACTSLIKELABEL = 0x00002000,
    OLEMISC_NOUIACTIVATE = 0x00004000,
    OLEMISC_ALIGNABLE = 0x00008000,
    OLEMISC_SIMPLEFRAME = 0x00010000,
    OLEMISC_SETCLIENTSITEFIRST = 0x00020000,
    OLEMISC_IMEMODE = 0x00040000,
    OLEMISC_IGNOREACTIVATEWHENVISIBLE = 0x00080000,
    OLEMISC_WANTSTOMENUMERGE = 0x00100000,
    OLEMISC_SUPPORTSMULTILEVELUNDO = 0x00200000
  } OLEMISC;
  typedef enum tagOLECLOSE {
    OLECLOSE_SAVEIFDIRTY = 0,
    OLECLOSE_NOSAVE = 1,
    OLECLOSE_PROMPTSAVE = 2
  } OLECLOSE;
cpp_quote("")
  HRESULT SetClientSite ([in, unique] IOleClientSite *pClientSite);
  HRESULT GetClientSite ([out] IOleClientSite **ppClientSite);
  HRESULT SetHostNames ([in] LPCOLESTR szContainerApp,[in, unique] LPCOLESTR szContainerObj);
  HRESULT Close ([in] DWORD dwSaveOption);
  HRESULT SetMoniker ([in] DWORD dwWhichMoniker,[in, unique] IMoniker *pmk);
  HRESULT GetMoniker ([in] DWORD dwAssign,[in] DWORD dwWhichMoniker,[out] IMoniker **ppmk);
  HRESULT InitFromData ([in, unique] IDataObject *pDataObject,[in] BOOL fCreation,[in] DWORD dwReserved);
  HRESULT GetClipboardData ([in] DWORD dwReserved,[out] IDataObject **ppDataObject);
  HRESULT DoVerb ([in] LONG iVerb,[in, unique] LPMSG lpmsg,[in, unique] IOleClientSite *pActiveSite,[in] LONG lindex,[in] HWND hwndParent,[in, unique] LPCRECT lprcPosRect);
  HRESULT EnumVerbs ([out] IEnumOLEVERB **ppEnumOleVerb);
  HRESULT Update (void);
  HRESULT IsUpToDate (void);
  HRESULT GetUserClassID ([out] CLSID *pClsid);
  HRESULT GetUserType ([in] DWORD dwFormOfType,[out] LPOLESTR *pszUserType);
  HRESULT SetExtent ([in] DWORD dwDrawAspect,[in] SIZEL *psizel);
  HRESULT GetExtent ([in] DWORD dwDrawAspect,[out] SIZEL *psizel);
  HRESULT Advise ([in, unique] IAdviseSink *pAdvSink,[out] DWORD *pdwConnection);
  HRESULT Unadvise ([in] DWORD dwConnection);
  HRESULT EnumAdvise ([out] IEnumSTATDATA **ppenumAdvise);
  HRESULT GetMiscStatus ([in] DWORD dwAspect,[out] DWORD *pdwStatus);
  HRESULT SetColorScheme ([in] LOGPALETTE *pLogpal);
}

cpp_quote("")
[uuid (B0916C84-7416-101a-BCEA-08002b2b79ef)]
interface IOLETypes {
  typedef enum tagOLERENDER {
    OLERENDER_NONE = 0,
    OLERENDER_DRAW = 1,
    OLERENDER_FORMAT = 2,
    OLERENDER_ASIS = 3
  } OLERENDER;
cpp_quote("")
  typedef OLERENDER *LPOLERENDER;
cpp_quote("")
  typedef struct tagOBJECTDESCRIPTOR {
    ULONG cbSize;
    CLSID clsid;
    DWORD dwDrawAspect;
    SIZEL sizel;
    POINTL pointl;
    DWORD dwStatus;
    DWORD dwFullUserTypeName;
    DWORD dwSrcOfCopy;
  } OBJECTDESCRIPTOR, *POBJECTDESCRIPTOR, *LPOBJECTDESCRIPTOR, LINKSRCDESCRIPTOR, *PLINKSRCDESCRIPTOR, *LPLINKSRCDESCRIPTOR;
}

cpp_quote("")
[object, uuid (00000114-0000-0000-C000-000000000046), pointer_default (unique)]
interface IOleWindow : IUnknown {
  typedef [unique] IOleWindow *LPOLEWINDOW;
cpp_quote("")
  [input_sync] HRESULT GetWindow ([out] HWND *phwnd);
  HRESULT ContextSensitiveHelp ([in] BOOL fEnterMode);
}

cpp_quote("")
[object, uuid (0000011d-0000-0000-C000-000000000046)]
interface IOleLink : IUnknown {
  typedef [unique] IOleLink *LPOLELINK;
cpp_quote("")
  typedef enum tagOLEUPDATE {
    OLEUPDATE_ALWAYS=1,
    OLEUPDATE_ONCALL=3
  } OLEUPDATE;
cpp_quote("")
  typedef OLEUPDATE *LPOLEUPDATE;
  typedef OLEUPDATE *POLEUPDATE;
cpp_quote("")
  typedef enum tagOLELINKBIND {
    OLELINKBIND_EVENIFCLASSDIFF = 1,
  } OLELINKBIND;
cpp_quote("")
  HRESULT SetUpdateOptions ([in] DWORD dwUpdateOpt);
  HRESULT GetUpdateOptions ([out] DWORD *pdwUpdateOpt);
  HRESULT SetSourceMoniker ([in, unique] IMoniker *pmk,[in] REFCLSID rclsid);
  HRESULT GetSourceMoniker ([out] IMoniker **ppmk);
  HRESULT SetSourceDisplayName ([in]LPCOLESTR pszStatusText);
  HRESULT GetSourceDisplayName ([out] LPOLESTR *ppszDisplayName);
  HRESULT BindToSource ([in] DWORD bindflags,[in, unique] IBindCtx *pbc);
  HRESULT BindIfRunning (void);
  HRESULT GetBoundSource ([out] IUnknown **ppunk);
  HRESULT UnbindSource (void);
  HRESULT Update ([in, unique] IBindCtx *pbc);
}

cpp_quote("")
[object, uuid (0000011c-0000-0000-C000-000000000046), pointer_default (unique)]
interface IOleItemContainer : IOleContainer {
  typedef [unique] IOleItemContainer *LPOLEITEMCONTAINER;
cpp_quote("")
  typedef enum tagBINDSPEED {
    BINDSPEED_INDEFINITE = 1,
    BINDSPEED_MODERATE = 2,
    BINDSPEED_IMMEDIATE = 3
  } BINDSPEED;
cpp_quote("")
  typedef [v1_enum] enum tagOLECONTF {
    OLECONTF_EMBEDDINGS = 1,
    OLECONTF_LINKS = 2,
    OLECONTF_OTHERS = 4,
    OLECONTF_ONLYUSER = 8,
    OLECONTF_ONLYIFRUNNING = 16
  } OLECONTF;
cpp_quote("")
  HRESULT GetObject ([in] LPOLESTR pszItem,[in] DWORD dwSpeedNeeded,[in, unique] IBindCtx *pbc,[in] REFIID riid,[out, iid_is (riid)] void **ppvObject);
  HRESULT GetObjectStorage ([in] LPOLESTR pszItem,[in, unique] IBindCtx *pbc,[in] REFIID riid,[out, iid_is (riid)] void **ppvStorage);
  HRESULT IsRunning ([in] LPOLESTR pszItem);
}

cpp_quote("")
[object, uuid (00000115-0000-0000-C000-000000000046), pointer_default (unique)]
interface IOleInPlaceUIWindow : IOleWindow {
  typedef [unique] IOleInPlaceUIWindow *LPOLEINPLACEUIWINDOW;
  typedef RECT BORDERWIDTHS;
  typedef LPRECT LPBORDERWIDTHS;
  typedef LPCRECT LPCBORDERWIDTHS;
cpp_quote("")
  [input_sync] HRESULT GetBorder ([out] LPRECT lprectBorder);
  [input_sync] HRESULT RequestBorderSpace ([in, unique] LPCBORDERWIDTHS pborderwidths);
  [input_sync] HRESULT SetBorderSpace ([in, unique] LPCBORDERWIDTHS pborderwidths);
  HRESULT SetActiveObject ([in, unique] IOleInPlaceActiveObject *pActiveObject,[in, string, unique] LPCOLESTR pszObjName);
}

cpp_quote("")
[object, uuid (00000117-0000-0000-C000-000000000046)]
interface IOleInPlaceActiveObject : IOleWindow {
  typedef [unique] IOleInPlaceActiveObject *LPOLEINPLACEACTIVEOBJECT;
cpp_quote("")
  [local] HRESULT TranslateAccelerator ([in] LPMSG lpmsg);
  [call_as (TranslateAccelerator)] HRESULT RemoteTranslateAccelerator ();
  [input_sync] HRESULT OnFrameWindowActivate ([in] BOOL fActivate);
  [input_sync] HRESULT OnDocWindowActivate ([in] BOOL fActivate);
  [local] HRESULT ResizeBorder ([in] LPCRECT prcBorder,[in, unique] IOleInPlaceUIWindow *pUIWindow,[in] BOOL fFrameWindow);
  [call_as (ResizeBorder), input_sync] HRESULT RemoteResizeBorder ([in] LPCRECT prcBorder,[in] REFIID riid,[in, unique, iid_is (riid)] IOleInPlaceUIWindow *pUIWindow,[in] BOOL fFrameWindow);
  HRESULT EnableModeless ([in] BOOL fEnable);
}

cpp_quote("")
[object, uuid (00000116-0000-0000-C000-000000000046), pointer_default (unique)]
interface IOleInPlaceFrame : IOleInPlaceUIWindow {
  typedef [unique] IOleInPlaceFrame *LPOLEINPLACEFRAME;
cpp_quote("")
  typedef struct tagOIFI {
    UINT cb;
    BOOL fMDIApp;
    HWND hwndFrame;
    HACCEL haccel;
    UINT cAccelEntries;
  } OLEINPLACEFRAMEINFO,*LPOLEINPLACEFRAMEINFO;
cpp_quote("")
  typedef struct tagOleMenuGroupWidths {
    LONG width[6];
  } OLEMENUGROUPWIDTHS,*LPOLEMENUGROUPWIDTHS;
cpp_quote("")
  typedef HGLOBAL HOLEMENU;
cpp_quote("")
  HRESULT InsertMenus ([in] HMENU hmenuShared,[in, out] LPOLEMENUGROUPWIDTHS lpMenuWidths);
  [input_sync] HRESULT SetMenu ([in] HMENU hmenuShared,[in] HOLEMENU holemenu,[in] HWND hwndActiveObject);
  HRESULT RemoveMenus ([in] HMENU hmenuShared);
  [input_sync] HRESULT SetStatusText ([in, unique] LPCOLESTR pszStatusText);
  HRESULT EnableModeless ([in] BOOL fEnable);
  HRESULT TranslateAccelerator ([in] LPMSG lpmsg,[in] WORD wID);
}

cpp_quote("")
[object, uuid (00000113-0000-0000-C000-000000000046), pointer_default (unique)]
interface IOleInPlaceObject : IOleWindow {
  typedef [unique] IOleInPlaceObject *LPOLEINPLACEOBJECT;
cpp_quote("")
  HRESULT InPlaceDeactivate (void);
  HRESULT UIDeactivate (void);
  [input_sync] HRESULT SetObjectRects ([in] LPCRECT lprcPosRect,[in] LPCRECT lprcClipRect);
  HRESULT ReactivateAndUndo (void);
}

cpp_quote("")
[object, uuid (00000119-0000-0000-C000-000000000046), pointer_default (unique)]
interface IOleInPlaceSite : IOleWindow {
  typedef [unique] IOleInPlaceSite *LPOLEINPLACESITE;
cpp_quote("")
  HRESULT CanInPlaceActivate (void);
  HRESULT OnInPlaceActivate (void);
  HRESULT OnUIActivate (void);
  HRESULT GetWindowContext ([out] IOleInPlaceFrame **ppFrame,[out] IOleInPlaceUIWindow **ppDoc,[out] LPRECT lprcPosRect,[out] LPRECT lprcClipRect,[in, out] LPOLEINPLACEFRAMEINFO lpFrameInfo);
  HRESULT Scroll ([in] SIZE scrollExtant);
  HRESULT OnUIDeactivate ([in] BOOL fUndoable);
  HRESULT OnInPlaceDeactivate (void);
  HRESULT DiscardUndoState (void);
  HRESULT DeactivateAndUndo (void);
  HRESULT OnPosRectChange ([in] LPCRECT lprcPosRect);
}

cpp_quote("")
[object, uuid (0000012a-0000-0000-C000-000000000046)]
interface IContinue : IUnknown {
  HRESULT FContinue ();
}

cpp_quote("")
[object, uuid (0000010d-0000-0000-C000-000000000046)]
interface IViewObject : IUnknown {
  typedef [unique] IViewObject *LPVIEWOBJECT;
cpp_quote("")
  [local] HRESULT Draw ([in] DWORD dwDrawAspect,[in] LONG lindex,[in, unique] void *pvAspect,[in, unique] DVTARGETDEVICE *ptd,[in] HDC hdcTargetDev,[in] HDC hdcDraw,[in] LPCRECTL lprcBounds,[in, unique] LPCRECTL lprcWBounds,[in] BOOL (*pfnContinue) (ULONG_PTR dwContinue),[in] ULONG_PTR dwContinue);
  [call_as (Draw)] HRESULT RemoteDraw ([in] DWORD dwDrawAspect,[in] LONG lindex,[in] ULONG_PTR pvAspect,[in, unique] DVTARGETDEVICE *ptd,[in] HDC hdcTargetDev,[in] HDC hdcDraw,[in, unique] LPCRECTL lprcBounds,[in, unique] LPCRECTL lprcWBounds,[in] IContinue *pContinue);
  [local] HRESULT GetColorSet ([in] DWORD dwDrawAspect,[in] LONG lindex,[in, unique] void *pvAspect,[in, unique] DVTARGETDEVICE *ptd,[in] HDC hicTargetDev,[out] LOGPALETTE **ppColorSet);
  [call_as (GetColorSet)] HRESULT RemoteGetColorSet ([in] DWORD dwDrawAspect,[in] LONG lindex,[in] ULONG_PTR pvAspect,[in, unique] DVTARGETDEVICE *ptd,[in] ULONG_PTR hicTargetDev,[out] LOGPALETTE **ppColorSet);
  [local] HRESULT Freeze ([in] DWORD dwDrawAspect,[in] LONG lindex,[in, unique] void *pvAspect,[out] DWORD *pdwFreeze);
  [call_as (Freeze)] HRESULT RemoteFreeze ([in] DWORD dwDrawAspect,[in] LONG lindex,[in] ULONG_PTR pvAspect,[out] DWORD *pdwFreeze);
  HRESULT Unfreeze ([in] DWORD dwFreeze);
  HRESULT SetAdvise ([in] DWORD aspects,[in] DWORD advf,[in, unique] IAdviseSink *pAdvSink);
  [local] HRESULT GetAdvise ([out, unique] DWORD *pAspects,[out, unique] DWORD *pAdvf,[out] IAdviseSink **ppAdvSink);
  [call_as (GetAdvise)] HRESULT RemoteGetAdvise ([out] DWORD *pAspects,[out] DWORD *pAdvf,[out] IAdviseSink **ppAdvSink);
}

cpp_quote("")
[object, uuid (00000127-0000-0000-C000-000000000046)]
interface IViewObject2 : IViewObject {
  typedef [unique] IViewObject2 *LPVIEWOBJECT2;
cpp_quote("")
  HRESULT GetExtent ([in] DWORD dwDrawAspect,[in] LONG lindex,[in, unique] DVTARGETDEVICE *ptd,[out] LPSIZEL lpsizel);
}

cpp_quote("")
[local, object, uuid (00000121-0000-0000-C000-000000000046)]
interface IDropSource : IUnknown {
  typedef [unique] IDropSource *LPDROPSOURCE;
cpp_quote("")
  HRESULT QueryContinueDrag ([in] BOOL fEscapePressed,[in] DWORD grfKeyState);
  HRESULT GiveFeedback ([in] DWORD dwEffect);
}

cpp_quote("")
[object, uuid (00000122-0000-0000-C000-000000000046), pointer_default (unique)]
interface IDropTarget : IUnknown {
  typedef [unique] IDropTarget *LPDROPTARGET;
cpp_quote("")
  const DWORD MK_ALT = 0x0020;
  const DWORD DROPEFFECT_NONE = 0;
  const DWORD DROPEFFECT_COPY = 1;
  const DWORD DROPEFFECT_MOVE = 2;
  const DWORD DROPEFFECT_LINK = 4;
  const DWORD DROPEFFECT_SCROLL = 0x80000000;
  cpp_quote("")
  const DWORD DD_DEFSCROLLINSET = 11;
  cpp_quote("")
  const DWORD DD_DEFSCROLLDELAY = 50;
  cpp_quote("")
  const DWORD DD_DEFSCROLLINTERVAL = 50;
  cpp_quote("")
  const DWORD DD_DEFDRAGDELAY = 200;
  cpp_quote("")
  const DWORD DD_DEFDRAGMINDIST = 2;
cpp_quote("")
  HRESULT DragEnter ([in, unique] IDataObject *pDataObj,[in] DWORD grfKeyState,[in] POINTL pt,[in, out] DWORD *pdwEffect);
  HRESULT DragOver ([in] DWORD grfKeyState,[in] POINTL pt,[in, out] DWORD *pdwEffect);
  HRESULT DragLeave (void);
  HRESULT Drop ([in, unique] IDataObject *pDataObj,[in] DWORD grfKeyState,[in] POINTL pt,[in, out] DWORD *pdwEffect);
}

cpp_quote("")
[local, object, uuid (0000012b-0000-0000-C000-000000000046), pointer_default (unique)]
interface IDropSourceNotify : IUnknown {
  HRESULT DragEnterTarget ([in] HWND hwndTarget);
  HRESULT DragLeaveTarget (void);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
[object, uuid (00000104-0000-0000-C000-000000000046), pointer_default (unique)]
interface IEnumOLEVERB : IUnknown {
  typedef [unique] IEnumOLEVERB *LPENUMOLEVERB;
cpp_quote("")
  typedef struct tagOLEVERB {
    LONG lVerb;
    LPOLESTR lpszVerbName;
    DWORD fuFlags;
    DWORD grfAttribs;
  } OLEVERB,*LPOLEVERB;
cpp_quote("")
  typedef [v1_enum] enum tagOLEVERBATTRIB {
    OLEVERBATTRIB_NEVERDIRTIES = 1,
    OLEVERBATTRIB_ONCONTAINERMENU = 2
  } OLEVERBATTRIB;
cpp_quote("")
  [local] HRESULT Next ([in] ULONG celt,[out, size_is (celt), length_is (*pceltFetched)]LPOLEVERB rgelt,[out] ULONG *pceltFetched);
  [call_as (Next)] HRESULT RemoteNext ([in] ULONG celt,[out, size_is (celt), length_is (*pceltFetched)]LPOLEVERB rgelt,[out] ULONG *pceltFetched);
  HRESULT Skip ([in] ULONG celt);
  HRESULT Reset ();
  HRESULT Clone ([out] IEnumOLEVERB **ppenum);
}
cpp_quote("#endif")
