/*** Autogenerated by WIDL 10.8 from include/windows.devices.radios.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_devices_radios_h__
#define __windows_devices_radios_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CDevices_CRadios_CIRadio_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CRadios_CIRadio_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CRadios_CIRadio __x_ABI_CWindows_CDevices_CRadios_CIRadio;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CRadios_CIRadio ABI::Windows::Devices::Radios::IRadio
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Radios {
                interface IRadio;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics ABI::Windows::Devices::Radios::IRadioStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Radios {
                interface IRadioStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CRadios_CRadio_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CRadios_CRadio_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Radios {
                class Radio;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CRadios_CRadio __x_ABI_CWindows_CDevices_CRadios_CRadio;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CRadios_CRadio_FWD_DEFINED__ */

#ifndef ____FIIterable_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
#define ____FIIterable_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CDevices__CRadios__CRadio __FIIterable_1_Windows__CDevices__CRadios__CRadio;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CDevices__CRadios__CRadio ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Devices::Radios::Radio* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
#define ____FIIterator_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CDevices__CRadios__CRadio __FIIterator_1_Windows__CDevices__CRadios__CRadio;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CDevices__CRadios__CRadio ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Devices::Radios::Radio* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CDevices__CRadios__CRadio __FIVectorView_1_Windows__CDevices__CRadios__CRadio;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CDevices__CRadios__CRadio ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Devices::Radios::Radio* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_RadioAccessStatus_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_RadioAccessStatus_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_RadioAccessStatus __FIAsyncOperationCompletedHandler_1_RadioAccessStatus;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_RadioAccessStatus ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Devices::Radios::RadioAccessStatus >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Radios::Radio* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_RadioAccessStatus_FWD_DEFINED__
#define ____FIAsyncOperation_1_RadioAccessStatus_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_RadioAccessStatus __FIAsyncOperation_1_RadioAccessStatus;
#ifdef __cplusplus
#define __FIAsyncOperation_1_RadioAccessStatus ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Radios::RadioAccessStatus >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Radios::Radio*,IInspectable* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CRadios_CRadioAccessStatus __x_ABI_CWindows_CDevices_CRadios_CRadioAccessStatus;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CRadios_CRadioKind __x_ABI_CWindows_CDevices_CRadios_CRadioKind;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CRadios_CRadioState __x_ABI_CWindows_CDevices_CRadios_CRadioState;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CDevices_CRadios_CIRadio_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CRadios_CIRadio_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CRadios_CIRadio __x_ABI_CWindows_CDevices_CRadios_CIRadio;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CRadios_CIRadio ABI::Windows::Devices::Radios::IRadio
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Radios {
                interface IRadio;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics ABI::Windows::Devices::Radios::IRadioStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Radios {
                interface IRadioStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
#define ____FIIterable_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CDevices__CRadios__CRadio __FIIterable_1_Windows__CDevices__CRadios__CRadio;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CDevices__CRadios__CRadio ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Devices::Radios::Radio* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
#define ____FIIterator_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CDevices__CRadios__CRadio __FIIterator_1_Windows__CDevices__CRadios__CRadio;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CDevices__CRadios__CRadio ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Devices::Radios::Radio* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CDevices__CRadios__CRadio __FIVectorView_1_Windows__CDevices__CRadios__CRadio;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CDevices__CRadios__CRadio ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Radios::Radio* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_RadioAccessStatus_FWD_DEFINED__
#define ____FIAsyncOperation_1_RadioAccessStatus_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_RadioAccessStatus __FIAsyncOperation_1_RadioAccessStatus;
#ifdef __cplusplus
#define __FIAsyncOperation_1_RadioAccessStatus ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Radios::RadioAccessStatus >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >* >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Radios {
                enum RadioAccessStatus {
                    RadioAccessStatus_Unspecified = 0,
                    RadioAccessStatus_Allowed = 1,
                    RadioAccessStatus_DeniedByUser = 2,
                    RadioAccessStatus_DeniedBySystem = 3
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CRadios_CRadioAccessStatus {
    RadioAccessStatus_Unspecified = 0,
    RadioAccessStatus_Allowed = 1,
    RadioAccessStatus_DeniedByUser = 2,
    RadioAccessStatus_DeniedBySystem = 3
};
#ifdef WIDL_using_Windows_Devices_Radios
#define RadioAccessStatus __x_ABI_CWindows_CDevices_CRadios_CRadioAccessStatus
#endif /* WIDL_using_Windows_Devices_Radios */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Radios {
                enum RadioKind {
                    RadioKind_Other = 0,
                    RadioKind_WiFi = 1,
                    RadioKind_MobileBroadband = 2,
                    RadioKind_Bluetooth = 3,
                    RadioKind_FM = 4
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CRadios_CRadioKind {
    RadioKind_Other = 0,
    RadioKind_WiFi = 1,
    RadioKind_MobileBroadband = 2,
    RadioKind_Bluetooth = 3,
    RadioKind_FM = 4
};
#ifdef WIDL_using_Windows_Devices_Radios
#define RadioKind __x_ABI_CWindows_CDevices_CRadios_CRadioKind
#endif /* WIDL_using_Windows_Devices_Radios */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Radios {
                enum RadioState {
                    RadioState_Unknown = 0,
                    RadioState_On = 1,
                    RadioState_Off = 2,
                    RadioState_Disabled = 3
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CRadios_CRadioState {
    RadioState_Unknown = 0,
    RadioState_On = 1,
    RadioState_Off = 2,
    RadioState_Disabled = 3
};
#ifdef WIDL_using_Windows_Devices_Radios
#define RadioState __x_ABI_CWindows_CDevices_CRadios_CRadioState
#endif /* WIDL_using_Windows_Devices_Radios */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
/*****************************************************************************
 * IRadio interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CRadios_CIRadio_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CRadios_CIRadio_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CRadios_CIRadio, 0x252118df, 0xb33e, 0x416a, 0x87,0x5f, 0x1c,0xf3,0x8a,0xe2,0xd8,0x3e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Radios {
                MIDL_INTERFACE("252118df-b33e-416a-875f-1cf38ae2d83e")
                IRadio : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE SetStateAsync(
                        ABI::Windows::Devices::Radios::RadioState value,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Radios::RadioAccessStatus > **retval) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_StateChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Radios::Radio*,IInspectable* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_StateChanged(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_State(
                        ABI::Windows::Devices::Radios::RadioState **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Name(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Kind(
                        ABI::Windows::Devices::Radios::RadioKind *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CRadios_CIRadio, 0x252118df, 0xb33e, 0x416a, 0x87,0x5f, 0x1c,0xf3,0x8a,0xe2,0xd8,0x3e)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CRadios_CIRadioVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadio *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadio *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadio *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadio *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadio *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadio *This,
        TrustLevel *trustLevel);

    /*** IRadio methods ***/
    HRESULT (STDMETHODCALLTYPE *SetStateAsync)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadio *This,
        __x_ABI_CWindows_CDevices_CRadios_CRadioState value,
        __FIAsyncOperation_1_RadioAccessStatus **retval);

    HRESULT (STDMETHODCALLTYPE *add_StateChanged)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadio *This,
        __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_StateChanged)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadio *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *get_State)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadio *This,
        __x_ABI_CWindows_CDevices_CRadios_CRadioState **value);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadio *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Kind)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadio *This,
        __x_ABI_CWindows_CDevices_CRadios_CRadioKind *value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CRadios_CIRadioVtbl;

interface __x_ABI_CWindows_CDevices_CRadios_CIRadio {
    CONST_VTBL __x_ABI_CWindows_CDevices_CRadios_CIRadioVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CRadios_CIRadio_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CRadios_CIRadio_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CRadios_CIRadio_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CRadios_CIRadio_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CRadios_CIRadio_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CRadios_CIRadio_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IRadio methods ***/
#define __x_ABI_CWindows_CDevices_CRadios_CIRadio_SetStateAsync(This,value,retval) (This)->lpVtbl->SetStateAsync(This,value,retval)
#define __x_ABI_CWindows_CDevices_CRadios_CIRadio_add_StateChanged(This,handler,cookie) (This)->lpVtbl->add_StateChanged(This,handler,cookie)
#define __x_ABI_CWindows_CDevices_CRadios_CIRadio_remove_StateChanged(This,cookie) (This)->lpVtbl->remove_StateChanged(This,cookie)
#define __x_ABI_CWindows_CDevices_CRadios_CIRadio_get_State(This,value) (This)->lpVtbl->get_State(This,value)
#define __x_ABI_CWindows_CDevices_CRadios_CIRadio_get_Name(This,value) (This)->lpVtbl->get_Name(This,value)
#define __x_ABI_CWindows_CDevices_CRadios_CIRadio_get_Kind(This,value) (This)->lpVtbl->get_Kind(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadio_QueryInterface(__x_ABI_CWindows_CDevices_CRadios_CIRadio* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CRadios_CIRadio_AddRef(__x_ABI_CWindows_CDevices_CRadios_CIRadio* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CRadios_CIRadio_Release(__x_ABI_CWindows_CDevices_CRadios_CIRadio* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadio_GetIids(__x_ABI_CWindows_CDevices_CRadios_CIRadio* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadio_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CRadios_CIRadio* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadio_GetTrustLevel(__x_ABI_CWindows_CDevices_CRadios_CIRadio* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IRadio methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadio_SetStateAsync(__x_ABI_CWindows_CDevices_CRadios_CIRadio* This,__x_ABI_CWindows_CDevices_CRadios_CRadioState value,__FIAsyncOperation_1_RadioAccessStatus **retval) {
    return This->lpVtbl->SetStateAsync(This,value,retval);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadio_add_StateChanged(__x_ABI_CWindows_CDevices_CRadios_CIRadio* This,__FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_StateChanged(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadio_remove_StateChanged(__x_ABI_CWindows_CDevices_CRadios_CIRadio* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_StateChanged(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadio_get_State(__x_ABI_CWindows_CDevices_CRadios_CIRadio* This,__x_ABI_CWindows_CDevices_CRadios_CRadioState **value) {
    return This->lpVtbl->get_State(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadio_get_Name(__x_ABI_CWindows_CDevices_CRadios_CIRadio* This,HSTRING *value) {
    return This->lpVtbl->get_Name(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadio_get_Kind(__x_ABI_CWindows_CDevices_CRadios_CIRadio* This,__x_ABI_CWindows_CDevices_CRadios_CRadioKind *value) {
    return This->lpVtbl->get_Kind(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Radios
#define IID_IRadio IID___x_ABI_CWindows_CDevices_CRadios_CIRadio
#define IRadioVtbl __x_ABI_CWindows_CDevices_CRadios_CIRadioVtbl
#define IRadio __x_ABI_CWindows_CDevices_CRadios_CIRadio
#define IRadio_QueryInterface __x_ABI_CWindows_CDevices_CRadios_CIRadio_QueryInterface
#define IRadio_AddRef __x_ABI_CWindows_CDevices_CRadios_CIRadio_AddRef
#define IRadio_Release __x_ABI_CWindows_CDevices_CRadios_CIRadio_Release
#define IRadio_GetIids __x_ABI_CWindows_CDevices_CRadios_CIRadio_GetIids
#define IRadio_GetRuntimeClassName __x_ABI_CWindows_CDevices_CRadios_CIRadio_GetRuntimeClassName
#define IRadio_GetTrustLevel __x_ABI_CWindows_CDevices_CRadios_CIRadio_GetTrustLevel
#define IRadio_SetStateAsync __x_ABI_CWindows_CDevices_CRadios_CIRadio_SetStateAsync
#define IRadio_add_StateChanged __x_ABI_CWindows_CDevices_CRadios_CIRadio_add_StateChanged
#define IRadio_remove_StateChanged __x_ABI_CWindows_CDevices_CRadios_CIRadio_remove_StateChanged
#define IRadio_get_State __x_ABI_CWindows_CDevices_CRadios_CIRadio_get_State
#define IRadio_get_Name __x_ABI_CWindows_CDevices_CRadios_CIRadio_get_Name
#define IRadio_get_Kind __x_ABI_CWindows_CDevices_CRadios_CIRadio_get_Kind
#endif /* WIDL_using_Windows_Devices_Radios */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CRadios_CIRadio_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IRadioStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CRadios_CIRadioStatics, 0x5fb6a12e, 0x67cb, 0x46ae, 0xaa,0xe9, 0x65,0x91,0x9f,0x86,0xef,0xf4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Radios {
                MIDL_INTERFACE("5fb6a12e-67cb-46ae-aae9-65919f86eff4")
                IRadioStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetRadiosAsync(
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetDeviceSelector(
                        HSTRING *selector) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FromIdAsync(
                        HSTRING id,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Radios::Radio* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE RequestAccessAsync(
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Radios::RadioAccessStatus > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CRadios_CIRadioStatics, 0x5fb6a12e, 0x67cb, 0x46ae, 0xaa,0xe9, 0x65,0x91,0x9f,0x86,0xef,0xf4)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CRadios_CIRadioStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics *This,
        TrustLevel *trustLevel);

    /*** IRadioStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRadiosAsync)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics *This,
        __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio **value);

    HRESULT (STDMETHODCALLTYPE *GetDeviceSelector)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics *This,
        HSTRING *selector);

    HRESULT (STDMETHODCALLTYPE *FromIdAsync)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics *This,
        HSTRING id,
        __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio **value);

    HRESULT (STDMETHODCALLTYPE *RequestAccessAsync)(
        __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics *This,
        __FIAsyncOperation_1_RadioAccessStatus **value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CRadios_CIRadioStaticsVtbl;

interface __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics {
    CONST_VTBL __x_ABI_CWindows_CDevices_CRadios_CIRadioStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IRadioStatics methods ***/
#define __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_GetRadiosAsync(This,value) (This)->lpVtbl->GetRadiosAsync(This,value)
#define __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_GetDeviceSelector(This,selector) (This)->lpVtbl->GetDeviceSelector(This,selector)
#define __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_FromIdAsync(This,id,value) (This)->lpVtbl->FromIdAsync(This,id,value)
#define __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_RequestAccessAsync(This,value) (This)->lpVtbl->RequestAccessAsync(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_QueryInterface(__x_ABI_CWindows_CDevices_CRadios_CIRadioStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_AddRef(__x_ABI_CWindows_CDevices_CRadios_CIRadioStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_Release(__x_ABI_CWindows_CDevices_CRadios_CIRadioStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_GetIids(__x_ABI_CWindows_CDevices_CRadios_CIRadioStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CRadios_CIRadioStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_GetTrustLevel(__x_ABI_CWindows_CDevices_CRadios_CIRadioStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IRadioStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_GetRadiosAsync(__x_ABI_CWindows_CDevices_CRadios_CIRadioStatics* This,__FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio **value) {
    return This->lpVtbl->GetRadiosAsync(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_GetDeviceSelector(__x_ABI_CWindows_CDevices_CRadios_CIRadioStatics* This,HSTRING *selector) {
    return This->lpVtbl->GetDeviceSelector(This,selector);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_FromIdAsync(__x_ABI_CWindows_CDevices_CRadios_CIRadioStatics* This,HSTRING id,__FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio **value) {
    return This->lpVtbl->FromIdAsync(This,id,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_RequestAccessAsync(__x_ABI_CWindows_CDevices_CRadios_CIRadioStatics* This,__FIAsyncOperation_1_RadioAccessStatus **value) {
    return This->lpVtbl->RequestAccessAsync(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Radios
#define IID_IRadioStatics IID___x_ABI_CWindows_CDevices_CRadios_CIRadioStatics
#define IRadioStaticsVtbl __x_ABI_CWindows_CDevices_CRadios_CIRadioStaticsVtbl
#define IRadioStatics __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics
#define IRadioStatics_QueryInterface __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_QueryInterface
#define IRadioStatics_AddRef __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_AddRef
#define IRadioStatics_Release __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_Release
#define IRadioStatics_GetIids __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_GetIids
#define IRadioStatics_GetRuntimeClassName __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_GetRuntimeClassName
#define IRadioStatics_GetTrustLevel __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_GetTrustLevel
#define IRadioStatics_GetRadiosAsync __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_GetRadiosAsync
#define IRadioStatics_GetDeviceSelector __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_GetDeviceSelector
#define IRadioStatics_FromIdAsync __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_FromIdAsync
#define IRadioStatics_RequestAccessAsync __x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_RequestAccessAsync
#endif /* WIDL_using_Windows_Devices_Radios */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CRadios_CIRadioStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Radios.Radio
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Radios_Radio_DEFINED
#define RUNTIMECLASS_Windows_Devices_Radios_Radio_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Radios_Radio[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','R','a','d','i','o','s','.','R','a','d','i','o',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Radios_Radio[] = L"Windows.Devices.Radios.Radio";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Radios_Radio[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','R','a','d','i','o','s','.','R','a','d','i','o',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Radios_Radio_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IIterable<ABI::Windows::Devices::Radios::Radio* > interface
 */
#ifndef ____FIIterable_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CDevices__CRadios__CRadio, 0xe82500af, 0x1f53, 0x504e, 0xb8,0xbe, 0xda,0xc4,0xfb,0xb6,0x90,0x84);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("e82500af-1f53-504e-b8be-dac4fbb69084")
                IIterable<ABI::Windows::Devices::Radios::Radio* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Radios::Radio*, ABI::Windows::Devices::Radios::IRadio* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CDevices__CRadios__CRadio, 0xe82500af, 0x1f53, 0x504e, 0xb8,0xbe, 0xda,0xc4,0xfb,0xb6,0x90,0x84)
#endif
#else
typedef struct __FIIterable_1_Windows__CDevices__CRadios__CRadioVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CDevices__CRadios__CRadio *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CDevices__CRadios__CRadio *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CDevices__CRadios__CRadio *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CDevices__CRadios__CRadio *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CDevices__CRadios__CRadio *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CDevices__CRadios__CRadio *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Devices::Radios::Radio* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CDevices__CRadios__CRadio *This,
        __FIIterator_1_Windows__CDevices__CRadios__CRadio **value);

    END_INTERFACE
} __FIIterable_1_Windows__CDevices__CRadios__CRadioVtbl;

interface __FIIterable_1_Windows__CDevices__CRadios__CRadio {
    CONST_VTBL __FIIterable_1_Windows__CDevices__CRadios__CRadioVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CDevices__CRadios__CRadio_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CDevices__CRadios__CRadio_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CDevices__CRadios__CRadio_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CDevices__CRadios__CRadio_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CDevices__CRadios__CRadio_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CDevices__CRadios__CRadio_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Devices::Radios::Radio* > methods ***/
#define __FIIterable_1_Windows__CDevices__CRadios__CRadio_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CDevices__CRadios__CRadio_QueryInterface(__FIIterable_1_Windows__CDevices__CRadios__CRadio* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CDevices__CRadios__CRadio_AddRef(__FIIterable_1_Windows__CDevices__CRadios__CRadio* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CDevices__CRadios__CRadio_Release(__FIIterable_1_Windows__CDevices__CRadios__CRadio* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CDevices__CRadios__CRadio_GetIids(__FIIterable_1_Windows__CDevices__CRadios__CRadio* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CDevices__CRadios__CRadio_GetRuntimeClassName(__FIIterable_1_Windows__CDevices__CRadios__CRadio* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CDevices__CRadios__CRadio_GetTrustLevel(__FIIterable_1_Windows__CDevices__CRadios__CRadio* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Devices::Radios::Radio* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CDevices__CRadios__CRadio_First(__FIIterable_1_Windows__CDevices__CRadios__CRadio* This,__FIIterator_1_Windows__CDevices__CRadios__CRadio **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_Radio IID___FIIterable_1_Windows__CDevices__CRadios__CRadio
#define IIterable_RadioVtbl __FIIterable_1_Windows__CDevices__CRadios__CRadioVtbl
#define IIterable_Radio __FIIterable_1_Windows__CDevices__CRadios__CRadio
#define IIterable_Radio_QueryInterface __FIIterable_1_Windows__CDevices__CRadios__CRadio_QueryInterface
#define IIterable_Radio_AddRef __FIIterable_1_Windows__CDevices__CRadios__CRadio_AddRef
#define IIterable_Radio_Release __FIIterable_1_Windows__CDevices__CRadios__CRadio_Release
#define IIterable_Radio_GetIids __FIIterable_1_Windows__CDevices__CRadios__CRadio_GetIids
#define IIterable_Radio_GetRuntimeClassName __FIIterable_1_Windows__CDevices__CRadios__CRadio_GetRuntimeClassName
#define IIterable_Radio_GetTrustLevel __FIIterable_1_Windows__CDevices__CRadios__CRadio_GetTrustLevel
#define IIterable_Radio_First __FIIterable_1_Windows__CDevices__CRadios__CRadio_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Devices::Radios::Radio* > interface
 */
#ifndef ____FIIterator_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CDevices__CRadios__CRadio, 0xcf37ede7, 0xeaec, 0x5b8f, 0xad,0x31, 0x4d,0x51,0xab,0xd9,0xdb,0x05);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("cf37ede7-eaec-5b8f-ad31-4d51abd9db05")
                IIterator<ABI::Windows::Devices::Radios::Radio* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Radios::Radio*, ABI::Windows::Devices::Radios::IRadio* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CDevices__CRadios__CRadio, 0xcf37ede7, 0xeaec, 0x5b8f, 0xad,0x31, 0x4d,0x51,0xab,0xd9,0xdb,0x05)
#endif
#else
typedef struct __FIIterator_1_Windows__CDevices__CRadios__CRadioVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CDevices__CRadios__CRadio *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CDevices__CRadios__CRadio *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CDevices__CRadios__CRadio *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CDevices__CRadios__CRadio *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CDevices__CRadios__CRadio *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CDevices__CRadios__CRadio *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Devices::Radios::Radio* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CDevices__CRadios__CRadio *This,
        __x_ABI_CWindows_CDevices_CRadios_CIRadio **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CDevices__CRadios__CRadio *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CDevices__CRadios__CRadio *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CDevices__CRadios__CRadio *This,
        UINT32 items_size,
        __x_ABI_CWindows_CDevices_CRadios_CIRadio **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CDevices__CRadios__CRadioVtbl;

interface __FIIterator_1_Windows__CDevices__CRadios__CRadio {
    CONST_VTBL __FIIterator_1_Windows__CDevices__CRadios__CRadioVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CDevices__CRadios__CRadio_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CDevices__CRadios__CRadio_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CDevices__CRadios__CRadio_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CDevices__CRadios__CRadio_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CDevices__CRadios__CRadio_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CDevices__CRadios__CRadio_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Devices::Radios::Radio* > methods ***/
#define __FIIterator_1_Windows__CDevices__CRadios__CRadio_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CDevices__CRadios__CRadio_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CDevices__CRadios__CRadio_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CDevices__CRadios__CRadio_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CDevices__CRadios__CRadio_QueryInterface(__FIIterator_1_Windows__CDevices__CRadios__CRadio* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CDevices__CRadios__CRadio_AddRef(__FIIterator_1_Windows__CDevices__CRadios__CRadio* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CDevices__CRadios__CRadio_Release(__FIIterator_1_Windows__CDevices__CRadios__CRadio* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CDevices__CRadios__CRadio_GetIids(__FIIterator_1_Windows__CDevices__CRadios__CRadio* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CDevices__CRadios__CRadio_GetRuntimeClassName(__FIIterator_1_Windows__CDevices__CRadios__CRadio* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CDevices__CRadios__CRadio_GetTrustLevel(__FIIterator_1_Windows__CDevices__CRadios__CRadio* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Devices::Radios::Radio* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CDevices__CRadios__CRadio_get_Current(__FIIterator_1_Windows__CDevices__CRadios__CRadio* This,__x_ABI_CWindows_CDevices_CRadios_CIRadio **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CDevices__CRadios__CRadio_get_HasCurrent(__FIIterator_1_Windows__CDevices__CRadios__CRadio* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CDevices__CRadios__CRadio_MoveNext(__FIIterator_1_Windows__CDevices__CRadios__CRadio* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CDevices__CRadios__CRadio_GetMany(__FIIterator_1_Windows__CDevices__CRadios__CRadio* This,UINT32 items_size,__x_ABI_CWindows_CDevices_CRadios_CIRadio **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_Radio IID___FIIterator_1_Windows__CDevices__CRadios__CRadio
#define IIterator_RadioVtbl __FIIterator_1_Windows__CDevices__CRadios__CRadioVtbl
#define IIterator_Radio __FIIterator_1_Windows__CDevices__CRadios__CRadio
#define IIterator_Radio_QueryInterface __FIIterator_1_Windows__CDevices__CRadios__CRadio_QueryInterface
#define IIterator_Radio_AddRef __FIIterator_1_Windows__CDevices__CRadios__CRadio_AddRef
#define IIterator_Radio_Release __FIIterator_1_Windows__CDevices__CRadios__CRadio_Release
#define IIterator_Radio_GetIids __FIIterator_1_Windows__CDevices__CRadios__CRadio_GetIids
#define IIterator_Radio_GetRuntimeClassName __FIIterator_1_Windows__CDevices__CRadios__CRadio_GetRuntimeClassName
#define IIterator_Radio_GetTrustLevel __FIIterator_1_Windows__CDevices__CRadios__CRadio_GetTrustLevel
#define IIterator_Radio_get_Current __FIIterator_1_Windows__CDevices__CRadios__CRadio_get_Current
#define IIterator_Radio_get_HasCurrent __FIIterator_1_Windows__CDevices__CRadios__CRadio_get_HasCurrent
#define IIterator_Radio_MoveNext __FIIterator_1_Windows__CDevices__CRadios__CRadio_MoveNext
#define IIterator_Radio_GetMany __FIIterator_1_Windows__CDevices__CRadios__CRadio_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Devices::Radios::Radio* > interface
 */
#ifndef ____FIVectorView_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CDevices__CRadios__CRadio, 0x65066c36, 0x090b, 0x5466, 0xb8,0xe5, 0xe7,0x56,0x5d,0xc3,0x41,0x75);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("65066c36-090b-5466-b8e5-e7565dc34175")
                IVectorView<ABI::Windows::Devices::Radios::Radio* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Radios::Radio*, ABI::Windows::Devices::Radios::IRadio* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CDevices__CRadios__CRadio, 0x65066c36, 0x090b, 0x5466, 0xb8,0xe5, 0xe7,0x56,0x5d,0xc3,0x41,0x75)
#endif
#else
typedef struct __FIVectorView_1_Windows__CDevices__CRadios__CRadioVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CDevices__CRadios__CRadio *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CDevices__CRadios__CRadio *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CDevices__CRadios__CRadio *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CDevices__CRadios__CRadio *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CDevices__CRadios__CRadio *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CDevices__CRadios__CRadio *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Devices::Radios::Radio* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CDevices__CRadios__CRadio *This,
        UINT32 index,
        __x_ABI_CWindows_CDevices_CRadios_CIRadio **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CDevices__CRadios__CRadio *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CDevices__CRadios__CRadio *This,
        __x_ABI_CWindows_CDevices_CRadios_CIRadio *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CDevices__CRadios__CRadio *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CDevices_CRadios_CIRadio **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CDevices__CRadios__CRadioVtbl;

interface __FIVectorView_1_Windows__CDevices__CRadios__CRadio {
    CONST_VTBL __FIVectorView_1_Windows__CDevices__CRadios__CRadioVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CDevices__CRadios__CRadio_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CDevices__CRadios__CRadio_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CDevices__CRadios__CRadio_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Devices::Radios::Radio* > methods ***/
#define __FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CDevices__CRadios__CRadio_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CDevices__CRadios__CRadio_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CRadios__CRadio_QueryInterface(__FIVectorView_1_Windows__CDevices__CRadios__CRadio* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CDevices__CRadios__CRadio_AddRef(__FIVectorView_1_Windows__CDevices__CRadios__CRadio* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CDevices__CRadios__CRadio_Release(__FIVectorView_1_Windows__CDevices__CRadios__CRadio* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetIids(__FIVectorView_1_Windows__CDevices__CRadios__CRadio* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetRuntimeClassName(__FIVectorView_1_Windows__CDevices__CRadios__CRadio* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetTrustLevel(__FIVectorView_1_Windows__CDevices__CRadios__CRadio* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Devices::Radios::Radio* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetAt(__FIVectorView_1_Windows__CDevices__CRadios__CRadio* This,UINT32 index,__x_ABI_CWindows_CDevices_CRadios_CIRadio **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CRadios__CRadio_get_Size(__FIVectorView_1_Windows__CDevices__CRadios__CRadio* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CRadios__CRadio_IndexOf(__FIVectorView_1_Windows__CDevices__CRadios__CRadio* This,__x_ABI_CWindows_CDevices_CRadios_CIRadio *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetMany(__FIVectorView_1_Windows__CDevices__CRadios__CRadio* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CDevices_CRadios_CIRadio **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_Radio IID___FIVectorView_1_Windows__CDevices__CRadios__CRadio
#define IVectorView_RadioVtbl __FIVectorView_1_Windows__CDevices__CRadios__CRadioVtbl
#define IVectorView_Radio __FIVectorView_1_Windows__CDevices__CRadios__CRadio
#define IVectorView_Radio_QueryInterface __FIVectorView_1_Windows__CDevices__CRadios__CRadio_QueryInterface
#define IVectorView_Radio_AddRef __FIVectorView_1_Windows__CDevices__CRadios__CRadio_AddRef
#define IVectorView_Radio_Release __FIVectorView_1_Windows__CDevices__CRadios__CRadio_Release
#define IVectorView_Radio_GetIids __FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetIids
#define IVectorView_Radio_GetRuntimeClassName __FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetRuntimeClassName
#define IVectorView_Radio_GetTrustLevel __FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetTrustLevel
#define IVectorView_Radio_GetAt __FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetAt
#define IVectorView_Radio_get_Size __FIVectorView_1_Windows__CDevices__CRadios__CRadio_get_Size
#define IVectorView_Radio_IndexOf __FIVectorView_1_Windows__CDevices__CRadios__CRadio_IndexOf
#define IVectorView_Radio_GetMany __FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Devices::Radios::Radio* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio, 0x8a5c7e3a, 0x80e2, 0x585b, 0x86,0x30, 0x7a,0x8e,0x77,0x7f,0x03,0x54);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("8a5c7e3a-80e2-585b-8630-7a8e777f0354")
            IAsyncOperationCompletedHandler<ABI::Windows::Devices::Radios::Radio* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Radios::Radio*, ABI::Windows::Devices::Radios::IRadio* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio, 0x8a5c7e3a, 0x80e2, 0x585b, 0x86,0x30, 0x7a,0x8e,0x77,0x7f,0x03,0x54)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadioVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Radios::Radio* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio *This,
        __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadioVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadioVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Radios::Radio* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio_Release(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Radios::Radio* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio* This,__FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_Radio IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio
#define IAsyncOperationCompletedHandler_RadioVtbl __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadioVtbl
#define IAsyncOperationCompletedHandler_Radio __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio
#define IAsyncOperationCompletedHandler_Radio_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio_QueryInterface
#define IAsyncOperationCompletedHandler_Radio_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio_AddRef
#define IAsyncOperationCompletedHandler_Radio_Release __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio_Release
#define IAsyncOperationCompletedHandler_Radio_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Devices::Radios::RadioAccessStatus > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_RadioAccessStatus_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_RadioAccessStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_RadioAccessStatus, 0xbd248e73, 0xf05f, 0x574c, 0xae,0x3d, 0x9b,0x95,0xc4,0xbf,0x28,0x2a);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("bd248e73-f05f-574c-ae3d-9b95c4bf282a")
            IAsyncOperationCompletedHandler<ABI::Windows::Devices::Radios::RadioAccessStatus > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Devices::Radios::RadioAccessStatus >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_RadioAccessStatus, 0xbd248e73, 0xf05f, 0x574c, 0xae,0x3d, 0x9b,0x95,0xc4,0xbf,0x28,0x2a)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_RadioAccessStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_RadioAccessStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_RadioAccessStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_RadioAccessStatus *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Radios::RadioAccessStatus > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_RadioAccessStatus *This,
        __FIAsyncOperation_1_RadioAccessStatus *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_RadioAccessStatusVtbl;

interface __FIAsyncOperationCompletedHandler_1_RadioAccessStatus {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_RadioAccessStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_RadioAccessStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_RadioAccessStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_RadioAccessStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Radios::RadioAccessStatus > methods ***/
#define __FIAsyncOperationCompletedHandler_1_RadioAccessStatus_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_RadioAccessStatus_QueryInterface(__FIAsyncOperationCompletedHandler_1_RadioAccessStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_RadioAccessStatus_AddRef(__FIAsyncOperationCompletedHandler_1_RadioAccessStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_RadioAccessStatus_Release(__FIAsyncOperationCompletedHandler_1_RadioAccessStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Radios::RadioAccessStatus > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_RadioAccessStatus_Invoke(__FIAsyncOperationCompletedHandler_1_RadioAccessStatus* This,__FIAsyncOperation_1_RadioAccessStatus *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_RadioAccessStatus IID___FIAsyncOperationCompletedHandler_1_RadioAccessStatus
#define IAsyncOperationCompletedHandler_RadioAccessStatusVtbl __FIAsyncOperationCompletedHandler_1_RadioAccessStatusVtbl
#define IAsyncOperationCompletedHandler_RadioAccessStatus __FIAsyncOperationCompletedHandler_1_RadioAccessStatus
#define IAsyncOperationCompletedHandler_RadioAccessStatus_QueryInterface __FIAsyncOperationCompletedHandler_1_RadioAccessStatus_QueryInterface
#define IAsyncOperationCompletedHandler_RadioAccessStatus_AddRef __FIAsyncOperationCompletedHandler_1_RadioAccessStatus_AddRef
#define IAsyncOperationCompletedHandler_RadioAccessStatus_Release __FIAsyncOperationCompletedHandler_1_RadioAccessStatus_Release
#define IAsyncOperationCompletedHandler_RadioAccessStatus_Invoke __FIAsyncOperationCompletedHandler_1_RadioAccessStatus_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_RadioAccessStatus_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio, 0xd30691e6, 0x60a0, 0x59c9, 0x89,0x65, 0x5b,0xbe,0x28,0x2e,0x82,0x08);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("d30691e6-60a0-59c9-8965-5bbe282e8208")
            IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio, 0xd30691e6, 0x60a0, 0x59c9, 0x89,0x65, 0x5b,0xbe,0x28,0x2e,0x82,0x08)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadioVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio *This,
        __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadioVtbl;

interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadioVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >* > methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_QueryInterface(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_AddRef(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_Release(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_Invoke(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio* This,__FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IVectorView_Radio IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio
#define IAsyncOperationCompletedHandler_IVectorView_RadioVtbl __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadioVtbl
#define IAsyncOperationCompletedHandler_IVectorView_Radio __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio
#define IAsyncOperationCompletedHandler_IVectorView_Radio_QueryInterface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_QueryInterface
#define IAsyncOperationCompletedHandler_IVectorView_Radio_AddRef __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_AddRef
#define IAsyncOperationCompletedHandler_IVectorView_Radio_Release __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_Release
#define IAsyncOperationCompletedHandler_IVectorView_Radio_Invoke __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Devices::Radios::Radio* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio, 0xeac62c40, 0x8dbc, 0x5854, 0x8b,0xa0, 0xb7,0xb9,0x94,0x0e,0x73,0x89);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("eac62c40-8dbc-5854-8ba0-b7b9940e7389")
            IAsyncOperation<ABI::Windows::Devices::Radios::Radio* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Radios::Radio*, ABI::Windows::Devices::Radios::IRadio* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio, 0xeac62c40, 0x8dbc, 0x5854, 0x8b,0xa0, 0xb7,0xb9,0x94,0x0e,0x73,0x89)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadioVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Devices::Radios::Radio* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio *This,
        __x_ABI_CWindows_CDevices_CRadios_CIRadio **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadioVtbl;

interface __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio {
    CONST_VTBL __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadioVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Devices::Radios::Radio* > methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_QueryInterface(__FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_AddRef(__FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_Release(__FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_GetIids(__FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_GetTrustLevel(__FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Devices::Radios::Radio* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_put_Completed(__FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_get_Completed(__FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CRadios__CRadio **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_GetResults(__FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio* This,__x_ABI_CWindows_CDevices_CRadios_CIRadio **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_Radio IID___FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio
#define IAsyncOperation_RadioVtbl __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadioVtbl
#define IAsyncOperation_Radio __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio
#define IAsyncOperation_Radio_QueryInterface __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_QueryInterface
#define IAsyncOperation_Radio_AddRef __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_AddRef
#define IAsyncOperation_Radio_Release __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_Release
#define IAsyncOperation_Radio_GetIids __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_GetIids
#define IAsyncOperation_Radio_GetRuntimeClassName __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_GetRuntimeClassName
#define IAsyncOperation_Radio_GetTrustLevel __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_GetTrustLevel
#define IAsyncOperation_Radio_put_Completed __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_put_Completed
#define IAsyncOperation_Radio_get_Completed __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_get_Completed
#define IAsyncOperation_Radio_GetResults __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Devices::Radios::RadioAccessStatus > interface
 */
#ifndef ____FIAsyncOperation_1_RadioAccessStatus_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_RadioAccessStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_RadioAccessStatus, 0x21fb30ef, 0x072f, 0x502c, 0x98,0x98, 0xd0,0xc3,0xb2,0xcd,0x9a,0xc5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("21fb30ef-072f-502c-9898-d0c3b2cd9ac5")
            IAsyncOperation<ABI::Windows::Devices::Radios::RadioAccessStatus > : IAsyncOperation_impl<ABI::Windows::Devices::Radios::RadioAccessStatus >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_RadioAccessStatus, 0x21fb30ef, 0x072f, 0x502c, 0x98,0x98, 0xd0,0xc3,0xb2,0xcd,0x9a,0xc5)
#endif
#else
typedef struct __FIAsyncOperation_1_RadioAccessStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_RadioAccessStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_RadioAccessStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_RadioAccessStatus *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_RadioAccessStatus *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_RadioAccessStatus *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_RadioAccessStatus *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Devices::Radios::RadioAccessStatus > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_RadioAccessStatus *This,
        __FIAsyncOperationCompletedHandler_1_RadioAccessStatus *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_RadioAccessStatus *This,
        __FIAsyncOperationCompletedHandler_1_RadioAccessStatus **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_RadioAccessStatus *This,
        __x_ABI_CWindows_CDevices_CRadios_CRadioAccessStatus *results);

    END_INTERFACE
} __FIAsyncOperation_1_RadioAccessStatusVtbl;

interface __FIAsyncOperation_1_RadioAccessStatus {
    CONST_VTBL __FIAsyncOperation_1_RadioAccessStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_RadioAccessStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_RadioAccessStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_RadioAccessStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_RadioAccessStatus_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_RadioAccessStatus_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_RadioAccessStatus_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Devices::Radios::RadioAccessStatus > methods ***/
#define __FIAsyncOperation_1_RadioAccessStatus_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_RadioAccessStatus_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_RadioAccessStatus_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_RadioAccessStatus_QueryInterface(__FIAsyncOperation_1_RadioAccessStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_RadioAccessStatus_AddRef(__FIAsyncOperation_1_RadioAccessStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_RadioAccessStatus_Release(__FIAsyncOperation_1_RadioAccessStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_RadioAccessStatus_GetIids(__FIAsyncOperation_1_RadioAccessStatus* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_RadioAccessStatus_GetRuntimeClassName(__FIAsyncOperation_1_RadioAccessStatus* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_RadioAccessStatus_GetTrustLevel(__FIAsyncOperation_1_RadioAccessStatus* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Devices::Radios::RadioAccessStatus > methods ***/
static inline HRESULT __FIAsyncOperation_1_RadioAccessStatus_put_Completed(__FIAsyncOperation_1_RadioAccessStatus* This,__FIAsyncOperationCompletedHandler_1_RadioAccessStatus *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_RadioAccessStatus_get_Completed(__FIAsyncOperation_1_RadioAccessStatus* This,__FIAsyncOperationCompletedHandler_1_RadioAccessStatus **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_RadioAccessStatus_GetResults(__FIAsyncOperation_1_RadioAccessStatus* This,__x_ABI_CWindows_CDevices_CRadios_CRadioAccessStatus *results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_RadioAccessStatus IID___FIAsyncOperation_1_RadioAccessStatus
#define IAsyncOperation_RadioAccessStatusVtbl __FIAsyncOperation_1_RadioAccessStatusVtbl
#define IAsyncOperation_RadioAccessStatus __FIAsyncOperation_1_RadioAccessStatus
#define IAsyncOperation_RadioAccessStatus_QueryInterface __FIAsyncOperation_1_RadioAccessStatus_QueryInterface
#define IAsyncOperation_RadioAccessStatus_AddRef __FIAsyncOperation_1_RadioAccessStatus_AddRef
#define IAsyncOperation_RadioAccessStatus_Release __FIAsyncOperation_1_RadioAccessStatus_Release
#define IAsyncOperation_RadioAccessStatus_GetIids __FIAsyncOperation_1_RadioAccessStatus_GetIids
#define IAsyncOperation_RadioAccessStatus_GetRuntimeClassName __FIAsyncOperation_1_RadioAccessStatus_GetRuntimeClassName
#define IAsyncOperation_RadioAccessStatus_GetTrustLevel __FIAsyncOperation_1_RadioAccessStatus_GetTrustLevel
#define IAsyncOperation_RadioAccessStatus_put_Completed __FIAsyncOperation_1_RadioAccessStatus_put_Completed
#define IAsyncOperation_RadioAccessStatus_get_Completed __FIAsyncOperation_1_RadioAccessStatus_get_Completed
#define IAsyncOperation_RadioAccessStatus_GetResults __FIAsyncOperation_1_RadioAccessStatus_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_RadioAccessStatus_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >* > interface
 */
#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio, 0x040b54a1, 0x203e, 0x58f5, 0x94,0x3f, 0xc1,0xcc,0xa8,0x6b,0xd5,0x32);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("040b54a1-203e-58f5-943f-c1cca86bd532")
            IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >* > : IAsyncOperation_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio, 0x040b54a1, 0x203e, 0x58f5, 0x94,0x3f, 0xc1,0xcc,0xa8,0x6b,0xd5,0x32)
#endif
#else
typedef struct __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadioVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio *This,
        __FIVectorView_1_Windows__CDevices__CRadios__CRadio **results);

    END_INTERFACE
} __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadioVtbl;

interface __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio {
    CONST_VTBL __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadioVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >* > methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_QueryInterface(__FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_AddRef(__FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_Release(__FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetIids(__FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetRuntimeClassName(__FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetTrustLevel(__FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Radios::Radio* >* > methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_put_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_get_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetResults(__FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio* This,__FIVectorView_1_Windows__CDevices__CRadios__CRadio **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IVectorView_Radio IID___FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio
#define IAsyncOperation_IVectorView_RadioVtbl __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadioVtbl
#define IAsyncOperation_IVectorView_Radio __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio
#define IAsyncOperation_IVectorView_Radio_QueryInterface __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_QueryInterface
#define IAsyncOperation_IVectorView_Radio_AddRef __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_AddRef
#define IAsyncOperation_IVectorView_Radio_Release __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_Release
#define IAsyncOperation_IVectorView_Radio_GetIids __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetIids
#define IAsyncOperation_IVectorView_Radio_GetRuntimeClassName __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetRuntimeClassName
#define IAsyncOperation_IVectorView_Radio_GetTrustLevel __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetTrustLevel
#define IAsyncOperation_IVectorView_Radio_put_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_put_Completed
#define IAsyncOperation_IVectorView_Radio_get_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_get_Completed
#define IAsyncOperation_IVectorView_Radio_GetResults __FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1___FIVectorView_1_Windows__CDevices__CRadios__CRadio_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Devices::Radios::Radio*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable, 0xfc6aa329, 0xb586, 0x5ebb, 0x9e,0x85, 0x3f,0x6b,0x84,0xeb,0xdf,0x18);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("fc6aa329-b586-5ebb-9e85-3f6b84ebdf18")
            ITypedEventHandler<ABI::Windows::Devices::Radios::Radio*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Radios::Radio*, ABI::Windows::Devices::Radios::IRadio* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable, 0xfc6aa329, 0xb586, 0x5ebb, 0x9e,0x85, 0x3f,0x6b,0x84,0xeb,0xdf,0x18)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::Devices::Radios::Radio*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable *This,
        __x_ABI_CWindows_CDevices_CRadios_CIRadio *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Devices::Radios::Radio*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable_Release(__FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Devices::Radios::Radio*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable* This,__x_ABI_CWindows_CDevices_CRadios_CIRadio *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_Radio_IInspectable IID___FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable
#define ITypedEventHandler_Radio_IInspectableVtbl __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectableVtbl
#define ITypedEventHandler_Radio_IInspectable __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable
#define ITypedEventHandler_Radio_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable_QueryInterface
#define ITypedEventHandler_Radio_IInspectable_AddRef __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable_AddRef
#define ITypedEventHandler_Radio_IInspectable_Release __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable_Release
#define ITypedEventHandler_Radio_IInspectable_Invoke __FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CDevices__CRadios__CRadio_IInspectable_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_devices_radios_h__ */
