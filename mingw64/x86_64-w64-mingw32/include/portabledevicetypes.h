/*** Autogenerated by WIDL 10.8 from include/portabledevicetypes.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __portabledevicetypes_h__
#define __portabledevicetypes_h__

/* Forward declarations */

#ifndef __IWpdSerializer_FWD_DEFINED__
#define __IWpdSerializer_FWD_DEFINED__
typedef interface IWpdSerializer IWpdSerializer;
#ifdef __cplusplus
interface IWpdSerializer;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceValues_FWD_DEFINED__
#define __IPortableDeviceValues_FWD_DEFINED__
typedef interface IPortableDeviceValues IPortableDeviceValues;
#ifdef __cplusplus
interface IPortableDeviceValues;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceKeyCollection_FWD_DEFINED__
#define __IPortableDeviceKeyCollection_FWD_DEFINED__
typedef interface IPortableDeviceKeyCollection IPortableDeviceKeyCollection;
#ifdef __cplusplus
interface IPortableDeviceKeyCollection;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDevicePropVariantCollection_FWD_DEFINED__
#define __IPortableDevicePropVariantCollection_FWD_DEFINED__
typedef interface IPortableDevicePropVariantCollection IPortableDevicePropVariantCollection;
#ifdef __cplusplus
interface IPortableDevicePropVariantCollection;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceValuesCollection_FWD_DEFINED__
#define __IPortableDeviceValuesCollection_FWD_DEFINED__
typedef interface IPortableDeviceValuesCollection IPortableDeviceValuesCollection;
#ifdef __cplusplus
interface IPortableDeviceValuesCollection;
#endif /* __cplusplus */
#endif

#ifndef __WpdSerializer_FWD_DEFINED__
#define __WpdSerializer_FWD_DEFINED__
#ifdef __cplusplus
typedef class WpdSerializer WpdSerializer;
#else
typedef struct WpdSerializer WpdSerializer;
#endif /* defined __cplusplus */
#endif /* defined __WpdSerializer_FWD_DEFINED__ */

#ifndef __PortableDeviceValues_FWD_DEFINED__
#define __PortableDeviceValues_FWD_DEFINED__
#ifdef __cplusplus
typedef class PortableDeviceValues PortableDeviceValues;
#else
typedef struct PortableDeviceValues PortableDeviceValues;
#endif /* defined __cplusplus */
#endif /* defined __PortableDeviceValues_FWD_DEFINED__ */

#ifndef __PortableDeviceKeyCollection_FWD_DEFINED__
#define __PortableDeviceKeyCollection_FWD_DEFINED__
#ifdef __cplusplus
typedef class PortableDeviceKeyCollection PortableDeviceKeyCollection;
#else
typedef struct PortableDeviceKeyCollection PortableDeviceKeyCollection;
#endif /* defined __cplusplus */
#endif /* defined __PortableDeviceKeyCollection_FWD_DEFINED__ */

#ifndef __PortableDevicePropVariantCollection_FWD_DEFINED__
#define __PortableDevicePropVariantCollection_FWD_DEFINED__
#ifdef __cplusplus
typedef class PortableDevicePropVariantCollection PortableDevicePropVariantCollection;
#else
typedef struct PortableDevicePropVariantCollection PortableDevicePropVariantCollection;
#endif /* defined __cplusplus */
#endif /* defined __PortableDevicePropVariantCollection_FWD_DEFINED__ */

#ifndef __PortableDeviceValuesCollection_FWD_DEFINED__
#define __PortableDeviceValuesCollection_FWD_DEFINED__
#ifdef __cplusplus
typedef class PortableDeviceValuesCollection PortableDeviceValuesCollection;
#else
typedef struct PortableDeviceValuesCollection PortableDeviceValuesCollection;
#endif /* defined __cplusplus */
#endif /* defined __PortableDeviceValuesCollection_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <propsys.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef enum tagWPD_STREAM_UNITS {
    WPD_STREAM_UNITS_BYTES = 0x0,
    WPD_STREAM_UNITS_FRAMES = 0x1,
    WPD_STREAM_UNITS_ROWS = 0x2,
    WPD_STREAM_UNITS_MILLISECONDS = 0x4,
    WPD_STREAM_UNITS_MICROSECONDS = 0x8
} WPD_STREAM_UNITS;
#ifndef __IWpdSerializer_FWD_DEFINED__
#define __IWpdSerializer_FWD_DEFINED__
typedef interface IWpdSerializer IWpdSerializer;
#ifdef __cplusplus
interface IWpdSerializer;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceValues_FWD_DEFINED__
#define __IPortableDeviceValues_FWD_DEFINED__
typedef interface IPortableDeviceValues IPortableDeviceValues;
#ifdef __cplusplus
interface IPortableDeviceValues;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceKeyCollection_FWD_DEFINED__
#define __IPortableDeviceKeyCollection_FWD_DEFINED__
typedef interface IPortableDeviceKeyCollection IPortableDeviceKeyCollection;
#ifdef __cplusplus
interface IPortableDeviceKeyCollection;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDevicePropVariantCollection_FWD_DEFINED__
#define __IPortableDevicePropVariantCollection_FWD_DEFINED__
typedef interface IPortableDevicePropVariantCollection IPortableDevicePropVariantCollection;
#ifdef __cplusplus
interface IPortableDevicePropVariantCollection;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceValuesCollection_FWD_DEFINED__
#define __IPortableDeviceValuesCollection_FWD_DEFINED__
typedef interface IPortableDeviceValuesCollection IPortableDeviceValuesCollection;
#ifdef __cplusplus
interface IPortableDeviceValuesCollection;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IWpdSerializer interface
 */
#ifndef __IWpdSerializer_INTERFACE_DEFINED__
#define __IWpdSerializer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWpdSerializer, 0xb32f4002, 0xbb27, 0x45ff, 0xaf,0x4f, 0x06,0x63,0x1c,0x1e,0x8d,0xad);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b32f4002-bb27-45ff-af4f-06631c1e8dad")
IWpdSerializer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetIPortableDeviceValuesFromBuffer(
        BYTE *buffer,
        DWORD input_buffer_length,
        IPortableDeviceValues **params) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteIPortableDeviceValuesToBuffer(
        DWORD output_buffer_length,
        IPortableDeviceValues *results,
        BYTE *buffer,
        DWORD *bytes_written) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBufferFromIPortableDeviceValues(
        IPortableDeviceValues *source,
        BYTE **buffer,
        DWORD *buffer_size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSerializedSize(
        IPortableDeviceValues *source,
        DWORD *size) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWpdSerializer, 0xb32f4002, 0xbb27, 0x45ff, 0xaf,0x4f, 0x06,0x63,0x1c,0x1e,0x8d,0xad)
#endif
#else
typedef struct IWpdSerializerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWpdSerializer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWpdSerializer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWpdSerializer *This);

    /*** IWpdSerializer methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIPortableDeviceValuesFromBuffer)(
        IWpdSerializer *This,
        BYTE *buffer,
        DWORD input_buffer_length,
        IPortableDeviceValues **params);

    HRESULT (STDMETHODCALLTYPE *WriteIPortableDeviceValuesToBuffer)(
        IWpdSerializer *This,
        DWORD output_buffer_length,
        IPortableDeviceValues *results,
        BYTE *buffer,
        DWORD *bytes_written);

    HRESULT (STDMETHODCALLTYPE *GetBufferFromIPortableDeviceValues)(
        IWpdSerializer *This,
        IPortableDeviceValues *source,
        BYTE **buffer,
        DWORD *buffer_size);

    HRESULT (STDMETHODCALLTYPE *GetSerializedSize)(
        IWpdSerializer *This,
        IPortableDeviceValues *source,
        DWORD *size);

    END_INTERFACE
} IWpdSerializerVtbl;

interface IWpdSerializer {
    CONST_VTBL IWpdSerializerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWpdSerializer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWpdSerializer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWpdSerializer_Release(This) (This)->lpVtbl->Release(This)
/*** IWpdSerializer methods ***/
#define IWpdSerializer_GetIPortableDeviceValuesFromBuffer(This,buffer,input_buffer_length,params) (This)->lpVtbl->GetIPortableDeviceValuesFromBuffer(This,buffer,input_buffer_length,params)
#define IWpdSerializer_WriteIPortableDeviceValuesToBuffer(This,output_buffer_length,results,buffer,bytes_written) (This)->lpVtbl->WriteIPortableDeviceValuesToBuffer(This,output_buffer_length,results,buffer,bytes_written)
#define IWpdSerializer_GetBufferFromIPortableDeviceValues(This,source,buffer,buffer_size) (This)->lpVtbl->GetBufferFromIPortableDeviceValues(This,source,buffer,buffer_size)
#define IWpdSerializer_GetSerializedSize(This,source,size) (This)->lpVtbl->GetSerializedSize(This,source,size)
#else
/*** IUnknown methods ***/
static inline HRESULT IWpdSerializer_QueryInterface(IWpdSerializer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWpdSerializer_AddRef(IWpdSerializer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWpdSerializer_Release(IWpdSerializer* This) {
    return This->lpVtbl->Release(This);
}
/*** IWpdSerializer methods ***/
static inline HRESULT IWpdSerializer_GetIPortableDeviceValuesFromBuffer(IWpdSerializer* This,BYTE *buffer,DWORD input_buffer_length,IPortableDeviceValues **params) {
    return This->lpVtbl->GetIPortableDeviceValuesFromBuffer(This,buffer,input_buffer_length,params);
}
static inline HRESULT IWpdSerializer_WriteIPortableDeviceValuesToBuffer(IWpdSerializer* This,DWORD output_buffer_length,IPortableDeviceValues *results,BYTE *buffer,DWORD *bytes_written) {
    return This->lpVtbl->WriteIPortableDeviceValuesToBuffer(This,output_buffer_length,results,buffer,bytes_written);
}
static inline HRESULT IWpdSerializer_GetBufferFromIPortableDeviceValues(IWpdSerializer* This,IPortableDeviceValues *source,BYTE **buffer,DWORD *buffer_size) {
    return This->lpVtbl->GetBufferFromIPortableDeviceValues(This,source,buffer,buffer_size);
}
static inline HRESULT IWpdSerializer_GetSerializedSize(IWpdSerializer* This,IPortableDeviceValues *source,DWORD *size) {
    return This->lpVtbl->GetSerializedSize(This,source,size);
}
#endif
#endif

#endif


#endif  /* __IWpdSerializer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceValues interface
 */
#ifndef __IPortableDeviceValues_INTERFACE_DEFINED__
#define __IPortableDeviceValues_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceValues, 0x6848f6f2, 0x3155, 0x4f86, 0xb6,0xf5, 0x26,0x3e,0xee,0xab,0x31,0x43);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6848f6f2-3155-4f86-b6f5-263eeeab3143")
IPortableDeviceValues : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        DWORD *pcelt) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        const DWORD index,
        PROPERTYKEY *pKey,
        PROPVARIANT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetValue(
        REFPROPERTYKEY key,
        const PROPVARIANT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetValue(
        REFPROPERTYKEY key,
        PROPVARIANT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStringValue(
        REFPROPERTYKEY key,
        LPCWSTR Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStringValue(
        REFPROPERTYKEY key,
        LPWSTR *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUnsignedIntegerValue(
        REFPROPERTYKEY key,
        const ULONG Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUnsignedIntegerValue(
        REFPROPERTYKEY key,
        ULONG *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSignedIntegerValue(
        REFPROPERTYKEY key,
        const LONG Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignedIntegerValue(
        REFPROPERTYKEY key,
        LONG *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUnsignedLargeIntegerValue(
        REFPROPERTYKEY key,
        const ULONGLONG Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUnsignedLargeIntegerValue(
        REFPROPERTYKEY key,
        ULONGLONG *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSignedLargeIntegerValue(
        REFPROPERTYKEY key,
        const LONGLONG Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignedLargeIntegerValue(
        REFPROPERTYKEY key,
        LONGLONG *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFloatValue(
        REFPROPERTYKEY key,
        const FLOAT Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFloatValue(
        REFPROPERTYKEY key,
        FLOAT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetErrorValue(
        REFPROPERTYKEY key,
        const HRESULT Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetErrorValue(
        REFPROPERTYKEY key,
        HRESULT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetKeyValue(
        REFPROPERTYKEY key,
        REFPROPERTYKEY Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetKeyValue(
        REFPROPERTYKEY key,
        PROPERTYKEY *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBoolValue(
        REFPROPERTYKEY key,
        const WINBOOL Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBoolValue(
        REFPROPERTYKEY key,
        WINBOOL *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIUnknownValue(
        REFPROPERTYKEY key,
        IUnknown *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIUnknownValue(
        REFPROPERTYKEY key,
        IUnknown **ppValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGuidValue(
        REFPROPERTYKEY key,
        REFGUID Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGuidValue(
        REFPROPERTYKEY key,
        GUID *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBufferValue(
        REFPROPERTYKEY key,
        BYTE *pValue,
        DWORD cbValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBufferValue(
        REFPROPERTYKEY key,
        BYTE **ppValue,
        DWORD *pcbValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIPortableDeviceValuesValue(
        REFPROPERTYKEY key,
        IPortableDeviceValues *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIPortableDeviceValuesValue(
        REFPROPERTYKEY key,
        IPortableDeviceValues **ppValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIPortableDevicePropVariantCollectionValue(
        REFPROPERTYKEY key,
        IPortableDevicePropVariantCollection *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIPortableDevicePropVariantCollectionValue(
        REFPROPERTYKEY key,
        IPortableDevicePropVariantCollection **ppValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIPortableDeviceKeyCollectionValue(
        REFPROPERTYKEY key,
        IPortableDeviceKeyCollection *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIPortableDeviceKeyCollectionValue(
        REFPROPERTYKEY key,
        IPortableDeviceKeyCollection **ppValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIPortableDeviceValuesCollectionValue(
        REFPROPERTYKEY key,
        IPortableDeviceValuesCollection *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIPortableDeviceValuesCollectionValue(
        REFPROPERTYKEY key,
        IPortableDeviceValuesCollection **ppValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveValue(
        REFPROPERTYKEY key) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyValuesFromPropertyStore(
        IPropertyStore *pStore) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyValuesToPropertyStore(
        IPropertyStore *pStore) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceValues, 0x6848f6f2, 0x3155, 0x4f86, 0xb6,0xf5, 0x26,0x3e,0xee,0xab,0x31,0x43)
#endif
#else
typedef struct IPortableDeviceValuesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceValues *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceValues *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceValues *This);

    /*** IPortableDeviceValues methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IPortableDeviceValues *This,
        DWORD *pcelt);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IPortableDeviceValues *This,
        const DWORD index,
        PROPERTYKEY *pKey,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *SetValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        const PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *SetStringValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        LPCWSTR Value);

    HRESULT (STDMETHODCALLTYPE *GetStringValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        LPWSTR *pValue);

    HRESULT (STDMETHODCALLTYPE *SetUnsignedIntegerValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        const ULONG Value);

    HRESULT (STDMETHODCALLTYPE *GetUnsignedIntegerValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        ULONG *pValue);

    HRESULT (STDMETHODCALLTYPE *SetSignedIntegerValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        const LONG Value);

    HRESULT (STDMETHODCALLTYPE *GetSignedIntegerValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        LONG *pValue);

    HRESULT (STDMETHODCALLTYPE *SetUnsignedLargeIntegerValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        const ULONGLONG Value);

    HRESULT (STDMETHODCALLTYPE *GetUnsignedLargeIntegerValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        ULONGLONG *pValue);

    HRESULT (STDMETHODCALLTYPE *SetSignedLargeIntegerValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        const LONGLONG Value);

    HRESULT (STDMETHODCALLTYPE *GetSignedLargeIntegerValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        LONGLONG *pValue);

    HRESULT (STDMETHODCALLTYPE *SetFloatValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        const FLOAT Value);

    HRESULT (STDMETHODCALLTYPE *GetFloatValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        FLOAT *pValue);

    HRESULT (STDMETHODCALLTYPE *SetErrorValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        const HRESULT Value);

    HRESULT (STDMETHODCALLTYPE *GetErrorValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        HRESULT *pValue);

    HRESULT (STDMETHODCALLTYPE *SetKeyValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        REFPROPERTYKEY Value);

    HRESULT (STDMETHODCALLTYPE *GetKeyValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        PROPERTYKEY *pValue);

    HRESULT (STDMETHODCALLTYPE *SetBoolValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        const WINBOOL Value);

    HRESULT (STDMETHODCALLTYPE *GetBoolValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        WINBOOL *pValue);

    HRESULT (STDMETHODCALLTYPE *SetIUnknownValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        IUnknown *pValue);

    HRESULT (STDMETHODCALLTYPE *GetIUnknownValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        IUnknown **ppValue);

    HRESULT (STDMETHODCALLTYPE *SetGuidValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        REFGUID Value);

    HRESULT (STDMETHODCALLTYPE *GetGuidValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        GUID *pValue);

    HRESULT (STDMETHODCALLTYPE *SetBufferValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        BYTE *pValue,
        DWORD cbValue);

    HRESULT (STDMETHODCALLTYPE *GetBufferValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        BYTE **ppValue,
        DWORD *pcbValue);

    HRESULT (STDMETHODCALLTYPE *SetIPortableDeviceValuesValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        IPortableDeviceValues *pValue);

    HRESULT (STDMETHODCALLTYPE *GetIPortableDeviceValuesValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        IPortableDeviceValues **ppValue);

    HRESULT (STDMETHODCALLTYPE *SetIPortableDevicePropVariantCollectionValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        IPortableDevicePropVariantCollection *pValue);

    HRESULT (STDMETHODCALLTYPE *GetIPortableDevicePropVariantCollectionValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        IPortableDevicePropVariantCollection **ppValue);

    HRESULT (STDMETHODCALLTYPE *SetIPortableDeviceKeyCollectionValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        IPortableDeviceKeyCollection *pValue);

    HRESULT (STDMETHODCALLTYPE *GetIPortableDeviceKeyCollectionValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        IPortableDeviceKeyCollection **ppValue);

    HRESULT (STDMETHODCALLTYPE *SetIPortableDeviceValuesCollectionValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        IPortableDeviceValuesCollection *pValue);

    HRESULT (STDMETHODCALLTYPE *GetIPortableDeviceValuesCollectionValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key,
        IPortableDeviceValuesCollection **ppValue);

    HRESULT (STDMETHODCALLTYPE *RemoveValue)(
        IPortableDeviceValues *This,
        REFPROPERTYKEY key);

    HRESULT (STDMETHODCALLTYPE *CopyValuesFromPropertyStore)(
        IPortableDeviceValues *This,
        IPropertyStore *pStore);

    HRESULT (STDMETHODCALLTYPE *CopyValuesToPropertyStore)(
        IPortableDeviceValues *This,
        IPropertyStore *pStore);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IPortableDeviceValues *This);

    END_INTERFACE
} IPortableDeviceValuesVtbl;

interface IPortableDeviceValues {
    CONST_VTBL IPortableDeviceValuesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceValues_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceValues_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceValues_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceValues methods ***/
#define IPortableDeviceValues_GetCount(This,pcelt) (This)->lpVtbl->GetCount(This,pcelt)
#define IPortableDeviceValues_GetAt(This,index,pKey,pValue) (This)->lpVtbl->GetAt(This,index,pKey,pValue)
#define IPortableDeviceValues_SetValue(This,key,pValue) (This)->lpVtbl->SetValue(This,key,pValue)
#define IPortableDeviceValues_GetValue(This,key,pValue) (This)->lpVtbl->GetValue(This,key,pValue)
#define IPortableDeviceValues_SetStringValue(This,key,Value) (This)->lpVtbl->SetStringValue(This,key,Value)
#define IPortableDeviceValues_GetStringValue(This,key,pValue) (This)->lpVtbl->GetStringValue(This,key,pValue)
#define IPortableDeviceValues_SetUnsignedIntegerValue(This,key,Value) (This)->lpVtbl->SetUnsignedIntegerValue(This,key,Value)
#define IPortableDeviceValues_GetUnsignedIntegerValue(This,key,pValue) (This)->lpVtbl->GetUnsignedIntegerValue(This,key,pValue)
#define IPortableDeviceValues_SetSignedIntegerValue(This,key,Value) (This)->lpVtbl->SetSignedIntegerValue(This,key,Value)
#define IPortableDeviceValues_GetSignedIntegerValue(This,key,pValue) (This)->lpVtbl->GetSignedIntegerValue(This,key,pValue)
#define IPortableDeviceValues_SetUnsignedLargeIntegerValue(This,key,Value) (This)->lpVtbl->SetUnsignedLargeIntegerValue(This,key,Value)
#define IPortableDeviceValues_GetUnsignedLargeIntegerValue(This,key,pValue) (This)->lpVtbl->GetUnsignedLargeIntegerValue(This,key,pValue)
#define IPortableDeviceValues_SetSignedLargeIntegerValue(This,key,Value) (This)->lpVtbl->SetSignedLargeIntegerValue(This,key,Value)
#define IPortableDeviceValues_GetSignedLargeIntegerValue(This,key,pValue) (This)->lpVtbl->GetSignedLargeIntegerValue(This,key,pValue)
#define IPortableDeviceValues_SetFloatValue(This,key,Value) (This)->lpVtbl->SetFloatValue(This,key,Value)
#define IPortableDeviceValues_GetFloatValue(This,key,pValue) (This)->lpVtbl->GetFloatValue(This,key,pValue)
#define IPortableDeviceValues_SetErrorValue(This,key,Value) (This)->lpVtbl->SetErrorValue(This,key,Value)
#define IPortableDeviceValues_GetErrorValue(This,key,pValue) (This)->lpVtbl->GetErrorValue(This,key,pValue)
#define IPortableDeviceValues_SetKeyValue(This,key,Value) (This)->lpVtbl->SetKeyValue(This,key,Value)
#define IPortableDeviceValues_GetKeyValue(This,key,pValue) (This)->lpVtbl->GetKeyValue(This,key,pValue)
#define IPortableDeviceValues_SetBoolValue(This,key,Value) (This)->lpVtbl->SetBoolValue(This,key,Value)
#define IPortableDeviceValues_GetBoolValue(This,key,pValue) (This)->lpVtbl->GetBoolValue(This,key,pValue)
#define IPortableDeviceValues_SetIUnknownValue(This,key,pValue) (This)->lpVtbl->SetIUnknownValue(This,key,pValue)
#define IPortableDeviceValues_GetIUnknownValue(This,key,ppValue) (This)->lpVtbl->GetIUnknownValue(This,key,ppValue)
#define IPortableDeviceValues_SetGuidValue(This,key,Value) (This)->lpVtbl->SetGuidValue(This,key,Value)
#define IPortableDeviceValues_GetGuidValue(This,key,pValue) (This)->lpVtbl->GetGuidValue(This,key,pValue)
#define IPortableDeviceValues_SetBufferValue(This,key,pValue,cbValue) (This)->lpVtbl->SetBufferValue(This,key,pValue,cbValue)
#define IPortableDeviceValues_GetBufferValue(This,key,ppValue,pcbValue) (This)->lpVtbl->GetBufferValue(This,key,ppValue,pcbValue)
#define IPortableDeviceValues_SetIPortableDeviceValuesValue(This,key,pValue) (This)->lpVtbl->SetIPortableDeviceValuesValue(This,key,pValue)
#define IPortableDeviceValues_GetIPortableDeviceValuesValue(This,key,ppValue) (This)->lpVtbl->GetIPortableDeviceValuesValue(This,key,ppValue)
#define IPortableDeviceValues_SetIPortableDevicePropVariantCollectionValue(This,key,pValue) (This)->lpVtbl->SetIPortableDevicePropVariantCollectionValue(This,key,pValue)
#define IPortableDeviceValues_GetIPortableDevicePropVariantCollectionValue(This,key,ppValue) (This)->lpVtbl->GetIPortableDevicePropVariantCollectionValue(This,key,ppValue)
#define IPortableDeviceValues_SetIPortableDeviceKeyCollectionValue(This,key,pValue) (This)->lpVtbl->SetIPortableDeviceKeyCollectionValue(This,key,pValue)
#define IPortableDeviceValues_GetIPortableDeviceKeyCollectionValue(This,key,ppValue) (This)->lpVtbl->GetIPortableDeviceKeyCollectionValue(This,key,ppValue)
#define IPortableDeviceValues_SetIPortableDeviceValuesCollectionValue(This,key,pValue) (This)->lpVtbl->SetIPortableDeviceValuesCollectionValue(This,key,pValue)
#define IPortableDeviceValues_GetIPortableDeviceValuesCollectionValue(This,key,ppValue) (This)->lpVtbl->GetIPortableDeviceValuesCollectionValue(This,key,ppValue)
#define IPortableDeviceValues_RemoveValue(This,key) (This)->lpVtbl->RemoveValue(This,key)
#define IPortableDeviceValues_CopyValuesFromPropertyStore(This,pStore) (This)->lpVtbl->CopyValuesFromPropertyStore(This,pStore)
#define IPortableDeviceValues_CopyValuesToPropertyStore(This,pStore) (This)->lpVtbl->CopyValuesToPropertyStore(This,pStore)
#define IPortableDeviceValues_Clear(This) (This)->lpVtbl->Clear(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceValues_QueryInterface(IPortableDeviceValues* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceValues_AddRef(IPortableDeviceValues* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceValues_Release(IPortableDeviceValues* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceValues methods ***/
static inline HRESULT IPortableDeviceValues_GetCount(IPortableDeviceValues* This,DWORD *pcelt) {
    return This->lpVtbl->GetCount(This,pcelt);
}
static inline HRESULT IPortableDeviceValues_GetAt(IPortableDeviceValues* This,const DWORD index,PROPERTYKEY *pKey,PROPVARIANT *pValue) {
    return This->lpVtbl->GetAt(This,index,pKey,pValue);
}
static inline HRESULT IPortableDeviceValues_SetValue(IPortableDeviceValues* This,REFPROPERTYKEY key,const PROPVARIANT *pValue) {
    return This->lpVtbl->SetValue(This,key,pValue);
}
static inline HRESULT IPortableDeviceValues_GetValue(IPortableDeviceValues* This,REFPROPERTYKEY key,PROPVARIANT *pValue) {
    return This->lpVtbl->GetValue(This,key,pValue);
}
static inline HRESULT IPortableDeviceValues_SetStringValue(IPortableDeviceValues* This,REFPROPERTYKEY key,LPCWSTR Value) {
    return This->lpVtbl->SetStringValue(This,key,Value);
}
static inline HRESULT IPortableDeviceValues_GetStringValue(IPortableDeviceValues* This,REFPROPERTYKEY key,LPWSTR *pValue) {
    return This->lpVtbl->GetStringValue(This,key,pValue);
}
static inline HRESULT IPortableDeviceValues_SetUnsignedIntegerValue(IPortableDeviceValues* This,REFPROPERTYKEY key,const ULONG Value) {
    return This->lpVtbl->SetUnsignedIntegerValue(This,key,Value);
}
static inline HRESULT IPortableDeviceValues_GetUnsignedIntegerValue(IPortableDeviceValues* This,REFPROPERTYKEY key,ULONG *pValue) {
    return This->lpVtbl->GetUnsignedIntegerValue(This,key,pValue);
}
static inline HRESULT IPortableDeviceValues_SetSignedIntegerValue(IPortableDeviceValues* This,REFPROPERTYKEY key,const LONG Value) {
    return This->lpVtbl->SetSignedIntegerValue(This,key,Value);
}
static inline HRESULT IPortableDeviceValues_GetSignedIntegerValue(IPortableDeviceValues* This,REFPROPERTYKEY key,LONG *pValue) {
    return This->lpVtbl->GetSignedIntegerValue(This,key,pValue);
}
static inline HRESULT IPortableDeviceValues_SetUnsignedLargeIntegerValue(IPortableDeviceValues* This,REFPROPERTYKEY key,const ULONGLONG Value) {
    return This->lpVtbl->SetUnsignedLargeIntegerValue(This,key,Value);
}
static inline HRESULT IPortableDeviceValues_GetUnsignedLargeIntegerValue(IPortableDeviceValues* This,REFPROPERTYKEY key,ULONGLONG *pValue) {
    return This->lpVtbl->GetUnsignedLargeIntegerValue(This,key,pValue);
}
static inline HRESULT IPortableDeviceValues_SetSignedLargeIntegerValue(IPortableDeviceValues* This,REFPROPERTYKEY key,const LONGLONG Value) {
    return This->lpVtbl->SetSignedLargeIntegerValue(This,key,Value);
}
static inline HRESULT IPortableDeviceValues_GetSignedLargeIntegerValue(IPortableDeviceValues* This,REFPROPERTYKEY key,LONGLONG *pValue) {
    return This->lpVtbl->GetSignedLargeIntegerValue(This,key,pValue);
}
static inline HRESULT IPortableDeviceValues_SetFloatValue(IPortableDeviceValues* This,REFPROPERTYKEY key,const FLOAT Value) {
    return This->lpVtbl->SetFloatValue(This,key,Value);
}
static inline HRESULT IPortableDeviceValues_GetFloatValue(IPortableDeviceValues* This,REFPROPERTYKEY key,FLOAT *pValue) {
    return This->lpVtbl->GetFloatValue(This,key,pValue);
}
static inline HRESULT IPortableDeviceValues_SetErrorValue(IPortableDeviceValues* This,REFPROPERTYKEY key,const HRESULT Value) {
    return This->lpVtbl->SetErrorValue(This,key,Value);
}
static inline HRESULT IPortableDeviceValues_GetErrorValue(IPortableDeviceValues* This,REFPROPERTYKEY key,HRESULT *pValue) {
    return This->lpVtbl->GetErrorValue(This,key,pValue);
}
static inline HRESULT IPortableDeviceValues_SetKeyValue(IPortableDeviceValues* This,REFPROPERTYKEY key,REFPROPERTYKEY Value) {
    return This->lpVtbl->SetKeyValue(This,key,Value);
}
static inline HRESULT IPortableDeviceValues_GetKeyValue(IPortableDeviceValues* This,REFPROPERTYKEY key,PROPERTYKEY *pValue) {
    return This->lpVtbl->GetKeyValue(This,key,pValue);
}
static inline HRESULT IPortableDeviceValues_SetBoolValue(IPortableDeviceValues* This,REFPROPERTYKEY key,const WINBOOL Value) {
    return This->lpVtbl->SetBoolValue(This,key,Value);
}
static inline HRESULT IPortableDeviceValues_GetBoolValue(IPortableDeviceValues* This,REFPROPERTYKEY key,WINBOOL *pValue) {
    return This->lpVtbl->GetBoolValue(This,key,pValue);
}
static inline HRESULT IPortableDeviceValues_SetIUnknownValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IUnknown *pValue) {
    return This->lpVtbl->SetIUnknownValue(This,key,pValue);
}
static inline HRESULT IPortableDeviceValues_GetIUnknownValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IUnknown **ppValue) {
    return This->lpVtbl->GetIUnknownValue(This,key,ppValue);
}
static inline HRESULT IPortableDeviceValues_SetGuidValue(IPortableDeviceValues* This,REFPROPERTYKEY key,REFGUID Value) {
    return This->lpVtbl->SetGuidValue(This,key,Value);
}
static inline HRESULT IPortableDeviceValues_GetGuidValue(IPortableDeviceValues* This,REFPROPERTYKEY key,GUID *pValue) {
    return This->lpVtbl->GetGuidValue(This,key,pValue);
}
static inline HRESULT IPortableDeviceValues_SetBufferValue(IPortableDeviceValues* This,REFPROPERTYKEY key,BYTE *pValue,DWORD cbValue) {
    return This->lpVtbl->SetBufferValue(This,key,pValue,cbValue);
}
static inline HRESULT IPortableDeviceValues_GetBufferValue(IPortableDeviceValues* This,REFPROPERTYKEY key,BYTE **ppValue,DWORD *pcbValue) {
    return This->lpVtbl->GetBufferValue(This,key,ppValue,pcbValue);
}
static inline HRESULT IPortableDeviceValues_SetIPortableDeviceValuesValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IPortableDeviceValues *pValue) {
    return This->lpVtbl->SetIPortableDeviceValuesValue(This,key,pValue);
}
static inline HRESULT IPortableDeviceValues_GetIPortableDeviceValuesValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IPortableDeviceValues **ppValue) {
    return This->lpVtbl->GetIPortableDeviceValuesValue(This,key,ppValue);
}
static inline HRESULT IPortableDeviceValues_SetIPortableDevicePropVariantCollectionValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IPortableDevicePropVariantCollection *pValue) {
    return This->lpVtbl->SetIPortableDevicePropVariantCollectionValue(This,key,pValue);
}
static inline HRESULT IPortableDeviceValues_GetIPortableDevicePropVariantCollectionValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IPortableDevicePropVariantCollection **ppValue) {
    return This->lpVtbl->GetIPortableDevicePropVariantCollectionValue(This,key,ppValue);
}
static inline HRESULT IPortableDeviceValues_SetIPortableDeviceKeyCollectionValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IPortableDeviceKeyCollection *pValue) {
    return This->lpVtbl->SetIPortableDeviceKeyCollectionValue(This,key,pValue);
}
static inline HRESULT IPortableDeviceValues_GetIPortableDeviceKeyCollectionValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IPortableDeviceKeyCollection **ppValue) {
    return This->lpVtbl->GetIPortableDeviceKeyCollectionValue(This,key,ppValue);
}
static inline HRESULT IPortableDeviceValues_SetIPortableDeviceValuesCollectionValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IPortableDeviceValuesCollection *pValue) {
    return This->lpVtbl->SetIPortableDeviceValuesCollectionValue(This,key,pValue);
}
static inline HRESULT IPortableDeviceValues_GetIPortableDeviceValuesCollectionValue(IPortableDeviceValues* This,REFPROPERTYKEY key,IPortableDeviceValuesCollection **ppValue) {
    return This->lpVtbl->GetIPortableDeviceValuesCollectionValue(This,key,ppValue);
}
static inline HRESULT IPortableDeviceValues_RemoveValue(IPortableDeviceValues* This,REFPROPERTYKEY key) {
    return This->lpVtbl->RemoveValue(This,key);
}
static inline HRESULT IPortableDeviceValues_CopyValuesFromPropertyStore(IPortableDeviceValues* This,IPropertyStore *pStore) {
    return This->lpVtbl->CopyValuesFromPropertyStore(This,pStore);
}
static inline HRESULT IPortableDeviceValues_CopyValuesToPropertyStore(IPortableDeviceValues* This,IPropertyStore *pStore) {
    return This->lpVtbl->CopyValuesToPropertyStore(This,pStore);
}
static inline HRESULT IPortableDeviceValues_Clear(IPortableDeviceValues* This) {
    return This->lpVtbl->Clear(This);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceValues_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceKeyCollection interface
 */
#ifndef __IPortableDeviceKeyCollection_INTERFACE_DEFINED__
#define __IPortableDeviceKeyCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceKeyCollection, 0xdada2357, 0xe0ad, 0x492e, 0x98,0xdb, 0xdd,0x61,0xc5,0x3b,0xa3,0x53);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dada2357-e0ad-492e-98db-dd61c53ba353")
IPortableDeviceKeyCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        DWORD *pcElems) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        const DWORD dwIndex,
        PROPERTYKEY *pKey) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        REFPROPERTYKEY Key) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        const DWORD dwIndex) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceKeyCollection, 0xdada2357, 0xe0ad, 0x492e, 0x98,0xdb, 0xdd,0x61,0xc5,0x3b,0xa3,0x53)
#endif
#else
typedef struct IPortableDeviceKeyCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceKeyCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceKeyCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceKeyCollection *This);

    /*** IPortableDeviceKeyCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IPortableDeviceKeyCollection *This,
        DWORD *pcElems);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IPortableDeviceKeyCollection *This,
        const DWORD dwIndex,
        PROPERTYKEY *pKey);

    HRESULT (STDMETHODCALLTYPE *Add)(
        IPortableDeviceKeyCollection *This,
        REFPROPERTYKEY Key);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IPortableDeviceKeyCollection *This);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IPortableDeviceKeyCollection *This,
        const DWORD dwIndex);

    END_INTERFACE
} IPortableDeviceKeyCollectionVtbl;

interface IPortableDeviceKeyCollection {
    CONST_VTBL IPortableDeviceKeyCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceKeyCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceKeyCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceKeyCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceKeyCollection methods ***/
#define IPortableDeviceKeyCollection_GetCount(This,pcElems) (This)->lpVtbl->GetCount(This,pcElems)
#define IPortableDeviceKeyCollection_GetAt(This,dwIndex,pKey) (This)->lpVtbl->GetAt(This,dwIndex,pKey)
#define IPortableDeviceKeyCollection_Add(This,Key) (This)->lpVtbl->Add(This,Key)
#define IPortableDeviceKeyCollection_Clear(This) (This)->lpVtbl->Clear(This)
#define IPortableDeviceKeyCollection_RemoveAt(This,dwIndex) (This)->lpVtbl->RemoveAt(This,dwIndex)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceKeyCollection_QueryInterface(IPortableDeviceKeyCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceKeyCollection_AddRef(IPortableDeviceKeyCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceKeyCollection_Release(IPortableDeviceKeyCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceKeyCollection methods ***/
static inline HRESULT IPortableDeviceKeyCollection_GetCount(IPortableDeviceKeyCollection* This,DWORD *pcElems) {
    return This->lpVtbl->GetCount(This,pcElems);
}
static inline HRESULT IPortableDeviceKeyCollection_GetAt(IPortableDeviceKeyCollection* This,const DWORD dwIndex,PROPERTYKEY *pKey) {
    return This->lpVtbl->GetAt(This,dwIndex,pKey);
}
static inline HRESULT IPortableDeviceKeyCollection_Add(IPortableDeviceKeyCollection* This,REFPROPERTYKEY Key) {
    return This->lpVtbl->Add(This,Key);
}
static inline HRESULT IPortableDeviceKeyCollection_Clear(IPortableDeviceKeyCollection* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT IPortableDeviceKeyCollection_RemoveAt(IPortableDeviceKeyCollection* This,const DWORD dwIndex) {
    return This->lpVtbl->RemoveAt(This,dwIndex);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceKeyCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDevicePropVariantCollection interface
 */
#ifndef __IPortableDevicePropVariantCollection_INTERFACE_DEFINED__
#define __IPortableDevicePropVariantCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDevicePropVariantCollection, 0x89b2e422, 0x4f1b, 0x4316, 0xbc,0xef, 0xa4,0x4a,0xfe,0xa8,0x3e,0xb3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("89b2e422-4f1b-4316-bcef-a44afea83eb3")
IPortableDevicePropVariantCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        DWORD *pcElems) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        const DWORD dwIndex,
        PROPVARIANT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        const PROPVARIANT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetType(
        VARTYPE *pvt) = 0;

    virtual HRESULT STDMETHODCALLTYPE ChangeType(
        const VARTYPE vt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        const DWORD dwIndex) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDevicePropVariantCollection, 0x89b2e422, 0x4f1b, 0x4316, 0xbc,0xef, 0xa4,0x4a,0xfe,0xa8,0x3e,0xb3)
#endif
#else
typedef struct IPortableDevicePropVariantCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDevicePropVariantCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDevicePropVariantCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDevicePropVariantCollection *This);

    /*** IPortableDevicePropVariantCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IPortableDevicePropVariantCollection *This,
        DWORD *pcElems);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IPortableDevicePropVariantCollection *This,
        const DWORD dwIndex,
        PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *Add)(
        IPortableDevicePropVariantCollection *This,
        const PROPVARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IPortableDevicePropVariantCollection *This,
        VARTYPE *pvt);

    HRESULT (STDMETHODCALLTYPE *ChangeType)(
        IPortableDevicePropVariantCollection *This,
        const VARTYPE vt);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IPortableDevicePropVariantCollection *This);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IPortableDevicePropVariantCollection *This,
        const DWORD dwIndex);

    END_INTERFACE
} IPortableDevicePropVariantCollectionVtbl;

interface IPortableDevicePropVariantCollection {
    CONST_VTBL IPortableDevicePropVariantCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDevicePropVariantCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDevicePropVariantCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDevicePropVariantCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDevicePropVariantCollection methods ***/
#define IPortableDevicePropVariantCollection_GetCount(This,pcElems) (This)->lpVtbl->GetCount(This,pcElems)
#define IPortableDevicePropVariantCollection_GetAt(This,dwIndex,pValue) (This)->lpVtbl->GetAt(This,dwIndex,pValue)
#define IPortableDevicePropVariantCollection_Add(This,pValue) (This)->lpVtbl->Add(This,pValue)
#define IPortableDevicePropVariantCollection_GetType(This,pvt) (This)->lpVtbl->GetType(This,pvt)
#define IPortableDevicePropVariantCollection_ChangeType(This,vt) (This)->lpVtbl->ChangeType(This,vt)
#define IPortableDevicePropVariantCollection_Clear(This) (This)->lpVtbl->Clear(This)
#define IPortableDevicePropVariantCollection_RemoveAt(This,dwIndex) (This)->lpVtbl->RemoveAt(This,dwIndex)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDevicePropVariantCollection_QueryInterface(IPortableDevicePropVariantCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDevicePropVariantCollection_AddRef(IPortableDevicePropVariantCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDevicePropVariantCollection_Release(IPortableDevicePropVariantCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDevicePropVariantCollection methods ***/
static inline HRESULT IPortableDevicePropVariantCollection_GetCount(IPortableDevicePropVariantCollection* This,DWORD *pcElems) {
    return This->lpVtbl->GetCount(This,pcElems);
}
static inline HRESULT IPortableDevicePropVariantCollection_GetAt(IPortableDevicePropVariantCollection* This,const DWORD dwIndex,PROPVARIANT *pValue) {
    return This->lpVtbl->GetAt(This,dwIndex,pValue);
}
static inline HRESULT IPortableDevicePropVariantCollection_Add(IPortableDevicePropVariantCollection* This,const PROPVARIANT *pValue) {
    return This->lpVtbl->Add(This,pValue);
}
static inline HRESULT IPortableDevicePropVariantCollection_GetType(IPortableDevicePropVariantCollection* This,VARTYPE *pvt) {
    return This->lpVtbl->GetType(This,pvt);
}
static inline HRESULT IPortableDevicePropVariantCollection_ChangeType(IPortableDevicePropVariantCollection* This,const VARTYPE vt) {
    return This->lpVtbl->ChangeType(This,vt);
}
static inline HRESULT IPortableDevicePropVariantCollection_Clear(IPortableDevicePropVariantCollection* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT IPortableDevicePropVariantCollection_RemoveAt(IPortableDevicePropVariantCollection* This,const DWORD dwIndex) {
    return This->lpVtbl->RemoveAt(This,dwIndex);
}
#endif
#endif

#endif


#endif  /* __IPortableDevicePropVariantCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceValuesCollection interface
 */
#ifndef __IPortableDeviceValuesCollection_INTERFACE_DEFINED__
#define __IPortableDeviceValuesCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceValuesCollection, 0x6e3f2d79, 0x4e07, 0x48c4, 0x82,0x08, 0xd8,0xc2,0xe5,0xaf,0x4a,0x99);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6e3f2d79-4e07-48c4-8208-d8c2e5af4a99")
IPortableDeviceValuesCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        DWORD *pcElems) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        DWORD dwIndex,
        IPortableDeviceValues **ppValues) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        IPortableDeviceValues *pValues) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        const DWORD dwIndex) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceValuesCollection, 0x6e3f2d79, 0x4e07, 0x48c4, 0x82,0x08, 0xd8,0xc2,0xe5,0xaf,0x4a,0x99)
#endif
#else
typedef struct IPortableDeviceValuesCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceValuesCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceValuesCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceValuesCollection *This);

    /*** IPortableDeviceValuesCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IPortableDeviceValuesCollection *This,
        DWORD *pcElems);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IPortableDeviceValuesCollection *This,
        DWORD dwIndex,
        IPortableDeviceValues **ppValues);

    HRESULT (STDMETHODCALLTYPE *Add)(
        IPortableDeviceValuesCollection *This,
        IPortableDeviceValues *pValues);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IPortableDeviceValuesCollection *This);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IPortableDeviceValuesCollection *This,
        const DWORD dwIndex);

    END_INTERFACE
} IPortableDeviceValuesCollectionVtbl;

interface IPortableDeviceValuesCollection {
    CONST_VTBL IPortableDeviceValuesCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceValuesCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceValuesCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceValuesCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceValuesCollection methods ***/
#define IPortableDeviceValuesCollection_GetCount(This,pcElems) (This)->lpVtbl->GetCount(This,pcElems)
#define IPortableDeviceValuesCollection_GetAt(This,dwIndex,ppValues) (This)->lpVtbl->GetAt(This,dwIndex,ppValues)
#define IPortableDeviceValuesCollection_Add(This,pValues) (This)->lpVtbl->Add(This,pValues)
#define IPortableDeviceValuesCollection_Clear(This) (This)->lpVtbl->Clear(This)
#define IPortableDeviceValuesCollection_RemoveAt(This,dwIndex) (This)->lpVtbl->RemoveAt(This,dwIndex)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceValuesCollection_QueryInterface(IPortableDeviceValuesCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceValuesCollection_AddRef(IPortableDeviceValuesCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceValuesCollection_Release(IPortableDeviceValuesCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceValuesCollection methods ***/
static inline HRESULT IPortableDeviceValuesCollection_GetCount(IPortableDeviceValuesCollection* This,DWORD *pcElems) {
    return This->lpVtbl->GetCount(This,pcElems);
}
static inline HRESULT IPortableDeviceValuesCollection_GetAt(IPortableDeviceValuesCollection* This,DWORD dwIndex,IPortableDeviceValues **ppValues) {
    return This->lpVtbl->GetAt(This,dwIndex,ppValues);
}
static inline HRESULT IPortableDeviceValuesCollection_Add(IPortableDeviceValuesCollection* This,IPortableDeviceValues *pValues) {
    return This->lpVtbl->Add(This,pValues);
}
static inline HRESULT IPortableDeviceValuesCollection_Clear(IPortableDeviceValuesCollection* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT IPortableDeviceValuesCollection_RemoveAt(IPortableDeviceValuesCollection* This,const DWORD dwIndex) {
    return This->lpVtbl->RemoveAt(This,dwIndex);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceValuesCollection_INTERFACE_DEFINED__ */

#ifndef __PortableDeviceTypesLib_LIBRARY_DEFINED__
#define __PortableDeviceTypesLib_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_PortableDeviceTypesLib, 0x2b00ba2f, 0xe750, 0x4beb, 0x92,0x35, 0x97,0x14,0x2e,0xde,0x1d,0x3e);

/*****************************************************************************
 * WpdSerializer coclass
 */

DEFINE_GUID(CLSID_WpdSerializer, 0x0b91a74b, 0xad7c, 0x4a9d, 0xb5,0x63, 0x29,0xee,0xf9,0x16,0x71,0x72);

#ifdef __cplusplus
class DECLSPEC_UUID("0b91a74b-ad7c-4a9d-b563-29eef9167172") WpdSerializer;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WpdSerializer, 0x0b91a74b, 0xad7c, 0x4a9d, 0xb5,0x63, 0x29,0xee,0xf9,0x16,0x71,0x72)
#endif
#endif

/*****************************************************************************
 * PortableDeviceValues coclass
 */

DEFINE_GUID(CLSID_PortableDeviceValues, 0x0c15d503, 0xd017, 0x47ce, 0x90,0x16, 0x7b,0x3f,0x97,0x87,0x21,0xcc);

#ifdef __cplusplus
class DECLSPEC_UUID("0c15d503-d017-47ce-9016-7b3f978721cc") PortableDeviceValues;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PortableDeviceValues, 0x0c15d503, 0xd017, 0x47ce, 0x90,0x16, 0x7b,0x3f,0x97,0x87,0x21,0xcc)
#endif
#endif

/*****************************************************************************
 * PortableDeviceKeyCollection coclass
 */

DEFINE_GUID(CLSID_PortableDeviceKeyCollection, 0xde2d022d, 0x2480, 0x43be, 0x97,0xf0, 0xd1,0xfa,0x2c,0xf9,0x8f,0x4f);

#ifdef __cplusplus
class DECLSPEC_UUID("de2d022d-2480-43be-97f0-d1fa2cf98f4f") PortableDeviceKeyCollection;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PortableDeviceKeyCollection, 0xde2d022d, 0x2480, 0x43be, 0x97,0xf0, 0xd1,0xfa,0x2c,0xf9,0x8f,0x4f)
#endif
#endif

/*****************************************************************************
 * PortableDevicePropVariantCollection coclass
 */

DEFINE_GUID(CLSID_PortableDevicePropVariantCollection, 0x08a99e2f, 0x6d6d, 0x4b80, 0xaf,0x5a, 0xba,0xf2,0xbc,0xbe,0x4c,0xb9);

#ifdef __cplusplus
class DECLSPEC_UUID("08a99e2f-6d6d-4b80-af5a-baf2bcbe4cb9") PortableDevicePropVariantCollection;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PortableDevicePropVariantCollection, 0x08a99e2f, 0x6d6d, 0x4b80, 0xaf,0x5a, 0xba,0xf2,0xbc,0xbe,0x4c,0xb9)
#endif
#endif

/*****************************************************************************
 * PortableDeviceValuesCollection coclass
 */

DEFINE_GUID(CLSID_PortableDeviceValuesCollection, 0x3882134d, 0x14cf, 0x4220, 0x9c,0xb4, 0x43,0x5f,0x86,0xd8,0x3f,0x60);

#ifdef __cplusplus
class DECLSPEC_UUID("3882134d-14cf-4220-9cb4-435f86d83f60") PortableDeviceValuesCollection;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PortableDeviceValuesCollection, 0x3882134d, 0x14cf, 0x4220, 0x9c,0xb4, 0x43,0x5f,0x86,0xd8,0x3f,0x60)
#endif
#endif

#endif /* __PortableDeviceTypesLib_LIBRARY_DEFINED__ */
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER LPSAFEARRAY_UserSize     (ULONG *, ULONG, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserMarshal  (ULONG *, unsigned char *, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserUnmarshal(ULONG *, unsigned char *, LPSAFEARRAY *);
void            __RPC_USER LPSAFEARRAY_UserFree     (ULONG *, LPSAFEARRAY *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __portabledevicetypes_h__ */
