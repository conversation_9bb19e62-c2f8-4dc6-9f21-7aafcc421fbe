/*
 * Copyright 2021 R<PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "inspectable.idl";
import "asyncinfo.idl";
import "windowscontracts.idl";
import "eventtoken.idl";
import "ivectorchangedeventargs.idl";

namespace Windows {
    namespace Foundation {

cpp_quote("#ifdef __cplusplus")
cpp_quote("} /* extern \"C\" */")
cpp_quote("namespace ABI { namespace Windows { namespace Foundation { namespace Internal {")
cpp_quote("template <class T> struct GetAbiType { typedef T type; };")
cpp_quote("template <class T> struct GetLogicalType { typedef T type; };")
cpp_quote("template <class L, class A> struct AggregateType {};")
cpp_quote("template <class L, class A> struct GetAbiType<AggregateType<L, A> > { typedef A type; };")
cpp_quote("template <class L, class A> struct GetLogicalType<AggregateType<L, A> > { typedef L type; };")
cpp_quote("}}}}")
cpp_quote("extern \"C\" {")
cpp_quote("#endif")

#ifdef __WIDL__
        [
            contract(Windows.Foundation.FoundationContract, 1.0),
            uuid(9de1c535-6ae1-11e0-84e1-18a905bcc53f)
        ]
        delegate HRESULT EventHandler<T>([in] IInspectable *sender, [in] T args);

        interface IAsyncOperation<TResult>;

        [
            contract(Windows.Foundation.FoundationContract, 1.0),
            uuid(fcdcf02c-e5d8-4478-915a-4d90b74b83a5)
        ]
        delegate HRESULT AsyncOperationCompletedHandler<TResult>([in] Windows.Foundation.IAsyncOperation<TResult> *info,
                                                                 [in] AsyncStatus status);

        [
            contract(Windows.Foundation.FoundationContract, 1.0),
            uuid(9fc2b0bb-e446-44e2-aa61-9cab8f636af2)
        ]
        interface IAsyncOperation<TResult> : IInspectable
        {
            [propput] HRESULT Completed([in] Windows.Foundation.AsyncOperationCompletedHandler<TResult> *handler);
            [propget] HRESULT Completed([out, retval] Windows.Foundation.AsyncOperationCompletedHandler<TResult> **handler);
            HRESULT GetResults([out, retval] TResult *results);
        }

        interface IAsyncActionWithProgress<TProgress>;

        [
            contract(Windows.Foundation.FoundationContract, 1.0),
            uuid(6d844858-0cff-4590-ae89-95a5a5c8b4b8)
        ]
        delegate HRESULT AsyncActionProgressHandler<TProgress>([in] Windows.Foundation.IAsyncActionWithProgress<TProgress> *info,
                                                               [in] TProgress progress);
        [
            contract(Windows.Foundation.FoundationContract, 1.0),
            uuid(9c029f91-cc84-44fd-ac26-0a6c4e555281)
        ]
        delegate HRESULT AsyncActionWithProgressCompletedHandler<TProgress>([in] Windows.Foundation.IAsyncActionWithProgress<TProgress> *info,
                                                                            [in] AsyncStatus status);

        [
            contract(Windows.Foundation.FoundationContract, 1.0),
            uuid(1f6db258-e803-48a1-9546-eb7353398884)
        ]
        interface IAsyncActionWithProgress<TProgress> : IInspectable
        {
            [propput] HRESULT Progress([in] Windows.Foundation.AsyncActionProgressHandler<TProgress> *handler);
            [propget] HRESULT Progress([out, retval] Windows.Foundation.AsyncActionProgressHandler<TProgress> **handler);
            [propput] HRESULT Completed([in] Windows.Foundation.AsyncActionWithProgressCompletedHandler<TProgress> *handler);
            [propget] HRESULT Completed([out, retval] Windows.Foundation.AsyncActionWithProgressCompletedHandler<TProgress> **handler);
            HRESULT GetResults();
        }

        interface IAsyncOperationWithProgress<TResult, TProgress>;

        [
            contract(Windows.Foundation.FoundationContract, 1.0),
            uuid(55690902-0aab-421a-8778-f8ce5026d758)
        ]
        delegate HRESULT AsyncOperationProgressHandler<TResult, TProgress>([in] Windows.Foundation.IAsyncOperationWithProgress<TResult, TProgress> *info,
                                                                           [in] TProgress progress);
        [
            contract(Windows.Foundation.FoundationContract, 1.0),
            uuid(e85df41d-6aa7-46e3-a8e2-f009d840c627)
        ]
        delegate HRESULT AsyncOperationWithProgressCompletedHandler<TResult, TProgress>([in] Windows.Foundation.IAsyncOperationWithProgress<TResult, TProgress> *info,
                                                                                        [in] AsyncStatus status);

        [
            contract(Windows.Foundation.FoundationContract, 1.0),
            uuid(b5d036d7-e297-498f-ba60-0289e76e23dd)
        ]
        interface IAsyncOperationWithProgress<TResult, TProgress> : IInspectable
        {
            [propput] HRESULT Progress([in] Windows.Foundation.AsyncOperationProgressHandler<TResult, TProgress> *handler);
            [propget] HRESULT Progress([out, retval] Windows.Foundation.AsyncOperationProgressHandler<TResult, TProgress> **handler);
            [propput] HRESULT Completed([in] Windows.Foundation.AsyncOperationWithProgressCompletedHandler<TResult, TProgress> *handler);
            [propget] HRESULT Completed([out, retval] Windows.Foundation.AsyncOperationWithProgressCompletedHandler<TResult, TProgress> **handler);
            HRESULT GetResults([out, retval] TResult *results);
        }

        [
            contract(Windows.Foundation.FoundationContract, 1.0),
            uuid(9de1c534-6ae1-11e0-84e1-18a905bcc53f)
        ]
        delegate HRESULT TypedEventHandler<TSender, TArgs>([in] TSender sender, [in] TArgs args);

        [
            contract(Windows.Foundation.FoundationContract, 1.0),
            uuid(61c17706-2d65-11e0-9ae8-d48564015472)
        ]
        interface IReference<T> : IInspectable
        {
            [propget] HRESULT Value([out, retval] T *value);
        }

        namespace Collections
        {
            [
                contract(Windows.Foundation.FoundationContract, 1.0),
                uuid(6a79e863-4300-459a-9966-cbb660963ee1)
            ]
            interface IIterator<T> : IInspectable
            {
                [propget] HRESULT Current([out, retval] T *value);
                [propget] HRESULT HasCurrent([out, retval] boolean *value);
                HRESULT MoveNext([out, retval] boolean *value);
                HRESULT GetMany([in] UINT32 items_size, [out] T *items, [out, retval] UINT32 *value);
            }

            [
                contract(Windows.Foundation.FoundationContract, 1.0),
                uuid(faa585ea-6214-4217-afda-7f46de5869b3)
            ]
            interface IIterable<T> : IInspectable
            {
                HRESULT First([out, retval] Windows.Foundation.Collections.IIterator<T> **value);
            }

            [
                contract(Windows.Foundation.FoundationContract, 1.0),
                uuid(9939f4df-050a-4c0f-aa60-77075f9c4777)
            ]
            interface IMapChangedEventArgs<T> : IInspectable
            {
                [propget] HRESULT CollectionChanged([out, retval] Windows.Foundation.Collections.CollectionChange *value);
                [propget] HRESULT Key([out, retval] T *key);
            }

            interface IObservableMap<K, V>;

            [
                contract(Windows.Foundation.FoundationContract, 1.0),
                uuid(179517f3-94ee-41f8-bddc-768a895544f3)
            ]
            delegate HRESULT MapChangedEventHandler<K, V>([in] Windows.Foundation.Collections.IObservableMap<K, V> *sender,
                                                          [in] Windows.Foundation.Collections.IMapChangedEventArgs<K> *args);

            [
                contract(Windows.Foundation.FoundationContract, 1.0),
                uuid(02b51929-c1c4-4a7e-8940-0312b5c18500)
            ]
            interface IKeyValuePair<K, V> : IInspectable
            {
                [propget] HRESULT Key([out, retval] K *key);
                [propget] HRESULT Value([out, retval] V *value);
            }

            [
                contract(Windows.Foundation.FoundationContract, 1.0),
                uuid(e480ce40-a338-4ada-adcf-272272e48cb9)
            ]
            interface IMapView<K, V> : IInspectable
                requires Windows.Foundation.Collections.IIterable<Windows.Foundation.Collections.IKeyValuePair<K, V> *>
            {
                HRESULT Lookup([in] K key, [out, retval] V *value);
                [propget] HRESULT Size([out, retval] unsigned int *size);
                HRESULT HasKey([in] K key, [out, retval] boolean *found);
                HRESULT Split([out] Windows.Foundation.Collections.IMapView<K, V> **first,
                              [out] Windows.Foundation.Collections.IMapView<K, V> **second);
            }

            [
                contract(Windows.Foundation.FoundationContract, 1.0),
                uuid(3c2925fe-8519-45c1-aa79-197b6718c1c1)
            ]
            interface IMap<K, V> : IInspectable
                requires Windows.Foundation.Collections.IIterable<Windows.Foundation.Collections.IKeyValuePair<K, V> *>
            {
                HRESULT Lookup([in] K key, [out, retval] V *value);
                [propget] HRESULT Size([out, retval] unsigned int *size);
                HRESULT HasKey([in] K key, [out, retval] boolean *found);
                HRESULT GetView([out, retval] Windows.Foundation.Collections.IMapView<K, V> **view);
                HRESULT Insert([in] K key, [in] V value, [out, retval] boolean *replaced);
                HRESULT Remove([in] K key);
                HRESULT Clear();
            }

            [
                contract(Windows.Foundation.FoundationContract, 1.0),
                uuid(65df2bf5-bf39-41b5-aebc-5a9d865e472b)
            ]
            interface IObservableMap<K, V> : IInspectable
                requires Windows.Foundation.Collections.IMap<K, V>
            {
                [eventadd] HRESULT MapChanged([in] Windows.Foundation.Collections.MapChangedEventHandler<K, V> *handler,
                                              [out, retval] EventRegistrationToken *token);
                [eventremove] HRESULT MapChanged([in] EventRegistrationToken token);
            }

            interface IObservableVector<T>;

            [
                contract(Windows.Foundation.FoundationContract, 1.0),
                uuid(0c051752-9fbf-4c70-aa0c-0e4c82d9a761)
            ]
            delegate HRESULT VectorChangedEventHandler<T>([in] Windows.Foundation.Collections.IObservableVector<T> *sender,
                                                          [in] Windows.Foundation.Collections.IVectorChangedEventArgs *args);

            [
                contract(Windows.Foundation.FoundationContract, 1.0),
                uuid(bbe1fa4c-b0e3-4583-baef-1f1b2e483e56)
            ]
            interface IVectorView<T> : IInspectable
                requires Windows.Foundation.Collections.IIterable<T>
            {
                HRESULT GetAt([in] UINT32 index, [out, retval] T *value);
                [propget] HRESULT Size([out, retval] UINT32 *value);
                HRESULT IndexOf([in, optional] T element, [out] UINT32 *index, [out, retval] BOOLEAN *value);
                HRESULT GetMany([in] UINT32 start_index, [in] UINT32 items_size, [out] T *items, [out, retval] UINT32 *value);
            }

            [
                contract(Windows.Foundation.FoundationContract, 1.0),
                uuid(913337e9-11a1-4345-a3a2-4e7f956e222d)
            ]
            interface IVector<T> : IInspectable
                requires Windows.Foundation.Collections.IIterable<T>
            {
                HRESULT GetAt([in, optional] UINT32 index, [out, retval] T *value);
                [propget] HRESULT Size([out, retval] UINT32 *value);
                HRESULT GetView([out, retval] Windows.Foundation.Collections.IVectorView<T> **value);
                HRESULT IndexOf([in, optional] T element, [out] UINT32 *index, [out, retval] BOOLEAN *value);
                HRESULT SetAt([in] UINT32 index, [in, optional] T value);
                HRESULT InsertAt([in] UINT32 index, [in, optional] T value);
                HRESULT RemoveAt([in] UINT32 index);
                HRESULT Append([in, optional] T value);
                HRESULT RemoveAtEnd();
                HRESULT Clear();
                HRESULT GetMany([in] UINT32 start_index, [in] UINT32 items_size, [out] T *items, [out, retval] UINT32 *value);
                HRESULT ReplaceAll([in] UINT32 count, [in] T *items);
            }

            [
                contract(Windows.Foundation.FoundationContract, 1.0),
                uuid(5917eb53-50b4-4a0d-b309-65862b3f1dbc)
            ]
            interface IObservableVector<T> : IInspectable
                requires Windows.Foundation.Collections.IVector<T>
            {
                [eventadd] HRESULT VectorChanged([in] Windows.Foundation.Collections.VectorChangedEventHandler<T> *handler,
                                              [out, retval] EventRegistrationToken *token);
                [eventremove] HRESULT VectorChanged([in] EventRegistrationToken token);
            }
        }
#endif
    }
}
