/*** Autogenerated by WIDL 10.8 from include/netlistmgr.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __netlistmgr_h__
#define __netlistmgr_h__

/* Forward declarations */

#ifndef __INetworkCostManager_FWD_DEFINED__
#define __INetworkCostManager_FWD_DEFINED__
typedef interface INetworkCostManager INetworkCostManager;
#ifdef __cplusplus
interface INetworkCostManager;
#endif /* __cplusplus */
#endif

#ifndef __INetworkConnectionCost_FWD_DEFINED__
#define __INetworkConnectionCost_FWD_DEFINED__
typedef interface INetworkConnectionCost INetworkConnectionCost;
#ifdef __cplusplus
interface INetworkConnectionCost;
#endif /* __cplusplus */
#endif

#ifndef __INetworkCostManagerEvents_FWD_DEFINED__
#define __INetworkCostManagerEvents_FWD_DEFINED__
typedef interface INetworkCostManagerEvents INetworkCostManagerEvents;
#ifdef __cplusplus
interface INetworkCostManagerEvents;
#endif /* __cplusplus */
#endif

#ifndef __IEnumNetworks_FWD_DEFINED__
#define __IEnumNetworks_FWD_DEFINED__
typedef interface IEnumNetworks IEnumNetworks;
#ifdef __cplusplus
interface IEnumNetworks;
#endif /* __cplusplus */
#endif

#ifndef __IEnumNetworkConnections_FWD_DEFINED__
#define __IEnumNetworkConnections_FWD_DEFINED__
typedef interface IEnumNetworkConnections IEnumNetworkConnections;
#ifdef __cplusplus
interface IEnumNetworkConnections;
#endif /* __cplusplus */
#endif

#ifndef __INetworkListManager_FWD_DEFINED__
#define __INetworkListManager_FWD_DEFINED__
typedef interface INetworkListManager INetworkListManager;
#ifdef __cplusplus
interface INetworkListManager;
#endif /* __cplusplus */
#endif

#ifndef __NetworkListManager_FWD_DEFINED__
#define __NetworkListManager_FWD_DEFINED__
#ifdef __cplusplus
typedef class NetworkListManager NetworkListManager;
#else
typedef struct NetworkListManager NetworkListManager;
#endif /* defined __cplusplus */
#endif /* defined __NetworkListManager_FWD_DEFINED__ */

#ifndef __INetworkListManagerEvents_FWD_DEFINED__
#define __INetworkListManagerEvents_FWD_DEFINED__
typedef interface INetworkListManagerEvents INetworkListManagerEvents;
#ifdef __cplusplus
interface INetworkListManagerEvents;
#endif /* __cplusplus */
#endif

#ifndef __INetworkConnectionEvents_FWD_DEFINED__
#define __INetworkConnectionEvents_FWD_DEFINED__
typedef interface INetworkConnectionEvents INetworkConnectionEvents;
#ifdef __cplusplus
interface INetworkConnectionEvents;
#endif /* __cplusplus */
#endif

#ifndef __INetworkConnection_FWD_DEFINED__
#define __INetworkConnection_FWD_DEFINED__
typedef interface INetworkConnection INetworkConnection;
#ifdef __cplusplus
interface INetworkConnection;
#endif /* __cplusplus */
#endif

#ifndef __INetwork_FWD_DEFINED__
#define __INetwork_FWD_DEFINED__
typedef interface INetwork INetwork;
#ifdef __cplusplus
interface INetwork;
#endif /* __cplusplus */
#endif

#ifndef __INetworkEvents_FWD_DEFINED__
#define __INetworkEvents_FWD_DEFINED__
typedef interface INetworkEvents INetworkEvents;
#ifdef __cplusplus
interface INetworkEvents;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <objidl.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __IEnumNetworks_FWD_DEFINED__
#define __IEnumNetworks_FWD_DEFINED__
typedef interface IEnumNetworks IEnumNetworks;
#ifdef __cplusplus
interface IEnumNetworks;
#endif /* __cplusplus */
#endif

#ifndef __IEnumNetworkConnections_FWD_DEFINED__
#define __IEnumNetworkConnections_FWD_DEFINED__
typedef interface IEnumNetworkConnections IEnumNetworkConnections;
#ifdef __cplusplus
interface IEnumNetworkConnections;
#endif /* __cplusplus */
#endif

#ifndef __INetwork_FWD_DEFINED__
#define __INetwork_FWD_DEFINED__
typedef interface INetwork INetwork;
#ifdef __cplusplus
interface INetwork;
#endif /* __cplusplus */
#endif

#ifndef __INetworkConnection_FWD_DEFINED__
#define __INetworkConnection_FWD_DEFINED__
typedef interface INetworkConnection INetworkConnection;
#ifdef __cplusplus
interface INetworkConnection;
#endif /* __cplusplus */
#endif

#ifndef __INetworkConnectionEvents_FWD_DEFINED__
#define __INetworkConnectionEvents_FWD_DEFINED__
typedef interface INetworkConnectionEvents INetworkConnectionEvents;
#ifdef __cplusplus
interface INetworkConnectionEvents;
#endif /* __cplusplus */
#endif

#ifndef __INetworkCostManager_FWD_DEFINED__
#define __INetworkCostManager_FWD_DEFINED__
typedef interface INetworkCostManager INetworkCostManager;
#ifdef __cplusplus
interface INetworkCostManager;
#endif /* __cplusplus */
#endif

#ifndef __INetworkEvents_FWD_DEFINED__
#define __INetworkEvents_FWD_DEFINED__
typedef interface INetworkEvents INetworkEvents;
#ifdef __cplusplus
interface INetworkEvents;
#endif /* __cplusplus */
#endif

#ifndef __INetworkListManager_FWD_DEFINED__
#define __INetworkListManager_FWD_DEFINED__
typedef interface INetworkListManager INetworkListManager;
#ifdef __cplusplus
interface INetworkListManager;
#endif /* __cplusplus */
#endif

#ifndef __INetworkListManagerEvents_FWD_DEFINED__
#define __INetworkListManagerEvents_FWD_DEFINED__
typedef interface INetworkListManagerEvents INetworkListManagerEvents;
#ifdef __cplusplus
interface INetworkListManagerEvents;
#endif /* __cplusplus */
#endif

typedef enum NLM_NETWORK_CLASS {
    NLM_NETWORK_IDENTIFYING = 0x1,
    NLM_NETWORK_IDENTIFIED = 0x2,
    NLM_NETWORK_UNIDENTIFIED = 0x3
} NLM_NETWORK_CLASS;
typedef enum NLM_INTERNET_CONNECTIVITY {
    NLM_INTERNET_CONNECTIVITY_WEBHIJACK = 0x1,
    NLM_INTERNET_CONNECTIVITY_PROXIED = 0x2,
    NLM_INTERNET_CONNECTIVITY_CORPORATE = 0x4
} NLM_INTERNET_CONNECTIVITY;
typedef enum NLM_CONNECTIVITY {
    NLM_CONNECTIVITY_DISCONNECTED = 0x0,
    NLM_CONNECTIVITY_IPV4_NOTRAFFIC = 0x1,
    NLM_CONNECTIVITY_IPV6_NOTRAFFIC = 0x2,
    NLM_CONNECTIVITY_IPV4_SUBNET = 0x10,
    NLM_CONNECTIVITY_IPV4_LOCALNETWORK = 0x20,
    NLM_CONNECTIVITY_IPV4_INTERNET = 0x40,
    NLM_CONNECTIVITY_IPV6_SUBNET = 0x100,
    NLM_CONNECTIVITY_IPV6_LOCALNETWORK = 0x200,
    NLM_CONNECTIVITY_IPV6_INTERNET = 0x400
} NLM_CONNECTIVITY;
typedef enum NLM_DOMAIN_TYPE {
    NLM_DOMAIN_TYPE_NON_DOMAIN_NETWORK = 0x0,
    NLM_DOMAIN_TYPE_DOMAIN_NETWORK = 0x1,
    NLM_DOMAIN_TYPE_DOMAIN_AUTHENTICATED = 0x2
} NLM_DOMAIN_TYPE;
typedef enum NLM_ENUM_NETWORK {
    NLM_ENUM_NETWORK_CONNECTED = 0x1,
    NLM_ENUM_NETWORK_DISCONNECTED = 0x2,
    NLM_ENUM_NETWORK_ALL = 0x3
} NLM_ENUM_NETWORK;
typedef enum NLM_CONNECTION_COST {
    NLM_CONNECTION_COST_UNKNOWN = 0x0,
    NLM_CONNECTION_COST_UNRESTRICTED = 0x1,
    NLM_CONNECTION_COST_FIXED = 0x2,
    NLM_CONNECTION_COST_VARIABLE = 0x4,
    NLM_CONNECTION_COST_OVERDATALIMIT = 0x10000,
    NLM_CONNECTION_COST_CONGESTED = 0x20000,
    NLM_CONNECTION_COST_ROAMING = 0x40000,
    NLM_CONNECTION_COST_APPROACHINGDATALIMIT = 0x80000
} NLM_CONNECTION_COST;
typedef struct NLM_SOCKADDR {
    BYTE data[128];
} NLM_SOCKADDR;
#define NLM_UNKNOWN_DATAPLAN_STATUS (0xffffffff)

typedef struct NLM_USAGE_DATA {
    DWORD UsageInMegabytes;
    FILETIME LastSyncTime;
} NLM_USAGE_DATA;
typedef struct NLM_DATAPLAN_STATUS {
    GUID InterfaceGuid;
    NLM_USAGE_DATA UsageData;
    DWORD DataLimitInMegabytes;
    DWORD InboundBandwidthInKbps;
    DWORD OutboundBandwidthInKbps;
    FILETIME NextBillingCycle;
    DWORD MaxTransferSizeInMegabytes;
    DWORD Reserved;
} NLM_DATAPLAN_STATUS;
/*****************************************************************************
 * INetworkCostManager interface
 */
#ifndef __INetworkCostManager_INTERFACE_DEFINED__
#define __INetworkCostManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetworkCostManager, 0xdcb00008, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dcb00008-570f-4a9b-8d69-199fdba5723b")
INetworkCostManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCost(
        DWORD *pCost,
        NLM_SOCKADDR *pDestIPAddr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDataPlanStatus(
        NLM_DATAPLAN_STATUS *pDataPlanStatus,
        NLM_SOCKADDR *pDestIPAddr) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDestinationAddresses(
        UINT32 length,
        NLM_SOCKADDR *pDestIPAddrList,
        VARIANT_BOOL bAppend) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetworkCostManager, 0xdcb00008, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b)
#endif
#else
typedef struct INetworkCostManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetworkCostManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetworkCostManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetworkCostManager *This);

    /*** INetworkCostManager methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCost)(
        INetworkCostManager *This,
        DWORD *pCost,
        NLM_SOCKADDR *pDestIPAddr);

    HRESULT (STDMETHODCALLTYPE *GetDataPlanStatus)(
        INetworkCostManager *This,
        NLM_DATAPLAN_STATUS *pDataPlanStatus,
        NLM_SOCKADDR *pDestIPAddr);

    HRESULT (STDMETHODCALLTYPE *SetDestinationAddresses)(
        INetworkCostManager *This,
        UINT32 length,
        NLM_SOCKADDR *pDestIPAddrList,
        VARIANT_BOOL bAppend);

    END_INTERFACE
} INetworkCostManagerVtbl;

interface INetworkCostManager {
    CONST_VTBL INetworkCostManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetworkCostManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetworkCostManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetworkCostManager_Release(This) (This)->lpVtbl->Release(This)
/*** INetworkCostManager methods ***/
#define INetworkCostManager_GetCost(This,pCost,pDestIPAddr) (This)->lpVtbl->GetCost(This,pCost,pDestIPAddr)
#define INetworkCostManager_GetDataPlanStatus(This,pDataPlanStatus,pDestIPAddr) (This)->lpVtbl->GetDataPlanStatus(This,pDataPlanStatus,pDestIPAddr)
#define INetworkCostManager_SetDestinationAddresses(This,length,pDestIPAddrList,bAppend) (This)->lpVtbl->SetDestinationAddresses(This,length,pDestIPAddrList,bAppend)
#else
/*** IUnknown methods ***/
static inline HRESULT INetworkCostManager_QueryInterface(INetworkCostManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetworkCostManager_AddRef(INetworkCostManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetworkCostManager_Release(INetworkCostManager* This) {
    return This->lpVtbl->Release(This);
}
/*** INetworkCostManager methods ***/
static inline HRESULT INetworkCostManager_GetCost(INetworkCostManager* This,DWORD *pCost,NLM_SOCKADDR *pDestIPAddr) {
    return This->lpVtbl->GetCost(This,pCost,pDestIPAddr);
}
static inline HRESULT INetworkCostManager_GetDataPlanStatus(INetworkCostManager* This,NLM_DATAPLAN_STATUS *pDataPlanStatus,NLM_SOCKADDR *pDestIPAddr) {
    return This->lpVtbl->GetDataPlanStatus(This,pDataPlanStatus,pDestIPAddr);
}
static inline HRESULT INetworkCostManager_SetDestinationAddresses(INetworkCostManager* This,UINT32 length,NLM_SOCKADDR *pDestIPAddrList,VARIANT_BOOL bAppend) {
    return This->lpVtbl->SetDestinationAddresses(This,length,pDestIPAddrList,bAppend);
}
#endif
#endif

#endif


#endif  /* __INetworkCostManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetworkConnectionCost interface
 */
#ifndef __INetworkConnectionCost_INTERFACE_DEFINED__
#define __INetworkConnectionCost_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetworkConnectionCost, 0xdcb0000a, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dcb0000a-570f-4a9b-8d69-199fdba5723b")
INetworkConnectionCost : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCost(
        DWORD *pCost) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDataPlanStatus(
        NLM_DATAPLAN_STATUS *pDataPlanStatus) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetworkConnectionCost, 0xdcb0000a, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b)
#endif
#else
typedef struct INetworkConnectionCostVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetworkConnectionCost *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetworkConnectionCost *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetworkConnectionCost *This);

    /*** INetworkConnectionCost methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCost)(
        INetworkConnectionCost *This,
        DWORD *pCost);

    HRESULT (STDMETHODCALLTYPE *GetDataPlanStatus)(
        INetworkConnectionCost *This,
        NLM_DATAPLAN_STATUS *pDataPlanStatus);

    END_INTERFACE
} INetworkConnectionCostVtbl;

interface INetworkConnectionCost {
    CONST_VTBL INetworkConnectionCostVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetworkConnectionCost_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetworkConnectionCost_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetworkConnectionCost_Release(This) (This)->lpVtbl->Release(This)
/*** INetworkConnectionCost methods ***/
#define INetworkConnectionCost_GetCost(This,pCost) (This)->lpVtbl->GetCost(This,pCost)
#define INetworkConnectionCost_GetDataPlanStatus(This,pDataPlanStatus) (This)->lpVtbl->GetDataPlanStatus(This,pDataPlanStatus)
#else
/*** IUnknown methods ***/
static inline HRESULT INetworkConnectionCost_QueryInterface(INetworkConnectionCost* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetworkConnectionCost_AddRef(INetworkConnectionCost* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetworkConnectionCost_Release(INetworkConnectionCost* This) {
    return This->lpVtbl->Release(This);
}
/*** INetworkConnectionCost methods ***/
static inline HRESULT INetworkConnectionCost_GetCost(INetworkConnectionCost* This,DWORD *pCost) {
    return This->lpVtbl->GetCost(This,pCost);
}
static inline HRESULT INetworkConnectionCost_GetDataPlanStatus(INetworkConnectionCost* This,NLM_DATAPLAN_STATUS *pDataPlanStatus) {
    return This->lpVtbl->GetDataPlanStatus(This,pDataPlanStatus);
}
#endif
#endif

#endif


#endif  /* __INetworkConnectionCost_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetworkCostManagerEvents interface
 */
#ifndef __INetworkCostManagerEvents_INTERFACE_DEFINED__
#define __INetworkCostManagerEvents_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetworkCostManagerEvents, 0xdcb00009, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dcb00009-570f-4a9b-8d69-199fdba5723b")
INetworkCostManagerEvents : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CostChanged(
        DWORD newCost,
        NLM_SOCKADDR *pDestAddr) = 0;

    virtual HRESULT STDMETHODCALLTYPE DataPlanStatusChanged(
        NLM_SOCKADDR *pDestAddr) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetworkCostManagerEvents, 0xdcb00009, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b)
#endif
#else
typedef struct INetworkCostManagerEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetworkCostManagerEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetworkCostManagerEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetworkCostManagerEvents *This);

    /*** INetworkCostManagerEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *CostChanged)(
        INetworkCostManagerEvents *This,
        DWORD newCost,
        NLM_SOCKADDR *pDestAddr);

    HRESULT (STDMETHODCALLTYPE *DataPlanStatusChanged)(
        INetworkCostManagerEvents *This,
        NLM_SOCKADDR *pDestAddr);

    END_INTERFACE
} INetworkCostManagerEventsVtbl;

interface INetworkCostManagerEvents {
    CONST_VTBL INetworkCostManagerEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetworkCostManagerEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetworkCostManagerEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetworkCostManagerEvents_Release(This) (This)->lpVtbl->Release(This)
/*** INetworkCostManagerEvents methods ***/
#define INetworkCostManagerEvents_CostChanged(This,newCost,pDestAddr) (This)->lpVtbl->CostChanged(This,newCost,pDestAddr)
#define INetworkCostManagerEvents_DataPlanStatusChanged(This,pDestAddr) (This)->lpVtbl->DataPlanStatusChanged(This,pDestAddr)
#else
/*** IUnknown methods ***/
static inline HRESULT INetworkCostManagerEvents_QueryInterface(INetworkCostManagerEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetworkCostManagerEvents_AddRef(INetworkCostManagerEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetworkCostManagerEvents_Release(INetworkCostManagerEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** INetworkCostManagerEvents methods ***/
static inline HRESULT INetworkCostManagerEvents_CostChanged(INetworkCostManagerEvents* This,DWORD newCost,NLM_SOCKADDR *pDestAddr) {
    return This->lpVtbl->CostChanged(This,newCost,pDestAddr);
}
static inline HRESULT INetworkCostManagerEvents_DataPlanStatusChanged(INetworkCostManagerEvents* This,NLM_SOCKADDR *pDestAddr) {
    return This->lpVtbl->DataPlanStatusChanged(This,pDestAddr);
}
#endif
#endif

#endif


#endif  /* __INetworkCostManagerEvents_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumNetworks interface
 */
#ifndef __IEnumNetworks_INTERFACE_DEFINED__
#define __IEnumNetworks_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumNetworks, 0xdcb00003, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dcb00003-570f-4a9b-8d69-199fdba5723b")
IEnumNetworks : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IEnumVARIANT **ppEnumVar) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        INetwork **rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumNetworks **ppEnumNetwork) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumNetworks, 0xdcb00003, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b)
#endif
#else
typedef struct IEnumNetworksVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumNetworks *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumNetworks *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumNetworks *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IEnumNetworks *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IEnumNetworks *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IEnumNetworks *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IEnumNetworks *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IEnumNetworks methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IEnumNetworks *This,
        IEnumVARIANT **ppEnumVar);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumNetworks *This,
        ULONG celt,
        INetwork **rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumNetworks *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumNetworks *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumNetworks *This,
        IEnumNetworks **ppEnumNetwork);

    END_INTERFACE
} IEnumNetworksVtbl;

interface IEnumNetworks {
    CONST_VTBL IEnumNetworksVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumNetworks_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumNetworks_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumNetworks_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IEnumNetworks_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IEnumNetworks_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IEnumNetworks_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IEnumNetworks_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IEnumNetworks methods ***/
#define IEnumNetworks_get__NewEnum(This,ppEnumVar) (This)->lpVtbl->get__NewEnum(This,ppEnumVar)
#define IEnumNetworks_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumNetworks_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumNetworks_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumNetworks_Clone(This,ppEnumNetwork) (This)->lpVtbl->Clone(This,ppEnumNetwork)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumNetworks_QueryInterface(IEnumNetworks* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumNetworks_AddRef(IEnumNetworks* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumNetworks_Release(IEnumNetworks* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IEnumNetworks_GetTypeInfoCount(IEnumNetworks* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IEnumNetworks_GetTypeInfo(IEnumNetworks* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IEnumNetworks_GetIDsOfNames(IEnumNetworks* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IEnumNetworks_Invoke(IEnumNetworks* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IEnumNetworks methods ***/
static inline HRESULT IEnumNetworks_get__NewEnum(IEnumNetworks* This,IEnumVARIANT **ppEnumVar) {
    return This->lpVtbl->get__NewEnum(This,ppEnumVar);
}
static inline HRESULT IEnumNetworks_Next(IEnumNetworks* This,ULONG celt,INetwork **rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static inline HRESULT IEnumNetworks_Skip(IEnumNetworks* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumNetworks_Reset(IEnumNetworks* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumNetworks_Clone(IEnumNetworks* This,IEnumNetworks **ppEnumNetwork) {
    return This->lpVtbl->Clone(This,ppEnumNetwork);
}
#endif
#endif

#endif


#endif  /* __IEnumNetworks_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumNetworkConnections interface
 */
#ifndef __IEnumNetworkConnections_INTERFACE_DEFINED__
#define __IEnumNetworkConnections_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumNetworkConnections, 0xdcb00006, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dcb00006-570f-4a9b-8d69-199fdba5723b")
IEnumNetworkConnections : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IEnumVARIANT **ppEnumVar) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        INetworkConnection **rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumNetworkConnections **ppEnumNetwork) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumNetworkConnections, 0xdcb00006, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b)
#endif
#else
typedef struct IEnumNetworkConnectionsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumNetworkConnections *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumNetworkConnections *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumNetworkConnections *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IEnumNetworkConnections *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IEnumNetworkConnections *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IEnumNetworkConnections *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IEnumNetworkConnections *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IEnumNetworkConnections methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IEnumNetworkConnections *This,
        IEnumVARIANT **ppEnumVar);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumNetworkConnections *This,
        ULONG celt,
        INetworkConnection **rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumNetworkConnections *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumNetworkConnections *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumNetworkConnections *This,
        IEnumNetworkConnections **ppEnumNetwork);

    END_INTERFACE
} IEnumNetworkConnectionsVtbl;

interface IEnumNetworkConnections {
    CONST_VTBL IEnumNetworkConnectionsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumNetworkConnections_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumNetworkConnections_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumNetworkConnections_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IEnumNetworkConnections_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IEnumNetworkConnections_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IEnumNetworkConnections_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IEnumNetworkConnections_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IEnumNetworkConnections methods ***/
#define IEnumNetworkConnections_get__NewEnum(This,ppEnumVar) (This)->lpVtbl->get__NewEnum(This,ppEnumVar)
#define IEnumNetworkConnections_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumNetworkConnections_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumNetworkConnections_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumNetworkConnections_Clone(This,ppEnumNetwork) (This)->lpVtbl->Clone(This,ppEnumNetwork)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumNetworkConnections_QueryInterface(IEnumNetworkConnections* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumNetworkConnections_AddRef(IEnumNetworkConnections* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumNetworkConnections_Release(IEnumNetworkConnections* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IEnumNetworkConnections_GetTypeInfoCount(IEnumNetworkConnections* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IEnumNetworkConnections_GetTypeInfo(IEnumNetworkConnections* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IEnumNetworkConnections_GetIDsOfNames(IEnumNetworkConnections* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IEnumNetworkConnections_Invoke(IEnumNetworkConnections* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IEnumNetworkConnections methods ***/
static inline HRESULT IEnumNetworkConnections_get__NewEnum(IEnumNetworkConnections* This,IEnumVARIANT **ppEnumVar) {
    return This->lpVtbl->get__NewEnum(This,ppEnumVar);
}
static inline HRESULT IEnumNetworkConnections_Next(IEnumNetworkConnections* This,ULONG celt,INetworkConnection **rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static inline HRESULT IEnumNetworkConnections_Skip(IEnumNetworkConnections* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumNetworkConnections_Reset(IEnumNetworkConnections* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumNetworkConnections_Clone(IEnumNetworkConnections* This,IEnumNetworkConnections **ppEnumNetwork) {
    return This->lpVtbl->Clone(This,ppEnumNetwork);
}
#endif
#endif

#endif


#endif  /* __IEnumNetworkConnections_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetworkListManager interface
 */
#ifndef __INetworkListManager_INTERFACE_DEFINED__
#define __INetworkListManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetworkListManager, 0xdcb00000, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dcb00000-570f-4a9b-8d69-199fdba5723b")
INetworkListManager : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE GetNetworks(
        NLM_ENUM_NETWORK Flags,
        IEnumNetworks **ppEnumNetwork) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNetwork(
        GUID gdNetworkId,
        INetwork **ppNetwork) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNetworkConnections(
        IEnumNetworkConnections **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNetworkConnection(
        GUID gdNetworkConnectionId,
        INetworkConnection **ppNetworkConnection) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsConnectedToInternet(
        VARIANT_BOOL *pbIsConnected) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsConnected(
        VARIANT_BOOL *pbIsConnected) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConnectivity(
        NLM_CONNECTIVITY *pConnectivity) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetworkListManager, 0xdcb00000, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b)
#endif
#else
typedef struct INetworkListManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetworkListManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetworkListManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetworkListManager *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetworkListManager *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetworkListManager *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetworkListManager *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetworkListManager *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetworkListManager methods ***/
    HRESULT (STDMETHODCALLTYPE *GetNetworks)(
        INetworkListManager *This,
        NLM_ENUM_NETWORK Flags,
        IEnumNetworks **ppEnumNetwork);

    HRESULT (STDMETHODCALLTYPE *GetNetwork)(
        INetworkListManager *This,
        GUID gdNetworkId,
        INetwork **ppNetwork);

    HRESULT (STDMETHODCALLTYPE *GetNetworkConnections)(
        INetworkListManager *This,
        IEnumNetworkConnections **ppEnum);

    HRESULT (STDMETHODCALLTYPE *GetNetworkConnection)(
        INetworkListManager *This,
        GUID gdNetworkConnectionId,
        INetworkConnection **ppNetworkConnection);

    HRESULT (STDMETHODCALLTYPE *IsConnectedToInternet)(
        INetworkListManager *This,
        VARIANT_BOOL *pbIsConnected);

    HRESULT (STDMETHODCALLTYPE *IsConnected)(
        INetworkListManager *This,
        VARIANT_BOOL *pbIsConnected);

    HRESULT (STDMETHODCALLTYPE *GetConnectivity)(
        INetworkListManager *This,
        NLM_CONNECTIVITY *pConnectivity);

    END_INTERFACE
} INetworkListManagerVtbl;

interface INetworkListManager {
    CONST_VTBL INetworkListManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetworkListManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetworkListManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetworkListManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetworkListManager_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetworkListManager_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetworkListManager_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetworkListManager_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetworkListManager methods ***/
#define INetworkListManager_GetNetworks(This,Flags,ppEnumNetwork) (This)->lpVtbl->GetNetworks(This,Flags,ppEnumNetwork)
#define INetworkListManager_GetNetwork(This,gdNetworkId,ppNetwork) (This)->lpVtbl->GetNetwork(This,gdNetworkId,ppNetwork)
#define INetworkListManager_GetNetworkConnections(This,ppEnum) (This)->lpVtbl->GetNetworkConnections(This,ppEnum)
#define INetworkListManager_GetNetworkConnection(This,gdNetworkConnectionId,ppNetworkConnection) (This)->lpVtbl->GetNetworkConnection(This,gdNetworkConnectionId,ppNetworkConnection)
#define INetworkListManager_IsConnectedToInternet(This,pbIsConnected) (This)->lpVtbl->IsConnectedToInternet(This,pbIsConnected)
#define INetworkListManager_IsConnected(This,pbIsConnected) (This)->lpVtbl->IsConnected(This,pbIsConnected)
#define INetworkListManager_GetConnectivity(This,pConnectivity) (This)->lpVtbl->GetConnectivity(This,pConnectivity)
#else
/*** IUnknown methods ***/
static inline HRESULT INetworkListManager_QueryInterface(INetworkListManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetworkListManager_AddRef(INetworkListManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetworkListManager_Release(INetworkListManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetworkListManager_GetTypeInfoCount(INetworkListManager* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetworkListManager_GetTypeInfo(INetworkListManager* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetworkListManager_GetIDsOfNames(INetworkListManager* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetworkListManager_Invoke(INetworkListManager* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetworkListManager methods ***/
static inline HRESULT INetworkListManager_GetNetworks(INetworkListManager* This,NLM_ENUM_NETWORK Flags,IEnumNetworks **ppEnumNetwork) {
    return This->lpVtbl->GetNetworks(This,Flags,ppEnumNetwork);
}
static inline HRESULT INetworkListManager_GetNetwork(INetworkListManager* This,GUID gdNetworkId,INetwork **ppNetwork) {
    return This->lpVtbl->GetNetwork(This,gdNetworkId,ppNetwork);
}
static inline HRESULT INetworkListManager_GetNetworkConnections(INetworkListManager* This,IEnumNetworkConnections **ppEnum) {
    return This->lpVtbl->GetNetworkConnections(This,ppEnum);
}
static inline HRESULT INetworkListManager_GetNetworkConnection(INetworkListManager* This,GUID gdNetworkConnectionId,INetworkConnection **ppNetworkConnection) {
    return This->lpVtbl->GetNetworkConnection(This,gdNetworkConnectionId,ppNetworkConnection);
}
static inline HRESULT INetworkListManager_IsConnectedToInternet(INetworkListManager* This,VARIANT_BOOL *pbIsConnected) {
    return This->lpVtbl->IsConnectedToInternet(This,pbIsConnected);
}
static inline HRESULT INetworkListManager_IsConnected(INetworkListManager* This,VARIANT_BOOL *pbIsConnected) {
    return This->lpVtbl->IsConnected(This,pbIsConnected);
}
static inline HRESULT INetworkListManager_GetConnectivity(INetworkListManager* This,NLM_CONNECTIVITY *pConnectivity) {
    return This->lpVtbl->GetConnectivity(This,pConnectivity);
}
#endif
#endif

#endif


#endif  /* __INetworkListManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * NetworkListManager coclass
 */

DEFINE_GUID(CLSID_NetworkListManager, 0xdcb00c01, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b);

#ifdef __cplusplus
class DECLSPEC_UUID("dcb00c01-570f-4a9b-8d69-199fdba5723b") NetworkListManager;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(NetworkListManager, 0xdcb00c01, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b)
#endif
#endif

/*****************************************************************************
 * INetworkListManagerEvents interface
 */
#ifndef __INetworkListManagerEvents_INTERFACE_DEFINED__
#define __INetworkListManagerEvents_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetworkListManagerEvents, 0xdcb00001, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dcb00001-570f-4a9b-8d69-199fdba5723b")
INetworkListManagerEvents : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ConnectivityChanged(
        NLM_CONNECTIVITY newConnectivity) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetworkListManagerEvents, 0xdcb00001, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b)
#endif
#else
typedef struct INetworkListManagerEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetworkListManagerEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetworkListManagerEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetworkListManagerEvents *This);

    /*** INetworkListManagerEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *ConnectivityChanged)(
        INetworkListManagerEvents *This,
        NLM_CONNECTIVITY newConnectivity);

    END_INTERFACE
} INetworkListManagerEventsVtbl;

interface INetworkListManagerEvents {
    CONST_VTBL INetworkListManagerEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetworkListManagerEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetworkListManagerEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetworkListManagerEvents_Release(This) (This)->lpVtbl->Release(This)
/*** INetworkListManagerEvents methods ***/
#define INetworkListManagerEvents_ConnectivityChanged(This,newConnectivity) (This)->lpVtbl->ConnectivityChanged(This,newConnectivity)
#else
/*** IUnknown methods ***/
static inline HRESULT INetworkListManagerEvents_QueryInterface(INetworkListManagerEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetworkListManagerEvents_AddRef(INetworkListManagerEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetworkListManagerEvents_Release(INetworkListManagerEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** INetworkListManagerEvents methods ***/
static inline HRESULT INetworkListManagerEvents_ConnectivityChanged(INetworkListManagerEvents* This,NLM_CONNECTIVITY newConnectivity) {
    return This->lpVtbl->ConnectivityChanged(This,newConnectivity);
}
#endif
#endif

#endif


#endif  /* __INetworkListManagerEvents_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetworkConnectionEvents interface
 */
#ifndef __INetworkConnectionEvents_INTERFACE_DEFINED__
#define __INetworkConnectionEvents_INTERFACE_DEFINED__

typedef enum NLM_CONNECTION_PROPERTY_CHANGE {
    NLM_CONNECTION_PROPERTY_CHANGE_AUTHENTICATION = 1
} NLM_CONNECTION_PROPERTY_CHANGE;
DEFINE_GUID(IID_INetworkConnectionEvents, 0xdcb00007, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dcb00007-570f-4a9b-8d69-199fdba5723b")
INetworkConnectionEvents : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE NetworkConnectionConnectivityChanged(
        GUID connectionId,
        NLM_CONNECTIVITY newConnectivity) = 0;

    virtual HRESULT STDMETHODCALLTYPE NetworkConnectionPropertyChanged(
        GUID connectionId,
        NLM_CONNECTION_PROPERTY_CHANGE flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetworkConnectionEvents, 0xdcb00007, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b)
#endif
#else
typedef struct INetworkConnectionEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetworkConnectionEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetworkConnectionEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetworkConnectionEvents *This);

    /*** INetworkConnectionEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *NetworkConnectionConnectivityChanged)(
        INetworkConnectionEvents *This,
        GUID connectionId,
        NLM_CONNECTIVITY newConnectivity);

    HRESULT (STDMETHODCALLTYPE *NetworkConnectionPropertyChanged)(
        INetworkConnectionEvents *This,
        GUID connectionId,
        NLM_CONNECTION_PROPERTY_CHANGE flags);

    END_INTERFACE
} INetworkConnectionEventsVtbl;

interface INetworkConnectionEvents {
    CONST_VTBL INetworkConnectionEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetworkConnectionEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetworkConnectionEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetworkConnectionEvents_Release(This) (This)->lpVtbl->Release(This)
/*** INetworkConnectionEvents methods ***/
#define INetworkConnectionEvents_NetworkConnectionConnectivityChanged(This,connectionId,newConnectivity) (This)->lpVtbl->NetworkConnectionConnectivityChanged(This,connectionId,newConnectivity)
#define INetworkConnectionEvents_NetworkConnectionPropertyChanged(This,connectionId,flags) (This)->lpVtbl->NetworkConnectionPropertyChanged(This,connectionId,flags)
#else
/*** IUnknown methods ***/
static inline HRESULT INetworkConnectionEvents_QueryInterface(INetworkConnectionEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetworkConnectionEvents_AddRef(INetworkConnectionEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetworkConnectionEvents_Release(INetworkConnectionEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** INetworkConnectionEvents methods ***/
static inline HRESULT INetworkConnectionEvents_NetworkConnectionConnectivityChanged(INetworkConnectionEvents* This,GUID connectionId,NLM_CONNECTIVITY newConnectivity) {
    return This->lpVtbl->NetworkConnectionConnectivityChanged(This,connectionId,newConnectivity);
}
static inline HRESULT INetworkConnectionEvents_NetworkConnectionPropertyChanged(INetworkConnectionEvents* This,GUID connectionId,NLM_CONNECTION_PROPERTY_CHANGE flags) {
    return This->lpVtbl->NetworkConnectionPropertyChanged(This,connectionId,flags);
}
#endif
#endif

#endif


#endif  /* __INetworkConnectionEvents_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetworkConnection interface
 */
#ifndef __INetworkConnection_INTERFACE_DEFINED__
#define __INetworkConnection_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetworkConnection, 0xdcb00005, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dcb00005-570f-4a9b-8d69-199fdba5723b")
INetworkConnection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE GetNetwork(
        INetwork **ppNetwork) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsConnectedToInternet(
        VARIANT_BOOL *pbIsConnected) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsConnected(
        VARIANT_BOOL *pbIsConnected) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConnectivity(
        NLM_CONNECTIVITY *pConnectivity) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConnectionId(
        GUID *pgdConnectionId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAdapterId(
        GUID *pgdAdapterId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDomainType(
        NLM_DOMAIN_TYPE *pDomainType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetworkConnection, 0xdcb00005, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b)
#endif
#else
typedef struct INetworkConnectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetworkConnection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetworkConnection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetworkConnection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetworkConnection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetworkConnection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetworkConnection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetworkConnection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetworkConnection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetNetwork)(
        INetworkConnection *This,
        INetwork **ppNetwork);

    HRESULT (STDMETHODCALLTYPE *get_IsConnectedToInternet)(
        INetworkConnection *This,
        VARIANT_BOOL *pbIsConnected);

    HRESULT (STDMETHODCALLTYPE *get_IsConnected)(
        INetworkConnection *This,
        VARIANT_BOOL *pbIsConnected);

    HRESULT (STDMETHODCALLTYPE *GetConnectivity)(
        INetworkConnection *This,
        NLM_CONNECTIVITY *pConnectivity);

    HRESULT (STDMETHODCALLTYPE *GetConnectionId)(
        INetworkConnection *This,
        GUID *pgdConnectionId);

    HRESULT (STDMETHODCALLTYPE *GetAdapterId)(
        INetworkConnection *This,
        GUID *pgdAdapterId);

    HRESULT (STDMETHODCALLTYPE *GetDomainType)(
        INetworkConnection *This,
        NLM_DOMAIN_TYPE *pDomainType);

    END_INTERFACE
} INetworkConnectionVtbl;

interface INetworkConnection {
    CONST_VTBL INetworkConnectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetworkConnection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetworkConnection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetworkConnection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetworkConnection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetworkConnection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetworkConnection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetworkConnection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetworkConnection methods ***/
#define INetworkConnection_GetNetwork(This,ppNetwork) (This)->lpVtbl->GetNetwork(This,ppNetwork)
#define INetworkConnection_get_IsConnectedToInternet(This,pbIsConnected) (This)->lpVtbl->get_IsConnectedToInternet(This,pbIsConnected)
#define INetworkConnection_get_IsConnected(This,pbIsConnected) (This)->lpVtbl->get_IsConnected(This,pbIsConnected)
#define INetworkConnection_GetConnectivity(This,pConnectivity) (This)->lpVtbl->GetConnectivity(This,pConnectivity)
#define INetworkConnection_GetConnectionId(This,pgdConnectionId) (This)->lpVtbl->GetConnectionId(This,pgdConnectionId)
#define INetworkConnection_GetAdapterId(This,pgdAdapterId) (This)->lpVtbl->GetAdapterId(This,pgdAdapterId)
#define INetworkConnection_GetDomainType(This,pDomainType) (This)->lpVtbl->GetDomainType(This,pDomainType)
#else
/*** IUnknown methods ***/
static inline HRESULT INetworkConnection_QueryInterface(INetworkConnection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetworkConnection_AddRef(INetworkConnection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetworkConnection_Release(INetworkConnection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetworkConnection_GetTypeInfoCount(INetworkConnection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetworkConnection_GetTypeInfo(INetworkConnection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetworkConnection_GetIDsOfNames(INetworkConnection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetworkConnection_Invoke(INetworkConnection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetworkConnection methods ***/
static inline HRESULT INetworkConnection_GetNetwork(INetworkConnection* This,INetwork **ppNetwork) {
    return This->lpVtbl->GetNetwork(This,ppNetwork);
}
static inline HRESULT INetworkConnection_get_IsConnectedToInternet(INetworkConnection* This,VARIANT_BOOL *pbIsConnected) {
    return This->lpVtbl->get_IsConnectedToInternet(This,pbIsConnected);
}
static inline HRESULT INetworkConnection_get_IsConnected(INetworkConnection* This,VARIANT_BOOL *pbIsConnected) {
    return This->lpVtbl->get_IsConnected(This,pbIsConnected);
}
static inline HRESULT INetworkConnection_GetConnectivity(INetworkConnection* This,NLM_CONNECTIVITY *pConnectivity) {
    return This->lpVtbl->GetConnectivity(This,pConnectivity);
}
static inline HRESULT INetworkConnection_GetConnectionId(INetworkConnection* This,GUID *pgdConnectionId) {
    return This->lpVtbl->GetConnectionId(This,pgdConnectionId);
}
static inline HRESULT INetworkConnection_GetAdapterId(INetworkConnection* This,GUID *pgdAdapterId) {
    return This->lpVtbl->GetAdapterId(This,pgdAdapterId);
}
static inline HRESULT INetworkConnection_GetDomainType(INetworkConnection* This,NLM_DOMAIN_TYPE *pDomainType) {
    return This->lpVtbl->GetDomainType(This,pDomainType);
}
#endif
#endif

#endif


#endif  /* __INetworkConnection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetwork interface
 */
#ifndef __INetwork_INTERFACE_DEFINED__
#define __INetwork_INTERFACE_DEFINED__

typedef enum NLM_NETWORK_CATEGORY {
    NLM_NETWORK_CATEGORY_PUBLIC = 0x0,
    NLM_NETWORK_CATEGORY_PRIVATE = 0x1,
    NLM_NETWORK_CATEGORY_DOMAIN_AUTHENTICATED = 0x2
} NLM_NETWORK_CATEGORY;
DEFINE_GUID(IID_INetwork, 0xdcb00002, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dcb00002-570f-4a9b-8d69-199fdba5723b")
INetwork : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE GetName(
        BSTR *pszNetworkName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetName(
        BSTR szNetworkNewName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDescription(
        BSTR *pszDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDescription(
        BSTR szDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNetworkId(
        GUID *pgdGuidNetworkId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDomainType(
        NLM_DOMAIN_TYPE *pNetworkType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNetworkConnections(
        IEnumNetworkConnections **ppEnumNetworkConnection) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTimeCreatedAndConnected(
        DWORD *pdwLowDateTimeCreated,
        DWORD *pdwHighDateTimeCreated,
        DWORD *pdwLowDateTimeConnected,
        DWORD *pdwHighDateTimeConnected) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsConnectedToInternet(
        VARIANT_BOOL *pbIsConnected) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsConnected(
        VARIANT_BOOL *pbIsConnected) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConnectivity(
        NLM_CONNECTIVITY *pConnectivity) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCategory(
        NLM_NETWORK_CATEGORY *pCategory) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCategory(
        NLM_NETWORK_CATEGORY NewCategory) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetwork, 0xdcb00002, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b)
#endif
#else
typedef struct INetworkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetwork *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetwork *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetwork *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetwork *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetwork *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetwork *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetwork *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetwork methods ***/
    HRESULT (STDMETHODCALLTYPE *GetName)(
        INetwork *This,
        BSTR *pszNetworkName);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        INetwork *This,
        BSTR szNetworkNewName);

    HRESULT (STDMETHODCALLTYPE *GetDescription)(
        INetwork *This,
        BSTR *pszDescription);

    HRESULT (STDMETHODCALLTYPE *SetDescription)(
        INetwork *This,
        BSTR szDescription);

    HRESULT (STDMETHODCALLTYPE *GetNetworkId)(
        INetwork *This,
        GUID *pgdGuidNetworkId);

    HRESULT (STDMETHODCALLTYPE *GetDomainType)(
        INetwork *This,
        NLM_DOMAIN_TYPE *pNetworkType);

    HRESULT (STDMETHODCALLTYPE *GetNetworkConnections)(
        INetwork *This,
        IEnumNetworkConnections **ppEnumNetworkConnection);

    HRESULT (STDMETHODCALLTYPE *GetTimeCreatedAndConnected)(
        INetwork *This,
        DWORD *pdwLowDateTimeCreated,
        DWORD *pdwHighDateTimeCreated,
        DWORD *pdwLowDateTimeConnected,
        DWORD *pdwHighDateTimeConnected);

    HRESULT (STDMETHODCALLTYPE *get_IsConnectedToInternet)(
        INetwork *This,
        VARIANT_BOOL *pbIsConnected);

    HRESULT (STDMETHODCALLTYPE *get_IsConnected)(
        INetwork *This,
        VARIANT_BOOL *pbIsConnected);

    HRESULT (STDMETHODCALLTYPE *GetConnectivity)(
        INetwork *This,
        NLM_CONNECTIVITY *pConnectivity);

    HRESULT (STDMETHODCALLTYPE *GetCategory)(
        INetwork *This,
        NLM_NETWORK_CATEGORY *pCategory);

    HRESULT (STDMETHODCALLTYPE *SetCategory)(
        INetwork *This,
        NLM_NETWORK_CATEGORY NewCategory);

    END_INTERFACE
} INetworkVtbl;

interface INetwork {
    CONST_VTBL INetworkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetwork_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetwork_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetwork_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetwork_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetwork_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetwork_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetwork_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetwork methods ***/
#define INetwork_GetName(This,pszNetworkName) (This)->lpVtbl->GetName(This,pszNetworkName)
#define INetwork_SetName(This,szNetworkNewName) (This)->lpVtbl->SetName(This,szNetworkNewName)
#define INetwork_GetDescription(This,pszDescription) (This)->lpVtbl->GetDescription(This,pszDescription)
#define INetwork_SetDescription(This,szDescription) (This)->lpVtbl->SetDescription(This,szDescription)
#define INetwork_GetNetworkId(This,pgdGuidNetworkId) (This)->lpVtbl->GetNetworkId(This,pgdGuidNetworkId)
#define INetwork_GetDomainType(This,pNetworkType) (This)->lpVtbl->GetDomainType(This,pNetworkType)
#define INetwork_GetNetworkConnections(This,ppEnumNetworkConnection) (This)->lpVtbl->GetNetworkConnections(This,ppEnumNetworkConnection)
#define INetwork_GetTimeCreatedAndConnected(This,pdwLowDateTimeCreated,pdwHighDateTimeCreated,pdwLowDateTimeConnected,pdwHighDateTimeConnected) (This)->lpVtbl->GetTimeCreatedAndConnected(This,pdwLowDateTimeCreated,pdwHighDateTimeCreated,pdwLowDateTimeConnected,pdwHighDateTimeConnected)
#define INetwork_get_IsConnectedToInternet(This,pbIsConnected) (This)->lpVtbl->get_IsConnectedToInternet(This,pbIsConnected)
#define INetwork_get_IsConnected(This,pbIsConnected) (This)->lpVtbl->get_IsConnected(This,pbIsConnected)
#define INetwork_GetConnectivity(This,pConnectivity) (This)->lpVtbl->GetConnectivity(This,pConnectivity)
#define INetwork_GetCategory(This,pCategory) (This)->lpVtbl->GetCategory(This,pCategory)
#define INetwork_SetCategory(This,NewCategory) (This)->lpVtbl->SetCategory(This,NewCategory)
#else
/*** IUnknown methods ***/
static inline HRESULT INetwork_QueryInterface(INetwork* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetwork_AddRef(INetwork* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetwork_Release(INetwork* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetwork_GetTypeInfoCount(INetwork* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetwork_GetTypeInfo(INetwork* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetwork_GetIDsOfNames(INetwork* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetwork_Invoke(INetwork* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetwork methods ***/
static inline HRESULT INetwork_GetName(INetwork* This,BSTR *pszNetworkName) {
    return This->lpVtbl->GetName(This,pszNetworkName);
}
static inline HRESULT INetwork_SetName(INetwork* This,BSTR szNetworkNewName) {
    return This->lpVtbl->SetName(This,szNetworkNewName);
}
static inline HRESULT INetwork_GetDescription(INetwork* This,BSTR *pszDescription) {
    return This->lpVtbl->GetDescription(This,pszDescription);
}
static inline HRESULT INetwork_SetDescription(INetwork* This,BSTR szDescription) {
    return This->lpVtbl->SetDescription(This,szDescription);
}
static inline HRESULT INetwork_GetNetworkId(INetwork* This,GUID *pgdGuidNetworkId) {
    return This->lpVtbl->GetNetworkId(This,pgdGuidNetworkId);
}
static inline HRESULT INetwork_GetDomainType(INetwork* This,NLM_DOMAIN_TYPE *pNetworkType) {
    return This->lpVtbl->GetDomainType(This,pNetworkType);
}
static inline HRESULT INetwork_GetNetworkConnections(INetwork* This,IEnumNetworkConnections **ppEnumNetworkConnection) {
    return This->lpVtbl->GetNetworkConnections(This,ppEnumNetworkConnection);
}
static inline HRESULT INetwork_GetTimeCreatedAndConnected(INetwork* This,DWORD *pdwLowDateTimeCreated,DWORD *pdwHighDateTimeCreated,DWORD *pdwLowDateTimeConnected,DWORD *pdwHighDateTimeConnected) {
    return This->lpVtbl->GetTimeCreatedAndConnected(This,pdwLowDateTimeCreated,pdwHighDateTimeCreated,pdwLowDateTimeConnected,pdwHighDateTimeConnected);
}
static inline HRESULT INetwork_get_IsConnectedToInternet(INetwork* This,VARIANT_BOOL *pbIsConnected) {
    return This->lpVtbl->get_IsConnectedToInternet(This,pbIsConnected);
}
static inline HRESULT INetwork_get_IsConnected(INetwork* This,VARIANT_BOOL *pbIsConnected) {
    return This->lpVtbl->get_IsConnected(This,pbIsConnected);
}
static inline HRESULT INetwork_GetConnectivity(INetwork* This,NLM_CONNECTIVITY *pConnectivity) {
    return This->lpVtbl->GetConnectivity(This,pConnectivity);
}
static inline HRESULT INetwork_GetCategory(INetwork* This,NLM_NETWORK_CATEGORY *pCategory) {
    return This->lpVtbl->GetCategory(This,pCategory);
}
static inline HRESULT INetwork_SetCategory(INetwork* This,NLM_NETWORK_CATEGORY NewCategory) {
    return This->lpVtbl->SetCategory(This,NewCategory);
}
#endif
#endif

#endif


#endif  /* __INetwork_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetworkEvents interface
 */
#ifndef __INetworkEvents_INTERFACE_DEFINED__
#define __INetworkEvents_INTERFACE_DEFINED__

typedef enum NLM_NETWORK_PROPERTY_CHANGE {
    NLM_NETWORK_PROPERTY_CHANGE_CONNECTION = 0x1,
    NLM_NETWORK_PROPERTY_CHANGE_DESCRIPTION = 0x2,
    NLM_NETWORK_PROPERTY_CHANGE_NAME = 0x4,
    NLM_NETWORK_PROPERTY_CHANGE_ICON = 0x8,
    NLM_NETWORK_PROPERTY_CHANGE_CATEGORY_VALUE = 0x10
} NLM_NETWORK_PROPERTY_CHANGE;
DEFINE_GUID(IID_INetworkEvents, 0xdcb00004, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dcb00004-570f-4a9b-8d69-199fdba5723b")
INetworkEvents : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE NetworkAdded(
        GUID networkId) = 0;

    virtual HRESULT STDMETHODCALLTYPE NetworkDeleted(
        GUID networkId) = 0;

    virtual HRESULT STDMETHODCALLTYPE NetworkConnectivityChanged(
        GUID networkId,
        NLM_CONNECTIVITY newConnectivity) = 0;

    virtual HRESULT STDMETHODCALLTYPE NetworkPropertyChanged(
        GUID networkId,
        NLM_NETWORK_PROPERTY_CHANGE flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetworkEvents, 0xdcb00004, 0x570f, 0x4a9b, 0x8d,0x69, 0x19,0x9f,0xdb,0xa5,0x72,0x3b)
#endif
#else
typedef struct INetworkEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetworkEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetworkEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetworkEvents *This);

    /*** INetworkEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *NetworkAdded)(
        INetworkEvents *This,
        GUID networkId);

    HRESULT (STDMETHODCALLTYPE *NetworkDeleted)(
        INetworkEvents *This,
        GUID networkId);

    HRESULT (STDMETHODCALLTYPE *NetworkConnectivityChanged)(
        INetworkEvents *This,
        GUID networkId,
        NLM_CONNECTIVITY newConnectivity);

    HRESULT (STDMETHODCALLTYPE *NetworkPropertyChanged)(
        INetworkEvents *This,
        GUID networkId,
        NLM_NETWORK_PROPERTY_CHANGE flags);

    END_INTERFACE
} INetworkEventsVtbl;

interface INetworkEvents {
    CONST_VTBL INetworkEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetworkEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetworkEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetworkEvents_Release(This) (This)->lpVtbl->Release(This)
/*** INetworkEvents methods ***/
#define INetworkEvents_NetworkAdded(This,networkId) (This)->lpVtbl->NetworkAdded(This,networkId)
#define INetworkEvents_NetworkDeleted(This,networkId) (This)->lpVtbl->NetworkDeleted(This,networkId)
#define INetworkEvents_NetworkConnectivityChanged(This,networkId,newConnectivity) (This)->lpVtbl->NetworkConnectivityChanged(This,networkId,newConnectivity)
#define INetworkEvents_NetworkPropertyChanged(This,networkId,flags) (This)->lpVtbl->NetworkPropertyChanged(This,networkId,flags)
#else
/*** IUnknown methods ***/
static inline HRESULT INetworkEvents_QueryInterface(INetworkEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetworkEvents_AddRef(INetworkEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetworkEvents_Release(INetworkEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** INetworkEvents methods ***/
static inline HRESULT INetworkEvents_NetworkAdded(INetworkEvents* This,GUID networkId) {
    return This->lpVtbl->NetworkAdded(This,networkId);
}
static inline HRESULT INetworkEvents_NetworkDeleted(INetworkEvents* This,GUID networkId) {
    return This->lpVtbl->NetworkDeleted(This,networkId);
}
static inline HRESULT INetworkEvents_NetworkConnectivityChanged(INetworkEvents* This,GUID networkId,NLM_CONNECTIVITY newConnectivity) {
    return This->lpVtbl->NetworkConnectivityChanged(This,networkId,newConnectivity);
}
static inline HRESULT INetworkEvents_NetworkPropertyChanged(INetworkEvents* This,GUID networkId,NLM_NETWORK_PROPERTY_CHANGE flags) {
    return This->lpVtbl->NetworkPropertyChanged(This,networkId,flags);
}
#endif
#endif

#endif


#endif  /* __INetworkEvents_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __netlistmgr_h__ */
