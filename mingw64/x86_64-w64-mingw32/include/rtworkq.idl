/*
 * Copyright 2020 Nikolay Sivov for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "unknwn.idl";

typedef enum
{
    RTWQ_STANDARD_WORKQUEUE = 0,
    RTWQ_WINDOW_WORKQUEUE = 1,
    RTWQ_MULTITHREADED_WORKQUEUE = 2,
} RTWQ_WORKQUEUE_TYPE;

typedef unsigned __int64 RTWQWORKITEM_KEY;

[
    object,
    uuid(ac6b7889-0740-4d51-8619-905994a55cc6),
    local
]
interface IRtwqAsyncResult : IUnknown
{
    HRESULT GetState([out] IUnknown **state);
    HRESULT GetStatus();
    HRESULT SetStatus([in] HRESULT status);
    HRESULT GetObject([out] IUnknown **object);
    IUnknown *GetStateNoAddRef();
}

[
    object,
    uuid(a27003cf-2354-4f2a-8d6a-ab7cff15437e),
    local
]
interface IRtwqAsyncCallback : IUnknown
{
    HRESULT GetParameters([out] DWORD *flags, [out] DWORD *queue);
    HRESULT Invoke([in] IRtwqAsyncResult *result);
}

[
    object,
    uuid(63d9255a-7ff1-4b61-8faf-ed6460dacf2b),
    local
]
interface IRtwqPlatformEvents : IUnknown
{
    HRESULT InitializationComplete(void);
    HRESULT ShutdownStart(void);
    HRESULT ShutdownComplete(void);
}

cpp_quote("#define RTWQ_E_ERROR(x)            ((HRESULT)(0xc00d0000L+x))")
cpp_quote("#define RTWQ_E_BUFFERTOOSMALL      RTWQ_E_ERROR(14001)")
cpp_quote("#define RTWQ_E_NOT_INITIALIZED     RTWQ_E_ERROR(14006)")
cpp_quote("#define RTWQ_E_UNEXPECTED          RTWQ_E_ERROR(14011)")
cpp_quote("#define RTWQ_E_NOT_FOUND           RTWQ_E_ERROR(14037)")
cpp_quote("#define RTWQ_E_OPERATION_CANCELLED RTWQ_E_ERROR(14061)")
cpp_quote("#define RTWQ_E_INVALID_WORKQUEUE   RTWQ_E_ERROR(14079)")
cpp_quote("#define RTWQ_E_SHUTDOWN            RTWQ_E_ERROR(16005)")

cpp_quote("#ifdef __WINESRC__")
cpp_quote("typedef struct tagRTWQASYNCRESULT")
cpp_quote("{")
cpp_quote("    IRtwqAsyncResult AsyncResult;")
cpp_quote("#else")
cpp_quote("typedef struct tagRTWQASYNCRESULT : public IRtwqAsyncResult {")
cpp_quote("#endif")
cpp_quote("    OVERLAPPED overlapped;")
cpp_quote("    IRtwqAsyncCallback *pCallback;")
cpp_quote("    HRESULT hrStatusResult;")
cpp_quote("    DWORD dwBytesTransferred;")
cpp_quote("    HANDLE hEvent;")
cpp_quote("} RTWQASYNCRESULT;")

cpp_quote("typedef void (WINAPI *RTWQPERIODICCALLBACK)(IUnknown *context);")

cpp_quote("HRESULT WINAPI RtwqAddPeriodicCallback(RTWQPERIODICCALLBACK callback, IUnknown *context, DWORD *key);")
cpp_quote("HRESULT WINAPI RtwqAllocateSerialWorkQueue(DWORD target_queue, DWORD *queue);")
cpp_quote("HRESULT WINAPI RtwqAllocateWorkQueue(RTWQ_WORKQUEUE_TYPE queue_type, DWORD *queue);")
cpp_quote("HRESULT WINAPI RtwqBeginRegisterWorkQueueWithMMCSS(DWORD queue, const WCHAR *mmcss_class, DWORD taskid, LONG priority, IRtwqAsyncCallback *callback, IUnknown *state);")
cpp_quote("HRESULT WINAPI RtwqBeginUnregisterWorkQueueWithMMCSS(DWORD queue, IRtwqAsyncCallback *callback, IUnknown *state);")
cpp_quote("HRESULT WINAPI RtwqCancelDeadline(HANDLE request);")
cpp_quote("HRESULT WINAPI RtwqCancelWorkItem(RTWQWORKITEM_KEY key);")
cpp_quote("HRESULT WINAPI RtwqCreateAsyncResult(IUnknown *object, IRtwqAsyncCallback *callback, IUnknown *state, IRtwqAsyncResult **result);")
cpp_quote("HRESULT WINAPI RtwqEndRegisterWorkQueueWithMMCSS(IRtwqAsyncResult *result, DWORD *taskid);")
cpp_quote("HRESULT WINAPI RtwqGetWorkQueueMMCSSClass(DWORD queue, WCHAR *mmcss_class, DWORD *length);")
cpp_quote("HRESULT WINAPI RtwqGetWorkQueueMMCSSPriority(DWORD queue, LONG *priority);")
cpp_quote("HRESULT WINAPI RtwqGetWorkQueueMMCSSTaskId(DWORD queue, DWORD *taskid);")
cpp_quote("HRESULT WINAPI RtwqInvokeCallback(IRtwqAsyncResult *result);")
cpp_quote("HRESULT WINAPI RtwqJoinWorkQueue(DWORD queue, HANDLE hFile, HANDLE *cookie);")
cpp_quote("HRESULT WINAPI RtwqLockPlatform(void);")
cpp_quote("HRESULT WINAPI RtwqLockSharedWorkQueue(const WCHAR *usageclass, LONG priority, DWORD *taskid, DWORD *queue);")
cpp_quote("HRESULT WINAPI RtwqLockWorkQueue(DWORD queue);")
cpp_quote("HRESULT WINAPI RtwqPutWaitingWorkItem(HANDLE event, LONG priority, IRtwqAsyncResult *result, RTWQWORKITEM_KEY *key);")
cpp_quote("HRESULT WINAPI RtwqPutWorkItem(DWORD queue, LONG priority, IRtwqAsyncResult *result);")
cpp_quote("HRESULT WINAPI RtwqRegisterPlatformEvents(IRtwqPlatformEvents *events);")
cpp_quote("HRESULT WINAPI RtwqRegisterPlatformWithMMCSS(const WCHAR *mmcss_class, DWORD *taskid, LONG priority);")
cpp_quote("HRESULT WINAPI RtwqRemovePeriodicCallback(DWORD key);")
cpp_quote("HRESULT WINAPI RtwqScheduleWorkItem(IRtwqAsyncResult *result, INT64 timeout, RTWQWORKITEM_KEY *key);")
cpp_quote("HRESULT WINAPI RtwqSetDeadline(DWORD queue_id, LONGLONG deadline, HANDLE *request);")
cpp_quote("HRESULT WINAPI RtwqSetDeadline2(DWORD queue_id, LONGLONG deadline, LONGLONG predeadline, HANDLE *request);")
cpp_quote("HRESULT WINAPI RtwqSetLongRunning(DWORD queue_id, WINBOOL enable);")
cpp_quote("HRESULT WINAPI RtwqShutdown(void);")
cpp_quote("HRESULT WINAPI RtwqStartup(void);")
cpp_quote("HRESULT WINAPI RtwqUnjoinWorkQueue(DWORD queue, HANDLE cookie);")
cpp_quote("HRESULT WINAPI RtwqUnlockPlatform(void);")
cpp_quote("HRESULT WINAPI RtwqUnlockWorkQueue(DWORD queue);")
cpp_quote("HRESULT WINAPI RtwqUnregisterPlatformEvents(IRtwqPlatformEvents *events);")
cpp_quote("HRESULT WINAPI RtwqUnregisterPlatformFromMMCSS(void);")
