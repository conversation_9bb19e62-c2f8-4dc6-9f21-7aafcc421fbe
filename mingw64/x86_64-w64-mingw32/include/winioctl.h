/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifdef DEFINE_GUID

#ifndef FAR
#define FAR
#endif

DEFINE_GUID(GUID_DEVINTERFACE_DISK,0x53f56307,0xb6bf,0x11d0,0x94,0xf2,0x00,0xa0,0xc9,0x1e,0xfb,0x8b);
DEFINE_GUID(GUID_DEVINTERFACE_CDROM,0x53f56308,0xb6bf,0x11d0,0x94,0xf2,0x00,0xa0,0xc9,0x1e,0xfb,0x8b);
DEFINE_GUID(GUID_DEVINTERFACE_PARTITION,0x53f5630a,0xb6bf,0x11d0,0x94,0xf2,0x00,0xa0,0xc9,0x1e,0xfb,0x8b);
DEFINE_GUID(GUID_DEVINTERFACE_TAPE,0x53f5630b,0xb6bf,0x11d0,0x94,0xf2,0x00,0xa0,0xc9,0x1e,0xfb,0x8b);
DEFINE_GUID(GUID_DEVINTERFACE_WRITEONCEDISK,0x53f5630c,0xb6bf,0x11d0,0x94,0xf2,0x00,0xa0,0xc9,0x1e,0xfb,0x8b);
DEFINE_GUID(GUID_DEVINTERFACE_VOLUME,0x53f5630d,0xb6bf,0x11d0,0x94,0xf2,0x00,0xa0,0xc9,0x1e,0xfb,0x8b);
DEFINE_GUID(GUID_DEVINTERFACE_MEDIUMCHANGER,0x53f56310,0xb6bf,0x11d0,0x94,0xf2,0x00,0xa0,0xc9,0x1e,0xfb,0x8b);
DEFINE_GUID(GUID_DEVINTERFACE_FLOPPY,0x53f56311,0xb6bf,0x11d0,0x94,0xf2,0x00,0xa0,0xc9,0x1e,0xfb,0x8b);
DEFINE_GUID(GUID_DEVINTERFACE_CDCHANGER,0x53f56312,0xb6bf,0x11d0,0x94,0xf2,0x00,0xa0,0xc9,0x1e,0xfb,0x8b);
DEFINE_GUID(GUID_DEVINTERFACE_STORAGEPORT,0x2accfe60,0xc130,0x11d2,0xb0,0x82,0x00,0xa0,0xc9,0x1e,0xfb,0x8b);
DEFINE_GUID(GUID_DEVINTERFACE_VMLUN,0x6f416619,0x9f29,0x42a5,0xb2,0x0b,0x37,0xe2,0x19,0xca,0x02,0xb0);
DEFINE_GUID(GUID_DEVINTERFACE_SES,0x1790c9ec,0x47d5,0x4df3,0xb5,0xaf,0x9a,0xdf,0x3c,0xf2,0x3e,0x48);
DEFINE_GUID(GUID_DEVINTERFACE_ZNSDISK,0xb87941c5,0xffdb,0x43c7,0xb6,0xb1,0x20,0xb6,0x32,0xf0,0xb1,0x09);
#define WDI_STORAGE_PREDICT_FAILURE_DPS_GUID {0xe9f2d03a,0x747c,0x41c2,{0xbb,0x9a,0x02,0xc6,0x2b,0x6d,0x5f,0xcb}};

DEFINE_GUID(GUID_DEVINTERFACE_SERVICE_VOLUME,0x6ead3d82,0x25ec,0x46bc,0xb7,0xfd,0xc1,0xf0,0xdf,0x8f,0x50,0x37);
DEFINE_GUID(GUID_DEVINTERFACE_HIDDEN_VOLUME,0x7f108a28,0x9833,0x4b3b,0xb7,0x80,0x2c,0x6b,0x5f,0xa5,0xc0,0x62);
DEFINE_GUID(GUID_DEVINTERFACE_UNIFIED_ACCESS_RPMB,0x27447c21,0xbcc3,0x4d07,0xa0,0x5b,0xa3,0x39,0x5b,0xb4,0xee,0xe7);
DEFINE_GUID(GUID_DEVINTERFACE_SCM_PHYSICAL_DEVICE,0x4283609d,0x4dc2,0x43be,0xbb,0xb4,0x4f,0x15,0xdf,0xce,0x2c,0x61);
DEFINE_GUID(GUID_SCM_PD_HEALTH_NOTIFICATION,0x9da2d386,0x72f5,0x4ee3,0x81,0x55,0xec,0xa0,0x67,0x8e,0x3b,0x6);
DEFINE_GUID(GUID_SCM_PD_PASSTHROUGH_INVDIMM,0x4309ac30,0x0d11,0x11e4,0x91,0x91,0x08,0x00,0x20,0x0c,0x9a,0x66);
DEFINE_GUID(GUID_DEVINTERFACE_COMPORT,0x86e0d1e0,0x8089,0x11d0,0x9c,0xe4,0x08,0x00,0x3e,0x30,0x1f,0x73);
DEFINE_GUID(GUID_DEVINTERFACE_SERENUM_BUS_ENUMERATOR,0x4D36E978,0xE325,0x11CE,0xBF,0xC1,0x08,0x00,0x2B,0xE1,0x03,0x18);

#define DiskClassGuid GUID_DEVINTERFACE_DISK
#define CdRomClassGuid GUID_DEVINTERFACE_CDROM
#define PartitionClassGuid GUID_DEVINTERFACE_PARTITION
#define TapeClassGuid GUID_DEVINTERFACE_TAPE
#define WriteOnceDiskClassGuid GUID_DEVINTERFACE_WRITEONCEDISK
#define VolumeClassGuid GUID_DEVINTERFACE_VOLUME
#define MediumChangerClassGuid GUID_DEVINTERFACE_MEDIUMCHANGER
#define FloppyClassGuid GUID_DEVINTERFACE_FLOPPY
#define CdChangerClassGuid GUID_DEVINTERFACE_CDCHANGER
#define StoragePortClassGuid GUID_DEVINTERFACE_STORAGEPORT
#define HiddenVolumeClassGuid GUID_DEVINTERFACE_HIDDEN_VOLUME
#define GUID_CLASS_COMPORT GUID_DEVINTERFACE_COMPORT
#define GUID_SERENUM_BUS_ENUMERATOR GUID_DEVINTERFACE_SERENUM_BUS_ENUMERATOR
#endif /* DEFINE_GUID */

#ifdef DEFINE_DEVPROPKEY

#ifndef __WRAPPED__
#define __WRAPPED__
#endif

DEFINE_DEVPROPKEY(DEVPKEY_Storage_Portable,0x4d1ebee8,0x803,0x4774,0x98,0x42,0xb7,0x7d,0xb5,0x2,0x65,0xe9,2);
DEFINE_DEVPROPKEY(DEVPKEY_Storage_Removable_Media,0x4d1ebee8,0x803,0x4774,0x98,0x42,0xb7,0x7d,0xb5,0x2,0x65,0xe9,3);
DEFINE_DEVPROPKEY(DEVPKEY_Storage_System_Critical,0x4d1ebee8,0x803,0x4774,0x98,0x42,0xb7,0x7d,0xb5,0x2,0x65,0xe9,4);
DEFINE_DEVPROPKEY(DEVPKEY_Storage_Disk_Number,0x4d1ebee8,0x803,0x4774,0x98,0x42,0xb7,0x7d,0xb5,0x2,0x65,0xe9,5);
DEFINE_DEVPROPKEY(DEVPKEY_Storage_Partition_Number,0x4d1ebee8,0x803,0x4774,0x98,0x42,0xb7,0x7d,0xb5,0x2,0x65,0xe9,6);
DEFINE_DEVPROPKEY(DEVPKEY_Storage_Mbr_Type,0x4d1ebee8,0x803,0x4774,0x98,0x42,0xb7,0x7d,0xb5,0x2,0x65,0xe9,7);
DEFINE_DEVPROPKEY(DEVPKEY_Storage_Gpt_Type,0x4d1ebee8,0x803,0x4774,0x98,0x42,0xb7,0x7d,0xb5,0x2,0x65,0xe9,8);
DEFINE_DEVPROPKEY(DEVPKEY_Storage_Gpt_Name,0x4d1ebee8,0x803,0x4774,0x98,0x42,0xb7,0x7d,0xb5,0x2,0x65,0xe9,9);

#endif /* DEFINE_DEVPROPKEY */

#ifndef _WINIOCTL_
#define _WINIOCTL_

#ifndef _DEVIOCTL_
#define _DEVIOCTL_

#ifndef DEVICE_TYPE
#define DEVICE_TYPE DWORD
#endif

#define FILE_DEVICE_BEEP 0x00000001
#define FILE_DEVICE_CD_ROM 0x00000002
#define FILE_DEVICE_CD_ROM_FILE_SYSTEM 0x00000003
#define FILE_DEVICE_CONTROLLER 0x00000004
#define FILE_DEVICE_DATALINK 0x00000005
#define FILE_DEVICE_DFS 0x00000006
#define FILE_DEVICE_DISK 0x00000007
#define FILE_DEVICE_DISK_FILE_SYSTEM 0x00000008
#define FILE_DEVICE_FILE_SYSTEM 0x00000009
#define FILE_DEVICE_INPORT_PORT 0x0000000a
#define FILE_DEVICE_KEYBOARD 0x0000000b
#define FILE_DEVICE_MAILSLOT 0x0000000c
#define FILE_DEVICE_MIDI_IN 0x0000000d
#define FILE_DEVICE_MIDI_OUT 0x0000000e
#define FILE_DEVICE_MOUSE 0x0000000f
#define FILE_DEVICE_MULTI_UNC_PROVIDER 0x00000010
#define FILE_DEVICE_NAMED_PIPE 0x00000011
#define FILE_DEVICE_NETWORK 0x00000012
#define FILE_DEVICE_NETWORK_BROWSER 0x00000013
#define FILE_DEVICE_NETWORK_FILE_SYSTEM 0x00000014
#define FILE_DEVICE_NULL 0x00000015
#define FILE_DEVICE_PARALLEL_PORT 0x00000016
#define FILE_DEVICE_PHYSICAL_NETCARD 0x00000017
#define FILE_DEVICE_PRINTER 0x00000018
#define FILE_DEVICE_SCANNER 0x00000019
#define FILE_DEVICE_SERIAL_MOUSE_PORT 0x0000001a
#define FILE_DEVICE_SERIAL_PORT 0x0000001b
#define FILE_DEVICE_SCREEN 0x0000001c
#define FILE_DEVICE_SOUND 0x0000001d
#define FILE_DEVICE_STREAMS 0x0000001e
#define FILE_DEVICE_TAPE 0x0000001f
#define FILE_DEVICE_TAPE_FILE_SYSTEM 0x00000020
#define FILE_DEVICE_TRANSPORT 0x00000021
#define FILE_DEVICE_UNKNOWN 0x00000022
#define FILE_DEVICE_VIDEO 0x00000023
#define FILE_DEVICE_VIRTUAL_DISK 0x00000024
#define FILE_DEVICE_WAVE_IN 0x00000025
#define FILE_DEVICE_WAVE_OUT 0x00000026
#define FILE_DEVICE_8042_PORT 0x00000027
#define FILE_DEVICE_NETWORK_REDIRECTOR 0x00000028
#define FILE_DEVICE_BATTERY 0x00000029
#define FILE_DEVICE_BUS_EXTENDER 0x0000002a
#define FILE_DEVICE_MODEM 0x0000002b
#define FILE_DEVICE_VDM 0x0000002c
#define FILE_DEVICE_MASS_STORAGE 0x0000002d
#define FILE_DEVICE_SMB 0x0000002e
#define FILE_DEVICE_KS 0x0000002f
#define FILE_DEVICE_CHANGER 0x00000030
#define FILE_DEVICE_SMARTCARD 0x00000031
#define FILE_DEVICE_ACPI 0x00000032
#define FILE_DEVICE_DVD 0x00000033
#define FILE_DEVICE_FULLSCREEN_VIDEO 0x00000034
#define FILE_DEVICE_DFS_FILE_SYSTEM 0x00000035
#define FILE_DEVICE_DFS_VOLUME 0x00000036
#define FILE_DEVICE_SERENUM 0x00000037
#define FILE_DEVICE_TERMSRV 0x00000038
#define FILE_DEVICE_KSEC 0x00000039
#define FILE_DEVICE_FIPS 0x0000003A
#define FILE_DEVICE_INFINIBAND 0x0000003B
#define FILE_DEVICE_VMBUS 0x0000003E
#define FILE_DEVICE_CRYPT_PROVIDER 0x0000003F
#define FILE_DEVICE_WPD 0x00000040
#define FILE_DEVICE_BLUETOOTH 0x00000041
#define FILE_DEVICE_MT_COMPOSITE 0x00000042
#define FILE_DEVICE_MT_TRANSPORT 0x00000043
#define FILE_DEVICE_BIOMETRIC 0x00000044
#define FILE_DEVICE_PMI 0x00000045
#define FILE_DEVICE_EHSTOR 0x00000046
#define FILE_DEVICE_DEVAPI 0x00000047
#define FILE_DEVICE_GPIO 0x00000048
#define FILE_DEVICE_USBEX 0x00000049
#define FILE_DEVICE_CONSOLE 0x00000050
#define FILE_DEVICE_NFP 0x00000051
#define FILE_DEVICE_SYSENV 0x00000052
#define FILE_DEVICE_VIRTUAL_BLOCK 0x00000053
#define FILE_DEVICE_POINT_OF_SERVICE 0x00000054
#define FILE_DEVICE_STORAGE_REPLICATION 0x00000055
#define FILE_DEVICE_TRUST_ENV 0x00000056
#define FILE_DEVICE_UCM 0x00000057
#define FILE_DEVICE_UCMTCPCI 0x00000058
#define FILE_DEVICE_PERSISTENT_MEMORY 0x00000059
#define FILE_DEVICE_NVDIMM 0x0000005a
#define FILE_DEVICE_HOLOGRAPHIC 0x0000005b
#define FILE_DEVICE_SDFXHCI 0x0000005c
#define FILE_DEVICE_UCMUCSI 0x0000005d
#define FILE_DEVICE_PRM 0x0000005e
#define FILE_DEVICE_EVENT_COLLECTOR 0x0000005f
#define FILE_DEVICE_USB4 0x00000060
#define FILE_DEVICE_SOUNDWIRE 0x00000061

#define CTL_CODE(DeviceType,Function,Method,Access) (((DeviceType) << 16) | ((Access) << 14) | ((Function) << 2) | (Method))

#define DEVICE_TYPE_FROM_CTL_CODE(ctrlCode) (((DWORD)(ctrlCode & 0xffff0000)) >> 16)
#define METHOD_FROM_CTL_CODE(ctrlCode) ((DWORD)(ctrlCode & 3))

#define METHOD_BUFFERED 0
#define METHOD_IN_DIRECT 1
#define METHOD_OUT_DIRECT 2
#define METHOD_NEITHER 3

#define METHOD_DIRECT_TO_HARDWARE METHOD_IN_DIRECT
#define METHOD_DIRECT_FROM_HARDWARE METHOD_OUT_DIRECT

#define FILE_ANY_ACCESS 0
#define FILE_SPECIAL_ACCESS (FILE_ANY_ACCESS)
#define FILE_READ_ACCESS (0x0001)
#define FILE_WRITE_ACCESS (0x0002)

#endif /* _DEVIOCTL_ */


#ifndef _NTDDSTOR_H_
#define _NTDDSTOR_H_

#ifdef __cplusplus
extern "C" {
#endif

#define IOCTL_STORAGE_BASE FILE_DEVICE_MASS_STORAGE

#define IOCTL_STORAGE_CHECK_VERIFY CTL_CODE(IOCTL_STORAGE_BASE,0x0200,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_STORAGE_CHECK_VERIFY2 CTL_CODE(IOCTL_STORAGE_BASE,0x0200,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_STORAGE_MEDIA_REMOVAL CTL_CODE(IOCTL_STORAGE_BASE,0x0201,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_STORAGE_EJECT_MEDIA CTL_CODE(IOCTL_STORAGE_BASE,0x0202,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_STORAGE_LOAD_MEDIA CTL_CODE(IOCTL_STORAGE_BASE,0x0203,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_STORAGE_LOAD_MEDIA2 CTL_CODE(IOCTL_STORAGE_BASE,0x0203,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_STORAGE_RESERVE CTL_CODE(IOCTL_STORAGE_BASE,0x0204,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_STORAGE_RELEASE CTL_CODE(IOCTL_STORAGE_BASE,0x0205,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_STORAGE_FIND_NEW_DEVICES CTL_CODE(IOCTL_STORAGE_BASE,0x0206,METHOD_BUFFERED,FILE_READ_ACCESS)

#define IOCTL_STORAGE_EJECTION_CONTROL CTL_CODE(IOCTL_STORAGE_BASE,0x0250,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_STORAGE_MCN_CONTROL CTL_CODE(IOCTL_STORAGE_BASE,0x0251,METHOD_BUFFERED,FILE_ANY_ACCESS)

#define IOCTL_STORAGE_GET_MEDIA_TYPES CTL_CODE(IOCTL_STORAGE_BASE,0x0300,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_STORAGE_GET_MEDIA_TYPES_EX CTL_CODE(IOCTL_STORAGE_BASE,0x0301,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_STORAGE_GET_MEDIA_SERIAL_NUMBER CTL_CODE(IOCTL_STORAGE_BASE,0x0304,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_STORAGE_GET_HOTPLUG_INFO CTL_CODE(IOCTL_STORAGE_BASE,0x0305,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_STORAGE_SET_HOTPLUG_INFO CTL_CODE(IOCTL_STORAGE_BASE,0x0306,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)

#define IOCTL_STORAGE_RESET_BUS CTL_CODE(IOCTL_STORAGE_BASE,0x0400,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_STORAGE_RESET_DEVICE CTL_CODE(IOCTL_STORAGE_BASE,0x0401,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_STORAGE_BREAK_RESERVATION CTL_CODE(IOCTL_STORAGE_BASE,0x0405,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_STORAGE_PERSISTENT_RESERVE_IN CTL_CODE(IOCTL_STORAGE_BASE, 0x0406, METHOD_BUFFERED, FILE_READ_ACCESS)
#define IOCTL_STORAGE_PERSISTENT_RESERVE_OUT CTL_CODE(IOCTL_STORAGE_BASE, 0x0407, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)

#define IOCTL_STORAGE_GET_DEVICE_NUMBER CTL_CODE(IOCTL_STORAGE_BASE,0x0420,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_STORAGE_GET_DEVICE_NUMBER_EX CTL_CODE(IOCTL_STORAGE_BASE,0x0421,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_STORAGE_PREDICT_FAILURE CTL_CODE(IOCTL_STORAGE_BASE,0x0440,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_STORAGE_FAILURE_PREDICTION_CONFIG CTL_CODE(IOCTL_STORAGE_BASE,0x0441,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_STORAGE_GET_COUNTERS CTL_CODE(IOCTL_STORAGE_BASE,0x442,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_STORAGE_READ_CAPACITY CTL_CODE(IOCTL_STORAGE_BASE,0x0450,METHOD_BUFFERED,FILE_READ_ACCESS)

#define IOCTL_STORAGE_GET_DEVICE_TELEMETRY CTL_CODE(IOCTL_STORAGE_BASE, 0x0470, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_STORAGE_DEVICE_TELEMETRY_NOTIFY CTL_CODE(IOCTL_STORAGE_BASE, 0x0471, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_STORAGE_DEVICE_TELEMETRY_QUERY_CAPS CTL_CODE(IOCTL_STORAGE_BASE, 0x0472, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_STORAGE_GET_DEVICE_TELEMETRY_RAW CTL_CODE(IOCTL_STORAGE_BASE, 0x0473, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)

#define IOCTL_STORAGE_SET_TEMPERATURE_THRESHOLD CTL_CODE(IOCTL_STORAGE_BASE, 0x0480, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)

#define IOCTL_STORAGE_PROTOCOL_COMMAND CTL_CODE(IOCTL_STORAGE_BASE, 0x04F0, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)

#define IOCTL_STORAGE_SET_PROPERTY CTL_CODE(IOCTL_STORAGE_BASE, 0x04FF, METHOD_BUFFERED, FILE_WRITE_ACCESS)
#define IOCTL_STORAGE_QUERY_PROPERTY CTL_CODE(IOCTL_STORAGE_BASE, 0x0500, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_STORAGE_MANAGE_DATA_SET_ATTRIBUTES CTL_CODE(IOCTL_STORAGE_BASE, 0x0501, METHOD_BUFFERED, FILE_WRITE_ACCESS)
#define IOCTL_STORAGE_GET_LB_PROVISIONING_MAP_RESOURCES CTL_CODE(IOCTL_STORAGE_BASE, 0x0502, METHOD_BUFFERED, FILE_READ_ACCESS)

#define IOCTL_STORAGE_REINITIALIZE_MEDIA CTL_CODE(IOCTL_STORAGE_BASE, 0x0590, METHOD_BUFFERED, FILE_WRITE_ACCESS)

#define IOCTL_STORAGE_GET_BC_PROPERTIES CTL_CODE(IOCTL_STORAGE_BASE, 0x0600, METHOD_BUFFERED, FILE_READ_ACCESS)
#define IOCTL_STORAGE_ALLOCATE_BC_STREAM CTL_CODE(IOCTL_STORAGE_BASE, 0x0601, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_STORAGE_FREE_BC_STREAM CTL_CODE(IOCTL_STORAGE_BASE, 0x0602, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)

#define IOCTL_STORAGE_CHECK_PRIORITY_HINT_SUPPORT CTL_CODE(IOCTL_STORAGE_BASE, 0x0620, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_STORAGE_START_DATA_INTEGRITY_CHECK CTL_CODE(IOCTL_STORAGE_BASE, 0x0621, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_STORAGE_STOP_DATA_INTEGRITY_CHECK CTL_CODE(IOCTL_STORAGE_BASE, 0x0622, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)

#define OBSOLETE_IOCTL_STORAGE_RESET_BUS CTL_CODE(IOCTL_STORAGE_BASE,0x0400,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define OBSOLETE_IOCTL_STORAGE_RESET_DEVICE CTL_CODE(IOCTL_STORAGE_BASE,0x0401,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)

#define IOCTL_STORAGE_FIRMWARE_GET_INFO CTL_CODE(IOCTL_STORAGE_BASE, 0x0700, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_STORAGE_FIRMWARE_DOWNLOAD CTL_CODE(IOCTL_STORAGE_BASE, 0x0701, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_STORAGE_FIRMWARE_ACTIVATE CTL_CODE(IOCTL_STORAGE_BASE, 0x0702, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)

#define IOCTL_STORAGE_ENABLE_IDLE_POWER CTL_CODE(IOCTL_STORAGE_BASE, 0x0720, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_STORAGE_GET_IDLE_POWERUP_REASON CTL_CODE(IOCTL_STORAGE_BASE, 0x0721, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_STORAGE_POWER_ACTIVE CTL_CODE(IOCTL_STORAGE_BASE, 0x0722, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_STORAGE_POWER_IDLE CTL_CODE(IOCTL_STORAGE_BASE, 0x0723, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define IOCTL_STORAGE_EVENT_NOTIFICATION CTL_CODE(IOCTL_STORAGE_BASE, 0x0724, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_STORAGE_DEVICE_POWER_CAP CTL_CODE(IOCTL_STORAGE_BASE, 0x0725, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_STORAGE_RPMB_COMMAND CTL_CODE(IOCTL_STORAGE_BASE, 0x0726, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_STORAGE_ATTRIBUTE_MANAGEMENT CTL_CODE(IOCTL_STORAGE_BASE, 0x0727, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_STORAGE_DIAGNOSTIC CTL_CODE(IOCTL_STORAGE_BASE, 0x0728, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_STORAGE_GET_PHYSICAL_ELEMENT_STATUS CTL_CODE(IOCTL_STORAGE_BASE, 0x0729, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_STORAGE_REMOVE_ELEMENT_AND_TRUNCATE CTL_CODE(IOCTL_STORAGE_BASE, 0x0730, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_STORAGE_GET_DEVICE_INTERNAL_LOG CTL_CODE(IOCTL_STORAGE_BASE, 0x0731, METHOD_BUFFERED, FILE_ANY_ACCESS)

typedef struct _STORAGE_READ_CAPACITY {
  ULONG Version;
  ULONG Size;
  ULONG BlockLength;
  LARGE_INTEGER NumberOfBlocks;
  LARGE_INTEGER DiskLength;
} STORAGE_READ_CAPACITY, *PSTORAGE_READ_CAPACITY;

#define IOCTL_STORAGE_MANAGE_DATA_SET_ATTRIBUTES CTL_CODE(IOCTL_STORAGE_BASE, 0x0501, METHOD_BUFFERED, FILE_WRITE_ACCESS)
#define DeviceDsmActionFlag_NonDestructive 0x80000000
#define DeviceDsmAction_None 0
#define DeviceDsmAction_Trim 1
#define DeviceDsmAction_Notification (2 | DeviceDsmActionFlag_NonDestructive)

#define IsDsmActionNonDestructive(x) ((BOOLEAN)(!!((x) & DeviceDsmActionFlag_NonDestructive)))

#define DEVICE_DSM_FLAG_ENTIRE_DATA_SET_RANGE __MSABI_LONG(0x00000001)

#define DEVICE_DSM_NOTIFY_FLAG_BEGIN             0x00000001
#define DEVICE_DSM_NOTIFY_FLAG_END               0x00000002

#define IOCTL_STORAGE_BC_VERSION                 1

#define STORAGE_PRIORITY_HINT_SUPPORTED          0x0001

  typedef DWORD DEVICE_DATA_MANAGEMENT_SET_ACTION;
  typedef struct _DEVICE_MANAGE_DATA_SET_ATTRIBUTES {
    DWORD                             Size;
    DEVICE_DATA_MANAGEMENT_SET_ACTION Action;
    DWORD                             Flags;
    DWORD                             ParameterBlockOffset;
    DWORD                             ParameterBlockLength;
    DWORD                             DataSetRangesOffset;
    DWORD                             DataSetRangesLength;
  } DEVICE_MANAGE_DATA_SET_ATTRIBUTES, *PDEVICE_MANAGE_DATA_SET_ATTRIBUTES;

  typedef struct _DEVICE_DATA_SET_RANGE {
    LONGLONG  StartingOffset;
    DWORDLONG LengthInBytes;
  } DEVICE_DATA_SET_RANGE, *PDEVICE_DATA_SET_RANGE;

  typedef struct _DEVICE_DSM_NOTIFICATION_PARAMETERS {
    ULONG Size;
    ULONG Flags;
    ULONG NumFileTypeIDs;
    GUID FileTypeID[1];
  } DEVICE_DSM_NOTIFICATION_PARAMETERS, *PDEVICE_DSM_NOTIFICATION_PARAMETERS;

  typedef struct _STORAGE_GET_BC_PROPERTIES_OUTPUT {
    ULONG MaximumRequestsPerPeriod;
    ULONG MinimumPeriod;
    ULONGLONG MaximumRequestSize;
    ULONG EstimatedTimePerRequest;
    ULONG NumOutStandingRequests;
    ULONGLONG RequestSize;
  } STORAGE_GET_BC_PROPERTIES_OUTPUT, *PSTORAGE_GET_BC_PROPERTIES_OUTPUT;

  typedef struct _STORAGE_ALLOCATE_BC_STREAM_INPUT {
    ULONG Version;
    ULONG RequestsPerPeriod;
    ULONG Period;
    BOOLEAN RetryFailures;
    BOOLEAN Discardable;
    BOOLEAN Reserved1[2];
    ULONG AccessType;
    ULONG AccessMode;
  } STORAGE_ALLOCATE_BC_STREAM_INPUT, *PSTORAGE_ALLOCATE_BC_STREAM_INPUT;

  typedef struct _STORAGE_ALLOCATE_BC_STREAM_OUTPUT {
    ULONGLONG RequestSize;
    ULONG NumOutStandingRequests;
  } STORAGE_ALLOCATE_BC_STREAM_OUTPUT, *PSTORAGE_ALLOCATE_BC_STREAM_OUTPUT;

  typedef struct _STORAGE_PRIORITY_HINT_SUPPORT {
    ULONG SupportFlags;
  } STORAGE_PRIORITY_HINT_SUPPORT, *PSTORAGE_PRIORITY_HINT_SUPPORT;

#if defined(_MSC_EXTENSIONS) || defined(__GNUC__)

  typedef struct _STORAGE_MEDIA_SERIAL_NUMBER_DATA {
    USHORT Reserved;
    USHORT SerialNumberLength;
    UCHAR SerialNumber[0];
  } STORAGE_MEDIA_SERIAL_NUMBER_DATA, *PSTORAGE_MEDIA_SERIAL_NUMBER_DATA;

  typedef struct _PERSISTENT_RESERVE_COMMAND {
    ULONG Version;
    ULONG Size;
    __C89_NAMELESS union {
      struct {
        UCHAR ServiceAction:5;
        UCHAR Reserved1:3;
        USHORT AllocationLength;
      } PR_IN;
      struct {
        UCHAR ServiceAction:5;
        UCHAR Reserved1:3;
        UCHAR Type:4;
        UCHAR Scope:4;
        UCHAR ParameterList[0];
      } PR_OUT;
    } DUMMYUNIONNAME;
  } PERSISTENT_RESERVE_COMMAND, *PPERSISTENT_RESERVE_COMMAND;

#endif /* defined(_MSC_EXTENSIONS) */

  typedef struct _STORAGE_HOTPLUG_INFO {
    DWORD Size;
    BOOLEAN MediaRemovable;
    BOOLEAN MediaHotplug;
    BOOLEAN DeviceHotplug;
    BOOLEAN WriteCacheEnableOverride;
  } STORAGE_HOTPLUG_INFO,*PSTORAGE_HOTPLUG_INFO;

  typedef struct _STORAGE_DEVICE_NUMBER {
    DEVICE_TYPE DeviceType;
    DWORD DeviceNumber;
    DWORD PartitionNumber;
  } STORAGE_DEVICE_NUMBER,*PSTORAGE_DEVICE_NUMBER;

  typedef struct _STORAGE_DEVICE_NUMBERS {
    DWORD Version;
    DWORD Size;
    DWORD NumberOfDevices;
    STORAGE_DEVICE_NUMBER Devices[ANYSIZE_ARRAY];
  } STORAGE_DEVICE_NUMBERS,*PSTORAGE_DEVICE_NUMBERS;

#define STORAGE_DEVICE_FLAGS_RANDOM_DEVICEGUID_REASON_CONFLICT 0x1
#define STORAGE_DEVICE_FLAGS_RANDOM_DEVICEGUID_REASON_NOHWID 0x2
#define STORAGE_DEVICE_FLAGS_PAGE_83_DEVICEGUID 0x4

  typedef struct _STORAGE_DEVICE_NUMBER_EX {
    DWORD Version;
    DWORD Size;
    DWORD Flags;
    DEVICE_TYPE DeviceType;
    DWORD DeviceNumber;
    GUID DeviceGuid;
    DWORD PartitionNumber;
  } STORAGE_DEVICE_NUMBER_EX,*PSTORAGE_DEVICE_NUMBER_EX;

  typedef struct _STORAGE_BUS_RESET_REQUEST {
    BYTE PathId;
  } STORAGE_BUS_RESET_REQUEST,*PSTORAGE_BUS_RESET_REQUEST;

  typedef struct STORAGE_BREAK_RESERVATION_REQUEST {
    DWORD Length;
    BYTE _unused;
    BYTE PathId;
    BYTE TargetId;
    BYTE Lun;
  } STORAGE_BREAK_RESERVATION_REQUEST,*PSTORAGE_BREAK_RESERVATION_REQUEST;

  typedef struct _PREVENT_MEDIA_REMOVAL {
    BOOLEAN PreventMediaRemoval;
  } PREVENT_MEDIA_REMOVAL,*PPREVENT_MEDIA_REMOVAL;

  typedef struct _CLASS_MEDIA_CHANGE_CONTEXT {
    DWORD MediaChangeCount;
    DWORD NewState;
  } CLASS_MEDIA_CHANGE_CONTEXT,*PCLASS_MEDIA_CHANGE_CONTEXT;

  typedef struct _TAPE_STATISTICS {
    DWORD Version;
    DWORD Flags;
    LARGE_INTEGER RecoveredWrites;
    LARGE_INTEGER UnrecoveredWrites;
    LARGE_INTEGER RecoveredReads;
    LARGE_INTEGER UnrecoveredReads;
    BYTE CompressionRatioReads;
    BYTE CompressionRatioWrites;
  } TAPE_STATISTICS,*PTAPE_STATISTICS;

#define RECOVERED_WRITES_VALID 0x00000001
#define UNRECOVERED_WRITES_VALID 0x00000002
#define RECOVERED_READS_VALID 0x00000004
#define UNRECOVERED_READS_VALID 0x00000008
#define WRITE_COMPRESSION_INFO_VALID 0x00000010
#define READ_COMPRESSION_INFO_VALID 0x00000020

  typedef struct _TAPE_GET_STATISTICS {
    DWORD Operation;
  } TAPE_GET_STATISTICS,*PTAPE_GET_STATISTICS;

#define TAPE_RETURN_STATISTICS __MSABI_LONG(0)
#define TAPE_RETURN_ENV_INFO __MSABI_LONG(1)
#define TAPE_RESET_STATISTICS __MSABI_LONG(2)

  typedef enum _STORAGE_MEDIA_TYPE {
    DDS_4mm = 0x20,
    MiniQic,
    Travan,
    QIC,
    MP_8mm,
    AME_8mm,
    AIT1_8mm,
    DLT,
    NCTP,
    IBM_3480,
    IBM_3490E,
    IBM_Magstar_3590,
    IBM_Magstar_MP,
    STK_DATA_D3,
    SONY_DTF,
    DV_6mm,
    DMI,
    SONY_D2,
    CLEANER_CARTRIDGE,
    CD_ROM,
    CD_R,
    CD_RW,
    DVD_ROM,
    DVD_R,
    DVD_RW,
    MO_3_RW,
    MO_5_WO,
    MO_5_RW,
    MO_5_LIMDOW,
    PC_5_WO,
    PC_5_RW,
    PD_5_RW,
    ABL_5_WO,
    PINNACLE_APEX_5_RW,
    SONY_12_WO,
    PHILIPS_12_WO,
    HITACHI_12_WO,
    CYGNET_12_WO,
    KODAK_14_WO,
    MO_NFR_525,
    NIKON_12_RW,
    IOMEGA_ZIP,
    IOMEGA_JAZ,
    SYQUEST_EZ135,
    SYQUEST_EZFLYER,
    SYQUEST_SYJET,
    AVATAR_F2,
    MP2_8mm,
    DST_S,
    DST_M,
    DST_L,
    VXATape_1,
    VXATape_2,
    STK_9840,
    LTO_Ultrium,
    LTO_Accelis,
    DVD_RAM,
    AIT_8mm,
    ADR_1,
    ADR_2,
    STK_9940,
    SAIT,
    VXATape
  } STORAGE_MEDIA_TYPE, *PSTORAGE_MEDIA_TYPE;

#define MEDIA_ERASEABLE 0x00000001
#define MEDIA_WRITE_ONCE 0x00000002
#define MEDIA_READ_ONLY 0x00000004
#define MEDIA_READ_WRITE 0x00000008

#define MEDIA_WRITE_PROTECTED 0x00000100
#define MEDIA_CURRENTLY_MOUNTED 0x80000000

  typedef enum _STORAGE_BUS_TYPE {
    BusTypeUnknown             = 0x00,
    BusTypeScsi                = 0x1,
    BusTypeAtapi               = 0x2,
    BusTypeAta                 = 0x3,
    BusType1394                = 0x4,
    BusTypeSsa                 = 0x5,
    BusTypeFibre               = 0x6,
    BusTypeUsb                 = 0x7,
    BusTypeRAID                = 0x8,
#if (_WIN32_WINNT >= 0x0600)
    BusTypeiScsi               = 0x9,
    BusTypeSas                 = 0xA,
    BusTypeSata                = 0xB,
    BusTypeSd                  = 0xC,
    BusTypeMmc                 = 0xD,
#endif /*(_WIN32_WINNT >= 0x0600)*/
#if (_WIN32_WINNT >= 0x0601)
    BusTypeVirtual             = 0xE,
    BusTypeFileBackedVirtual   = 0xF,
    BusTypeSpaces              = 0x10,
    BusTypeNvme                = 0x11,
    BusTypeSCM                 = 0x12,
    BusTypeUfs                 = 0x13,
#endif /*(_WIN32_WINNT >= 0x0601)*/
    BusTypeMax,
    BusTypeMaxReserved         = 0x7F 
  } STORAGE_BUS_TYPE, *PSTORAGE_BUS_TYPE;

#define SupportsDeviceSharing(BusType) ((BusType == BusTypeScsi) || (BusType == BusTypeFibre) || (BusType == BusTypeiScsi) || (BusType == BusTypeSas) || (BusType == BusTypeSpaces))

  typedef struct _DEVICE_MEDIA_INFO {
    union {
      struct {
	LARGE_INTEGER Cylinders;
	STORAGE_MEDIA_TYPE MediaType;
	DWORD TracksPerCylinder;
	DWORD SectorsPerTrack;
	DWORD BytesPerSector;
	DWORD NumberMediaSides;
	DWORD MediaCharacteristics;
      } DiskInfo;
      struct {
	LARGE_INTEGER Cylinders;
	STORAGE_MEDIA_TYPE MediaType;
	DWORD TracksPerCylinder;
	DWORD SectorsPerTrack;
	DWORD BytesPerSector;
	DWORD NumberMediaSides;
	DWORD MediaCharacteristics;
      } RemovableDiskInfo;
      struct {
	STORAGE_MEDIA_TYPE MediaType;
	DWORD MediaCharacteristics;
	DWORD CurrentBlockSize;
	STORAGE_BUS_TYPE BusType;
	union {
	  struct {
	    BYTE MediumType;
	    BYTE DensityCode;
	  } ScsiInformation;
	} BusSpecificData;
      } TapeInfo;
    } DeviceSpecific;
  } DEVICE_MEDIA_INFO,*PDEVICE_MEDIA_INFO;

  typedef struct _GET_MEDIA_TYPES {
    DWORD DeviceType;
    DWORD MediaInfoCount;
    DEVICE_MEDIA_INFO MediaInfo[1];
  } GET_MEDIA_TYPES,*PGET_MEDIA_TYPES;

  typedef struct _STORAGE_PREDICT_FAILURE {
    DWORD PredictFailure;
    BYTE VendorSpecific[512];
  } STORAGE_PREDICT_FAILURE,*PSTORAGE_PREDICT_FAILURE;

  typedef struct _STORAGE_FAILURE_PREDICTION_CONFIG {
    DWORD Version;
    DWORD Size;
    BOOLEAN Set;
    BOOLEAN Enabled;
    WORD Reserved;
  } STORAGE_FAILURE_PREDICTION_CONFIG,*PSTORAGE_FAILURE_PREDICTION_CONFIG;

#define STORAGE_FAILURE_PREDICTION_CONFIG_V1 1

#ifdef __cplusplus
}
#endif
#endif /* _NTDDSTOR_H_ */


#ifndef _NTDDDISK_H_
#define _NTDDDISK_H_

#define IOCTL_DISK_BASE FILE_DEVICE_DISK
#define IOCTL_DISK_GET_DRIVE_GEOMETRY CTL_CODE(IOCTL_DISK_BASE,0x0000,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_DISK_GET_PARTITION_INFO CTL_CODE(IOCTL_DISK_BASE,0x0001,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_DISK_SET_PARTITION_INFO CTL_CODE(IOCTL_DISK_BASE,0x0002,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_DISK_GET_DRIVE_LAYOUT CTL_CODE(IOCTL_DISK_BASE,0x0003,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_DISK_SET_DRIVE_LAYOUT CTL_CODE(IOCTL_DISK_BASE,0x0004,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_DISK_VERIFY CTL_CODE(IOCTL_DISK_BASE,0x0005,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_DISK_FORMAT_TRACKS CTL_CODE(IOCTL_DISK_BASE,0x0006,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_DISK_REASSIGN_BLOCKS CTL_CODE(IOCTL_DISK_BASE,0x0007,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_DISK_PERFORMANCE CTL_CODE(IOCTL_DISK_BASE,0x0008,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_DISK_IS_WRITABLE CTL_CODE(IOCTL_DISK_BASE,0x0009,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_DISK_LOGGING CTL_CODE(IOCTL_DISK_BASE,0x000a,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_DISK_FORMAT_TRACKS_EX CTL_CODE(IOCTL_DISK_BASE,0x000b,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_DISK_HISTOGRAM_STRUCTURE CTL_CODE(IOCTL_DISK_BASE,0x000c,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_DISK_HISTOGRAM_DATA CTL_CODE(IOCTL_DISK_BASE,0x000d,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_DISK_HISTOGRAM_RESET CTL_CODE(IOCTL_DISK_BASE,0x000e,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_DISK_REQUEST_STRUCTURE CTL_CODE(IOCTL_DISK_BASE,0x000f,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_DISK_REQUEST_DATA CTL_CODE(IOCTL_DISK_BASE,0x0010,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_DISK_PERFORMANCE_OFF CTL_CODE(IOCTL_DISK_BASE,0x0018,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_DISK_CONTROLLER_NUMBER CTL_CODE(IOCTL_DISK_BASE,0x0011,METHOD_BUFFERED,FILE_ANY_ACCESS)

#define SMART_GET_VERSION CTL_CODE(IOCTL_DISK_BASE,0x0020,METHOD_BUFFERED,FILE_READ_ACCESS)
#define SMART_SEND_DRIVE_COMMAND CTL_CODE(IOCTL_DISK_BASE,0x0021,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define SMART_RCV_DRIVE_DATA CTL_CODE(IOCTL_DISK_BASE,0x0022,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)

#define IOCTL_DISK_GET_PARTITION_INFO_EX CTL_CODE(IOCTL_DISK_BASE,0x0012,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_DISK_SET_PARTITION_INFO_EX CTL_CODE(IOCTL_DISK_BASE,0x0013,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_DISK_GET_DRIVE_LAYOUT_EX CTL_CODE(IOCTL_DISK_BASE,0x0014,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_DISK_SET_DRIVE_LAYOUT_EX CTL_CODE(IOCTL_DISK_BASE,0x0015,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_DISK_CREATE_DISK CTL_CODE(IOCTL_DISK_BASE,0x0016,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_DISK_GET_LENGTH_INFO CTL_CODE(IOCTL_DISK_BASE,0x0017,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_DISK_GET_DRIVE_GEOMETRY_EX CTL_CODE(IOCTL_DISK_BASE,0x0028,METHOD_BUFFERED,FILE_ANY_ACCESS)

#define IOCTL_DISK_REASSIGN_BLOCKS_EX CTL_CODE(IOCTL_DISK_BASE,0x0029,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_DISK_UPDATE_DRIVE_SIZE CTL_CODE(IOCTL_DISK_BASE,0x0032,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_DISK_GROW_PARTITION CTL_CODE(IOCTL_DISK_BASE,0x0034,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_DISK_GET_CACHE_INFORMATION CTL_CODE(IOCTL_DISK_BASE,0x0035,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_DISK_SET_CACHE_INFORMATION CTL_CODE(IOCTL_DISK_BASE,0x0036,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define OBSOLETE_DISK_GET_WRITE_CACHE_STATE CTL_CODE(IOCTL_DISK_BASE,0x0037,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_DISK_DELETE_DRIVE_LAYOUT CTL_CODE(IOCTL_DISK_BASE,0x0040,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_DISK_UPDATE_PROPERTIES CTL_CODE(IOCTL_DISK_BASE,0x0050,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_DISK_RESET_SNAPSHOT_INFO CTL_CODE(IOCTL_DISK_BASE,0x0084,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_DISK_FORMAT_DRIVE CTL_CODE(IOCTL_DISK_BASE,0x00f3,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_DISK_SENSE_DEVICE CTL_CODE(IOCTL_DISK_BASE,0x00f8,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_DISK_CHECK_VERIFY CTL_CODE(IOCTL_DISK_BASE,0x0200,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_DISK_MEDIA_REMOVAL CTL_CODE(IOCTL_DISK_BASE,0x0201,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_DISK_EJECT_MEDIA CTL_CODE(IOCTL_DISK_BASE,0x0202,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_DISK_LOAD_MEDIA CTL_CODE(IOCTL_DISK_BASE,0x0203,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_DISK_RESERVE CTL_CODE(IOCTL_DISK_BASE,0x0204,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_DISK_RELEASE CTL_CODE(IOCTL_DISK_BASE,0x0205,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_DISK_FIND_NEW_DEVICES CTL_CODE(IOCTL_DISK_BASE,0x0206,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_DISK_GET_MEDIA_TYPES CTL_CODE(IOCTL_DISK_BASE,0x0300,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_STORAGE_QUERY_PROPERTY CTL_CODE(IOCTL_STORAGE_BASE, 0x0500, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define PARTITION_ENTRY_UNUSED 0x00
#define PARTITION_FAT_12 0x01
#define PARTITION_XENIX_1 0x02
#define PARTITION_XENIX_2 0x03
#define PARTITION_FAT_16 0x04
#define PARTITION_EXTENDED 0x05
#define PARTITION_HUGE 0x06
#define PARTITION_IFS 0x07
#define PARTITION_OS2BOOTMGR 0x0A
#define PARTITION_FAT32 0x0B
#define PARTITION_FAT32_XINT13 0x0C
#define PARTITION_XINT13 0x0E
#define PARTITION_XINT13_EXTENDED 0x0F
#define PARTITION_PREP 0x41
#define PARTITION_LDM 0x42
#define PARTITION_UNIX 0x63

#define VALID_NTFT 0xC0

#define PARTITION_NTFT 0x80

#define IsRecognizedPartition(PartitionType) (((PartitionType & PARTITION_NTFT) && ((PartitionType & ~0xC0)==PARTITION_FAT_12)) || ((PartitionType & PARTITION_NTFT) && ((PartitionType & ~0xC0)==PARTITION_IFS)) || ((PartitionType & PARTITION_NTFT) && ((PartitionType & ~0xC0)==PARTITION_HUGE)) || ((PartitionType & PARTITION_NTFT) && ((PartitionType & ~0xC0)==PARTITION_FAT32)) || ((PartitionType & PARTITION_NTFT) && ((PartitionType & ~0xC0)==PARTITION_FAT32_XINT13)) || ((PartitionType & PARTITION_NTFT) && ((PartitionType & ~0xC0)==PARTITION_XINT13)) || ((PartitionType)==PARTITION_FAT_12) || ((PartitionType)==PARTITION_FAT_16) || ((PartitionType)==PARTITION_IFS) || ((PartitionType)==PARTITION_HUGE) || ((PartitionType)==PARTITION_FAT32) || ((PartitionType)==PARTITION_FAT32_XINT13) || ((PartitionType)==PARTITION_XINT13))
#define IsContainerPartition(PartitionType) ((PartitionType==PARTITION_EXTENDED) || (PartitionType==PARTITION_XINT13_EXTENDED))
#define IsFTPartition(PartitionType) (((PartitionType)&PARTITION_NTFT) && IsRecognizedPartition(PartitionType))

typedef enum _MEDIA_TYPE {
  Unknown,F5_1Pt2_512,F3_1Pt44_512,F3_2Pt88_512,F3_20Pt8_512,F3_720_512,F5_360_512,F5_320_512,F5_320_1024,F5_180_512,F5_160_512,
  RemovableMedia,FixedMedia,F3_120M_512,F3_640_512,F5_640_512,F5_720_512,F3_1Pt2_512,F3_1Pt23_1024,F5_1Pt23_1024,F3_128Mb_512,
  F3_230Mb_512,F8_256_128,F3_200Mb_512,F3_240M_512,F3_32M_512
} MEDIA_TYPE,*PMEDIA_TYPE;

typedef struct _FORMAT_PARAMETERS {
  MEDIA_TYPE MediaType;
  DWORD StartCylinderNumber;
  DWORD EndCylinderNumber;
  DWORD StartHeadNumber;
  DWORD EndHeadNumber;
} FORMAT_PARAMETERS,*PFORMAT_PARAMETERS;

typedef WORD BAD_TRACK_NUMBER;
typedef WORD *PBAD_TRACK_NUMBER;

typedef struct _FORMAT_EX_PARAMETERS {
  MEDIA_TYPE MediaType;
  DWORD StartCylinderNumber;
  DWORD EndCylinderNumber;
  DWORD StartHeadNumber;
  DWORD EndHeadNumber;
  WORD FormatGapLength;
  WORD SectorsPerTrack;
  WORD SectorNumber[1];
} FORMAT_EX_PARAMETERS,*PFORMAT_EX_PARAMETERS;

typedef struct _DISK_GEOMETRY {
  LARGE_INTEGER Cylinders;
  MEDIA_TYPE MediaType;
  DWORD TracksPerCylinder;
  DWORD SectorsPerTrack;
  DWORD BytesPerSector;
} DISK_GEOMETRY,*PDISK_GEOMETRY;

#define WMI_DISK_GEOMETRY_GUID { 0x25007f51,0x57c2,0x11d1,{ 0xa5,0x28,0x0,0xa0,0xc9,0x6,0x29,0x10 } }

typedef struct _PARTITION_INFORMATION {
  LARGE_INTEGER StartingOffset;
  LARGE_INTEGER PartitionLength;
  DWORD HiddenSectors;
  DWORD PartitionNumber;
  BYTE PartitionType;
  BOOLEAN BootIndicator;
  BOOLEAN RecognizedPartition;
  BOOLEAN RewritePartition;
} PARTITION_INFORMATION,*PPARTITION_INFORMATION;

typedef struct _SET_PARTITION_INFORMATION {
  BYTE PartitionType;
} SET_PARTITION_INFORMATION,*PSET_PARTITION_INFORMATION;

typedef struct _DRIVE_LAYOUT_INFORMATION {
  DWORD PartitionCount;
  DWORD Signature;
  PARTITION_INFORMATION PartitionEntry[1];
} DRIVE_LAYOUT_INFORMATION,*PDRIVE_LAYOUT_INFORMATION;

typedef struct _VERIFY_INFORMATION {
  LARGE_INTEGER StartingOffset;
  DWORD Length;
} VERIFY_INFORMATION,*PVERIFY_INFORMATION;

typedef struct _REASSIGN_BLOCKS {
  WORD Reserved;
  WORD Count;
  DWORD BlockNumber[1];
} REASSIGN_BLOCKS,*PREASSIGN_BLOCKS;

#include <pshpack1.h>
typedef struct _REASSIGN_BLOCKS_EX {
  WORD Reserved;
  WORD Count;
  LARGE_INTEGER BlockNumber[1];
} REASSIGN_BLOCKS_EX,*PREASSIGN_BLOCKS_EX;
#include <poppack.h>

typedef enum _PARTITION_STYLE {
  PARTITION_STYLE_MBR,PARTITION_STYLE_GPT,PARTITION_STYLE_RAW
} PARTITION_STYLE;

typedef struct _PARTITION_INFORMATION_GPT {
  GUID PartitionType;
  GUID PartitionId;
  DWORD64 Attributes;
  WCHAR Name [36];
} PARTITION_INFORMATION_GPT,*PPARTITION_INFORMATION_GPT;

#define GPT_ATTRIBUTE_PLATFORM_REQUIRED (0x0000000000000001)

#define GPT_BASIC_DATA_ATTRIBUTE_NO_DRIVE_LETTER (0x8000000000000000)
#define GPT_BASIC_DATA_ATTRIBUTE_HIDDEN (0x4000000000000000)
#define GPT_BASIC_DATA_ATTRIBUTE_SHADOW_COPY (0x2000000000000000)
#define GPT_BASIC_DATA_ATTRIBUTE_READ_ONLY (0x1000000000000000)

typedef struct _PARTITION_INFORMATION_MBR {
  BYTE PartitionType;
  BOOLEAN BootIndicator;
  BOOLEAN RecognizedPartition;
  DWORD HiddenSectors;
#if NTDDI_VERSION > NTDDI_WINBLUE
  GUID PartitionId;
#endif
} PARTITION_INFORMATION_MBR,*PPARTITION_INFORMATION_MBR;

typedef SET_PARTITION_INFORMATION SET_PARTITION_INFORMATION_MBR;
typedef PARTITION_INFORMATION_GPT SET_PARTITION_INFORMATION_GPT;

typedef struct _SET_PARTITION_INFORMATION_EX {
  PARTITION_STYLE PartitionStyle;
  __C89_NAMELESS union {
    SET_PARTITION_INFORMATION_MBR Mbr;
    SET_PARTITION_INFORMATION_GPT Gpt;
  } DUMMYUNIONNAME;
} SET_PARTITION_INFORMATION_EX,*PSET_PARTITION_INFORMATION_EX;

typedef struct _CREATE_DISK_GPT {
  GUID DiskId;
  DWORD MaxPartitionCount;
} CREATE_DISK_GPT,*PCREATE_DISK_GPT;

typedef struct _CREATE_DISK_MBR {
  DWORD Signature;
} CREATE_DISK_MBR,*PCREATE_DISK_MBR;

typedef struct _CREATE_DISK {
  PARTITION_STYLE PartitionStyle;
  __C89_NAMELESS union {
    CREATE_DISK_MBR Mbr;
    CREATE_DISK_GPT Gpt;
  } DUMMYUNIONNAME;
} CREATE_DISK,*PCREATE_DISK;

typedef struct _GET_LENGTH_INFORMATION {
  LARGE_INTEGER Length;
} GET_LENGTH_INFORMATION,*PGET_LENGTH_INFORMATION;

typedef struct _PARTITION_INFORMATION_EX {
  PARTITION_STYLE PartitionStyle;
  LARGE_INTEGER StartingOffset;
  LARGE_INTEGER PartitionLength;
  DWORD PartitionNumber;
  BOOLEAN RewritePartition;
#if NTDDI_VERSION >= NTDDI_WIN10_RS3
  BOOLEAN  IsServicePartition;
#endif
  __C89_NAMELESS union {
    PARTITION_INFORMATION_MBR Mbr;
    PARTITION_INFORMATION_GPT Gpt;
  } DUMMYUNIONNAME;
} PARTITION_INFORMATION_EX,*PPARTITION_INFORMATION_EX;

typedef struct _DRIVE_LAYOUT_INFORMATION_GPT {
  GUID DiskId;
  LARGE_INTEGER StartingUsableOffset;
  LARGE_INTEGER UsableLength;
  DWORD MaxPartitionCount;
} DRIVE_LAYOUT_INFORMATION_GPT,*PDRIVE_LAYOUT_INFORMATION_GPT;

typedef struct _DRIVE_LAYOUT_INFORMATION_MBR {
  DWORD Signature;
} DRIVE_LAYOUT_INFORMATION_MBR,*PDRIVE_LAYOUT_INFORMATION_MBR;

typedef struct _DRIVE_LAYOUT_INFORMATION_EX {
  DWORD PartitionStyle;
  DWORD PartitionCount;
  __C89_NAMELESS union {
    DRIVE_LAYOUT_INFORMATION_MBR Mbr;
    DRIVE_LAYOUT_INFORMATION_GPT Gpt;
  } DUMMYUNIONNAME;
  PARTITION_INFORMATION_EX PartitionEntry[1];
} DRIVE_LAYOUT_INFORMATION_EX,*PDRIVE_LAYOUT_INFORMATION_EX;

typedef enum _DETECTION_TYPE {
  DetectNone,DetectInt13,DetectExInt13
} DETECTION_TYPE;

typedef struct _DISK_INT13_INFO {
  WORD DriveSelect;
  DWORD MaxCylinders;
  WORD SectorsPerTrack;
  WORD MaxHeads;
  WORD NumberDrives;
} DISK_INT13_INFO,*PDISK_INT13_INFO;

typedef struct _DISK_EX_INT13_INFO {
  WORD ExBufferSize;
  WORD ExFlags;
  DWORD ExCylinders;
  DWORD ExHeads;
  DWORD ExSectorsPerTrack;
  DWORD64 ExSectorsPerDrive;
  WORD ExSectorSize;
  WORD ExReserved;
} DISK_EX_INT13_INFO,*PDISK_EX_INT13_INFO;

typedef struct _DISK_DETECTION_INFO {
  DWORD SizeOfDetectInfo;
  DETECTION_TYPE DetectionType;
  __C89_NAMELESS union {
    __C89_NAMELESS struct {
      DISK_INT13_INFO Int13;
      DISK_EX_INT13_INFO ExInt13;
    } DUMMYSTRUCTNAME;
  } DUMMYUNIONNAME;
} DISK_DETECTION_INFO,*PDISK_DETECTION_INFO;

typedef struct _DISK_PARTITION_INFO {
  DWORD SizeOfPartitionInfo;
  PARTITION_STYLE PartitionStyle;
  __C89_NAMELESS union {
    struct {
      DWORD Signature;
      DWORD CheckSum;
    } Mbr;
    struct {
      GUID DiskId;
    } Gpt;
  } DUMMYUNIONNAME;
} DISK_PARTITION_INFO,*PDISK_PARTITION_INFO;

#define DiskGeometryGetPartition(Geometry) ((PDISK_PARTITION_INFO)((Geometry)->Data))
#define DiskGeometryGetDetect(Geometry) ((PDISK_DETECTION_INFO)(((DWORD_PTR)DiskGeometryGetPartition(Geometry)+ DiskGeometryGetPartition(Geometry)->SizeOfPartitionInfo)))

typedef struct _DISK_GEOMETRY_EX {
  DISK_GEOMETRY Geometry;
  LARGE_INTEGER DiskSize;
  BYTE Data[1];
} DISK_GEOMETRY_EX,*PDISK_GEOMETRY_EX;

typedef struct _DISK_CONTROLLER_NUMBER {
  DWORD ControllerNumber;
  DWORD DiskNumber;
} DISK_CONTROLLER_NUMBER,*PDISK_CONTROLLER_NUMBER;

typedef enum {
  EqualPriority,KeepPrefetchedData,KeepReadData
} DISK_CACHE_RETENTION_PRIORITY;

typedef struct _DISK_CACHE_INFORMATION {
  BOOLEAN ParametersSavable;
  BOOLEAN ReadCacheEnabled;
  BOOLEAN WriteCacheEnabled;
  DISK_CACHE_RETENTION_PRIORITY ReadRetentionPriority;
  DISK_CACHE_RETENTION_PRIORITY WriteRetentionPriority;
  WORD DisablePrefetchTransferLength;
  BOOLEAN PrefetchScalar;
  __C89_NAMELESS union {
    struct {
      WORD Minimum;
      WORD Maximum;
      WORD MaximumBlocks;
    } ScalarPrefetch;
    struct {
      WORD Minimum;
      WORD Maximum;
    } BlockPrefetch;
  } DUMMYUNIONNAME;
} DISK_CACHE_INFORMATION,*PDISK_CACHE_INFORMATION;

typedef struct _DISK_GROW_PARTITION {
  DWORD PartitionNumber;
  LARGE_INTEGER BytesToGrow;
} DISK_GROW_PARTITION,*PDISK_GROW_PARTITION;

#define HIST_NO_OF_BUCKETS 24

typedef struct _HISTOGRAM_BUCKET {
  DWORD Reads;
  DWORD Writes;
} HISTOGRAM_BUCKET,*PHISTOGRAM_BUCKET;

#define HISTOGRAM_BUCKET_SIZE sizeof(HISTOGRAM_BUCKET)

typedef struct _DISK_HISTOGRAM {
  LARGE_INTEGER DiskSize;
  LARGE_INTEGER Start;
  LARGE_INTEGER End;
  LARGE_INTEGER Average;
  LARGE_INTEGER AverageRead;
  LARGE_INTEGER AverageWrite;
  DWORD Granularity;
  DWORD Size;
  DWORD ReadCount;
  DWORD WriteCount;
  PHISTOGRAM_BUCKET Histogram;
} DISK_HISTOGRAM,*PDISK_HISTOGRAM;

#define DISK_HISTOGRAM_SIZE sizeof(DISK_HISTOGRAM)

typedef struct _DISK_PERFORMANCE {
  LARGE_INTEGER BytesRead;
  LARGE_INTEGER BytesWritten;
  LARGE_INTEGER ReadTime;
  LARGE_INTEGER WriteTime;
  LARGE_INTEGER IdleTime;
  DWORD ReadCount;
  DWORD WriteCount;
  DWORD QueueDepth;
  DWORD SplitCount;
  LARGE_INTEGER QueryTime;
  DWORD StorageDeviceNumber;
  WCHAR StorageManagerName[8];
} DISK_PERFORMANCE,*PDISK_PERFORMANCE;

typedef struct _DISK_RECORD {
  LARGE_INTEGER ByteOffset;
  LARGE_INTEGER StartTime;
  LARGE_INTEGER EndTime;
  PVOID VirtualAddress;
  DWORD NumberOfBytes;
  BYTE DeviceNumber;
  BOOLEAN ReadRequest;
} DISK_RECORD,*PDISK_RECORD;

typedef struct _DISK_LOGGING {
  BYTE Function;
  PVOID BufferAddress;
  DWORD BufferSize;
} DISK_LOGGING,*PDISK_LOGGING;

#define DISK_LOGGING_START 0
#define DISK_LOGGING_STOP 1
#define DISK_LOGGING_DUMP 2
#define DISK_BINNING 3

typedef enum _BIN_TYPES {
  RequestSize,RequestLocation
} BIN_TYPES;

typedef struct _BIN_RANGE {
  LARGE_INTEGER StartValue;
  LARGE_INTEGER Length;
} BIN_RANGE,*PBIN_RANGE;

typedef struct _PERF_BIN {
  DWORD NumberOfBins;
  DWORD TypeOfBin;
  BIN_RANGE BinsRanges[1];
} PERF_BIN,*PPERF_BIN;

typedef struct _BIN_COUNT {
  BIN_RANGE BinRange;
  DWORD BinCount;
} BIN_COUNT,*PBIN_COUNT;

typedef struct _BIN_RESULTS {
  DWORD NumberOfBins;
  BIN_COUNT BinCounts[1];
} BIN_RESULTS,*PBIN_RESULTS;

#include <pshpack1.h>
typedef struct _GETVERSIONINPARAMS {
  BYTE bVersion;
  BYTE bRevision;
  BYTE bReserved;
  BYTE bIDEDeviceMap;
  DWORD fCapabilities;
  DWORD dwReserved[4];
} GETVERSIONINPARAMS,*PGETVERSIONINPARAMS,*LPGETVERSIONINPARAMS;
#include <poppack.h>

#define CAP_ATA_ID_CMD 1
#define CAP_ATAPI_ID_CMD 2
#define CAP_SMART_CMD 4

#include <pshpack1.h>
typedef struct _IDEREGS {
  BYTE bFeaturesReg;
  BYTE bSectorCountReg;
  BYTE bSectorNumberReg;
  BYTE bCylLowReg;
  BYTE bCylHighReg;
  BYTE bDriveHeadReg;
  BYTE bCommandReg;
  BYTE bReserved;
} IDEREGS,*PIDEREGS,*LPIDEREGS;
#include <poppack.h>

#define ATAPI_ID_CMD 0xA1
#define ID_CMD 0xEC
#define SMART_CMD 0xB0

#define SMART_CYL_LOW 0x4F
#define SMART_CYL_HI 0xC2

#include <pshpack1.h>
typedef struct _SENDCMDINPARAMS {
  DWORD cBufferSize;
  IDEREGS irDriveRegs;
  BYTE bDriveNumber;
  BYTE bReserved[3];
  DWORD dwReserved[4];
  BYTE bBuffer[1];
} SENDCMDINPARAMS,*PSENDCMDINPARAMS,*LPSENDCMDINPARAMS;
#include <poppack.h>

#include <pshpack1.h>
typedef struct _DRIVERSTATUS {
  BYTE bDriverError;
  BYTE bIDEError;
  BYTE bReserved[2];
  DWORD dwReserved[2];
} DRIVERSTATUS,*PDRIVERSTATUS,*LPDRIVERSTATUS;
#include <poppack.h>

#define SMART_NO_ERROR 0
#define SMART_IDE_ERROR 1
#define SMART_INVALID_FLAG 2
#define SMART_INVALID_COMMAND 3
#define SMART_INVALID_BUFFER 4
#define SMART_INVALID_DRIVE 5
#define SMART_INVALID_IOCTL 6
#define SMART_ERROR_NO_MEM 7
#define SMART_INVALID_REGISTER 8
#define SMART_NOT_SUPPORTED 9
#define SMART_NO_IDE_DEVICE 10

#define SMART_OFFLINE_ROUTINE_OFFLINE 0
#define SMART_SHORT_SELFTEST_OFFLINE 1
#define SMART_EXTENDED_SELFTEST_OFFLINE 2
#define SMART_ABORT_OFFLINE_SELFTEST 127
#define SMART_SHORT_SELFTEST_CAPTIVE 129
#define SMART_EXTENDED_SELFTEST_CAPTIVE 130

#include <pshpack1.h>
typedef struct _SENDCMDOUTPARAMS {
  DWORD cBufferSize;
  DRIVERSTATUS DriverStatus;
  BYTE bBuffer[1];
} SENDCMDOUTPARAMS,*PSENDCMDOUTPARAMS,*LPSENDCMDOUTPARAMS;
#include <poppack.h>

#define READ_ATTRIBUTE_BUFFER_SIZE 512
#define IDENTIFY_BUFFER_SIZE 512
#define READ_THRESHOLD_BUFFER_SIZE 512
#define SMART_LOG_SECTOR_SIZE 512

#define READ_ATTRIBUTES 0xD0
#define READ_THRESHOLDS 0xD1
#define ENABLE_DISABLE_AUTOSAVE 0xD2
#define SAVE_ATTRIBUTE_VALUES 0xD3
#define EXECUTE_OFFLINE_DIAGS 0xD4
#define SMART_READ_LOG 0xD5
#define SMART_WRITE_LOG 0xd6
#define ENABLE_SMART 0xD8
#define DISABLE_SMART 0xD9
#define RETURN_SMART_STATUS 0xDA
#define ENABLE_DISABLE_AUTO_OFFLINE 0xDB

#endif /* _NTDDDISK_H_ */


#define IOCTL_CHANGER_BASE FILE_DEVICE_CHANGER
#define IOCTL_CHANGER_GET_PARAMETERS CTL_CODE(IOCTL_CHANGER_BASE,0x0000,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_CHANGER_GET_STATUS CTL_CODE(IOCTL_CHANGER_BASE,0x0001,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_CHANGER_GET_PRODUCT_DATA CTL_CODE(IOCTL_CHANGER_BASE,0x0002,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_CHANGER_SET_ACCESS CTL_CODE(IOCTL_CHANGER_BASE,0x0004,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_CHANGER_GET_ELEMENT_STATUS CTL_CODE(IOCTL_CHANGER_BASE,0x0005,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_CHANGER_INITIALIZE_ELEMENT_STATUS CTL_CODE(IOCTL_CHANGER_BASE,0x0006,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_CHANGER_SET_POSITION CTL_CODE(IOCTL_CHANGER_BASE,0x0007,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_CHANGER_EXCHANGE_MEDIUM CTL_CODE(IOCTL_CHANGER_BASE,0x0008,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_CHANGER_MOVE_MEDIUM CTL_CODE(IOCTL_CHANGER_BASE,0x0009,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_CHANGER_REINITIALIZE_TRANSPORT CTL_CODE(IOCTL_CHANGER_BASE,0x000A,METHOD_BUFFERED,FILE_READ_ACCESS)
#define IOCTL_CHANGER_QUERY_VOLUME_TAGS CTL_CODE(IOCTL_CHANGER_BASE,0x000B,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)

#define MAX_VOLUME_ID_SIZE 36
#define MAX_VOLUME_TEMPLATE_SIZE 40

#define VENDOR_ID_LENGTH 8
#define PRODUCT_ID_LENGTH 16
#define REVISION_LENGTH 4
#define SERIAL_NUMBER_LENGTH 32

typedef enum _ELEMENT_TYPE {
  AllElements,ChangerTransport,ChangerSlot,ChangerIEPort,ChangerDrive,ChangerDoor,ChangerKeypad,ChangerMaxElement
} ELEMENT_TYPE,*PELEMENT_TYPE;

typedef struct _CHANGER_ELEMENT {
  ELEMENT_TYPE ElementType;
  DWORD ElementAddress;
} CHANGER_ELEMENT,*PCHANGER_ELEMENT;

typedef struct _CHANGER_ELEMENT_LIST {
  CHANGER_ELEMENT Element;
  DWORD NumberOfElements;
} CHANGER_ELEMENT_LIST ,*PCHANGER_ELEMENT_LIST;

#define CHANGER_BAR_CODE_SCANNER_INSTALLED 0x00000001
#define CHANGER_INIT_ELEM_STAT_WITH_RANGE 0x00000002
#define CHANGER_CLOSE_IEPORT 0x00000004
#define CHANGER_OPEN_IEPORT 0x00000008

#define CHANGER_STATUS_NON_VOLATILE 0x00000010
#define CHANGER_EXCHANGE_MEDIA 0x00000020
#define CHANGER_CLEANER_SLOT 0x00000040
#define CHANGER_LOCK_UNLOCK 0x00000080

#define CHANGER_CARTRIDGE_MAGAZINE 0x00000100
#define CHANGER_MEDIUM_FLIP 0x00000200
#define CHANGER_POSITION_TO_ELEMENT 0x00000400
#define CHANGER_REPORT_IEPORT_STATE 0x00000800

#define CHANGER_STORAGE_DRIVE 0x00001000
#define CHANGER_STORAGE_IEPORT 0x00002000
#define CHANGER_STORAGE_SLOT 0x00004000
#define CHANGER_STORAGE_TRANSPORT 0x00008000

#define CHANGER_DRIVE_CLEANING_REQUIRED 0x00010000

#define CHANGER_PREDISMOUNT_EJECT_REQUIRED 0x00020000

#define CHANGER_CLEANER_ACCESS_NOT_VALID 0x00040000
#define CHANGER_PREMOUNT_EJECT_REQUIRED 0x00080000

#define CHANGER_VOLUME_IDENTIFICATION 0x00100000
#define CHANGER_VOLUME_SEARCH 0x00200000
#define CHANGER_VOLUME_ASSERT 0x00400000
#define CHANGER_VOLUME_REPLACE 0x00800000
#define CHANGER_VOLUME_UNDEFINE 0x01000000

#define CHANGER_SERIAL_NUMBER_VALID 0x04000000

#define CHANGER_DEVICE_REINITIALIZE_CAPABLE 0x08000000
#define CHANGER_KEYPAD_ENABLE_DISABLE 0x10000000
#define CHANGER_DRIVE_EMPTY_ON_DOOR_ACCESS 0x20000000

#define CHANGER_RESERVED_BIT 0x80000000

#define CHANGER_PREDISMOUNT_ALIGN_TO_SLOT 0x80000001
#define CHANGER_PREDISMOUNT_ALIGN_TO_DRIVE 0x80000002
#define CHANGER_CLEANER_AUTODISMOUNT 0x80000004
#define CHANGER_TRUE_EXCHANGE_CAPABLE 0x80000008
#define CHANGER_SLOTS_USE_TRAYS 0x80000010
#define CHANGER_RTN_MEDIA_TO_ORIGINAL_ADDR 0x80000020
#define CHANGER_CLEANER_OPS_NOT_SUPPORTED 0x80000040
#define CHANGER_IEPORT_USER_CONTROL_OPEN 0x80000080
#define CHANGER_IEPORT_USER_CONTROL_CLOSE 0x80000100
#define CHANGER_MOVE_EXTENDS_IEPORT 0x80000200
#define CHANGER_MOVE_RETRACTS_IEPORT 0x80000400

#define CHANGER_TO_TRANSPORT 0x01
#define CHANGER_TO_SLOT 0x02
#define CHANGER_TO_IEPORT 0x04
#define CHANGER_TO_DRIVE 0x08

#define LOCK_UNLOCK_IEPORT 0x01
#define LOCK_UNLOCK_DOOR 0x02
#define LOCK_UNLOCK_KEYPAD 0x04

typedef struct _GET_CHANGER_PARAMETERS {
  DWORD Size;
  WORD NumberTransportElements;
  WORD NumberStorageElements;
  WORD NumberCleanerSlots;
  WORD NumberIEElements;
  WORD NumberDataTransferElements;
  WORD NumberOfDoors;
  WORD FirstSlotNumber;
  WORD FirstDriveNumber;
  WORD FirstTransportNumber;
  WORD FirstIEPortNumber;
  WORD FirstCleanerSlotAddress;
  WORD MagazineSize;
  DWORD DriveCleanTimeout;
  DWORD Features0;
  DWORD Features1;
  BYTE MoveFromTransport;
  BYTE MoveFromSlot;
  BYTE MoveFromIePort;
  BYTE MoveFromDrive;
  BYTE ExchangeFromTransport;
  BYTE ExchangeFromSlot;
  BYTE ExchangeFromIePort;
  BYTE ExchangeFromDrive;
  BYTE LockUnlockCapabilities;
  BYTE PositionCapabilities;
  BYTE Reserved1[2];
  DWORD Reserved2[2];
} GET_CHANGER_PARAMETERS,*PGET_CHANGER_PARAMETERS;

typedef struct _CHANGER_PRODUCT_DATA {
  BYTE VendorId[VENDOR_ID_LENGTH];
  BYTE ProductId[PRODUCT_ID_LENGTH];
  BYTE Revision[REVISION_LENGTH];
  BYTE SerialNumber[SERIAL_NUMBER_LENGTH];
  BYTE DeviceType;
} CHANGER_PRODUCT_DATA,*PCHANGER_PRODUCT_DATA;

#define LOCK_ELEMENT 0
#define UNLOCK_ELEMENT 1
#define EXTEND_IEPORT 2
#define RETRACT_IEPORT 3

typedef struct _CHANGER_SET_ACCESS {
  CHANGER_ELEMENT Element;
  DWORD Control;
} CHANGER_SET_ACCESS,*PCHANGER_SET_ACCESS;

typedef struct _CHANGER_READ_ELEMENT_STATUS {
  CHANGER_ELEMENT_LIST ElementList;
  BOOLEAN VolumeTagInfo;
} CHANGER_READ_ELEMENT_STATUS,*PCHANGER_READ_ELEMENT_STATUS;

typedef struct _CHANGER_ELEMENT_STATUS {
  CHANGER_ELEMENT Element;
  CHANGER_ELEMENT SrcElementAddress;
  DWORD Flags;
  DWORD ExceptionCode;
  BYTE TargetId;
  BYTE Lun;
  WORD Reserved;
  BYTE PrimaryVolumeID[MAX_VOLUME_ID_SIZE];
  BYTE AlternateVolumeID[MAX_VOLUME_ID_SIZE];
} CHANGER_ELEMENT_STATUS,*PCHANGER_ELEMENT_STATUS;

typedef struct _CHANGER_ELEMENT_STATUS_EX {
  CHANGER_ELEMENT Element;
  CHANGER_ELEMENT SrcElementAddress;
  DWORD Flags;
  DWORD ExceptionCode;
  BYTE TargetId;
  BYTE Lun;
  WORD Reserved;
  BYTE PrimaryVolumeID[MAX_VOLUME_ID_SIZE];
  BYTE AlternateVolumeID[MAX_VOLUME_ID_SIZE];
  BYTE VendorIdentification[VENDOR_ID_LENGTH];
  BYTE ProductIdentification[PRODUCT_ID_LENGTH];
  BYTE SerialNumber[SERIAL_NUMBER_LENGTH];
} CHANGER_ELEMENT_STATUS_EX,*PCHANGER_ELEMENT_STATUS_EX;

#define ELEMENT_STATUS_FULL 0x00000001
#define ELEMENT_STATUS_IMPEXP 0x00000002
#define ELEMENT_STATUS_EXCEPT 0x00000004
#define ELEMENT_STATUS_ACCESS 0x00000008
#define ELEMENT_STATUS_EXENAB 0x00000010
#define ELEMENT_STATUS_INENAB 0x00000020

#define ELEMENT_STATUS_PRODUCT_DATA 0x00000040

#define ELEMENT_STATUS_LUN_VALID 0x00001000
#define ELEMENT_STATUS_ID_VALID 0x00002000
#define ELEMENT_STATUS_NOT_BUS 0x00008000
#define ELEMENT_STATUS_INVERT 0x00400000
#define ELEMENT_STATUS_SVALID 0x00800000

#define ELEMENT_STATUS_PVOLTAG 0x10000000
#define ELEMENT_STATUS_AVOLTAG 0x20000000

#define ERROR_LABEL_UNREADABLE 0x00000001
#define ERROR_LABEL_QUESTIONABLE 0x00000002
#define ERROR_SLOT_NOT_PRESENT 0x00000004
#define ERROR_DRIVE_NOT_INSTALLED 0x00000008
#define ERROR_TRAY_MALFUNCTION 0x00000010
#define ERROR_INIT_STATUS_NEEDED 0x00000011
#define ERROR_UNHANDLED_ERROR 0xFFFFFFFF

typedef struct _CHANGER_INITIALIZE_ELEMENT_STATUS {
  CHANGER_ELEMENT_LIST ElementList;
  BOOLEAN BarCodeScan;
} CHANGER_INITIALIZE_ELEMENT_STATUS,*PCHANGER_INITIALIZE_ELEMENT_STATUS;

typedef struct _CHANGER_SET_POSITION {
  CHANGER_ELEMENT Transport;
  CHANGER_ELEMENT Destination;
  BOOLEAN Flip;
} CHANGER_SET_POSITION,*PCHANGER_SET_POSITION;

typedef struct _CHANGER_EXCHANGE_MEDIUM {
  CHANGER_ELEMENT Transport;
  CHANGER_ELEMENT Source;
  CHANGER_ELEMENT Destination1;
  CHANGER_ELEMENT Destination2;
  BOOLEAN Flip1;
  BOOLEAN Flip2;
} CHANGER_EXCHANGE_MEDIUM,*PCHANGER_EXCHANGE_MEDIUM;

typedef struct _CHANGER_MOVE_MEDIUM {
  CHANGER_ELEMENT Transport;
  CHANGER_ELEMENT Source;
  CHANGER_ELEMENT Destination;
  BOOLEAN Flip;
} CHANGER_MOVE_MEDIUM,*PCHANGER_MOVE_MEDIUM;

typedef struct _CHANGER_SEND_VOLUME_TAG_INFORMATION {
  CHANGER_ELEMENT StartingElement;
  DWORD ActionCode;
  BYTE VolumeIDTemplate[MAX_VOLUME_TEMPLATE_SIZE];
} CHANGER_SEND_VOLUME_TAG_INFORMATION,*PCHANGER_SEND_VOLUME_TAG_INFORMATION;

typedef struct _READ_ELEMENT_ADDRESS_INFO {
  DWORD NumberOfElements;
  CHANGER_ELEMENT_STATUS ElementStatus[1];
} READ_ELEMENT_ADDRESS_INFO,*PREAD_ELEMENT_ADDRESS_INFO;

#define SEARCH_ALL 0x0
#define SEARCH_PRIMARY 0x1
#define SEARCH_ALTERNATE 0x2
#define SEARCH_ALL_NO_SEQ 0x4
#define SEARCH_PRI_NO_SEQ 0x5
#define SEARCH_ALT_NO_SEQ 0x6

#define ASSERT_PRIMARY 0x8
#define ASSERT_ALTERNATE 0x9

#define REPLACE_PRIMARY 0xA
#define REPLACE_ALTERNATE 0xB

#define UNDEFINE_PRIMARY 0xC
#define UNDEFINE_ALTERNATE 0xD

typedef enum _CHANGER_DEVICE_PROBLEM_TYPE {
  DeviceProblemNone,DeviceProblemHardware,DeviceProblemCHMError,DeviceProblemDoorOpen,DeviceProblemCalibrationError,DeviceProblemTargetFailure,
  DeviceProblemCHMMoveError,DeviceProblemCHMZeroError,DeviceProblemCartridgeInsertError,DeviceProblemPositionError,DeviceProblemSensorError,
  DeviceProblemCartridgeEjectError,DeviceProblemGripperError,DeviceProblemDriveError
} CHANGER_DEVICE_PROBLEM_TYPE,*PCHANGER_DEVICE_PROBLEM_TYPE;

#define IOCTL_SERIAL_LSRMST_INSERT CTL_CODE(FILE_DEVICE_SERIAL_PORT,31,METHOD_BUFFERED,FILE_ANY_ACCESS)

#define IOCTL_SERENUM_EXPOSE_HARDWARE CTL_CODE(FILE_DEVICE_SERENUM,128,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_SERENUM_REMOVE_HARDWARE CTL_CODE(FILE_DEVICE_SERENUM,129,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_SERENUM_PORT_DESC CTL_CODE(FILE_DEVICE_SERENUM,130,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_SERENUM_GET_PORT_NAME CTL_CODE(FILE_DEVICE_SERENUM,131,METHOD_BUFFERED,FILE_ANY_ACCESS)

#define SERIAL_LSRMST_ESCAPE ((BYTE)0x00)

#define SERIAL_LSRMST_LSR_DATA ((BYTE)0x01)

#define SERIAL_LSRMST_LSR_NODATA ((BYTE)0x02)

#define SERIAL_LSRMST_MST ((BYTE)0x03)

#define SERIAL_IOC_FCR_FIFO_ENABLE ((DWORD)0x00000001)
#define SERIAL_IOC_FCR_RCVR_RESET ((DWORD)0x00000002)
#define SERIAL_IOC_FCR_XMIT_RESET ((DWORD)0x00000004)
#define SERIAL_IOC_FCR_DMA_MODE ((DWORD)0x00000008)
#define SERIAL_IOC_FCR_RES1 ((DWORD)0x00000010)
#define SERIAL_IOC_FCR_RES2 ((DWORD)0x00000020)
#define SERIAL_IOC_FCR_RCVR_TRIGGER_LSB ((DWORD)0x00000040)
#define SERIAL_IOC_FCR_RCVR_TRIGGER_MSB ((DWORD)0x00000080)

#define SERIAL_IOC_MCR_DTR ((DWORD)0x00000001)
#define SERIAL_IOC_MCR_RTS ((DWORD)0x00000002)
#define SERIAL_IOC_MCR_OUT1 ((DWORD)0x00000004)
#define SERIAL_IOC_MCR_OUT2 ((DWORD)0x00000008)
#define SERIAL_IOC_MCR_LOOP ((DWORD)0x00000010)

#ifndef _FILESYSTEMFSCTL_
#define _FILESYSTEMFSCTL_

#define FSCTL_REQUEST_OPLOCK_LEVEL_1 CTL_CODE(FILE_DEVICE_FILE_SYSTEM,0,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_REQUEST_OPLOCK_LEVEL_2 CTL_CODE(FILE_DEVICE_FILE_SYSTEM,1,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_REQUEST_BATCH_OPLOCK CTL_CODE(FILE_DEVICE_FILE_SYSTEM,2,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_OPLOCK_BREAK_ACKNOWLEDGE CTL_CODE(FILE_DEVICE_FILE_SYSTEM,3,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_OPBATCH_ACK_CLOSE_PENDING CTL_CODE(FILE_DEVICE_FILE_SYSTEM,4,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_OPLOCK_BREAK_NOTIFY CTL_CODE(FILE_DEVICE_FILE_SYSTEM,5,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_LOCK_VOLUME CTL_CODE(FILE_DEVICE_FILE_SYSTEM,6,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_UNLOCK_VOLUME CTL_CODE(FILE_DEVICE_FILE_SYSTEM,7,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_DISMOUNT_VOLUME CTL_CODE(FILE_DEVICE_FILE_SYSTEM,8,METHOD_BUFFERED,FILE_ANY_ACCESS)

#define FSCTL_IS_VOLUME_MOUNTED CTL_CODE(FILE_DEVICE_FILE_SYSTEM,10,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_IS_PATHNAME_VALID CTL_CODE(FILE_DEVICE_FILE_SYSTEM,11,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_MARK_VOLUME_DIRTY CTL_CODE(FILE_DEVICE_FILE_SYSTEM,12,METHOD_BUFFERED,FILE_ANY_ACCESS)

#define FSCTL_QUERY_RETRIEVAL_POINTERS CTL_CODE(FILE_DEVICE_FILE_SYSTEM,14,METHOD_NEITHER,FILE_ANY_ACCESS)
#define FSCTL_GET_COMPRESSION CTL_CODE(FILE_DEVICE_FILE_SYSTEM,15,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_SET_COMPRESSION CTL_CODE(FILE_DEVICE_FILE_SYSTEM,16,METHOD_BUFFERED,FILE_READ_DATA | FILE_WRITE_DATA)

#define FSCTL_SET_BOOTLOADER_ACCESSED CTL_CODE(FILE_DEVICE_FILE_SYSTEM,19,METHOD_NEITHER,FILE_ANY_ACCESS)
#define FSCTL_MARK_AS_SYSTEM_HIVE FSCTL_SET_BOOTLOADER_ACCESSED
#define FSCTL_OPLOCK_BREAK_ACK_NO_2 CTL_CODE(FILE_DEVICE_FILE_SYSTEM,20,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_INVALIDATE_VOLUMES CTL_CODE(FILE_DEVICE_FILE_SYSTEM,21,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_QUERY_FAT_BPB CTL_CODE(FILE_DEVICE_FILE_SYSTEM,22,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_REQUEST_FILTER_OPLOCK CTL_CODE(FILE_DEVICE_FILE_SYSTEM,23,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_FILESYSTEM_GET_STATISTICS CTL_CODE(FILE_DEVICE_FILE_SYSTEM,24,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_GET_NTFS_VOLUME_DATA CTL_CODE(FILE_DEVICE_FILE_SYSTEM,25,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_GET_NTFS_FILE_RECORD CTL_CODE(FILE_DEVICE_FILE_SYSTEM,26,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_GET_VOLUME_BITMAP CTL_CODE(FILE_DEVICE_FILE_SYSTEM,27,METHOD_NEITHER,FILE_ANY_ACCESS)
#define FSCTL_GET_RETRIEVAL_POINTERS CTL_CODE(FILE_DEVICE_FILE_SYSTEM,28,METHOD_NEITHER,FILE_ANY_ACCESS)
#define FSCTL_MOVE_FILE CTL_CODE(FILE_DEVICE_FILE_SYSTEM,29,METHOD_BUFFERED,FILE_SPECIAL_ACCESS)
#define FSCTL_IS_VOLUME_DIRTY CTL_CODE(FILE_DEVICE_FILE_SYSTEM,30,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_ALLOW_EXTENDED_DASD_IO CTL_CODE(FILE_DEVICE_FILE_SYSTEM,32,METHOD_NEITHER,FILE_ANY_ACCESS)
#define FSCTL_FIND_FILES_BY_SID CTL_CODE(FILE_DEVICE_FILE_SYSTEM,35,METHOD_NEITHER,FILE_ANY_ACCESS)
#define FSCTL_SET_OBJECT_ID CTL_CODE(FILE_DEVICE_FILE_SYSTEM,38,METHOD_BUFFERED,FILE_SPECIAL_ACCESS)
#define FSCTL_GET_OBJECT_ID CTL_CODE(FILE_DEVICE_FILE_SYSTEM,39,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_DELETE_OBJECT_ID CTL_CODE(FILE_DEVICE_FILE_SYSTEM,40,METHOD_BUFFERED,FILE_SPECIAL_ACCESS)
#define FSCTL_SET_REPARSE_POINT CTL_CODE(FILE_DEVICE_FILE_SYSTEM,41,METHOD_BUFFERED,FILE_SPECIAL_ACCESS)
#define FSCTL_GET_REPARSE_POINT CTL_CODE(FILE_DEVICE_FILE_SYSTEM,42,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_DELETE_REPARSE_POINT CTL_CODE(FILE_DEVICE_FILE_SYSTEM,43,METHOD_BUFFERED,FILE_SPECIAL_ACCESS)
#define FSCTL_ENUM_USN_DATA CTL_CODE(FILE_DEVICE_FILE_SYSTEM,44,METHOD_NEITHER,FILE_ANY_ACCESS)
#define FSCTL_SECURITY_ID_CHECK CTL_CODE(FILE_DEVICE_FILE_SYSTEM,45,METHOD_NEITHER,FILE_READ_DATA)
#define FSCTL_READ_USN_JOURNAL CTL_CODE(FILE_DEVICE_FILE_SYSTEM,46,METHOD_NEITHER,FILE_ANY_ACCESS)
#define FSCTL_SET_OBJECT_ID_EXTENDED CTL_CODE(FILE_DEVICE_FILE_SYSTEM,47,METHOD_BUFFERED,FILE_SPECIAL_ACCESS)
#define FSCTL_CREATE_OR_GET_OBJECT_ID CTL_CODE(FILE_DEVICE_FILE_SYSTEM,48,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_SET_SPARSE CTL_CODE(FILE_DEVICE_FILE_SYSTEM,49,METHOD_BUFFERED,FILE_SPECIAL_ACCESS)
#define FSCTL_SET_ZERO_DATA CTL_CODE(FILE_DEVICE_FILE_SYSTEM,50,METHOD_BUFFERED,FILE_WRITE_DATA)
#define FSCTL_QUERY_ALLOCATED_RANGES CTL_CODE(FILE_DEVICE_FILE_SYSTEM,51,METHOD_NEITHER,FILE_READ_DATA)
#define FSCTL_ENABLE_UPGRADE CTL_CODE(FILE_DEVICE_FILE_SYSTEM,52,METHOD_BUFFERED,FILE_WRITE_DATA)
#define FSCTL_SET_ENCRYPTION CTL_CODE(FILE_DEVICE_FILE_SYSTEM,53,METHOD_NEITHER,FILE_ANY_ACCESS)
#define FSCTL_ENCRYPTION_FSCTL_IO CTL_CODE(FILE_DEVICE_FILE_SYSTEM,54,METHOD_NEITHER,FILE_ANY_ACCESS)
#define FSCTL_WRITE_RAW_ENCRYPTED CTL_CODE(FILE_DEVICE_FILE_SYSTEM,55,METHOD_NEITHER,FILE_SPECIAL_ACCESS)
#define FSCTL_READ_RAW_ENCRYPTED CTL_CODE(FILE_DEVICE_FILE_SYSTEM,56,METHOD_NEITHER,FILE_SPECIAL_ACCESS)
#define FSCTL_CREATE_USN_JOURNAL CTL_CODE(FILE_DEVICE_FILE_SYSTEM,57,METHOD_NEITHER,FILE_ANY_ACCESS)
#define FSCTL_READ_FILE_USN_DATA CTL_CODE(FILE_DEVICE_FILE_SYSTEM,58,METHOD_NEITHER,FILE_ANY_ACCESS)
#define FSCTL_WRITE_USN_CLOSE_RECORD CTL_CODE(FILE_DEVICE_FILE_SYSTEM,59,METHOD_NEITHER,FILE_ANY_ACCESS)
#define FSCTL_EXTEND_VOLUME CTL_CODE(FILE_DEVICE_FILE_SYSTEM,60,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_QUERY_USN_JOURNAL CTL_CODE(FILE_DEVICE_FILE_SYSTEM,61,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_DELETE_USN_JOURNAL CTL_CODE(FILE_DEVICE_FILE_SYSTEM,62,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_MARK_HANDLE CTL_CODE(FILE_DEVICE_FILE_SYSTEM,63,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_SIS_COPYFILE CTL_CODE(FILE_DEVICE_FILE_SYSTEM,64,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_SIS_LINK_FILES CTL_CODE(FILE_DEVICE_FILE_SYSTEM,65,METHOD_BUFFERED,FILE_READ_DATA | FILE_WRITE_DATA)
#define FSCTL_HSM_MSG CTL_CODE(FILE_DEVICE_FILE_SYSTEM,66,METHOD_BUFFERED,FILE_READ_DATA | FILE_WRITE_DATA)
#define FSCTL_HSM_DATA CTL_CODE(FILE_DEVICE_FILE_SYSTEM,68,METHOD_NEITHER,FILE_READ_DATA | FILE_WRITE_DATA)
#define FSCTL_RECALL_FILE CTL_CODE(FILE_DEVICE_FILE_SYSTEM,69,METHOD_NEITHER,FILE_ANY_ACCESS)
#define FSCTL_READ_FROM_PLEX CTL_CODE(FILE_DEVICE_FILE_SYSTEM,71,METHOD_OUT_DIRECT,FILE_READ_DATA)
#define FSCTL_FILE_PREFETCH CTL_CODE(FILE_DEVICE_FILE_SYSTEM,72,METHOD_BUFFERED,FILE_SPECIAL_ACCESS)

typedef struct _PATHNAME_BUFFER {
  DWORD PathNameLength;
  WCHAR Name[1];
} PATHNAME_BUFFER,*PPATHNAME_BUFFER;

typedef struct _FSCTL_QUERY_FAT_BPB_BUFFER {
  BYTE First0x24BytesOfBootSector[0x24];
} FSCTL_QUERY_FAT_BPB_BUFFER,*PFSCTL_QUERY_FAT_BPB_BUFFER;

typedef struct {
  LARGE_INTEGER VolumeSerialNumber;
  LARGE_INTEGER NumberSectors;
  LARGE_INTEGER TotalClusters;
  LARGE_INTEGER FreeClusters;
  LARGE_INTEGER TotalReserved;
  DWORD BytesPerSector;
  DWORD BytesPerCluster;
  DWORD BytesPerFileRecordSegment;
  DWORD ClustersPerFileRecordSegment;
  LARGE_INTEGER MftValidDataLength;
  LARGE_INTEGER MftStartLcn;
  LARGE_INTEGER Mft2StartLcn;
  LARGE_INTEGER MftZoneStart;
  LARGE_INTEGER MftZoneEnd;
} NTFS_VOLUME_DATA_BUFFER,*PNTFS_VOLUME_DATA_BUFFER;

typedef struct {
  DWORD ByteCount;
  WORD MajorVersion;
  WORD MinorVersion;
} NTFS_EXTENDED_VOLUME_DATA,*PNTFS_EXTENDED_VOLUME_DATA;

typedef struct {
  LARGE_INTEGER StartingLcn;
} STARTING_LCN_INPUT_BUFFER,*PSTARTING_LCN_INPUT_BUFFER;

typedef struct {
  LARGE_INTEGER StartingLcn;
  LARGE_INTEGER BitmapSize;
  BYTE Buffer[1];
} VOLUME_BITMAP_BUFFER,*PVOLUME_BITMAP_BUFFER;

typedef struct {
  LARGE_INTEGER StartingVcn;
} STARTING_VCN_INPUT_BUFFER,*PSTARTING_VCN_INPUT_BUFFER;

typedef struct RETRIEVAL_POINTERS_BUFFER {
  DWORD ExtentCount;
  LARGE_INTEGER StartingVcn;
  struct {
    LARGE_INTEGER NextVcn;
    LARGE_INTEGER Lcn;
  } Extents[1];
} RETRIEVAL_POINTERS_BUFFER,*PRETRIEVAL_POINTERS_BUFFER;

typedef struct {
  LARGE_INTEGER FileReferenceNumber;
} NTFS_FILE_RECORD_INPUT_BUFFER,*PNTFS_FILE_RECORD_INPUT_BUFFER;

typedef struct {
  LARGE_INTEGER FileReferenceNumber;
  DWORD FileRecordLength;
  BYTE FileRecordBuffer[1];
} NTFS_FILE_RECORD_OUTPUT_BUFFER,*PNTFS_FILE_RECORD_OUTPUT_BUFFER;

typedef struct {
  HANDLE FileHandle;
  LARGE_INTEGER StartingVcn;
  LARGE_INTEGER StartingLcn;
  DWORD ClusterCount;
} MOVE_FILE_DATA,*PMOVE_FILE_DATA;

typedef struct _MOVE_FILE_RECORD_DATA {
  HANDLE FileHandle;
  LARGE_INTEGER SourceFileRecord;
  LARGE_INTEGER TargetFileRecord;
} MOVE_FILE_RECORD_DATA, *PMOVE_FILE_RECORD_DATA;

#ifdef _WIN64
typedef struct _MOVE_FILE_DATA32 {
  UINT32 FileHandle;
  LARGE_INTEGER StartingVcn;
  LARGE_INTEGER StartingLcn;
  DWORD ClusterCount;
} MOVE_FILE_DATA32,*PMOVE_FILE_DATA32;
#endif

typedef struct {
  DWORD Restart;
  SID Sid;
} FIND_BY_SID_DATA,*PFIND_BY_SID_DATA;

typedef struct {
  DWORD NextEntryOffset;
  DWORD FileIndex;
  DWORD FileNameLength;
  WCHAR FileName[1];
} FIND_BY_SID_OUTPUT,*PFIND_BY_SID_OUTPUT;

typedef struct {
  DWORDLONG StartFileReferenceNumber;
  USN LowUsn;
  USN HighUsn;
} MFT_ENUM_DATA,*PMFT_ENUM_DATA;

typedef struct {
  DWORDLONG MaximumSize;
  DWORDLONG AllocationDelta;
} CREATE_USN_JOURNAL_DATA,*PCREATE_USN_JOURNAL_DATA;

typedef struct {
  USN StartUsn;
  DWORD ReasonMask;
  DWORD ReturnOnlyOnClose;
  DWORDLONG Timeout;
  DWORDLONG BytesToWaitFor;
  DWORDLONG UsnJournalID;
} READ_USN_JOURNAL_DATA,*PREAD_USN_JOURNAL_DATA;

typedef struct {
  DWORD RecordLength;
  WORD MajorVersion;
  WORD MinorVersion;
  DWORDLONG FileReferenceNumber;
  DWORDLONG ParentFileReferenceNumber;
  USN Usn;
  LARGE_INTEGER TimeStamp;
  DWORD Reason;
  DWORD SourceInfo;
  DWORD SecurityId;
  DWORD FileAttributes;
  WORD FileNameLength;
  WORD FileNameOffset;
  WCHAR FileName[1];
} USN_RECORD,*PUSN_RECORD;

#define USN_PAGE_SIZE (0x1000)

#define USN_REASON_DATA_OVERWRITE (0x00000001)
#define USN_REASON_DATA_EXTEND (0x00000002)
#define USN_REASON_DATA_TRUNCATION (0x00000004)
#define USN_REASON_NAMED_DATA_OVERWRITE (0x00000010)
#define USN_REASON_NAMED_DATA_EXTEND (0x00000020)
#define USN_REASON_NAMED_DATA_TRUNCATION (0x00000040)
#define USN_REASON_FILE_CREATE (0x00000100)
#define USN_REASON_FILE_DELETE (0x00000200)
#define USN_REASON_EA_CHANGE (0x00000400)
#define USN_REASON_SECURITY_CHANGE (0x00000800)
#define USN_REASON_RENAME_OLD_NAME (0x00001000)
#define USN_REASON_RENAME_NEW_NAME (0x00002000)
#define USN_REASON_INDEXABLE_CHANGE (0x00004000)
#define USN_REASON_BASIC_INFO_CHANGE (0x00008000)
#define USN_REASON_HARD_LINK_CHANGE (0x00010000)
#define USN_REASON_COMPRESSION_CHANGE (0x00020000)
#define USN_REASON_ENCRYPTION_CHANGE (0x00040000)
#define USN_REASON_OBJECT_ID_CHANGE (0x00080000)
#define USN_REASON_REPARSE_POINT_CHANGE (0x00100000)
#define USN_REASON_STREAM_CHANGE (0x00200000)
#define USN_REASON_TRANSACTED_CHANGE (0x00400000)

#define USN_REASON_CLOSE (0x80000000)

typedef struct {
  DWORDLONG UsnJournalID;
  USN FirstUsn;
  USN NextUsn;
  USN LowestValidUsn;
  USN MaxUsn;
  DWORDLONG MaximumSize;
  DWORDLONG AllocationDelta;
} USN_JOURNAL_DATA,*PUSN_JOURNAL_DATA;

typedef struct {
  DWORDLONG UsnJournalID;
  DWORD DeleteFlags;
} DELETE_USN_JOURNAL_DATA,*PDELETE_USN_JOURNAL_DATA;

#define USN_DELETE_FLAG_DELETE (0x00000001)
#define USN_DELETE_FLAG_NOTIFY (0x00000002)

#define USN_DELETE_VALID_FLAGS (0x00000003)

typedef struct {
  DWORD UsnSourceInfo;
  HANDLE VolumeHandle;
  DWORD HandleInfo;
} MARK_HANDLE_INFO,*PMARK_HANDLE_INFO;

#ifdef _WIN64

typedef struct {
  DWORD UsnSourceInfo;
  UINT32 VolumeHandle;
  DWORD HandleInfo;

} MARK_HANDLE_INFO32,*PMARK_HANDLE_INFO32;
#endif

#define USN_SOURCE_DATA_MANAGEMENT (0x00000001)
#define USN_SOURCE_AUXILIARY_DATA (0x00000002)
#define USN_SOURCE_REPLICATION_MANAGEMENT (0x00000004)

#define MARK_HANDLE_PROTECT_CLUSTERS (0x00000001)
#define MARK_HANDLE_TXF_SYSTEM_LOG (0x00000004)
#define MARK_HANDLE_NOT_TXF_SYSTEM_LOG (0x00000008)
#define MARK_HANDLE_REALTIME (0x00000020)
#define MARK_HANDLE_NOT_REALTIME (0x00000040)

typedef struct {
  ACCESS_MASK DesiredAccess;
  DWORD SecurityIds[1];
} BULK_SECURITY_TEST_DATA,*PBULK_SECURITY_TEST_DATA;

#define VOLUME_IS_DIRTY                  (0x00000001)
#define VOLUME_UPGRADE_SCHEDULED         (0x00000002)
#define VOLUME_SESSION_OPEN              (0x00000004)

typedef struct _FILE_PREFETCH {
  DWORD Type;
  DWORD Count;
  DWORDLONG Prefetch[1];
} FILE_PREFETCH,*PFILE_PREFETCH;

typedef struct _FILE_PREFETCH_EX {
  ULONG Type;
  ULONG Count;
  PVOID Context;
  ULONGLONG Prefetch[1];
} FILE_PREFETCH_EX, *PFILE_PREFETCH_EX;

#define FILE_PREFETCH_TYPE_FOR_CREATE       0x1
#define FILE_PREFETCH_TYPE_FOR_DIRENUM      0x2
#define FILE_PREFETCH_TYPE_FOR_CREATE_EX    0x3
#define FILE_PREFETCH_TYPE_FOR_DIRENUM_EX   0x4

#define FILE_PREFETCH_TYPE_MAX              0x4

typedef struct _FILESYSTEM_STATISTICS {
  WORD FileSystemType;
  WORD Version;
  DWORD SizeOfCompleteStructure;
  DWORD UserFileReads;
  DWORD UserFileReadBytes;
  DWORD UserDiskReads;
  DWORD UserFileWrites;
  DWORD UserFileWriteBytes;
  DWORD UserDiskWrites;
  DWORD MetaDataReads;
  DWORD MetaDataReadBytes;
  DWORD MetaDataDiskReads;
  DWORD MetaDataWrites;
  DWORD MetaDataWriteBytes;
  DWORD MetaDataDiskWrites;
} FILESYSTEM_STATISTICS,*PFILESYSTEM_STATISTICS;

#define FILESYSTEM_STATISTICS_TYPE_NTFS 1
#define FILESYSTEM_STATISTICS_TYPE_FAT 2
#define FILESYSTEM_STATISTICS_TYPE_EXFAT 3

typedef struct _FAT_STATISTICS {
  DWORD CreateHits;
  DWORD SuccessfulCreates;
  DWORD FailedCreates;
  DWORD NonCachedReads;
  DWORD NonCachedReadBytes;
  DWORD NonCachedWrites;
  DWORD NonCachedWriteBytes;
  DWORD NonCachedDiskReads;
  DWORD NonCachedDiskWrites;
} FAT_STATISTICS,*PFAT_STATISTICS;

typedef struct _EXFAT_STATISTICS {
  DWORD CreateHits;
  DWORD SuccessfulCreates;
  DWORD FailedCreates;
  DWORD NonCachedReads;
  DWORD NonCachedReadBytes;
  DWORD NonCachedWrites;
  DWORD NonCachedWriteBytes;
  DWORD NonCachedDiskReads;
  DWORD NonCachedDiskWrites;
} EXFAT_STATISTICS, *PEXFAT_STATISTICS;

typedef struct _NTFS_STATISTICS {
  DWORD LogFileFullExceptions;
  DWORD OtherExceptions;
  DWORD MftReads;
  DWORD MftReadBytes;
  DWORD MftWrites;
  DWORD MftWriteBytes;
  struct {
    WORD Write;
    WORD Create;
    WORD SetInfo;
    WORD Flush;
  } MftWritesUserLevel;
  WORD MftWritesFlushForLogFileFull;
  WORD MftWritesLazyWriter;
  WORD MftWritesUserRequest;
  DWORD Mft2Writes;
  DWORD Mft2WriteBytes;
  struct {
    WORD Write;
    WORD Create;
    WORD SetInfo;
    WORD Flush;
  } Mft2WritesUserLevel;
  WORD Mft2WritesFlushForLogFileFull;
  WORD Mft2WritesLazyWriter;
  WORD Mft2WritesUserRequest;
  DWORD RootIndexReads;
  DWORD RootIndexReadBytes;
  DWORD RootIndexWrites;
  DWORD RootIndexWriteBytes;
  DWORD BitmapReads;
  DWORD BitmapReadBytes;
  DWORD BitmapWrites;
  DWORD BitmapWriteBytes;
  WORD BitmapWritesFlushForLogFileFull;
  WORD BitmapWritesLazyWriter;
  WORD BitmapWritesUserRequest;
  struct {
    WORD Write;
    WORD Create;
    WORD SetInfo;
  } BitmapWritesUserLevel;
  DWORD MftBitmapReads;
  DWORD MftBitmapReadBytes;
  DWORD MftBitmapWrites;
  DWORD MftBitmapWriteBytes;
  WORD MftBitmapWritesFlushForLogFileFull;
  WORD MftBitmapWritesLazyWriter;
  WORD MftBitmapWritesUserRequest;
  struct {
    WORD Write;
    WORD Create;
    WORD SetInfo;
    WORD Flush;
  } MftBitmapWritesUserLevel;
  DWORD UserIndexReads;
  DWORD UserIndexReadBytes;
  DWORD UserIndexWrites;
  DWORD UserIndexWriteBytes;
  DWORD LogFileReads;
  DWORD LogFileReadBytes;
  DWORD LogFileWrites;
  DWORD LogFileWriteBytes;
  struct {
    DWORD Calls;
    DWORD Clusters;
    DWORD Hints;
    DWORD RunsReturned;
    DWORD HintsHonored;
    DWORD HintsClusters;
    DWORD Cache;
    DWORD CacheClusters;
    DWORD CacheMiss;
    DWORD CacheMissClusters;
  } Allocate;
} NTFS_STATISTICS,*PNTFS_STATISTICS;

typedef struct _FILE_OBJECTID_BUFFER {
  BYTE ObjectId[16];
  __C89_NAMELESS union {
    __C89_NAMELESS struct {
      BYTE BirthVolumeId[16];
      BYTE BirthObjectId[16];
      BYTE DomainId[16];
    } DUMMYSTRUCTNAME;
    BYTE ExtendedInfo[48];
  } DUMMYUNIONNAME;
} FILE_OBJECTID_BUFFER,*PFILE_OBJECTID_BUFFER;

typedef struct _FILE_SET_SPARSE_BUFFER {
  BOOLEAN SetSparse;
} FILE_SET_SPARSE_BUFFER,*PFILE_SET_SPARSE_BUFFER;

typedef struct _FILE_ZERO_DATA_INFORMATION {
  LARGE_INTEGER FileOffset;
  LARGE_INTEGER BeyondFinalZero;
} FILE_ZERO_DATA_INFORMATION,*PFILE_ZERO_DATA_INFORMATION;

typedef struct _FILE_ALLOCATED_RANGE_BUFFER {
  LARGE_INTEGER FileOffset;
  LARGE_INTEGER Length;
} FILE_ALLOCATED_RANGE_BUFFER,*PFILE_ALLOCATED_RANGE_BUFFER;

typedef struct _ENCRYPTION_BUFFER {
  DWORD EncryptionOperation;
  BYTE Private[1];
} ENCRYPTION_BUFFER,*PENCRYPTION_BUFFER;

#define FILE_SET_ENCRYPTION 0x00000001
#define FILE_CLEAR_ENCRYPTION 0x00000002
#define STREAM_SET_ENCRYPTION 0x00000003
#define STREAM_CLEAR_ENCRYPTION 0x00000004

#define MAXIMUM_ENCRYPTION_VALUE 0x00000004

typedef struct _DECRYPTION_STATUS_BUFFER {
  BOOLEAN NoEncryptedStreams;
} DECRYPTION_STATUS_BUFFER,*PDECRYPTION_STATUS_BUFFER;

#define ENCRYPTION_FORMAT_DEFAULT (0x01)
#define COMPRESSION_FORMAT_SPARSE (0x4000)

typedef struct _REQUEST_RAW_ENCRYPTED_DATA {
  LONGLONG FileOffset;
  DWORD Length;
} REQUEST_RAW_ENCRYPTED_DATA,*PREQUEST_RAW_ENCRYPTED_DATA;

typedef struct _ENCRYPTED_DATA_INFO {
  DWORDLONG StartingFileOffset;
  DWORD OutputBufferOffset;
  DWORD BytesWithinFileSize;
  DWORD BytesWithinValidDataLength;
  WORD CompressionFormat;
  BYTE DataUnitShift;
  BYTE ChunkShift;
  BYTE ClusterShift;
  BYTE EncryptionFormat;
  WORD NumberOfDataBlocks;
  DWORD DataBlockSize[ANYSIZE_ARRAY];
} ENCRYPTED_DATA_INFO;
typedef ENCRYPTED_DATA_INFO *PENCRYPTED_DATA_INFO;

typedef struct _PLEX_READ_DATA_REQUEST {
  LARGE_INTEGER ByteOffset;
  DWORD ByteLength;
  DWORD PlexNumber;
} PLEX_READ_DATA_REQUEST,*PPLEX_READ_DATA_REQUEST;

typedef struct _SI_COPYFILE {
  DWORD SourceFileNameLength;
  DWORD DestinationFileNameLength;
  DWORD Flags;
  WCHAR FileNameBuffer[1];
} SI_COPYFILE,*PSI_COPYFILE;

#define COPYFILE_SIS_LINK 0x0001
#define COPYFILE_SIS_REPLACE 0x0002
#define COPYFILE_SIS_FLAGS 0x0003

typedef struct _STORAGE_DESCRIPTOR_HEADER {
  DWORD Version;
  DWORD Size;
} STORAGE_DESCRIPTOR_HEADER, *PSTORAGE_DESCRIPTOR_HEADER;

typedef enum _STORAGE_PROPERTY_ID {
  StorageDeviceProperty = 0,
  StorageAdapterProperty,
  StorageDeviceIdProperty,
  StorageDeviceUniqueIdProperty,
  StorageDeviceWriteCacheProperty,
  StorageMiniportProperty,
  StorageAccessAlignmentProperty,
  StorageDeviceSeekPenaltyProperty,
  StorageDeviceTrimProperty,
  StorageDeviceWriteAggregationProperty,
  StorageDeviceDeviceTelemetryProperty,
  StorageDeviceLBProvisioningProperty,
  StorageDevicePowerProperty,
  StorageDeviceCopyOffloadProperty,
  StorageDeviceResiliencyProperty,
  StorageDeviceMediumProductType,
  StorageAdapterRpmbProperty,
  StorageAdapterCryptoProperty,
  StorageDeviceIoCapabilityProperty = 48,
  StorageAdapterProtocolSpecificProperty,
  StorageDeviceProtocolSpecificProperty,
  StorageAdapterTemperatureProperty,
  StorageDeviceTemperatureProperty,
  StorageAdapterPhysicalTopologyProperty,
  StorageDevicePhysicalTopologyProperty,
  StorageDeviceAttributesProperty,
  StorageDeviceManagementStatus,
  StorageAdapterSerialNumberProperty,
  StorageDeviceLocationProperty,
  StorageDeviceNumaProperty,
  StorageDeviceZonedDeviceProperty,
  StorageDeviceUnsafeShutdownCount,
  StorageDeviceEnduranceProperty,
  StorageDeviceLedStateProperty,
  StorageDeviceSelfEncryptionProperty = 64,
  StorageFruIdProperty
} STORAGE_PROPERTY_ID, *PSTORAGE_PROPERTY_ID;

typedef enum _STORAGE_QUERY_TYPE {
  PropertyStandardQuery     = 0,
  PropertyExistsQuery       = 1,
  PropertyMaskQuery         = 2,
  PropertyQueryMaxDefined   = 3 
} STORAGE_QUERY_TYPE, *PSTORAGE_QUERY_TYPE;

typedef enum _STORAGE_SET_TYPE {
  PropertyStandardSet = 0,
  PropertyExistsSet,
  PropertySetMaxDefined
} STORAGE_SET_TYPE, *PSTORAGE_SET_TYPE;

typedef struct _STORAGE_PROPERTY_QUERY {
  STORAGE_PROPERTY_ID PropertyId;
  STORAGE_QUERY_TYPE  QueryType;
  BYTE                AdditionalParameters[1];
} STORAGE_PROPERTY_QUERY, *PSTORAGE_PROPERTY_QUERY;

typedef struct _STORAGE_PROPERTY_SET {
  STORAGE_PROPERTY_ID PropertyId;
  STORAGE_SET_TYPE SetType;
  BYTE AdditionalParameters[1];
} STORAGE_PROPERTY_SET, *PSTORAGE_PROPERTY_SET;

typedef struct _STORAGE_DEVICE_DESCRIPTOR {
  DWORD            Version;
  DWORD            Size;
  BYTE             DeviceType;
  BYTE             DeviceTypeModifier;
  BOOLEAN          RemovableMedia;
  BOOLEAN          CommandQueueing;
  DWORD            VendorIdOffset;
  DWORD            ProductIdOffset;
  DWORD            ProductRevisionOffset;
  DWORD            SerialNumberOffset;
  STORAGE_BUS_TYPE BusType;
  DWORD            RawPropertiesLength;
  BYTE             RawDeviceProperties[1];
} STORAGE_DEVICE_DESCRIPTOR, *PSTORAGE_DEVICE_DESCRIPTOR;

typedef struct _STORAGE_ADAPTER_DESCRIPTOR {
  DWORD   Version;
  DWORD   Size;
  DWORD   MaximumTransferLength;
  DWORD   MaximumPhysicalPages;
  DWORD   AlignmentMask;
  BOOLEAN AdapterUsesPio;
  BOOLEAN AdapterScansDown;
  BOOLEAN CommandQueueing;
  BOOLEAN AcceleratedTransfer;
  BYTE    BusType;
  WORD    BusMajorVersion;
  WORD    BusMinorVersion;
#if NTDDI_VERSION >= NTDDI_WIN8
  BYTE SrbType;
  BYTE AddressType;
#endif
} STORAGE_ADAPTER_DESCRIPTOR, *PSTORAGE_ADAPTER_DESCRIPTOR;

#if NTDDI_VERSION >= NTDDI_WIN8

#define NO_SRBTYPE_ADAPTER_DESCRIPTOR_SIZE UFIELD_OFFSET(STORAGE_ADAPTER_DESCRIPTOR, SrbType)

#ifndef SRB_TYPE_SCSI_REQUEST_BLOCK
#define SRB_TYPE_SCSI_REQUEST_BLOCK 0
#endif

#ifndef SRB_TYPE_STORAGE_REQUEST_BLOCK
#define SRB_TYPE_STORAGE_REQUEST_BLOCK 1
#endif

#ifndef STORAGE_ADDRESS_TYPE_BTL8
#define STORAGE_ADDRESS_TYPE_BTL8 0
#endif

#endif

typedef struct _STORAGE_DEVICE_ID_DESCRIPTOR {
  DWORD Version;
  DWORD Size;
  DWORD NumberOfIdentifiers;
  BYTE  Identifiers[1];
} STORAGE_DEVICE_ID_DESCRIPTOR, *PSTORAGE_DEVICE_ID_DESCRIPTOR;

typedef struct _VOLUME_GET_GPT_ATTRIBUTES_INFORMATION {
  ULONGLONG GptAttributes;
} VOLUME_GET_GPT_ATTRIBUTES_INFORMATION, *PVOLUME_GET_GPT_ATTRIBUTES_INFORMATION;

#if (_WIN32_WINNT >= 0x0600)
#define FSCTL_MAKE_MEDIA_COMPATIBLE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 76, METHOD_BUFFERED, FILE_WRITE_DATA)
#define FSCTL_SET_DEFECT_MANAGEMENT CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 77, METHOD_BUFFERED, FILE_WRITE_DATA)
#define FSCTL_QUERY_SPARING_INFO CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 78, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_QUERY_ON_DISK_VOLUME_INFO CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 79, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SET_VOLUME_COMPRESSION_STATE CTL_CODE(FILE_DEVICE_FILE_SYSTEM,80, METHOD_BUFFERED, FILE_SPECIAL_ACCESS)
#define FSCTL_TXFS_MODIFY_RM CTL_CODE(FILE_DEVICE_FILE_SYSTEM,81, METHOD_BUFFERED, FILE_WRITE_DATA)
#define FSCTL_TXFS_QUERY_RM_INFORMATION CTL_CODE(FILE_DEVICE_FILE_SYSTEM,82, METHOD_BUFFERED, FILE_READ_DATA)
#define FSCTL_TXFS_ROLLFORWARD_REDO CTL_CODE(FILE_DEVICE_FILE_SYSTEM,84, METHOD_BUFFERED, FILE_WRITE_DATA)
#define FSCTL_TXFS_ROLLFORWARD_UNDO CTL_CODE(FILE_DEVICE_FILE_SYSTEM,85, METHOD_BUFFERED, FILE_WRITE_DATA)
#define FSCTL_TXFS_START_RM CTL_CODE(FILE_DEVICE_FILE_SYSTEM,86, METHOD_BUFFERED, FILE_WRITE_DATA)
#define FSCTL_TXFS_SHUTDOWN_RM CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 87, METHOD_BUFFERED, FILE_WRITE_DATA)
#define FSCTL_TXFS_READ_BACKUP_INFORMATION CTL_CODE(FILE_DEVICE_FILE_SYSTEM,88, METHOD_BUFFERED, FILE_READ_DATA)
#define FSCTL_TXFS_WRITE_BACKUP_INFORMATION CTL_CODE(FILE_DEVICE_FILE_SYSTEM,89, METHOD_BUFFERED, FILE_WRITE_DATA)
#define FSCTL_TXFS_CREATE_SECONDARY_RM CTL_CODE(FILE_DEVICE_FILE_SYSTEM,90,METHOD_BUFFERED, FILE_WRITE_DATA)
#define FSCTL_TXFS_GET_METADATA_INFO CTL_CODE(FILE_DEVICE_FILE_SYSTEM,91, METHOD_BUFFERED, FILE_READ_DATA)
#define FSCTL_TXFS_GET_TRANSACTED_VERSION CTL_CODE(FILE_DEVICE_FILE_SYSTEM,92, METHOD_BUFFERED, FILE_READ_DATA)
#define FSCTL_TXFS_SAVEPOINT_INFORMATION CTL_CODE(FILE_DEVICE_FILE_SYSTEM,94, METHOD_BUFFERED, FILE_WRITE_DATA)
#define FSCTL_TXFS_CREATE_MINIVERSION CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 95, METHOD_BUFFERED, FILE_WRITE_DATA)
#define FSCTL_TXFS_TRANSACTION_ACTIVE CTL_CODE(FILE_DEVICE_FILE_SYSTEM,99, METHOD_BUFFERED, FILE_READ_DATA)
#define FSCTL_SET_ZERO_ON_DEALLOCATION CTL_CODE(FILE_DEVICE_FILE_SYSTEM,101, METHOD_BUFFERED, FILE_SPECIAL_ACCESS)
#define FSCTL_SET_REPAIR CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 102, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_GET_REPAIR CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 103, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_WAIT_FOR_REPAIR CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 104, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_INITIATE_REPAIR CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 106, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_CSC_INTERNAL CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 107, METHOD_NEITHER, FILE_ANY_ACCESS)
#define FSCTL_SHRINK_VOLUME CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 108, METHOD_BUFFERED, FILE_SPECIAL_ACCESS)
#define FSCTL_SET_SHORT_NAME_BEHAVIOR CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 109, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_DFSR_SET_GHOST_HANDLE_STATE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 110, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_TXFS_LIST_TRANSACTION_LOCKED_FILES CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 120, METHOD_BUFFERED, FILE_READ_DATA)
#define FSCTL_TXFS_LIST_TRANSACTIONS CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 121, METHOD_BUFFERED, FILE_READ_DATA)
#define FSCTL_QUERY_PAGEFILE_ENCRYPTION CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 122, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_RESET_VOLUME_ALLOCATION_HINTS CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 123, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_TXFS_READ_BACKUP_INFORMATION2 CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 126, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define SET_REPAIR_ENABLED 0x00000001
#define SET_REPAIR_VOLUME_BITMAP_SCAN 0x00000002
#define SET_REPAIR_DELETE_CROSSLINK 0x00000004
#define SET_REPAIR_WARN_ABOUT_DATA_LOSS 0x00000008
#define SET_REPAIR_DISABLED_AND_BUGCHECK_ON_CORRUPT 0x00000010

typedef struct _FILE_MAKE_COMPATIBLE_BUFFER {
  BOOLEAN CloseDisc;
} FILE_MAKE_COMPATIBLE_BUFFER, *PFILE_MAKE_COMPATIBLE_BUFFER;

typedef struct _FILE_SET_DEFECT_MGMT_BUFFER {
  BOOLEAN Disable;
} FILE_SET_DEFECT_MGMT_BUFFER, *PFILE_SET_DEFECT_MGMT_BUFFER;

typedef struct _FILE_QUERY_SPARING_BUFFER {
  ULONG   SparingUnitBytes;
  BOOLEAN SoftwareSparing;
  ULONG   TotalSpareBlocks;
  ULONG   FreeSpareBlocks;
} FILE_QUERY_SPARING_BUFFER, *PFILE_QUERY_SPARING_BUFFER;

typedef struct _FILE_QUERY_ON_DISK_VOL_INFO_BUFFER {
  LARGE_INTEGER DirectoryCount;
  LARGE_INTEGER FileCount;
  WORD          FsFormatMajVersion;
  WORD          FsFormatMinVersion;
  WCHAR         FsFormatName[12];
  LARGE_INTEGER FormatTime;
  LARGE_INTEGER LastUpdateTime;
  WCHAR         CopyrightInfo[34];
  WCHAR         AbstractInfo[34];
  WCHAR         FormattingImplementationInfo[34];
  WCHAR         LastModifyingImplementationInfo[34];
} FILE_QUERY_ON_DISK_VOL_INFO_BUFFER, *PFILE_QUERY_ON_DISK_VOL_INFO_BUFFER;

#define SET_REPAIR_ENABLED                          0x00000001
#define SET_REPAIR_VOLUME_BITMAP_SCAN               0x00000002
#define SET_REPAIR_DELETE_CROSSLINK                 0x00000004
#define SET_REPAIR_WARN_ABOUT_DATA_LOSS             0x00000008
#define SET_REPAIR_DISABLED_AND_BUGCHECK_ON_CORRUPT 0x00000010
#define SET_REPAIR_VALID_MASK                       0x0000001F

typedef enum _SHRINK_VOLUME_REQUEST_TYPES {
  ShrinkPrepare = 1,
  ShrinkCommit,
  ShrinkAbort
} SHRINK_VOLUME_REQUEST_TYPES;

typedef struct _SHRINK_VOLUME_INFORMATION {
  SHRINK_VOLUME_REQUEST_TYPES ShrinkRequestType;
  DWORDLONG                   Flags;
  LONGLONG                    NewNumberOfSectors;
} SHRINK_VOLUME_INFORMATION, *PSHRINK_VOLUME_INFORMATION;

#define TXFS_RM_FLAG_LOGGING_MODE 0x00000001
#define TXFS_RM_FLAG_RENAME_RM 0x00000002
#define TXFS_RM_FLAG_LOG_CONTAINER_COUNT_MAX 0x00000004
#define TXFS_RM_FLAG_LOG_CONTAINER_COUNT_MIN 0x00000008
#define TXFS_RM_FLAG_LOG_GROWTH_INCREMENT_NUM_CONTAINERS 0x00000010
#define TXFS_RM_FLAG_LOG_GROWTH_INCREMENT_PERCENT 0x00000020
#define TXFS_RM_FLAG_LOG_AUTO_SHRINK_PERCENTAGE 0x00000040
#define TXFS_RM_FLAG_LOG_NO_CONTAINER_COUNT_MAX 0x00000080
#define TXFS_RM_FLAG_LOG_NO_CONTAINER_COUNT_MIN 0x00000100
#define TXFS_RM_FLAG_GROW_LOG 0x00000400
#define TXFS_RM_FLAG_SHRINK_LOG 0x00000800
#define TXFS_RM_FLAG_ENFORCE_MINIMUM_SIZE 0x00001000
#define TXFS_RM_FLAG_PRESERVE_CHANGES 0x00002000
#define TXFS_RM_FLAG_RESET_RM_AT_NEXT_START 0x00004000
#define TXFS_RM_FLAG_DO_NOT_RESET_RM_AT_NEXT_START 0x00008000
#define TXFS_RM_FLAG_PREFER_CONSISTENCY 0x00010000
#define TXFS_RM_FLAG_PREFER_AVAILABILITY 0x00020000

#define TXFS_LOGGING_MODE_SIMPLE 1
#define TXFS_LOGGING_MODE_FULL 2

#define TXFS_TRANSACTION_STATE_NONE      0
#define TXFS_TRANSACTION_STATE_ACTIVE    1
#define TXFS_TRANSACTION_STATE_PREPARED  2
#define TXFS_TRANSACTION_STATE_NOTACTIVE 3

#define TXFS_MODIFY_RM_VALID_FLAGS (TXFS_RM_FLAG_LOGGING_MODE                        | \
                                    TXFS_RM_FLAG_RENAME_RM                           | \
                                    TXFS_RM_FLAG_LOG_CONTAINER_COUNT_MAX             | \
                                    TXFS_RM_FLAG_LOG_CONTAINER_COUNT_MIN             | \
                                    TXFS_RM_FLAG_LOG_GROWTH_INCREMENT_NUM_CONTAINERS | \
                                    TXFS_RM_FLAG_LOG_GROWTH_INCREMENT_PERCENT        | \
                                    TXFS_RM_FLAG_LOG_AUTO_SHRINK_PERCENTAGE          | \
                                    TXFS_RM_FLAG_LOG_NO_CONTAINER_COUNT_MAX          | \
                                    TXFS_RM_FLAG_LOG_NO_CONTAINER_COUNT_MIN          | \
                                    TXFS_RM_FLAG_SHRINK_LOG                          | \
                                    TXFS_RM_FLAG_GROW_LOG                            | \
                                    TXFS_RM_FLAG_ENFORCE_MINIMUM_SIZE                | \
                                    TXFS_RM_FLAG_PRESERVE_CHANGES                    | \
                                    TXFS_RM_FLAG_RESET_RM_AT_NEXT_START              | \
                                    TXFS_RM_FLAG_DO_NOT_RESET_RM_AT_NEXT_START       | \
                                    TXFS_RM_FLAG_PREFER_CONSISTENCY                  | \
                                    TXFS_RM_FLAG_PREFER_AVAILABILITY)

typedef struct _TXFS_MODIFY_RM {
  ULONG     Flags;
  ULONG     LogContainerCountMax;
  ULONG     LogContainerCountMin;
  ULONG     LogContainerCount;
  ULONG     LogGrowthIncrement;
  ULONG     LogAutoShrinkPercentage;
  ULONGLONG Reserved;
  USHORT    LoggingMode;
} TXFS_MODIFY_RM, *PTXFS_MODIFY_RM;

#define TXFS_RM_STATE_NOT_STARTED 0
#define TXFS_RM_STATE_STARTING 1
#define TXFS_RM_STATE_ACTIVE 2
#define TXFS_RM_STATE_SHUTTING_DOWN 3

#define TXFS_QUERY_RM_INFORMATION_VALID_FLAGS                           \
                (TXFS_RM_FLAG_LOG_GROWTH_INCREMENT_NUM_CONTAINERS   |   \
                 TXFS_RM_FLAG_LOG_GROWTH_INCREMENT_PERCENT          |   \
                 TXFS_RM_FLAG_LOG_NO_CONTAINER_COUNT_MAX            |   \
                 TXFS_RM_FLAG_LOG_NO_CONTAINER_COUNT_MIN            |   \
                 TXFS_RM_FLAG_RESET_RM_AT_NEXT_START                |   \
                 TXFS_RM_FLAG_DO_NOT_RESET_RM_AT_NEXT_START         |   \
                 TXFS_RM_FLAG_PREFER_CONSISTENCY                    |   \
                 TXFS_RM_FLAG_PREFER_AVAILABILITY)

typedef struct _TXFS_QUERY_RM_INFORMATION {
  ULONG         BytesRequired;
  ULONGLONG     TailLsn;
  ULONGLONG     CurrentLsn;
  ULONGLONG     ArchiveTailLsn;
  ULONGLONG     LogContainerSize;
  LARGE_INTEGER HighestVirtualClock;
  ULONG         LogContainerCount;
  ULONG         LogContainerCountMax;
  ULONG         LogContainerCountMin;
  ULONG         LogGrowthIncrement;
  ULONG         LogAutoShrinkPercentage;
  ULONG         Flags;
  USHORT        LoggingMode;
  USHORT        Reserved;
  ULONG         RmState;
  ULONGLONG     LogCapacity;
  ULONGLONG     LogFree;
  ULONGLONG     TopsSize;
  ULONGLONG     TopsUsed;
  ULONGLONG     TransactionCount;
  ULONGLONG     OnePCCount;
  ULONGLONG     TwoPCCount;
  ULONGLONG     NumberLogFileFull;
  ULONGLONG     OldestTransactionAge;
  GUID          RMName;
  ULONG         TmLogPathOffset;
} TXFS_QUERY_RM_INFORMATION, *PTXFS_QUERY_RM_INFORMATION;

#define TXFS_ROLLFORWARD_REDO_FLAG_USE_LAST_REDO_LSN 0x01
#define TXFS_ROLLFORWARD_REDO_FLAG_USE_LAST_VIRTUAL_CLOCK 0x02

#define TXFS_ROLLFORWARD_REDO_VALID_FLAGS                               \
                (TXFS_ROLLFORWARD_REDO_FLAG_USE_LAST_REDO_LSN |         \
                 TXFS_ROLLFORWARD_REDO_FLAG_USE_LAST_VIRTUAL_CLOCK)

typedef struct _TXFS_ROLLFORWARD_REDO_INFORMATION {
  LARGE_INTEGER LastVirtualClock;
  ULONGLONG     LastRedoLsn;
  ULONGLONG     HighestRecoveryLsn;
  ULONG         Flags;
} TXFS_ROLLFORWARD_REDO_INFORMATION, *PTXFS_ROLLFORWARD_REDO_INFORMATION;

#define TXFS_START_RM_FLAG_LOG_CONTAINER_COUNT_MAX 0x00000001
#define TXFS_START_RM_FLAG_LOG_CONTAINER_COUNT_MIN 0x00000002
#define TXFS_START_RM_FLAG_LOG_CONTAINER_SIZE 0x00000004
#define TXFS_START_RM_FLAG_LOG_GROWTH_INCREMENT_NUM_CONTAINERS 0x00000008
#define TXFS_START_RM_FLAG_LOG_GROWTH_INCREMENT_PERCENT 0x00000010
#define TXFS_START_RM_FLAG_LOG_AUTO_SHRINK_PERCENTAGE 0x00000020
#define TXFS_START_RM_FLAG_LOG_NO_CONTAINER_COUNT_MAX 0x00000040
#define TXFS_START_RM_FLAG_LOG_NO_CONTAINER_COUNT_MIN 0x00000080
#define TXFS_START_RM_FLAG_RECOVER_BEST_EFFORT 0x00000200
#define TXFS_START_RM_FLAG_LOGGING_MODE 0x00000400
#define TXFS_START_RM_FLAG_PRESERVE_CHANGES 0x00000800
#define TXFS_START_RM_FLAG_PREFER_CONSISTENCY 0x00001000
#define TXFS_START_RM_FLAG_PREFER_AVAILABILITY 0x00002000

#define TXFS_START_RM_VALID_FLAGS                                           \
                (TXFS_START_RM_FLAG_LOG_CONTAINER_COUNT_MAX             |   \
                 TXFS_START_RM_FLAG_LOG_CONTAINER_COUNT_MIN             |   \
                 TXFS_START_RM_FLAG_LOG_CONTAINER_SIZE                  |   \
                 TXFS_START_RM_FLAG_LOG_GROWTH_INCREMENT_NUM_CONTAINERS |   \
                 TXFS_START_RM_FLAG_LOG_GROWTH_INCREMENT_PERCENT        |   \
                 TXFS_START_RM_FLAG_LOG_AUTO_SHRINK_PERCENTAGE          |   \
                 TXFS_START_RM_FLAG_RECOVER_BEST_EFFORT                 |   \
                 TXFS_START_RM_FLAG_LOG_NO_CONTAINER_COUNT_MAX          |   \
                 TXFS_START_RM_FLAG_LOGGING_MODE                        |   \
                 TXFS_START_RM_FLAG_PRESERVE_CHANGES                    |   \
                 TXFS_START_RM_FLAG_PREFER_CONSISTENCY                  |   \
                 TXFS_START_RM_FLAG_PREFER_AVAILABILITY)

typedef struct _TXFS_START_RM_INFORMATION {
  ULONG     Flags;
  ULONGLONG LogContainerSize;
  ULONG     LogContainerCountMin;
  ULONG     LogContainerCountMax;
  ULONG     LogGrowthIncrement;
  ULONG     LogAutoShrinkPercentage;
  ULONG     TmLogPathOffset;
  USHORT    TmLogPathLength;
  USHORT    LoggingMode;
  USHORT    LogPathLength;
  USHORT    Reserved;
  WCHAR     LogPath[1];
} TXFS_START_RM_INFORMATION, *PTXFS_START_RM_INFORMATION;

typedef struct _TXFS_GET_METADATA_INFO_OUT {
  struct {
    LONGLONG LowPart;
    LONGLONG HighPart;
  } TxfFileId;
  GUID      LockingTransaction;
  ULONGLONG LastLsn;
  ULONG     TransactionState;
} TXFS_GET_METADATA_INFO_OUT, *PTXFS_GET_METADATA_INFO_OUT;

#define TXFS_LIST_TRANSACTION_LOCKED_FILES_ENTRY_FLAG_CREATED 0x00000001
#define TXFS_LIST_TRANSACTION_LOCKED_FILES_ENTRY_FLAG_DELETED 0x00000002

typedef struct _TXFS_LIST_TRANSACTION_LOCKED_FILES_ENTRY {
  ULONGLONG Offset;
  ULONG     NameFlags;
  LONGLONG  FileId;
  ULONG     Reserved1;
  ULONG     Reserved2;
  LONGLONG  Reserved3;
  WCHAR     FileName[1];
} TXFS_LIST_TRANSACTION_LOCKED_FILES_ENTRY, *PTXFS_LIST_TRANSACTION_LOCKED_FILES_ENTRY;

typedef struct _TXFS_LIST_TRANSACTION_LOCKED_FILES {
  GUID      KtmTransaction;
  ULONGLONG NumberOfFiles;
  ULONGLONG BufferSizeRequired;
  ULONGLONG Offset;
} TXFS_LIST_TRANSACTION_LOCKED_FILES, *PTXFS_LIST_TRANSACTION_LOCKED_FILES;

typedef struct _TXFS_LIST_TRANSACTIONS_ENTRY {
  GUID     TransactionId;
  ULONG    TransactionState;
  ULONG    Reserved1;
  ULONG    Reserved2;
  LONGLONG Reserved3;
} TXFS_LIST_TRANSACTIONS_ENTRY, *PTXFS_LIST_TRANSACTIONS_ENTRY;

typedef struct _TXFS_LIST_TRANSACTIONS {
  ULONGLONG NumberOfTransactions;
  ULONGLONG BufferSizeRequired;
} TXFS_LIST_TRANSACTIONS, *PTXFS_LIST_TRANSACTIONS;

typedef struct _TXFS_READ_BACKUP_INFORMATION_OUT {
  __C89_NAMELESS union {
    ULONG BufferLength;
    UCHAR Buffer;
  } DUMMYUNIONNAME;
} TXFS_READ_BACKUP_INFORMATION_OUT, *PTXFS_READ_BACKUP_INFORMATION_OUT;

typedef struct _TXFS_WRITE_BACKUP_INFORMATION {
  UCHAR Buffer;
} TXFS_WRITE_BACKUP_INFORMATION, *PTXFS_WRITE_BACKUP_INFORMATION;

#define TXFS_TRANSACTED_VERSION_NONTRANSACTED 0xFFFFFFFE
#define TXFS_TRANSACTED_VERSION_UNCOMMITTED 0xFFFFFFFF

typedef struct _TXFS_GET_TRANSACTED_VERSION {
  ULONG  ThisBaseVersion;
  ULONG  LatestVersion;
  USHORT ThisMiniVersion;
  USHORT FirstMiniVersion;
  USHORT LatestMiniVersion;
} TXFS_GET_TRANSACTED_VERSION, *PTXFS_GET_TRANSACTED_VERSION;

#define TXFS_SAVEPOINT_SET 1
#define TXFS_SAVEPOINT_ROLLBACK 2
#define TXFS_SAVEPOINT_CLEAR 4
#define TXFS_SAVEPOINT_CLEAR_ALL 16

typedef struct _TXFS_SAVEPOINT_INFORMATION {
  HANDLE KtmTransaction;
  ULONG  ActionCode;
  ULONG  SavepointId;
} TXFS_SAVEPOINT_INFORMATION, *PTXFS_SAVEPOINT_INFORMATION;

typedef struct _TXFS_CREATE_MINIVERSION_INFO {
  USHORT StructureVersion;
  USHORT StructureLength;
  ULONG  BaseVersion;
  USHORT MiniVersion;
} TXFS_CREATE_MINIVERSION_INFO, *PTXFS_CREATE_MINIVERSION_INFO;

typedef struct _TXFS_TRANSACTION_ACTIVE_INFO {
  BOOLEAN TransactionsActiveAtSnapshot;
} TXFS_TRANSACTION_ACTIVE_INFO, *PTXFS_TRANSACTION_ACTIVE_INFO;

typedef enum _WRITE_CACHE_TYPE {
  WriteCacheTypeUnknown        = 0,
  WriteCacheTypeNone           = 1,
  WriteCacheTypeWriteBack      = 2,
  WriteCacheTypeWriteThrough   = 3 
} WRITE_CACHE_TYPE;

typedef enum _WRITE_CACHE_ENABLE {
  WriteCacheEnableUnknown   = 0,
  WriteCacheDisabled        = 1,
  WriteCacheEnabled         = 2 
} WRITE_CACHE_ENABLE;

typedef enum _WRITE_CACHE_CHANGE {
  WriteCacheChangeUnknown   = 0,
  WriteCacheNotChangeable   = 1,
  WriteCacheChangeable      = 2 
} WRITE_CACHE_CHANGE;

typedef enum _WRITE_THROUGH {
  WriteThroughUnknown        = 0,
  WriteThroughNotSupported   = 1,
  WriteThroughSupported      = 2 
} WRITE_THROUGH;

typedef struct _STORAGE_WRITE_CACHE_PROPERTY {
  DWORD              Version;
  DWORD              Size;
  WRITE_CACHE_TYPE   WriteCacheType;
  WRITE_CACHE_ENABLE WriteCacheEnabled;
  WRITE_CACHE_CHANGE WriteCacheChangeable;
  WRITE_THROUGH      WriteThroughSupported;
  BOOLEAN            FlushCacheSupported;
  BOOLEAN            UserDefinedPowerProtection;
  BOOLEAN            NVCacheEnabled;
} STORAGE_WRITE_CACHE_PROPERTY, *PSTORAGE_WRITE_CACHE_PROPERTY;

typedef enum _STORAGE_PORT_CODE_SET {
  StoragePortCodeSetReserved = 0,
  StoragePortCodeSetStorport = 1,
  StoragePortCodeSetSCSIport = 2,
  StoragePortCodeSetSpaceport = 3,
  StoragePortCodeSetATAport = 4,
  StoragePortCodeSetUSBport = 5,
  StoragePortCodeSetSBP2port = 6,
  StoragePortCodeSetSDport = 7
} STORAGE_PORT_CODE_SET, *PSTORAGE_PORT_CODE_SET;

typedef struct _STORAGE_MINIPORT_DESCRIPTOR {
  DWORD                 Version;
  DWORD                 Size;
  STORAGE_PORT_CODE_SET Portdriver;
  BOOLEAN               LUNResetSupported;
  BOOLEAN               TargetResetSupported;
} STORAGE_MINIPORT_DESCRIPTOR, *PSTORAGE_MINIPORT_DESCRIPTOR;

typedef enum _STORAGE_IDENTIFIER_CODE_SET {
  StorageIdCodeSetReserved = 0,
  StorageIdCodeSetBinary = 1,
  StorageIdCodeSetAscii = 2,
  StorageIdCodeSetUtf8 = 3
} STORAGE_IDENTIFIER_CODE_SET, *PSTORAGE_IDENTIFIER_CODE_SET;

typedef enum _STORAGE_IDENTIFIER_TYPE {
  StorageIdTypeVendorSpecific = 0,
  StorageIdTypeVendorId = 1,
  StorageIdTypeEUI64 = 2,
  StorageIdTypeFCPHName = 3,
  StorageIdTypePortRelative = 4,
  StorageIdTypeTargetPortGroup = 5,
  StorageIdTypeLogicalUnitGroup = 6,
  StorageIdTypeMD5LogicalUnitIdentifier = 7,
  StorageIdTypeScsiNameString = 8
} STORAGE_IDENTIFIER_TYPE, *PSTORAGE_IDENTIFIER_TYPE;

#define StorageIdTypeNAA StorageIdTypeFCPHName

typedef enum _STORAGE_ID_NAA_FORMAT {
  StorageIdNAAFormatIEEEExtended = 2,
  StorageIdNAAFormatIEEERegistered = 3,
  StorageIdNAAFormatIEEEERegisteredExtended = 5
} STORAGE_ID_NAA_FORMAT, *PSTORAGE_ID_NAA_FORMAT;

typedef enum _STORAGE_ASSOCIATION_TYPE {
  StorageIdAssocDevice = 0,
  StorageIdAssocPort = 1,
  StorageIdAssocTarget = 2
} STORAGE_ASSOCIATION_TYPE, *PSTORAGE_ASSOCIATION_TYPE;

typedef struct _STORAGE_IDENTIFIER {
  STORAGE_IDENTIFIER_CODE_SET CodeSet;
  STORAGE_IDENTIFIER_TYPE Type;
  USHORT IdentifierSize;
  USHORT NextOffset;
  STORAGE_ASSOCIATION_TYPE Association;
  UCHAR Identifier[1];
} STORAGE_IDENTIFIER, *PSTORAGE_IDENTIFIER;

typedef struct _STORAGE_ACCESS_ALIGNMENT_DESCRIPTOR {
  DWORD Version;
  DWORD Size;
  DWORD BytesPerCacheLine;
  DWORD BytesOffsetForCacheAlignment;
  DWORD BytesPerLogicalSector;
  DWORD BytesPerPhysicalSector;
  DWORD BytesOffsetForSectorAlignment;
} STORAGE_ACCESS_ALIGNMENT_DESCRIPTOR, *PSTORAGE_ACCESS_ALIGNMENT_DESCRIPTOR;

typedef struct _STORAGE_MEDIUM_PRODUCT_TYPE_DESCRIPTOR {
  DWORD Version;
  DWORD Size;
  DWORD MediumProductType;
} STORAGE_MEDIUM_PRODUCT_TYPE_DESCRIPTOR, *PSTORAGE_MEDIUM_PRODUCT_TYPE_DESCRIPTOR;

#endif /*(_WIN32_WINNT >= 0x0600)*/

typedef struct _DEVICE_SEEK_PENALTY_DESCRIPTOR {
  DWORD   Version;
  DWORD   Size;
  BOOLEAN IncursSeekPenalty;
} DEVICE_SEEK_PENALTY_DESCRIPTOR, *PDEVICE_SEEK_PENALTY_DESCRIPTOR;

typedef struct _DEVICE_WRITE_AGGREGATION_DESCRIPTOR {
  ULONG Version;
  ULONG Size;
  BOOLEAN BenefitsFromWriteAggregation;
} DEVICE_WRITE_AGGREGATION_DESCRIPTOR, *PDEVICE_WRITE_AGGREGATION_DESCRIPTOR;

typedef struct _DEVICE_TRIM_DESCRIPTOR {
  DWORD   Version;
  DWORD   Size;
  BOOLEAN TrimEnabled;
} DEVICE_TRIM_DESCRIPTOR, *PDEVICE_TRIM_DESCRIPTOR;

typedef struct _DEVICE_LB_PROVISIONING_DESCRIPTOR {
  DWORD Version;
  DWORD Size;
  BYTE ThinProvisioningEnabled : 1;
  BYTE ThinProvisioningReadZeros : 1;
  BYTE AnchorSupported : 3;
  BYTE UnmapGranularityAlignmentValid : 1;
  BYTE GetFreeSpaceSupported : 1;
  BYTE MapSupported : 1;
  BYTE Reserved1[7];
  DWORDLONG OptimalUnmapGranularity;
  DWORDLONG UnmapGranularityAlignment;
#if NTDDI_VERSION >= NTDDI_WINBLUE
  DWORD MaxUnmapLbaCount;
  DWORD MaxUnmapBlockDescriptorCount;
#endif
} DEVICE_LB_PROVISIONING_DESCRIPTOR, *PDEVICE_LB_PROVISIONING_DESCRIPTOR;

#define DEVICE_LB_PROVISIONING_DESCRIPTOR_V1_SIZE RTL_SIZEOF_THROUGH_FIELD(DEVICE_LB_PROVISIONING_DESCRIPTOR, UnmapGranularityAlignment)

typedef struct _STORAGE_LB_PROVISIONING_MAP_RESOURCES {
  DWORD Size;
  DWORD Version;
  BYTE AvailableMappingResourcesValid : 1;
  BYTE UsedMappingResourcesValid : 1;
  BYTE Reserved0 : 6;
  BYTE Reserved1[3];
  BYTE AvailableMappingResourcesScope : 2;
  BYTE UsedMappingResourcesScope : 2;
  BYTE Reserved2 : 4;
  BYTE Reserved3[3];
  DWORDLONG AvailableMappingResources;
  DWORDLONG UsedMappingResources;
} STORAGE_LB_PROVISIONING_MAP_RESOURCES, *PSTORAGE_LB_PROVISIONING_MAP_RESOURCES;

typedef struct _DEVICE_POWER_DESCRIPTOR {
  DWORD Version;
  DWORD Size;
  BOOLEAN DeviceAttentionSupported;
  BOOLEAN AsynchronousNotificationSupported;
  BOOLEAN IdlePowerManagementEnabled;
  BOOLEAN D3ColdEnabled;
  BOOLEAN D3ColdSupported;
  BOOLEAN NoVerifyDuringIdlePower;
  BYTE Reserved[2];
  DWORD IdleTimeoutInMS;
} DEVICE_POWER_DESCRIPTOR, *PDEVICE_POWER_DESCRIPTOR;

typedef struct _DEVICE_COPY_OFFLOAD_DESCRIPTOR {
  DWORD Version;
  DWORD Size;
  DWORD MaximumTokenLifetime;
  DWORD DefaultTokenLifetime;
  DWORDLONG MaximumTransferSize;
  DWORDLONG OptimalTransferCount;
  DWORD MaximumDataDescriptors;
  DWORD MaximumTransferLengthPerDescriptor;
  DWORD OptimalTransferLengthPerDescriptor;
  WORD OptimalTransferLengthGranularity;
  BYTE Reserved[2];
} DEVICE_COPY_OFFLOAD_DESCRIPTOR, *PDEVICE_COPY_OFFLOAD_DESCRIPTOR;

typedef struct _STORAGE_DEVICE_RESILIENCY_DESCRIPTOR {
  DWORD Version;
  DWORD Size;
  DWORD NameOffset;
  DWORD NumberOfLogicalCopies;
  DWORD NumberOfPhysicalCopies;
  DWORD PhysicalDiskRedundancy;
  DWORD NumberOfColumns;
  DWORD Interleave;
} STORAGE_DEVICE_RESILIENCY_DESCRIPTOR, *PSTORAGE_DEVICE_RESILIENCY_DESCRIPTOR;

typedef enum _STORAGE_RPMB_FRAME_TYPE {
  StorageRpmbFrameTypeUnknown = 0,
  StorageRpmbFrameTypeStandard,
  StorageRpmbFrameTypeMax
} STORAGE_RPMB_FRAME_TYPE, *PSTORAGE_RPMB_FRAME_TYPE;

#define STORAGE_RPMB_DESCRIPTOR_VERSION_1 1

#define STORAGE_RPMB_MINIMUM_RELIABLE_WRITE_SIZE 512

typedef struct _STORAGE_RPMB_DESCRIPTOR {
  DWORD Version;
  DWORD Size;
  DWORD SizeInBytes;
  DWORD MaxReliableWriteSizeInBytes;
  STORAGE_RPMB_FRAME_TYPE FrameFormat;
} STORAGE_RPMB_DESCRIPTOR, *PSTORAGE_RPMB_DESCRIPTOR;

typedef enum _STORAGE_CRYPTO_ALGORITHM_ID {
  StorageCryptoAlgorithmUnknown = 0,
  StorageCryptoAlgorithmXTSAES = 1,
  StorageCryptoAlgorithmBitlockerAESCBC,
  StorageCryptoAlgorithmAESECB,
  StorageCryptoAlgorithmESSIVAESCBC,
  StorageCryptoAlgorithmMax
} STORAGE_CRYPTO_ALGORITHM_ID, *PSTORAGE_CRYPTO_ALGORITHM_ID;

typedef enum _STORAGE_CRYPTO_KEY_SIZE {
  StorageCryptoKeySizeUnknown = 0,
  StorageCryptoKeySize128Bits = 1,
  StorageCryptoKeySize192Bits,
  StorageCryptoKeySize256Bits,
  StorageCryptoKeySize512Bits
} STORAGE_CRYPTO_KEY_SIZE, *PSTORAGE_CRYPTO_KEY_SIZE;

#define STORAGE_CRYPTO_CAPABILITY_VERSION_1 1

typedef struct _STORAGE_CRYPTO_CAPABILITY {
  DWORD Version;
  DWORD Size;
  DWORD CryptoCapabilityIndex;
  STORAGE_CRYPTO_ALGORITHM_ID AlgorithmId;
  STORAGE_CRYPTO_KEY_SIZE KeySize;
  DWORD DataUnitSizeBitmask;
} STORAGE_CRYPTO_CAPABILITY, *PSTORAGE_CRYPTO_CAPABILITY;

#define STORAGE_CRYPTO_DESCRIPTOR_VERSION_1 1

typedef struct _STORAGE_CRYPTO_DESCRIPTOR {
  DWORD Version;
  DWORD Size;
  DWORD NumKeysSupported;
  DWORD NumCryptoCapabilities;
  STORAGE_CRYPTO_CAPABILITY CryptoCapabilities[ANYSIZE_ARRAY];
} STORAGE_CRYPTO_DESCRIPTOR, *PSTORAGE_CRYPTO_DESCRIPTOR;

#define STORAGE_TIER_NAME_LENGTH (256)
#define STORAGE_TIER_DESCRIPTION_LENGTH (512)

#define STORAGE_TIER_FLAG_NO_SEEK_PENALTY (0x00020000)
#define STORAGE_TIER_FLAG_WRITE_BACK_CACHE (0x00200000)
#define STORAGE_TIER_FLAG_READ_CACHE (0x00400000)
#define STORAGE_TIER_FLAG_PARITY (0x00800000)
#define STORAGE_TIER_FLAG_SMR (0x01000000)

typedef enum _STORAGE_TIER_MEDIA_TYPE {
  StorageTierMediaTypeUnspecified = 0,
  StorageTierMediaTypeDisk = 1,
  StorageTierMediaTypeSsd = 2,
  StorageTierMediaTypeScm = 4,
  StorageTierMediaTypeMax
} STORAGE_TIER_MEDIA_TYPE, *PSTORAGE_TIER_MEDIA_TYPE;

typedef enum _STORAGE_TIER_CLASS {
  StorageTierClassUnspecified = 0,
  StorageTierClassCapacity,
  StorageTierClassPerformance,
  StorageTierClassMax
} STORAGE_TIER_CLASS, *PSTORAGE_TIER_CLASS;

typedef struct _STORAGE_TIER {
  GUID Id;
  WCHAR Name[STORAGE_TIER_NAME_LENGTH];
  WCHAR Description[STORAGE_TIER_NAME_LENGTH];
  DWORDLONG Flags;
  DWORDLONG ProvisionedCapacity;
  STORAGE_TIER_MEDIA_TYPE MediaType;
  STORAGE_TIER_CLASS Class;
} STORAGE_TIER, *PSTORAGE_TIER;

typedef struct _STORAGE_DEVICE_TIERING_DESCRIPTOR {
  DWORD Version;
  DWORD Size;
  DWORD Flags;
  DWORD TotalNumberOfTiers;
  DWORD NumberOfTiersReturned;
  STORAGE_TIER Tiers[ANYSIZE_ARRAY];
} STORAGE_DEVICE_TIERING_DESCRIPTOR, *PSTORAGE_DEVICE_TIERING_DESCRIPTOR;

typedef struct _STORAGE_DEVICE_FAULT_DOMAIN_DESCRIPTOR {
  DWORD Version;
  DWORD Size;
  DWORD NumberOfFaultDomains;
  GUID FaultDomainIds[ANYSIZE_ARRAY];
} STORAGE_DEVICE_FAULT_DOMAIN_DESCRIPTOR, *PSTORAGE_DEVICE_FAULT_DOMAIN_DESCRIPTOR;

typedef enum _STORAGE_PROTOCOL_TYPE {
  ProtocolTypeUnknown = 0x00,
  ProtocolTypeScsi,
  ProtocolTypeAta,
  ProtocolTypeNvme,
  ProtocolTypeSd,
  ProtocolTypeUfs,
  ProtocolTypeProprietary = 0x7E,
  ProtocolTypeMaxReserved = 0x7F
} STORAGE_PROTOCOL_TYPE, *PSTORAGE_PROTOCOL_TYPE;

typedef enum _STORAGE_PROTOCOL_NVME_DATA_TYPE {
  NVMeDataTypeUnknown = 0,
  NVMeDataTypeIdentify,
  NVMeDataTypeLogPage,
  NVMeDataTypeFeature
} STORAGE_PROTOCOL_NVME_DATA_TYPE, *PSTORAGE_PROTOCOL_NVME_DATA_TYPE;

typedef enum _STORAGE_PROTOCOL_ATA_DATA_TYPE {
  AtaDataTypeUnknown = 0,
  AtaDataTypeIdentify,
  AtaDataTypeLogPage
} STORAGE_PROTOCOL_ATA_DATA_TYPE, *PSTORAGE_PROTOCOL_ATA_DATA_TYPE;

typedef enum _STORAGE_PROTOCOL_UFS_DATA_TYPE {
  UfsDataTypeUnknown = 0,
  UfsDataTypeQueryDescriptor,
  UfsDataTypeQueryAttribute,
  UfsDataTypeQueryFlag,
  UfsDataTypeQueryDmeAttribute,
  UfsDataTypeQueryDmePeerAttribute,
  UfsDataTypeMax
} STORAGE_PROTOCOL_UFS_DATA_TYPE, *PSTORAGE_PROTOCOL_UFS_DATA_TYPE;

typedef union _STORAGE_PROTOCOL_DATA_SUBVALUE_GET_LOG_PAGE {
  __C89_NAMELESS struct {
    DWORD RetainAsynEvent : 1;
    DWORD LogSpecificField : 4;
    DWORD Reserved : 27;
  };
  DWORD AsUlong;
} STORAGE_PROTOCOL_DATA_SUBVALUE_GET_LOG_PAGE, *PSTORAGE_PROTOCOL_DATA_SUBVALUE_GET_LOG_PAGE;

typedef struct _STORAGE_PROTOCOL_SPECIFIC_DATA {
  STORAGE_PROTOCOL_TYPE ProtocolType;
  DWORD DataType;
  DWORD ProtocolDataRequestValue;
  DWORD ProtocolDataRequestSubValue;
  DWORD ProtocolDataOffset;
  DWORD ProtocolDataLength;
  DWORD FixedProtocolReturnData;
  DWORD ProtocolDataRequestSubValue2;
  DWORD ProtocolDataRequestSubValue3;
  DWORD ProtocolDataRequestSubValue4;
} STORAGE_PROTOCOL_SPECIFIC_DATA, *PSTORAGE_PROTOCOL_SPECIFIC_DATA;

typedef struct _STORAGE_PROTOCOL_SPECIFIC_DATA_EXT {
  STORAGE_PROTOCOL_TYPE ProtocolType;
  DWORD DataType;
  DWORD ProtocolDataValue;
  DWORD ProtocolDataSubValue;
  DWORD ProtocolDataOffset;
  DWORD ProtocolDataLength;
  DWORD FixedProtocolReturnData;
  DWORD ProtocolDataSubValue2;
  DWORD ProtocolDataSubValue3;
  DWORD ProtocolDataSubValue4;
  DWORD ProtocolDataSubValue5;
  DWORD Reserved[5];
} STORAGE_PROTOCOL_SPECIFIC_DATA_EXT, *PSTORAGE_PROTOCOL_SPECIFIC_DATA_EXT;

typedef struct _STORAGE_PROTOCOL_DATA_DESCRIPTOR {
  DWORD Version;
  DWORD Size;
  STORAGE_PROTOCOL_SPECIFIC_DATA ProtocolSpecificData;
} STORAGE_PROTOCOL_DATA_DESCRIPTOR, *PSTORAGE_PROTOCOL_DATA_DESCRIPTOR;

typedef struct _STORAGE_PROTOCOL_DATA_DESCRIPTOR_EXT {
  DWORD Version;
  DWORD Size;
  STORAGE_PROTOCOL_SPECIFIC_DATA_EXT ProtocolSpecificData;
} STORAGE_PROTOCOL_DATA_DESCRIPTOR_EXT, *PSTORAGE_PROTOCOL_DATA_DESCRIPTOR_EXT;

#define STORAGE_TEMPERATURE_VALUE_NOT_REPORTED 0x8000

typedef struct _STORAGE_TEMPERATURE_INFO {
  WORD Index;
  SHORT Temperature;
  SHORT OverThreshold;
  SHORT UnderThreshold;
  BOOLEAN OverThresholdChangable;
  BOOLEAN UnderThresholdChangable;
  BOOLEAN EventGenerated;
  BYTE Reserved0;
  DWORD Reserved1;
} STORAGE_TEMPERATURE_INFO, *PSTORAGE_TEMPERATURE_INFO;

typedef struct _STORAGE_TEMPERATURE_DATA_DESCRIPTOR {
  DWORD Version;
  DWORD Size;
  SHORT CriticalTemperature;
  SHORT WarningTemperature;
  WORD InfoCount;
  BYTE Reserved0[2];
  DWORD Reserved1[2];
  STORAGE_TEMPERATURE_INFO TemperatureInfo[ANYSIZE_ARRAY];
} STORAGE_TEMPERATURE_DATA_DESCRIPTOR, *PSTORAGE_TEMPERATURE_DATA_DESCRIPTOR;

#define STORAGE_TEMPERATURE_THRESHOLD_FLAG_ADAPTER_REQUEST 0x0001

typedef struct _STORAGE_TEMPERATURE_THRESHOLD {
  DWORD Version;
  DWORD Size;
  WORD Flags;
  WORD Index;
  SHORT Threshold;
  BOOLEAN OverThreshold;
  BYTE Reserved;
} STORAGE_TEMPERATURE_THRESHOLD, *PSTORAGE_TEMPERATURE_THRESHOLD;

#define STORAGE_PROTOCOL_STRUCTURE_VERSION 0x1

typedef struct _STORAGE_PROTOCOL_COMMAND {
  DWORD Version;
  DWORD Length;
  STORAGE_PROTOCOL_TYPE ProtocolType;
  DWORD Flags;
  DWORD ReturnStatus;
  DWORD ErrorCode;
  DWORD CommandLength;
  DWORD ErrorInfoLength;
  DWORD DataToDeviceTransferLength;
  DWORD DataFromDeviceTransferLength;
  DWORD TimeOutValue;
  DWORD ErrorInfoOffset;
  DWORD DataToDeviceBufferOffset;
  DWORD DataFromDeviceBufferOffset;
  DWORD CommandSpecific;
  DWORD Reserved0;
  DWORD FixedProtocolReturnData;
  DWORD Reserved1[3];
  BYTE Command[ANYSIZE_ARRAY];
} STORAGE_PROTOCOL_COMMAND, *PSTORAGE_PROTOCOL_COMMAND;

#define STORAGE_PROTOCOL_COMMAND_FLAG_ADAPTER_REQUEST 0x80000000

#define STORAGE_PROTOCOL_STATUS_PENDING 0x0
#define STORAGE_PROTOCOL_STATUS_SUCCESS 0x1
#define STORAGE_PROTOCOL_STATUS_ERROR 0x2
#define STORAGE_PROTOCOL_STATUS_INVALID_REQUEST 0x3
#define STORAGE_PROTOCOL_STATUS_NO_DEVICE 0x4
#define STORAGE_PROTOCOL_STATUS_BUSY 0x5
#define STORAGE_PROTOCOL_STATUS_DATA_OVERRUN 0x6
#define STORAGE_PROTOCOL_STATUS_INSUFFICIENT_RESOURCES 0x7
#define STORAGE_PROTOCOL_STATUS_THROTTLED_REQUEST 0x8
#define STORAGE_PROTOCOL_STATUS_NOT_SUPPORTED 0xFF

#define STORAGE_PROTOCOL_COMMAND_LENGTH_NVME 0x40

#define STORAGE_PROTOCOL_SPECIFIC_NVME_ADMIN_COMMAND 0x01
#define STORAGE_PROTOCOL_SPECIFIC_NVME_NVM_COMMAND 0x02

#if (_WIN32_WINNT >= 0x0601)
typedef struct _REQUEST_OPLOCK_INPUT_BUFFER {
  WORD  StructureVersion;
  WORD  StructureLength;
  DWORD RequestedOplockLevel;
  DWORD Flags;
} REQUEST_OPLOCK_INPUT_BUFFER, *PREQUEST_OPLOCK_INPUT_BUFFER;

typedef struct _REQUEST_OPLOCK_OUTPUT_BUFFER {
  WORD        StructureVersion;
  WORD        StructureLength;
  DWORD       OriginalOplockLevel;
  DWORD       NewOplockLevel;
  DWORD       Flags;
  ACCESS_MASK AccessMode;
  WORD        ShareMode;
} REQUEST_OPLOCK_OUTPUT_BUFFER, *PREQUEST_OPLOCK_OUTPUT_BUFFER;

#define PERSISTENT_VOLUME_STATE_SHORT_NAME_CREATION_DISABLED        (0x00000001)

typedef struct _BOOT_AREA_INFO {
  ULONG BootSectorCount;
  struct {
    LARGE_INTEGER Offset;
  } BootSectors[2];
} BOOT_AREA_INFO, *PBOOT_AREA_INFO;

typedef struct _RETRIEVAL_POINTER_BASE {
  LARGE_INTEGER FileAreaOffset;
} RETRIEVAL_POINTER_BASE, *PRETRIEVAL_POINTER_BASE;

typedef struct _FILE_FS_PERSISTENT_VOLUME_INFORMATION {
  ULONG VolumeFlags;
  ULONG FlagMask;
  ULONG Version;
  ULONG Reserved;
} FILE_FS_PERSISTENT_VOLUME_INFORMATION, *PFILE_FS_PERSISTENT_VOLUME_INFORMATION;

typedef struct _FILE_SYSTEM_RECOGNITION_INFORMATION {
  CHAR FileSystem[9];
} FILE_SYSTEM_RECOGNITION_INFORMATION, *PFILE_SYSTEM_RECOGNITION_INFORMATION;

typedef struct _FILE_SYSTEM_RECOGNITION_STRUCTURE {
  UCHAR  Jmp[3];
  UCHAR  FsName[8];
  UCHAR  MustBeZero[5];
  ULONG  Identifier;
  USHORT Length;
  USHORT Checksum;
} FILE_SYSTEM_RECOGNITION_STRUCTURE;

#define OPLOCK_LEVEL_CACHE_READ         (0x00000001)
#define OPLOCK_LEVEL_CACHE_HANDLE       (0x00000002)
#define OPLOCK_LEVEL_CACHE_WRITE        (0x00000004)

#define REQUEST_OPLOCK_INPUT_FLAG_REQUEST               (0x00000001)
#define REQUEST_OPLOCK_INPUT_FLAG_ACK                   (0x00000002)
#define REQUEST_OPLOCK_INPUT_FLAG_COMPLETE_ACK_ON_CLOSE (0x00000004)

#define REQUEST_OPLOCK_CURRENT_VERSION          1

#define REQUEST_OPLOCK_OUTPUT_FLAG_ACK_REQUIRED     (0x00000001)
#define REQUEST_OPLOCK_OUTPUT_FLAG_MODES_PROVIDED   (0x00000002)

#define SD_GLOBAL_CHANGE_TYPE_MACHINE_SID   1

typedef struct _SD_CHANGE_MACHINE_SID_INPUT {
  USHORT CurrentMachineSIDOffset;
  USHORT CurrentMachineSIDLength;
  USHORT NewMachineSIDOffset;
  USHORT NewMachineSIDLength;
} SD_CHANGE_MACHINE_SID_INPUT, *PSD_CHANGE_MACHINE_SID_INPUT;

typedef struct _SD_CHANGE_MACHINE_SID_OUTPUT {
  ULONGLONG NumSDChangedSuccess;
  ULONGLONG NumSDChangedFail;
  ULONGLONG NumSDUnused;
  ULONGLONG NumSDTotal;
  ULONGLONG NumMftSDChangedSuccess;
  ULONGLONG NumMftSDChangedFail;
  ULONGLONG NumMftSDTotal;
} SD_CHANGE_MACHINE_SID_OUTPUT, *PSD_CHANGE_MACHINE_SID_OUTPUT;

#define ENCRYPTED_DATA_INFO_SPARSE_FILE    1

typedef struct _EXTENDED_ENCRYPTED_DATA_INFO {
  ULONG ExtendedCode;
  ULONG Length;
  ULONG Flags;
  ULONG Reserved;
} EXTENDED_ENCRYPTED_DATA_INFO, *PEXTENDED_ENCRYPTED_DATA_INFO;

typedef struct _LOOKUP_STREAM_FROM_CLUSTER_INPUT {
  DWORD         Flags;
  DWORD         NumberOfClusters;
  LARGE_INTEGER Cluster[1];
} LOOKUP_STREAM_FROM_CLUSTER_INPUT, *PLOOKUP_STREAM_FROM_CLUSTER_INPUT;

typedef struct _LOOKUP_STREAM_FROM_CLUSTER_OUTPUT {
  DWORD Offset;
  DWORD NumberOfMatches;
  DWORD BufferSizeRequired;
} LOOKUP_STREAM_FROM_CLUSTER_OUTPUT, *PLOOKUP_STREAM_FROM_CLUSTER_OUTPUT;

typedef struct _LOOKUP_STREAM_FROM_CLUSTER_ENTRY {
  DWORD         OffsetToNext;
  DWORD         Flags;
  LARGE_INTEGER Reserved;
  LARGE_INTEGER Cluster;
  WCHAR         FileName[1];
} LOOKUP_STREAM_FROM_CLUSTER_ENTRY, *PLOOKUP_STREAM_FROM_CLUSTER_ENTRY;

#define LOOKUP_STREAM_FROM_CLUSTER_ENTRY_ATTRIBUTE_MASK 0xff000000
#define LOOKUP_STREAM_FROM_CLUSTER_ENTRY_FLAG_PAGE_FILE 0x00000001
#define LOOKUP_STREAM_FROM_CLUSTER_ENTRY_FLAG_DENY_DEFRAG_SET 0x00000002
#define LOOKUP_STREAM_FROM_CLUSTER_ENTRY_FLAG_FS_SYSTEM_FILE 0x00000004
#define LOOKUP_STREAM_FROM_CLUSTER_ENTRY_FLAG_TXF_SYSTEM_FILE 0x00000008
#define LOOKUP_STREAM_FROM_CLUSTER_ENTRY_ATTRIBUTE_DATA 0x01000000
#define LOOKUP_STREAM_FROM_CLUSTER_ENTRY_ATTRIBUTE_INDEX 0x02000000
#define LOOKUP_STREAM_FROM_CLUSTER_ENTRY_ATTRIBUTE_SYSTEM 0x03000000

typedef struct _FILE_TYPE_NOTIFICATION_INPUT {
  ULONG Flags;
  ULONG NumFileTypeIDs;
  GUID FileTypeID[1];
} FILE_TYPE_NOTIFICATION_INPUT, *PFILE_TYPE_NOTIFICATION_INPUT;

#define FILE_TYPE_NOTIFICATION_FLAG_USAGE_BEGIN     0x00000001
#define FILE_TYPE_NOTIFICATION_FLAG_USAGE_END       0x00000002

#define FSCTL_QUERY_DEPENDENT_VOLUME CTL_CODE(FILE_DEVICE_FILE_SYSTEM,124, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SD_GLOBAL_CHANGE CTL_CODE(FILE_DEVICE_FILE_SYSTEM,125, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_LOOKUP_STREAM_FROM_CLUSTER CTL_CODE(FILE_DEVICE_FILE_SYSTEM,127, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_TXFS_WRITE_BACKUP_INFORMATION2 CTL_CODE(FILE_DEVICE_FILE_SYSTEM,128, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_FILE_TYPE_NOTIFICATION CTL_CODE(FILE_DEVICE_FILE_SYSTEM,129, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_GET_BOOT_AREA_INFO CTL_CODE(FILE_DEVICE_FILE_SYSTEM,140, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_GET_RETRIEVAL_POINTER_BASE CTL_CODE(FILE_DEVICE_FILE_SYSTEM,141, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SET_PERSISTENT_VOLUME_STATE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 142, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_QUERY_PERSISTENT_VOLUME_STATE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 143, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_REQUEST_OPLOCK CTL_CODE(FILE_DEVICE_FILE_SYSTEM,144,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define FSCTL_CSV_TUNNEL_REQUEST CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 145, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_IS_CSV_FILE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 146, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_QUERY_FILE_SYSTEM_RECOGNITION CTL_CODE(FILE_DEVICE_FILE_SYSTEM,147, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_CSV_GET_VOLUME_PATH_NAME CTL_CODE(FILE_DEVICE_FILE_SYSTEM,148, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_CSV_GET_VOLUME_NAME_FOR_VOLUME_MOUNT_POINT CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 149, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_CSV_GET_VOLUME_PATH_NAMES_FOR_VOLUME_NAME CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 150, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_IS_FILE_ON_CSV_VOLUME CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 151, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_CSV_INTERNAL CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 155, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SET_EXTERNAL_BACKING CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 195, METHOD_BUFFERED, FILE_SPECIAL_ACCESS)
#define FSCTL_GET_EXTERNAL_BACKING CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 196, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_DELETE_EXTERNAL_BACKING CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 197, METHOD_BUFFERED, FILE_SPECIAL_ACCESS)
#define FSCTL_ENUM_EXTERNAL_BACKING CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 198, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_ENUM_OVERLAY CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 199, METHOD_NEITHER, FILE_ANY_ACCESS)
#define FSCTL_ADD_OVERLAY CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 204, METHOD_BUFFERED, FILE_WRITE_DATA)
#define FSCTL_REMOVE_OVERLAY CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 205, METHOD_BUFFERED, FILE_WRITE_DATA)
#define FSCTL_UPDATE_OVERLAY CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 206, METHOD_BUFFERED, FILE_WRITE_DATA)
#define FSCTL_GET_WOF_VERSION CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 218, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SUSPEND_OVERLAY CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 225, METHOD_BUFFERED, FILE_ANY_ACCESS)

typedef struct _CSV_NAMESPACE_INFO {
  ULONG Version;
  ULONG DeviceNumber;
  LARGE_INTEGER StartingOffset;
  ULONG SectorSize;
} CSV_NAMESPACE_INFO, *PCSV_NAMESPACE_INFO;

#define CSV_NAMESPACE_INFO_V1 (sizeof(CSV_NAMESPACE_INFO))
#define CSV_INVALID_DEVICE_NUMBER 0xFFFFFFFF

#endif /*(_WIN32_WINNT >= 0x0601)*/

#if (_WIN32_WINNT >= _WIN32_WINNT_WIN8)
#define FSCTL_FILE_LEVEL_TRIM CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 130, METHOD_BUFFERED, FILE_WRITE_DATA)
#define FSCTL_CORRUPTION_HANDLING CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 152, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_OFFLOAD_READ CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 153, METHOD_BUFFERED, FILE_READ_ACCESS)
#define FSCTL_OFFLOAD_WRITE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 154, METHOD_BUFFERED, FILE_WRITE_ACCESS)
#define FSCTL_SET_PURGE_FAILURE_MODE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 156, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_QUERY_FILE_LAYOUT CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 157, METHOD_NEITHER, FILE_ANY_ACCESS)
#define FSCTL_IS_VOLUME_OWNED_BYCSVFS CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 158, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_GET_INTEGRITY_INFORMATION CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 159, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SET_INTEGRITY_INFORMATION CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 160, METHOD_BUFFERED, FILE_READ_DATA | FILE_WRITE_DATA)
#define FSCTL_QUERY_FILE_REGIONS CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 161, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_RKF_INTERNAL CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 171, METHOD_NEITHER, FILE_ANY_ACCESS)
#define FSCTL_SCRUB_DATA CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 172, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_REPAIR_COPIES CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 173, METHOD_BUFFERED, FILE_READ_DATA | FILE_WRITE_DATA)
#define FSCTL_DISABLE_LOCAL_BUFFERING CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 174, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_CSV_MGMT_LOCK CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 175, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_CSV_QUERY_DOWN_LEVEL_FILE_SYSTEM_CHARACTERISTICS CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 176, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_ADVANCE_FILE_ID CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 177, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_CSV_SYNC_TUNNEL_REQUEST CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 178, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_CSV_QUERY_VETO_FILE_DIRECT_IO CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 179, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_WRITE_USN_REASON CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 180, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_CSV_CONTROL CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 181, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_GET_REFS_VOLUME_DATA CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 182, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_CSV_H_BREAKING_SYNC_TUNNEL_REQUEST CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 185, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SHUFFLE_FILE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 208, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#endif /*_WIN32_WINNT >= _WIN32_WINNT_WIN8 */

#if (_WIN32_WINNT >= _WIN32_WINNT_WINBLUE)
#define FSCTL_QUERY_STORAGE_CLASSES CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 187, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_QUERY_REGION_INFO CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 188, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_USN_TRACK_MODIFIED_RANGES CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 189, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_QUERY_SHARED_VIRTUAL_DISK_SUPPORT CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 192, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SVHDX_SYNC_TUNNEL_REQUEST CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 193, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SVHDX_SET_INITIATOR_INFORMATION CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 194, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_DUPLICATE_EXTENTS_TO_FILE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 209, METHOD_BUFFERED, FILE_WRITE_DATA)
#define FSCTL_SPARSE_OVERALLOCATE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 211, METHOD_BUFFERED, FILE_SPECIAL_ACCESS)
#define FSCTL_STORAGE_QOS_CONTROL CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 212, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SVHDX_ASYNC_TUNNEL_REQUEST CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 217, METHOD_BUFFERED, FILE_ANY_ACCESS)
#endif /* (_WIN32_WINNT >= _WIN32_WINNT_WINBLUE) */

#if (_WIN32_WINNT >= _WIN32_WINNT_WINTHRESHOLD)
#define FSCTL_INITIATE_FILE_METADATA_OPTIMIZATION CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 215, METHOD_BUFFERED, FILE_SPECIAL_ACCESS)
#define FSCTL_QUERY_FILE_METADATA_OPTIMIZATION CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 216, METHOD_BUFFERED, FILE_SPECIAL_ACCESS)
#define FSCTL_HCS_SYNC_TUNNEL_REQUEST CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 219, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_HCS_ASYNC_TUNNEL_REQUEST CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 220, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_QUERY_EXTENT_READ_CACHE_INFO CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 221, METHOD_NEITHER, FILE_ANY_ACCESS)
#define FSCTL_QUERY_REFS_VOLUME_COUNTER_INFO CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 222, METHOD_NEITHER, FILE_ANY_ACCESS)
#define FSCTL_CLEAN_VOLUME_METADATA CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 223, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SET_INTEGRITY_INFORMATION_EX CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 224, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_VIRTUAL_STORAGE_QUERY_PROPERTY CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 226, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_FILESYSTEM_GET_STATISTICS_EX CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 227, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_QUERY_VOLUME_CONTAINER_STATE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 228, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SET_LAYER_ROOT CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 229, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_HCS_SYNC_NO_WRITE_TUNNEL_REQUEST CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 238, METHOD_BUFFERED, FILE_ANY_ACCESS)
#endif /* (_WIN32_WINNT >= _WIN32_WINNT_WINTHRESHOLD) */

#if (_WIN32_WINNT >= _WIN32_WINNT_WIN10_TH2)
#define FSCTL_QUERY_DIRECT_ACCESS_EXTENTS CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 230, METHOD_NEITHER, FILE_ANY_ACCESS)
#define FSCTL_NOTIFY_STORAGE_SPACE_ALLOCATION CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 231, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SSDI_STORAGE_REQUEST CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 232, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_GHOST_FILE_EXTENTS CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 235, METHOD_BUFFERED, FILE_WRITE_ACCESS)
#define FSCTL_QUERY_GHOSTED_FILE_EXTENTS CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 236, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_UNMAP_SPACE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 237, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_QUERY_REFS_SMR_VOLUME_INFO CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 247, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SET_REFS_SMR_VOLUME_GC_PARAMETERS CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 248, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SET_REFS_FILE_STRICTLY_SEQUENTIAL CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 249, METHOD_BUFFERED, FILE_ANY_ACCESS)
#endif /* (_WIN32_WINNT >= _WIN32_WINNT_WIN10_TH2) */

#if (_WIN32_WINNT >= _WIN32_WINNT_WIN10_RS1)
#define FSCTL_QUERY_DIRECT_IMAGE_ORIGINAL_BASE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 233, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_READ_UNPRIVILEGED_USN_JOURNAL CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 234, METHOD_NEITHER, FILE_ANY_ACCESS)
#define FSCTL_START_VIRTUALIZATION_INSTANCE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 240, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_GET_FILTER_FILE_IDENTIFIER CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 241, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_START_VIRTUALIZATION_INSTANCE_EX CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 256, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SET_REPARSE_POINT_EX CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 259, METHOD_BUFFERED, FILE_SPECIAL_ACCESS)
#endif /* (_WIN32_WINNT >= _WIN32_WINNT_WIN10_RS1) */

#if (_WIN32_WINNT >= _WIN32_WINNT_WIN10_RS2)
#define FSCTL_STREAMS_QUERY_PARAMETERS CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 241, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_STREAMS_ASSOCIATE_ID CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 242, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_STREAMS_QUERY_ID CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 243, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_GET_RETRIEVAL_POINTERS_AND_REFCOUNT CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 244, METHOD_NEITHER, FILE_ANY_ACCESS)
#define FSCTL_QUERY_VOLUME_NUMA_INFO CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 245, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_REFS_DEALLOCATE_RANGES CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 246, METHOD_BUFFERED, FILE_ANY_ACCESS)
#endif /* (_WIN32_WINNT >= _WIN32_WINNT_WIN10_RS2) */

#if (_WIN32_WINNT >= _WIN32_WINNT_WIN10_RS3)
#define FSCTL_DUPLICATE_EXTENTS_TO_FILE_EX CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 250, METHOD_BUFFERED, FILE_WRITE_DATA)
#define FSCTL_QUERY_BAD_RANGES CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 251, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SET_DAX_ALLOC_ALIGNMENT_HINT CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 252, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_DELETE_CORRUPTED_REFS_CONTAINER CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 253, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_SCRUB_UNDISCOVERABLE_ID CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 254, METHOD_BUFFERED, FILE_ANY_ACCESS)
#endif /* (_WIN32_WINNT >= _WIN32_WINNT_WIN10_RS3) */

#if (_WIN32_WINNT >= _WIN32_WINNT_WIN10_RS4)
#define FSCTL_NOTIFY_DATA_CHANGE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 255, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_ENCRYPTION_KEY_CONTROL CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 257, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_VIRTUAL_STORAGE_SET_BEHAVIOR CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 258, METHOD_BUFFERED, FILE_ANY_ACCESS)
#endif /* (_WIN32_WINNT >= _WIN32_WINNT_WIN10_RS4) */

#if (_WIN32_WINNT >= _WIN32_WINNT_WIN10_RS5)
#define FSCTL_REARRANGE_FILE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 264, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define FSCTL_VIRTUAL_STORAGE_PASSTHROUGH CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 265, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_GET_RETRIEVAL_POINTER_COUNT CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 266, METHOD_NEITHER, FILE_ANY_ACCESS)
#if defined(_WIN64)
#define FSCTL_ENABLE_PER_IO_FLAGS CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 267, METHOD_BUFFERED, FILE_ANY_ACCESS)
#endif
#endif /* (_WIN32_WINNT >= _WIN32_WINNT_WIN10_RS5) */

#if (NTDDI_VERSION >= NTDDI_WIN10_RS5)
#define FSCTL_QUERY_ASYNC_DUPLICATE_EXTENTS_STATUS CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 268, METHOD_BUFFERED, FILE_ANY_ACCESS)
#endif
#if (NTDDI_VERSION >= NTDDI_WIN10_MN)
#define FSCTL_SMB_SHARE_FLUSH_AND_PURGE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 271, METHOD_BUFFERED, FILE_ANY_ACCESS)
#endif
#if (NTDDI_VERSION >= NTDDI_WIN10_FE)
#define FSCTL_REFS_STREAM_SNAPSHOT_MANAGEMENT CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 272, METHOD_BUFFERED, FILE_ANY_ACCESS)
#endif
#if (NTDDI_VERSION >= NTDDI_WIN10_CO)
#define FSCTL_MANAGE_BYPASS_IO CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 274, METHOD_BUFFERED, FILE_ANY_ACCESS)
#endif
#if (NTDDI_VERSION >= NTDDI_WIN10_FE)
#define FSCTL_REFS_DEALLOCATE_RANGES_EX CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 275, METHOD_BUFFERED, FILE_ANY_ACCESS)
#endif
#if (NTDDI_VERSION >= NTDDI_WIN10_FE)
#define FSCTL_SET_CACHED_RUNS_STATE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 276, METHOD_BUFFERED, FILE_ANY_ACCESS)
#endif
#if (NTDDI_VERSION >= NTDDI_WIN10_NI)
#define FSCTL_REFS_SET_VOLUME_COMPRESSION_INFO CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 277, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_REFS_QUERY_VOLUME_COMPRESSION_INFO CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 278, METHOD_BUFFERED, FILE_ANY_ACCESS)
#endif
#if (NTDDI_VERSION >= NTDDI_WIN10_NI)
#define FSCTL_DUPLICATE_CLUSTER CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 279, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_CREATE_LCN_WEAK_REFERENCE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 280, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_DELETE_LCN_WEAK_REFERENCE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 281, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_QUERY_LCN_WEAK_REFERENCE CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 282, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_DELETE_LCN_WEAK_REFERENCES CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 283, METHOD_BUFFERED, FILE_ANY_ACCESS)
#endif
#if (NTDDI_VERSION >= NTDDI_WIN10_NI)
#define FSCTL_REFS_SET_VOLUME_DEDUP_INFO CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 284, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define FSCTL_REFS_QUERY_VOLUME_DEDUP_INFO CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 285, METHOD_BUFFERED, FILE_ANY_ACCESS)
#endif
#if (NTDDI_VERSION >= NTDDI_WIN10_RS5)
#define FSCTL_LMR_QUERY_INFO CTL_CODE(FILE_DEVICE_FILE_SYSTEM, 286, METHOD_BUFFERED, FILE_ANY_ACCESS)
#endif

#if (_WIN32_WINNT >= _WIN32_WINNT_WINBLUE)
typedef struct _DUPLICATE_EXTENTS_DATA {
  HANDLE FileHandle;
  LARGE_INTEGER SourceFileOffset;
  LARGE_INTEGER TargetFileOffset;
  LARGE_INTEGER ByteCount;
} DUPLICATE_EXTENTS_DATA, *PDUPLICATE_EXTENTS_DATA;

#if ((NTDDI_VERSION >= NTDDI_WIN10_RS2) && defined(_WIN64))
typedef struct _DUPLICATE_EXTENTS_DATA32 {
  UINT32 FileHandle;
  LARGE_INTEGER SourceFileOffset;
  LARGE_INTEGER TargetFileOffset;
  LARGE_INTEGER ByteCount;
} DUPLICATE_EXTENTS_DATA32, *PDUPLICATE_EXTENTS_DATA32;
#endif /* ((NTDDI_VERSION >= NTDDI_WIN10_RS2) && defined(_WIN64)) */

#endif /* (_WIN32_WINNT >= _WIN32_WINNT_WINBLUE) */

#if (NTDDI_VERSION >= NTDDI_WIN10_RS3)
#define DUPLICATE_EXTENTS_DATA_EX_SOURCE_ATOMIC 0x00000001

#if (NTDDI_VERSION >= NTDDI_WIN10_VB)
#define DUPLICATE_EXTENTS_DATA_EX_ASYNC 0x00000002
#endif /* (NTDDI_VERSION >= NTDDI_WIN10_VB) */

typedef struct _DUPLICATE_EXTENTS_DATA_EX {
  SIZE_T Size;
  HANDLE FileHandle;
  LARGE_INTEGER SourceFileOffset;
  LARGE_INTEGER TargetFileOffset;
  LARGE_INTEGER ByteCount;
  DWORD Flags;
} DUPLICATE_EXTENTS_DATA_EX, *PDUPLICATE_EXTENTS_DATA_EX;

#if ((NTDDI_VERSION >= NTDDI_WIN10_RS3) && defined(_WIN64))
typedef struct _DUPLICATE_EXTENTS_DATA_EX32 {
  DWORD32 Size;
  DWORD32 FileHandle;
  LARGE_INTEGER SourceFileOffset;
  LARGE_INTEGER TargetFileOffset;
  LARGE_INTEGER ByteCount;
  DWORD Flags;
} DUPLICATE_EXTENTS_DATA_EX32, *PDUPLICATE_EXTENTS_DATA_EX32;
#endif /* ((NTDDI_VERSION >= NTDDI_WIN10_RS3) && defined(_WIN64)) */

#endif /* (NTDDI_VERSION >= NTDDI_WIN10_RS3) */

#endif /* _FILESYSTEMFSCTL_ */


#define IOCTL_VOLUME_BASE ((DWORD) 'V')

#define IOCTL_VOLUME_GET_VOLUME_DISK_EXTENTS CTL_CODE(IOCTL_VOLUME_BASE,0,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_VOLUME_SUPPORTS_ONLINE_OFFLINE CTL_CODE(IOCTL_VOLUME_BASE,1,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_VOLUME_ONLINE                  CTL_CODE(IOCTL_VOLUME_BASE,2,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_VOLUME_OFFLINE                 CTL_CODE(IOCTL_VOLUME_BASE,3,METHOD_BUFFERED,FILE_READ_ACCESS | FILE_WRITE_ACCESS)
#define IOCTL_VOLUME_IS_OFFLINE              CTL_CODE(IOCTL_VOLUME_BASE,4,METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VOLUME_IS_IO_CAPABLE           CTL_CODE(IOCTL_VOLUME_BASE,5,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_VOLUME_QUERY_FAILOVER_SET      CTL_CODE(IOCTL_VOLUME_BASE,6,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_VOLUME_QUERY_VOLUME_NUMBER     CTL_CODE(IOCTL_VOLUME_BASE,7,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_VOLUME_LOGICAL_TO_PHYSICAL     CTL_CODE(IOCTL_VOLUME_BASE,8,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_VOLUME_PHYSICAL_TO_LOGICAL     CTL_CODE(IOCTL_VOLUME_BASE,9,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_VOLUME_IS_CLUSTERED            CTL_CODE(IOCTL_VOLUME_BASE,12,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_VOLUME_GET_GPT_ATTRIBUTES      CTL_CODE(IOCTL_VOLUME_BASE,14,METHOD_BUFFERED,FILE_ANY_ACCESS)

typedef struct _DISK_EXTENT {
  DWORD DiskNumber;
  LARGE_INTEGER StartingOffset;
  LARGE_INTEGER ExtentLength;
} DISK_EXTENT,*PDISK_EXTENT;

typedef struct _VOLUME_DISK_EXTENTS {
  DWORD NumberOfDiskExtents;
  DISK_EXTENT Extents[1];
} VOLUME_DISK_EXTENTS,*PVOLUME_DISK_EXTENTS;

#if (_WIN32_WINNT >= 0x0603)

#define WOF_CURRENT_VERSION 1

typedef struct _WOF_EXTERNAL_INFO {
  DWORD Version;
  DWORD Provider;
} WOF_EXTERNAL_INFO, *PWOF_EXTERNAL_INFO;

#endif /*(_WIN32_WINNT >= 0x0603)*/

#if (_WIN32_WINNT >= 0x0A00)

#define WOF_PROVIDER_FILE 2

#define FILE_PROVIDER_CURRENT_VERSION 1

#define FILE_PROVIDER_COMPRESSION_XPRESS4K 0
#define FILE_PROVIDER_COMPRESSION_LZX 1
#define FILE_PROVIDER_COMPRESSION_XPRESS8K 2
#define FILE_PROVIDER_COMPRESSION_XPRESS16K 3

typedef struct _FILE_PROVIDER_EXTERNAL_INFO_V0 {
  DWORD Version;
  DWORD Algorithm;
} FILE_PROVIDER_EXTERNAL_INFO_V0, *PFILE_PROVIDER_EXTERNAL_INFO_V0;

typedef struct _FILE_PROVIDER_EXTERNAL_INFO_V1 {
  DWORD Version;
  DWORD Algorithm;
  DWORD Flags;
} FILE_PROVIDER_EXTERNAL_INFO_V1, *PFILE_PROVIDER_EXTERNAL_INFO_V1;

#endif /*(_WIN32_WINNT >= 0x0A00)*/

#endif /* _WINIOCTL_ */

