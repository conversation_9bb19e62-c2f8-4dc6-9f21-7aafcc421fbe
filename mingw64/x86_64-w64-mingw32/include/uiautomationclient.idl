/*
 * Copyright 2012 <PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#define DO_NO_IMPORTS
import "uiautomationcore.idl";

cpp_quote( "#ifndef _INC_UIAUTOMATIONCOREAPI" )

enum TreeScope {
    TreeScope_Element     = 0x01,
    TreeScope_Children    = 0x02,
    TreeScope_Descendants = 0x04,
    TreeScope_Parent      = 0x08,
    TreeScope_Ancestors   = 0x10,
    TreeScope_Subtree     = TreeScope_Element | TreeScope_Children | TreeScope_Descendants,
};

enum PropertyConditionFlags {
    PropertyConditionFlags_None       = 0x00,
    PropertyConditionFlags_IgnoreCase = 0x01,
};

enum AutomationElementMode {
    AutomationElementMode_None = 0x00,
    AutomationElementMode_Full = 0x01,
};

enum TreeTraversalOptions {
    TreeTraversalOptions_Default          = 0x00,
    TreeTraversalOptions_PostOrder        = 0x01,
    TreeTraversalOptions_LastToFirstOrder = 0x02,
};

enum ConnectionRecoveryBehaviorOptions {
    ConnectionRecoveryBehaviorOptions_Disabled = 0x00,
    ConnectionRecoveryBehaviorOptions_Enabled  = 0x01,
};

enum CoalesceEventsOptions {
    CoalesceEventsOptions_Disabled = 0x00,
    CoalesceEventsOptions_Enabled  = 0x01,
};

cpp_quote( "#endif" )

struct ExtendedProperty
{
    BSTR PropertyName;
    BSTR PropertyValue;
};

typedef void * UIA_HWND;

[
    uuid(944de083-8fb8-45cf-bcb7-c477acb2f897),
    lcid(0),
    id(2),
    version(1.0)
]
library UIAutomationClient {
    importlib ("stdole2.tlb");

    [dllname("<no entry points>")]
    module UIA_PatternIds
    {
        const long UIA_InvokePatternId         = 10000;
        const long UIA_SelectionPatternId      = 10001;
        const long UIA_ValuePatternId          = 10002;
        const long UIA_RangeValuePatternId     = 10003;
        const long UIA_ScrollPatternId         = 10004;
        const long UIA_ExpandCollapsePatternId = 10005;
        const long UIA_GridPatternId           = 10006;
        const long UIA_GridItemPatternId       = 10007;
        const long UIA_MultipleViewPatternId   = 10008;
        const long UIA_WindowPatternId         = 10009;
        const long UIA_SelectionItemPatternId  = 10010;
        const long UIA_DockPatternId           = 10011;
        const long UIA_TablePatternId          = 10012;
        const long UIA_TableItemPatternId      = 10013;
        const long UIA_TextPatternId           = 10014;
        const long UIA_TogglePatternId         = 10015;
        const long UIA_TransformPatternId      = 10016;
        const long UIA_ScrollItemPatternId     = 10017;
        const long UIA_LegacyIAccessiblePatternId = 10018;
        const long UIA_ItemContainerPatternId     = 10019;
        const long UIA_VirtualizedItemPatternId   = 10020;
        const long UIA_SynchronizedInputPatternId = 10021;
        const long UIA_ObjectModelPatternId = 10022;
        const long UIA_AnnotationPatternId  = 10023;
        const long UIA_TextPattern2Id       = 10024;
        const long UIA_StylesPatternId      = 10025;
        const long UIA_SpreadsheetPatternId = 10026;
        const long UIA_SpreadsheetItemPatternId  = 10027;
        const long UIA_TransformPattern2Id       = 10028;
        const long UIA_TextChildPatternId        = 10029;
        const long UIA_DragPatternId             = 10030;
        const long UIA_DropTargetPatternId       = 10031;
        const long UIA_TextEditPatternId         = 10032;
        const long UIA_CustomNavigationPatternId = 10033;
    }

    [dllname("<no entry points>")]
    module UIA_EventIds
    {
        const long UIA_ToolTipOpenedEventId = 20000;
        const long UIA_ToolTipClosedEventId = 20001;
        const long UIA_StructureChangedEventId = 20002;
        const long UIA_MenuOpenedEventId = 20003;
        const long UIA_AutomationPropertyChangedEventId = 20004;
        const long UIA_AutomationFocusChangedEventId = 20005;
        const long UIA_AsyncContentLoadedEventId = 20006;
        const long UIA_MenuClosedEventId = 20007;
        const long UIA_LayoutInvalidatedEventId = 20008;
        const long UIA_Invoke_InvokedEventId = 20009;
        const long UIA_SelectionItem_ElementAddedToSelectionEventId = 20010;
        const long UIA_SelectionItem_ElementRemovedFromSelectionEventId = 20011;
        const long UIA_SelectionItem_ElementSelectedEventId= 20012;
        const long UIA_Selection_InvalidatedEventId = 20013;
        const long UIA_Text_TextSelectionChangedEventId = 20014;
        const long UIA_Text_TextChangedEventId = 20015;
        const long UIA_Window_WindowOpenedEventId = 20016;
        const long UIA_Window_WindowClosedEventId = 20017;
        const long UIA_MenuModeStartEventId = 20018;
        const long UIA_MenuModeEndEventId = 20019;
        const long UIA_InputReachedTargetEventId = 20020;
        const long UIA_InputReachedOtherElementEventId = 20021;
        const long UIA_InputDiscardedEventId = 20022;
        const long UIA_SystemAlertEventId = 20023;
        const long UIA_LiveRegionChangedEventId = 20024;
        const long UIA_HostedFragmentRootsInvalidatedEventId = 20025;
        const long UIA_Drag_DragStartEventId = 20026;
        const long UIA_Drag_DragCancelEventId = 20027;
        const long UIA_Drag_DragCompleteEventId = 20028;
        const long UIA_DropTarget_DragEnterEventId = 20029;
        const long UIA_DropTarget_DragLeaveEventId = 20030;
        const long UIA_DropTarget_DroppedEventId = 20031;
        const long UIA_TextEdit_TextChangedEventId = 20032;
        const long UIA_TextEdit_ConversionTargetChangedEventId = 20033;
        const long UIA_ChangesEventId = 20034;
        const long UIA_NotificationEventId = 20035;
    }

    [dllname("<no entry points>")]
    module UIA_PropertyIds
    {
        const long UIA_RuntimeIdPropertyId              = 30000;
        const long UIA_BoundingRectanglePropertyId      = 30001;
        const long UIA_ProcessIdPropertyId              = 30002;
        const long UIA_ControlTypePropertyId            = 30003;
        const long UIA_LocalizedControlTypePropertyId   = 30004;
        const long UIA_NamePropertyId                   = 30005;
        const long UIA_AcceleratorKeyPropertyId         = 30006;
        const long UIA_AccessKeyPropertyId              = 30007;
        const long UIA_HasKeyboardFocusPropertyId       = 30008;
        const long UIA_IsKeyboardFocusablePropertyId    = 30009;
        const long UIA_IsEnabledPropertyId              = 30010;
        const long UIA_AutomationIdPropertyId           = 30011;
        const long UIA_ClassNamePropertyId              = 30012;
        const long UIA_HelpTextPropertyId               = 30013;
        const long UIA_ClickablePointPropertyId         = 30014;
        const long UIA_CulturePropertyId                = 30015;
        const long UIA_IsControlElementPropertyId       = 30016;
        const long UIA_IsContentElementPropertyId       = 30017;
        const long UIA_LabeledByPropertyId              = 30018;
        const long UIA_IsPasswordPropertyId             = 30019;
        const long UIA_NativeWindowHandlePropertyId     = 30020;
        const long UIA_ItemTypePropertyId               = 30021;
        const long UIA_IsOffscreenPropertyId            = 30022;
        const long UIA_OrientationPropertyId            = 30023;
        const long UIA_FrameworkIdPropertyId            = 30024;
        const long UIA_IsRequiredForFormPropertyId      = 30025;
        const long UIA_ItemStatusPropertyId             = 30026;
        const long UIA_IsDockPatternAvailablePropertyId = 30027;
        const long UIA_IsExpandCollapsePatternAvailablePropertyId = 30028;
        const long UIA_IsGridItemPatternAvailablePropertyId       = 30029;
        const long UIA_IsGridPatternAvailablePropertyId           = 30030;
        const long UIA_IsInvokePatternAvailablePropertyId         = 30031;
        const long UIA_IsMultipleViewPatternAvailablePropertyId   = 30032;
        const long UIA_IsRangeValuePatternAvailablePropertyId     = 30033;
        const long UIA_IsScrollPatternAvailablePropertyId         = 30034;
        const long UIA_IsScrollItemPatternAvailablePropertyId     = 30035;
        const long UIA_IsSelectionItemPatternAvailablePropertyId  = 30036;
        const long UIA_IsSelectionPatternAvailablePropertyId      = 30037;
        const long UIA_IsTablePatternAvailablePropertyId          = 30038;
        const long UIA_IsTableItemPatternAvailablePropertyId      = 30039;
        const long UIA_IsTextPatternAvailablePropertyId           = 30040;
        const long UIA_IsTogglePatternAvailablePropertyId         = 30041;
        const long UIA_IsTransformPatternAvailablePropertyId      = 30042;
        const long UIA_IsValuePatternAvailablePropertyId          = 30043;
        const long UIA_IsWindowPatternAvailablePropertyId         = 30044;
        const long UIA_ValueValuePropertyId             = 30045;
        const long UIA_ValueIsReadOnlyPropertyId        = 30046;
        const long UIA_RangeValueValuePropertyId        = 30047;
        const long UIA_RangeValueIsReadOnlyPropertyId   = 30048;
        const long UIA_RangeValueMinimumPropertyId      = 30049;
        const long UIA_RangeValueMaximumPropertyId      = 30050;
        const long UIA_RangeValueLargeChangePropertyId  = 30051;
        const long UIA_RangeValueSmallChangePropertyId  = 30052;
        const long UIA_ScrollHorizontalScrollPercentPropertyId    = 30053;
        const long UIA_ScrollHorizontalViewSizePropertyId         = 30054;
        const long UIA_ScrollVerticalScrollPercentPropertyId      = 30055;
        const long UIA_ScrollVerticalViewSizePropertyId           = 30056;
        const long UIA_ScrollHorizontallyScrollablePropertyId     = 30057;
        const long UIA_ScrollVerticallyScrollablePropertyId       = 30058;
        const long UIA_SelectionSelectionPropertyId               = 30059;
        const long UIA_SelectionCanSelectMultiplePropertyId       = 30060;
        const long UIA_SelectionIsSelectionRequiredPropertyId     = 30061;
        const long UIA_GridRowCountPropertyId           = 30062;
        const long UIA_GridColumnCountPropertyId        = 30063;
        const long UIA_GridItemRowPropertyId            = 30064;
        const long UIA_GridItemColumnPropertyId         = 30065;
        const long UIA_GridItemRowSpanPropertyId        = 30066;
        const long UIA_GridItemColumnSpanPropertyId     = 30067;
        const long UIA_GridItemContainingGridPropertyId = 30068;
        const long UIA_DockDockPositionPropertyId       = 30069;
        const long UIA_ExpandCollapseExpandCollapseStatePropertyId = 30070;
        const long UIA_MultipleViewCurrentViewPropertyId           = 30071;
        const long UIA_MultipleViewSupportedViewsPropertyId        = 30072;
        const long UIA_WindowCanMaximizePropertyId                 = 30073;
        const long UIA_WindowCanMinimizePropertyId                 = 30074;
        const long UIA_WindowWindowVisualStatePropertyId           = 30075;
        const long UIA_WindowWindowInteractionStatePropertyId      = 30076;
        const long UIA_WindowIsModalPropertyId                     = 30077;
        const long UIA_WindowIsTopmostPropertyId                   = 30078;
        const long UIA_SelectionItemIsSelectedPropertyId           = 30079;
        const long UIA_SelectionItemSelectionContainerPropertyId   = 30080;
        const long UIA_TableRowHeadersPropertyId                   = 30081;
        const long UIA_TableColumnHeadersPropertyId                = 30082;
        const long UIA_TableRowOrColumnMajorPropertyId             = 30083;
        const long UIA_TableItemRowHeaderItemsPropertyId           = 30084;
        const long UIA_TableItemColumnHeaderItemsPropertyId        = 30085;
        const long UIA_ToggleToggleStatePropertyId                 = 30086;
        const long UIA_TransformCanMovePropertyId                  = 30087;
        const long UIA_TransformCanResizePropertyId                = 30088;
        const long UIA_TransformCanRotatePropertyId                = 30089;
        const long UIA_IsLegacyIAccessiblePatternAvailablePropertyId  = 30090;
        const long UIA_LegacyIAccessibleChildIdPropertyId          = 30091;
        const long UIA_LegacyIAccessibleNamePropertyId             = 30092;
        const long UIA_LegacyIAccessibleValuePropertyId            = 30093;
        const long UIA_LegacyIAccessibleDescriptionPropertyId      = 30094;
        const long UIA_LegacyIAccessibleRolePropertyId             = 30095;
        const long UIA_LegacyIAccessibleStatePropertyId            = 30096;
        const long UIA_LegacyIAccessibleHelpPropertyId             = 30097;
        const long UIA_LegacyIAccessibleKeyboardShortcutPropertyId = 30098;
        const long UIA_LegacyIAccessibleSelectionPropertyId        = 30099;
        const long UIA_LegacyIAccessibleDefaultActionPropertyId    = 30100;
        const long UIA_AriaRolePropertyId                          = 30101;
        const long UIA_AriaPropertiesPropertyId                    = 30102;
        const long UIA_IsDataValidForFormPropertyId                = 30103;
        const long UIA_ControllerForPropertyId                     = 30104;
        const long UIA_DescribedByPropertyId                       = 30105;
        const long UIA_FlowsToPropertyId                           = 30106;
        const long UIA_ProviderDescriptionPropertyId               = 30107;
        const long UIA_IsItemContainerPatternAvailablePropertyId   = 30108;
        const long UIA_IsVirtualizedItemPatternAvailablePropertyId = 30109;
        const long UIA_IsSynchronizedInputPatternAvailablePropertyId  = 30110;
        const long UIA_OptimizeForVisualContentPropertyId      = 30111;
        const long UIA_IsObjectModelPatternAvailablePropertyId = 30112;
        const long UIA_AnnotationAnnotationTypeIdPropertyId    = 30113;
        const long UIA_AnnotationAnnotationTypeNamePropertyId  = 30114;
        const long UIA_AnnotationAuthorPropertyId              = 30115;
        const long UIA_AnnotationDateTimePropertyId            = 30116;
        const long UIA_AnnotationTargetPropertyId              = 30117;
        const long UIA_IsAnnotationPatternAvailablePropertyId  = 30118;
        const long UIA_IsTextPattern2AvailablePropertyId       = 30119;
        const long UIA_StylesStyleIdPropertyId                 = 30120;
        const long UIA_StylesStyleNamePropertyId               = 30121;
        const long UIA_StylesFillColorPropertyId               = 30122;
        const long UIA_StylesFillPatternStylePropertyId        = 30123;
        const long UIA_StylesShapePropertyId                   = 30124;
        const long UIA_StylesFillPatternColorPropertyId        = 30125;
        const long UIA_StylesExtendedPropertiesPropertyId      = 30126;
        const long UIA_IsStylesPatternAvailablePropertyId      = 30127;
        const long UIA_IsSpreadsheetPatternAvailablePropertyId     = 30128;
        const long UIA_SpreadsheetItemFormulaPropertyId            = 30129;
        const long UIA_SpreadsheetItemAnnotationObjectsPropertyId  = 30130;
        const long UIA_SpreadsheetItemAnnotationTypesPropertyId    = 30131;
        const long UIA_IsSpreadsheetItemPatternAvailablePropertyId = 30132;
        const long UIA_Transform2CanZoomPropertyId                 = 30133;
        const long UIA_IsTransformPattern2AvailablePropertyId      = 30134;
        const long UIA_LiveSettingPropertyId                       = 30135;
        const long UIA_IsTextChildPatternAvailablePropertyId       = 30136;
        const long UIA_IsDragPatternAvailablePropertyId            = 30137;
        const long UIA_DragIsGrabbedPropertyId                     = 30138;
        const long UIA_DragDropEffectPropertyId                    = 30139;
        const long UIA_DragDropEffectsPropertyId                   = 30140;
        const long UIA_IsDropTargetPatternAvailablePropertyId      = 30141;
        const long UIA_DropTargetDropTargetEffectPropertyId        = 30142;
        const long UIA_DropTargetDropTargetEffectsPropertyId       = 30143;
        const long UIA_DragGrabbedItemsPropertyId                  = 30144;
        const long UIA_Transform2ZoomLevelPropertyId               = 30145;
        const long UIA_Transform2ZoomMinimumPropertyId             = 30146;
        const long UIA_Transform2ZoomMaximumPropertyId             = 30147;
        const long UIA_FlowsFromPropertyId                         = 30148;
        const long UIA_IsTextEditPatternAvailablePropertyId        = 30149;
        const long UIA_IsPeripheralPropertyId                      = 30150;
        const long UIA_IsCustomNavigationPatternAvailablePropertyId = 30151;
        const long UIA_PositionInSetPropertyId         = 30152;
        const long UIA_SizeOfSetPropertyId             = 30153;
        const long UIA_LevelPropertyId                 = 30154;
        const long UIA_AnnotationTypesPropertyId       = 30155;
        const long UIA_AnnotationObjectsPropertyId     = 30156;
        const long UIA_LandmarkTypePropertyId          = 30157;
        const long UIA_LocalizedLandmarkTypePropertyId = 30158;
        const long UIA_FullDescriptionPropertyId       = 30159;
        const long UIA_FillColorPropertyId             = 30160;
        const long UIA_OutlineColorPropertyId          = 30161;
        const long UIA_FillTypePropertyId              = 30162;
        const long UIA_VisualEffectsPropertyId         = 30163;
        const long UIA_OutlineThicknessPropertyId      = 30164;
        const long UIA_CenterPointPropertyId           = 30165;
        const long UIA_RotationPropertyId              = 30166;
        const long UIA_SizePropertyId                  = 30167;
        const long UIA_IsSelectionPattern2AvailablePropertyId  = 30168;
        const long UIA_Selection2FirstSelectedItemPropertyId   = 30169;
        const long UIA_Selection2LastSelectedItemPropertyId    = 30170;
        const long UIA_Selection2CurrentSelectedItemPropertyId = 30171;
        const long UIA_Selection2ItemCountPropertyId           = 30172;
        const long UIA_HeadingLevelPropertyId                  = 30173;
        const long UIA_IsDialogPropertyId                      = 30174;
    }

    [dllname("<no entry points>")]
    module UIA_TextAttributeIds
    {
        const long UIA_AnimationStyleAttributeId = 40000;
        const long UIA_BackgroundColorAttributeId = 40001;
        const long UIA_BulletStyleAttributeId = 40002;
        const long UIA_CapStyleAttributeId = 40003;
        const long UIA_CultureAttributeId = 40004;
        const long UIA_FontNameAttributeId = 40005;
        const long UIA_FontSizeAttributeId = 40006;
        const long UIA_FontWeightAttributeId = 40007;
        const long UIA_ForegroundColorAttributeId = 40008;
        const long UIA_HorizontalTextAlignmentAttributeId = 40009;
        const long UIA_IndentationFirstLineAttributeId = 40010;
        const long UIA_IndentationLeadingAttributeId = 40011;
        const long UIA_IndentationTrailingAttributeId = 40012;
        const long UIA_IsHiddenAttributeId = 40013;
        const long UIA_IsItalicAttributeId = 40014;
        const long UIA_IsReadOnlyAttributeId = 40015;
        const long UIA_IsSubscriptAttributeId = 40016;
        const long UIA_IsSuperscriptAttributeId = 40017;
        const long UIA_MarginBottomAttributeId = 40018;
        const long UIA_MarginLeadingAttributeId = 40019;
        const long UIA_MarginTopAttributeId = 40020;
        const long UIA_MarginTrailingAttributeId = 40021;
        const long UIA_OutlineStylesAttributeId = 40022;
        const long UIA_OverlineColorAttributeId = 40023;
        const long UIA_OverlineStyleAttributeId = 40024;
        const long UIA_StrikethroughColorAttributeId = 40025;
        const long UIA_StrikethroughStyleAttributeId = 40026;
        const long UIA_TabsAttributeId = 40027;
        const long UIA_TextFlowDirectionsAttributeId = 40028;
        const long UIA_UnderlineColorAttributeId = 40029;
        const long UIA_UnderlineStyleAttributeId = 40030;
        const long UIA_AnnotationTypesAttributeId = 40031;
        const long UIA_AnnotationObjectsAttributeId = 40032;
        const long UIA_StyleNameAttributeId = 40033;
        const long UIA_StyleIdAttributeId = 40034;
        const long UIA_LinkAttributeId = 40035;
        const long UIA_IsActiveAttributeId = 40036;
        const long UIA_SelectionActiveEndAttributeId = 40037;
        const long UIA_CaretPositionAttributeId = 40038;
        const long UIA_CaretBidiModeAttributeId = 40039;
        const long UIA_LineSpacingAttributeId = 40040;
        const long UIA_BeforeParagraphSpacingAttributeId = 40041;
        const long UIA_AfterParagraphSpacingAttributeId = 40042;
        const long UIA_SayAsInterpretAsAttributeId = 40043;
    }

    [dllname("<no entry points>")]
    module UIA_ControlTypeIds
    {
        const long UIA_ButtonControlTypeId = 50000;
        const long UIA_CalendarControlTypeId = 50001;
        const long UIA_CheckBoxControlTypeId = 50002;
        const long UIA_ComboBoxControlTypeId = 50003;
        const long UIA_EditControlTypeId = 50004;
        const long UIA_HyperlinkControlTypeId = 50005;
        const long UIA_ImageControlTypeId = 50006;
        const long UIA_ListItemControlTypeId = 50007;
        const long UIA_ListControlTypeId = 50008;
        const long UIA_MenuControlTypeId = 50009;
        const long UIA_MenuBarControlTypeId = 50010;
        const long UIA_MenuItemControlTypeId = 50011;
        const long UIA_ProgressBarControlTypeId = 50012;
        const long UIA_RadioButtonControlTypeId = 50013;
        const long UIA_ScrollBarControlTypeId = 50014;
        const long UIA_SliderControlTypeId = 50015;
        const long UIA_SpinnerControlTypeId = 50016;
        const long UIA_StatusBarControlTypeId = 50017;
        const long UIA_TabControlTypeId = 50018;
        const long UIA_TabItemControlTypeId = 50019;
        const long UIA_TextControlTypeId = 50020;
        const long UIA_ToolBarControlTypeId = 50021;
        const long UIA_ToolTipControlTypeId = 50022;
        const long UIA_TreeControlTypeId = 50023;
        const long UIA_TreeItemControlTypeId = 50024;
        const long UIA_CustomControlTypeId = 50025;
        const long UIA_GroupControlTypeId = 50026;
        const long UIA_ThumbControlTypeId = 50027;
        const long UIA_DataGridControlTypeId = 50028;
        const long UIA_DataItemControlTypeId = 50029;
        const long UIA_DocumentControlTypeId = 50030;
        const long UIA_SplitButtonControlTypeId = 50031;
        const long UIA_WindowControlTypeId = 50032;
        const long UIA_PaneControlTypeId = 50033;
        const long UIA_HeaderControlTypeId = 50034;
        const long UIA_HeaderItemControlTypeId = 50035;
        const long UIA_TableControlTypeId = 50036;
        const long UIA_TitleBarControlTypeId = 50037;
        const long UIA_SeparatorControlTypeId = 50038;
        const long UIA_SemanticZoomControlTypeId = 50039;
        const long UIA_AppBarControlTypeId = 50040;
    }

    [dllname("<no entry points>")]
    module UIA_AnnotationTypes
    {
        const long AnnotationType_Unknown = 60000;
        const long AnnotationType_SpellingError = 60001;
        const long AnnotationType_GrammarError = 60002;
        const long AnnotationType_Comment = 60003;
        const long AnnotationType_FormulaError = 60004;
        const long AnnotationType_TrackChanges = 60005;
        const long AnnotationType_Header = 60006;
        const long AnnotationType_Footer = 60007;
        const long AnnotationType_Highlighted = 60008;
        const long AnnotationType_Endnote = 60009;
        const long AnnotationType_Footnote = 60010;
        const long AnnotationType_InsertionChange = 60011;
        const long AnnotationType_DeletionChange = 60012;
        const long AnnotationType_MoveChange = 60013;
        const long AnnotationType_FormatChange = 60014;
        const long AnnotationType_UnsyncedChange = 60015;
        const long AnnotationType_EditingLockedChange = 60016;
        const long AnnotationType_ExternalChange = 60017;
        const long AnnotationType_ConflictingChange = 60018;
        const long AnnotationType_Author = 60019;
        const long AnnotationType_AdvancedProofingIssue = 60020;
        const long AnnotationType_DataValidationError = 60021;
        const long AnnotationType_CircularReferenceError = 60022;
        const long AnnotationType_Mathematics = 60023;
        const long AnnotationType_Sensitive = 60024;
    }

    [dllname("<no entry points>")]
    module UIA_StyleIds
    {
        const long StyleId_Custom = 70000;
        const long StyleId_Heading1 = 70001;
        const long StyleId_Heading2 = 70002;
        const long StyleId_Heading3 = 70003;
        const long StyleId_Heading4 = 70004;
        const long StyleId_Heading5 = 70005;
        const long StyleId_Heading6 = 70006;
        const long StyleId_Heading7 = 70007;
        const long StyleId_Heading8 = 70008;
        const long StyleId_Heading9 = 70009;
        const long StyleId_Title = 70010;
        const long StyleId_Subtitle = 70011;
        const long StyleId_Normal = 70012;
        const long StyleId_Emphasis = 70013;
        const long StyleId_Quote = 70014;
        const long StyleId_BulletedList = 70015;
        const long StyleId_NumberedList = 70016;
    }

    [dllname("<no entry points>")]
    module UIA_LandmarkTypeIds
    {
        const long UIA_CustomLandmarkTypeId = 80000;
        const long UIA_FormLandmarkTypeId = 80001;
        const long UIA_MainLandmarkTypeId = 80002;
        const long UIA_NavigationLandmarkTypeId = 80003;
        const long UIA_SearchLandmarkTypeId = 80004;
    }

    interface IUIAutomationElement;
    interface IUIAutomationElementArray;

    [
        object,
        uuid(352ffba8-0973-437c-a61f-f64cafd81df9),
        pointer_default(unique)
    ]
    interface IUIAutomationCondition : IUnknown
    {
    }

    [
        object,
        uuid(1b4e1f2e-75eb-4d0b-8952-5a69988e2307),
        pointer_default(unique)
    ]
    interface IUIAutomationBoolCondition : IUIAutomationCondition
    {
        [propget] HRESULT BooleanValue([out, retval] BOOL *boolVal);
    }

    [
        object,
        uuid(99ebf2cb-5578-4267-9ad4-afd6ea77e94b),
        pointer_default(unique)
    ]
    interface IUIAutomationPropertyCondition : IUIAutomationCondition
    {
        [propget] HRESULT PropertyId([out, retval] PROPERTYID *propertyId);
        [propget] HRESULT PropertyValue([out, retval] VARIANT *propertyValue);
        [propget] HRESULT PropertyConditionFlags([out, retval] enum PropertyConditionFlags *flags);
    }

    [
        object,
        uuid(a7d0af36-b912-45fe-9855-091ddc174aec),
        pointer_default(unique)
    ]
    interface IUIAutomationAndCondition : IUIAutomationCondition
    {
        [propget] HRESULT ChildCount([out, retval] int *childCount);

        HRESULT GetChildrenAsNativeArray(
                [out, size_is( ,*childArrayCount)] IUIAutomationCondition ***childArray,
                [out] int *childArrayCount);

        HRESULT GetChildren([out, retval] SAFEARRAY(IUIAutomationCondition) *childArray);
    }

    [
        object,
        uuid(8753f032-3db1-47b5-a1fc-6e34a266c712),
        pointer_default(unique)
    ]
    interface IUIAutomationOrCondition : IUIAutomationCondition
    {
        [propget] HRESULT ChildCount([out, retval] int *childCount);

        HRESULT GetChildrenAsNativeArray(
                [out, size_is( ,*childArrayCount)] IUIAutomationCondition ***childArray,
                [out] int *childArrayCount);

        HRESULT GetChildren([out, retval] SAFEARRAY(IUIAutomationCondition) *childArray);
    }

    [
        object,
        uuid(f528b657-847b-498c-8896-d52b565407a1),
        pointer_default(unique)
    ]
    interface IUIAutomationNotCondition : IUIAutomationCondition
    {
        HRESULT GetChild([out, retval] IUIAutomationCondition **condition);
    }

    [
        object,
        uuid(b32a92b5-bc25-4078-9c08-d7ee95c48e03),
        pointer_default(unique)
    ]
    interface IUIAutomationCacheRequest : IUnknown
    {
        HRESULT AddProperty([in] PROPERTYID propertyId);
        HRESULT AddPattern([in] PATTERNID patternId);
        HRESULT Clone([out, retval] IUIAutomationCacheRequest **clonedRequest);

        [propget] HRESULT TreeScope([out, retval] enum TreeScope *scope);
        [propput] HRESULT TreeScope([in] enum TreeScope scope);
        [propget] HRESULT TreeFilter([out, retval] IUIAutomationCondition **filter);
        [propput] HRESULT TreeFilter([in] IUIAutomationCondition *filter);
        [propget] HRESULT AutomationElementMode([out, retval] enum AutomationElementMode *mode);
        [propput] HRESULT AutomationElementMode([in] enum AutomationElementMode mode);
    }

    [
        object,
        uuid(4042c624-389c-4afc-a630-9df854a541fc),
        pointer_default(unique)
    ]
    interface IUIAutomationTreeWalker : IUnknown
    {
        HRESULT GetParentElement(
                [in] IUIAutomationElement *element,
                [out, retval] IUIAutomationElement **parent);
        HRESULT GetFirstChildElement(
                [in] IUIAutomationElement *element,
                [out, retval] IUIAutomationElement **first);
        HRESULT GetLastChildElement(
                [in] IUIAutomationElement *element,
                [out, retval] IUIAutomationElement **last);
        HRESULT GetNextSiblingElement(
                [in] IUIAutomationElement *element,
                [out, retval] IUIAutomationElement **next);
        HRESULT GetPreviousSiblingElement(
                [in] IUIAutomationElement *element,
                [out, retval] IUIAutomationElement **previous);
        HRESULT NormalizeElement(
                [in] IUIAutomationElement *element,
                [out, retval] IUIAutomationElement **normalized);

        HRESULT GetParentElementBuildCache(
                [in] IUIAutomationElement *element,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [out, retval] IUIAutomationElement **parent);
        HRESULT GetFirstChildElementBuildCache(
                [in] IUIAutomationElement *element,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [out, retval] IUIAutomationElement **first);
        HRESULT GetLastChildElementBuildCache(
                [in] IUIAutomationElement *element,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [out, retval] IUIAutomationElement **last);
        HRESULT GetNextSiblingElementBuildCache(
                [in] IUIAutomationElement *element,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [out, retval] IUIAutomationElement **next);
        HRESULT GetPreviousSiblingElementBuildCache(
                [in] IUIAutomationElement *element,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [out, retval] IUIAutomationElement **previous);
        HRESULT NormalizeElementBuildCache(
                [in] IUIAutomationElement *element,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [out, retval] IUIAutomationElement **normalized);

        [propget] HRESULT Condition([out, retval] IUIAutomationCondition **condition);
    }

    [
        object,
        uuid(146c3c17-f12e-4e22-8c27-f894b9b79c69),
        pointer_default(unique),
        oleautomation
    ]
    interface IUIAutomationEventHandler : IUnknown
    {
        HRESULT HandleAutomationEvent(
                [in] IUIAutomationElement *sender,
                [in] EVENTID eventId);
    }

    [
        object,
        uuid(40cd37d4-c756-4b0c-8c6f-bddfeeb13b50),
        pointer_default(unique),
        oleautomation
    ]
    interface IUIAutomationPropertyChangedEventHandler : IUnknown
    {
        HRESULT HandlePropertyChangedEvent(
                [in] IUIAutomationElement *sender,
                [in] PROPERTYID propertyId,
                [in] VARIANT newValue);
    }

    [
        object,
        uuid(e81d1b4e-11c5-42f8-9754-e7036c79f054),
        pointer_default(unique),
        oleautomation
    ]
    interface IUIAutomationStructureChangedEventHandler : IUnknown
    {
        HRESULT HandleStructureChangedEvent(
                [in] IUIAutomationElement *sender,
                [in] enum StructureChangeType changeType,
                [in] SAFEARRAY(int) runtimeId);
    }

    [
        object,
        uuid(c270f6b5-5c69-4290-9745-7a7f97169468),
        pointer_default(unique),
        oleautomation
    ]
    interface IUIAutomationFocusChangedEventHandler : IUnknown
    {
        HRESULT HandleFocusChangedEvent([in] IUIAutomationElement *sender);
    }

    [
        object,
        uuid(92faa680-e704-4156-931a-e32d5bb38f3f),
        pointer_default(unique),
        oleautomation
    ]
    interface IUIAutomationTextEditTextChangedEventHandler : IUnknown
    {
        HRESULT HandleTextEditTextChangedEvent(
                [in] IUIAutomationElement *sender,
                [in] enum TextEditChangeType textEditChangeType,
                [in] SAFEARRAY(BSTR) eventStrings);
    }

    [
        object,
        uuid(58edca55-2c3e-4980-b1b9-56c17f27a2a0),
        pointer_default(unique),
        oleautomation
    ]
    interface IUIAutomationChangesEventHandler : IUnknown
    {
        HRESULT HandleChangesEvent(
                [in] IUIAutomationElement *sender,
                [in, size_is(changesCount)] struct UiaChangeInfo *uiaChanges,
                [in] int changesCount);
    }

    [
        object,
        uuid(c7cb2637-e6c2-4d0c-85de-4948c02175c7),
        pointer_default(unique),
        oleautomation
    ]
    interface IUIAutomationNotificationEventHandler : IUnknown
    {
        HRESULT HandleNotificationEvent(
                [in] IUIAutomationElement *sender,
                enum NotificationKind notificationKind,
                enum NotificationProcessing notificationProcessing,
                [in] BSTR displayString,
                [in] BSTR activityId);
    }

    [
        object,
        uuid(fb377fbe-8ea6-46d5-9c73-6499642d3059),
        pointer_default(unique)
    ]
    interface IUIAutomationInvokePattern : IUnknown
    {
        HRESULT Invoke();
    }

    [
        object,
        uuid(fde5ef97-1464-48f6-90bf-43d0948e86ec),
        pointer_default(unique)
    ]
    interface IUIAutomationDockPattern : IUnknown
    {
        HRESULT SetDockPosition([in] enum DockPosition dockPos);
        [propget] HRESULT CurrentDockPosition([out, retval] enum DockPosition *retVal);
        [propget] HRESULT CachedDockPosition([out, retval] enum DockPosition *retVal);
    }

    [
        object,
        uuid(619be086-1f4e-4ee4-bafa-************),
        pointer_default(unique)
    ]
    interface IUIAutomationExpandCollapsePattern : IUnknown
    {
        HRESULT Expand();
        HRESULT Collapse();
        [propget] HRESULT CurrentExpandCollapseState([out, retval] enum ExpandCollapseState *retVal);
        [propget] HRESULT CachedExpandCollapseState([out, retval] enum ExpandCollapseState *retVal);
    }

    [
        object,
        uuid(414c3cdc-856b-4f5b-8538-3131c6302550),
        pointer_default(unique)
    ]
    interface IUIAutomationGridPattern : IUnknown
    {
        HRESULT GetItem(
                [in] int row,
                [in] int column,
                [out, retval] IUIAutomationElement **element);

        [propget] HRESULT CurrentRowCount([out, retval] int *retVal);
        [propget] HRESULT CurrentColumnCount([out, retval] int *retVal);
        [propget] HRESULT CachedRowCount([out, retval] int *retVal);
        [propget] HRESULT CachedColumnCount([out, retval] int *retVal);
    }

    [
        object,
        uuid(78f8ef57-66c3-4e09-bd7c-e79b2004894d),
        pointer_default(unique)
    ]
    interface IUIAutomationGridItemPattern : IUnknown
    {
        [propget] HRESULT CurrentContainingGrid([out, retval] IUIAutomationElement **retVal);
        [propget] HRESULT CurrentRow([out, retval] int *retVal);
        [propget] HRESULT CurrentColumn([out, retval] int *retVal);
        [propget] HRESULT CurrentRowSpan([out, retval] int *retVal);
        [propget] HRESULT CurrentColumnSpan([out, retval] int *retVal);
        [propget] HRESULT CachedContainingGrid([out, retval] IUIAutomationElement **retVal);
        [propget] HRESULT CachedRow([out, retval] int *retVal);
        [propget] HRESULT CachedColumn([out, retval] int *retVal);
        [propget] HRESULT CachedRowSpan([out, retval] int *retVal);
        [propget] HRESULT CachedColumnSpan([out, retval] int *retVal);
    }

    [
        object,
        uuid(8d253c91-1dc5-4bb5-b18f-ade16fa495e8),
        pointer_default(unique)
    ]
    interface IUIAutomationMultipleViewPattern : IUnknown
    {
        HRESULT GetViewName(
                [in] int view,
                [out, retval] BSTR *name);

        HRESULT SetCurrentView([in] int view);

        [propget] HRESULT CurrentCurrentView([out, retval] int *retVal);
        HRESULT GetCurrentSupportedViews([out, retval] SAFEARRAY(int) *retVal);

        [propget] HRESULT CachedCurrentView([out, retval] int *retVal);
        HRESULT GetCachedSupportedViews([out, retval] SAFEARRAY(int) *retVal);
    }

    [
        object,
        uuid(71c284b3-c14d-4d14-981e-19751b0d756d),
        pointer_default(unique)
    ]
    interface IUIAutomationObjectModelPattern : IUnknown
    {
        HRESULT GetUnderlyingObjectModel([out, retval] IUnknown **retVal);
    }

    [
        object,
        uuid(59213f4f-7346-49e5-b120-80555987a148),
        pointer_default(unique)
    ]
    interface IUIAutomationRangeValuePattern : IUnknown
    {
        HRESULT SetValue([in] double val);

        [propget] HRESULT CurrentValue([out, retval] double *retVal);
        [propget] HRESULT CurrentIsReadOnly([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentMaximum([out, retval] double *retVal);
        [propget] HRESULT CurrentMinimum([out, retval] double *retVal);
        [propget] HRESULT CurrentLargeChange([out, retval] double *retVal);
        [propget] HRESULT CurrentSmallChange([out, retval] double *retVal);

        [propget] HRESULT CachedValue([out, retval] double *retVal);
        [propget] HRESULT CachedIsReadOnly([out, retval] BOOL *retVal);
        [propget] HRESULT CachedMaximum([out, retval] double *retVal);
        [propget] HRESULT CachedMinimum([out, retval] double *retVal);
        [propget] HRESULT CachedLargeChange([out, retval] double *retVal);
        [propget] HRESULT CachedSmallChange([out, retval] double *retVal);
    }

    [
        object,
        uuid(88f4d42a-e881-459d-a77c-73bbbb7e02dc),
        pointer_default(unique)
    ]
    interface IUIAutomationScrollPattern : IUnknown
    {
        HRESULT Scroll(
                [in] enum ScrollAmount horizontalAmount,
                [in] enum ScrollAmount verticalAmount);
        HRESULT SetScrollPercent(
                [in] double horizontalPercent,
                [in] double verticalPercent);

        [propget] HRESULT CurrentHorizontalScrollPercent([out, retval] double *retVal);
        [propget] HRESULT CurrentVerticalScrollPercent([out, retval] double *retVal);
        [propget] HRESULT CurrentHorizontalViewSize([out, retval] double *retVal);
        [propget] HRESULT CurrentVerticalViewSize([out, retval] double *retVal);
        [propget] HRESULT CurrentHorizontallyScrollable([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentVerticallyScrollable([out, retval] BOOL *retVal);

        [propget] HRESULT CachedHorizontalScrollPercent([out, retval] double *retVal);
        [propget] HRESULT CachedVerticalScrollPercent([out, retval] double *retVal);
        [propget] HRESULT CachedHorizontalViewSize([out, retval] double *retVal);
        [propget] HRESULT CachedVerticalViewSize([out, retval] double *retVal);
        [propget] HRESULT CachedHorizontallyScrollable([out, retval] BOOL *retVal);
        [propget] HRESULT CachedVerticallyScrollable([out, retval] BOOL *retVal);
    }

    [
        object,
        uuid(b488300f-d015-4f19-9c29-bb595e3645ef),
        pointer_default(unique)
    ]
    interface IUIAutomationScrollItemPattern : IUnknown
    {
        HRESULT ScrollIntoView();
    }

    [
        object,
        uuid(5ed5202e-b2ac-47a6-b638-4b0bf140d78e),
        pointer_default(unique)
    ]
    interface IUIAutomationSelectionPattern : IUnknown
    {
        HRESULT GetCurrentSelection([out, retval] IUIAutomationElementArray **retVal);
        [propget] HRESULT CurrentCanSelectMultiple([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentIsSelectionRequired([out, retval] BOOL *retVal);

        HRESULT GetCachedSelection([out, retval] IUIAutomationElementArray **retVal);
        [propget] HRESULT CachedCanSelectMultiple([out, retval] BOOL *retVal);
        [propget] HRESULT CachedIsSelectionRequired([out, retval] BOOL *retVal);
    }

    [
        object,
        uuid(a8efa66a-0fda-421a-9194-38021f3578ea),
        pointer_default(unique)
    ]
    interface IUIAutomationSelectionItemPattern : IUnknown
    {
        HRESULT Select();
        HRESULT AddToSelection();
        HRESULT RemoveFromSelection();

        [propget] HRESULT CurrentIsSelected([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentSelectionContainer([out, retval] IUIAutomationElement **retVal);

        [propget] HRESULT CachedIsSelected([out, retval] BOOL *retVal);
        [propget] HRESULT CachedSelectionContainer([out, retval] IUIAutomationElement **retVal);
    }

    [
        object,
        uuid(2233be0b-afb7-448b-9fda-3b378aa5eae1),
        pointer_default(unique)
    ]
    interface IUIAutomationSynchronizedInputPattern : IUnknown
    {
        HRESULT StartListening([in] enum SynchronizedInputType inputType);
        HRESULT Cancel();
    }

    [
        object,
        uuid(620e691c-ea96-4710-a850-754b24ce2417),
        pointer_default(unique)
    ]
    interface IUIAutomationTablePattern : IUnknown
    {
        HRESULT GetCurrentRowHeaders([out, retval] IUIAutomationElementArray **retVal);
        HRESULT GetCurrentColumnHeaders([out, retval] IUIAutomationElementArray **retVal);
        [propget] HRESULT CurrentRowOrColumnMajor([out, retval] enum RowOrColumnMajor *retVal);

        HRESULT GetCachedRowHeaders([out, retval] IUIAutomationElementArray **retVal);
        HRESULT GetCachedColumnHeaders([out, retval] IUIAutomationElementArray **retVal);
        [propget] HRESULT CachedRowOrColumnMajor([out, retval] enum RowOrColumnMajor *retVal);
    }

    [
        object,
        uuid(0b964eb3-ef2e-4464-9c79-61d61737a27e),
        pointer_default(unique)
    ]
    interface IUIAutomationTableItemPattern : IUnknown
    {
        HRESULT GetCurrentRowHeaderItems([out, retval] IUIAutomationElementArray **retVal);
        HRESULT GetCurrentColumnHeaderItems([out, retval] IUIAutomationElementArray **retVal);

        HRESULT GetCachedRowHeaderItems([out, retval] IUIAutomationElementArray **retVal);
        HRESULT GetCachedColumnHeaderItems([out, retval] IUIAutomationElementArray **retVal);
    }

    [
        object,
        uuid(94cf8058-9b8d-4ab9-8bfd-4cd0a33c8c70),
        pointer_default(unique)
    ]
    interface IUIAutomationTogglePattern : IUnknown
    {
        HRESULT Toggle();

        [propget] HRESULT CurrentToggleState([out, retval] enum ToggleState *retVal);
        [propget] HRESULT CachedToggleState([out, retval] enum ToggleState *retVal);
    }

    [
        object,
        uuid(a9b55844-a55d-4ef0-926d-569c16ff89bb),
        pointer_default(unique)
    ]
    interface IUIAutomationTransformPattern : IUnknown
    {
        HRESULT Move(
                [in] double x,
                [in] double y);
        HRESULT Resize(
                [in] double width,
                [in] double height);
        HRESULT Rotate([in] double degrees);

        [propget] HRESULT CurrentCanMove([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentCanResize([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentCanRotate([out, retval] BOOL *retVal);

        [propget] HRESULT CachedCanMove([out, retval] BOOL *retVal);
        [propget] HRESULT CachedCanResize([out, retval] BOOL *retVal);
        [propget] HRESULT CachedCanRotate([out, retval] BOOL *retVal);
    }

    [
        object,
        uuid(a94cd8b1-0844-4cd6-9d2d-640537ab39e9),
        pointer_default(unique)
    ]
    interface IUIAutomationValuePattern : IUnknown
    {
        HRESULT SetValue([in] BSTR val);

        [propget] HRESULT CurrentValue([out, retval] BSTR *retVal);
        [propget] HRESULT CurrentIsReadOnly([out, retval] BOOL *retVal);
        [propget] HRESULT CachedValue([out, retval] BSTR *retVal);
        [propget] HRESULT CachedIsReadOnly([out, retval] BOOL *retVal);
    }

    [
        object,
        uuid(0faef453-9208-43ef-bbb2-3b485177864f),
        pointer_default(unique)
    ]
    interface IUIAutomationWindowPattern : IUnknown
    {
        HRESULT Close();

        HRESULT WaitForInputIdle(
                [in] int milliseconds,
                [out, retval] BOOL *success);
        HRESULT SetWindowVisualState([in] enum WindowVisualState state);

        [propget] HRESULT CurrentCanMaximize([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentCanMinimize([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentIsModal([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentIsTopmost([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentWindowVisualState([out, retval] enum WindowVisualState *retVal);
        [propget] HRESULT CurrentWindowInteractionState([out, retval] enum WindowInteractionState *retVal);
        [propget] HRESULT CachedCanMaximize([out, retval] BOOL *retVal);
        [propget] HRESULT CachedCanMinimize([out, retval] BOOL *retVal);
        [propget] HRESULT CachedIsModal([out, retval] BOOL *retVal);
        [propget] HRESULT CachedIsTopmost([out, retval] BOOL *retVal);
        [propget] HRESULT CachedWindowVisualState([out, retval] enum WindowVisualState *retVal);
        [propget] HRESULT CachedWindowInteractionState([out, retval] enum WindowInteractionState *retVal);
    }

    [
        object,
        uuid(a543cc6a-f4ae-494b-8239-c814481187a8),
        pointer_default(unique)
    ]
    interface IUIAutomationTextRange : IUnknown
    {
        HRESULT Clone([out, retval] IUIAutomationTextRange **clonedRange);
        HRESULT Compare(
                [in] IUIAutomationTextRange *range,
                [out, retval] BOOL *areSame);
        HRESULT CompareEndpoints(
                [in] enum TextPatternRangeEndpoint srcEndPoint,
                [in] IUIAutomationTextRange *range,
                [in] enum TextPatternRangeEndpoint targetEndPoint,
                [out, retval] int *compValue);

        HRESULT ExpandToEnclosingUnit([in] enum TextUnit textUnit);

        HRESULT FindAttribute(
                [in] TEXTATTRIBUTEID attr,
                [in] VARIANT val,
                [in] BOOL backward,
                [out, retval] IUIAutomationTextRange **found);
        HRESULT FindText(
                [in] BSTR text,
                [in] BOOL backward,
                [in] BOOL ignoreCase,
                [out, retval] IUIAutomationTextRange **found);

        HRESULT GetAttributeValue(
                [in] TEXTATTRIBUTEID attr,
                [out, retval] VARIANT *value);
        HRESULT GetBoundingRectangles([out, retval] SAFEARRAY(double) *boundingRects);
        HRESULT GetEnclosingElement([out, retval] IUIAutomationElement **enclosingElement);
        HRESULT GetText(
                [in] int maxLength,
                [out, retval] BSTR *text);

        HRESULT Move(
                [in] enum TextUnit unit,
                [in] int count,
                [out, retval] int *moved);
        HRESULT MoveEndpointByUnit(
                [in] enum TextPatternRangeEndpoint endpoint,
                [in] enum TextUnit unit,
                [in] int count,
                [out, retval] int *moved);
        HRESULT MoveEndpointByRange(
                [in] enum TextPatternRangeEndpoint srcEndPoint,
                [in] IUIAutomationTextRange *range,
                [in] enum TextPatternRangeEndpoint targetEndPoint);

        HRESULT Select();
        HRESULT AddToSelection();
        HRESULT RemoveFromSelection( );
        HRESULT ScrollIntoView([in] BOOL alignToTop);

        HRESULT GetChildren([out, retval] IUIAutomationElementArray **children);
    }

    [
        object,
        uuid(bb9b40e0-5e04-46bd-9be0-4b601b9afad4),
        pointer_default(unique)
    ]
    interface IUIAutomationTextRange2 : IUIAutomationTextRange
    {
        HRESULT ShowContextMenu();
    }

    [
        object,
        uuid(ce4ae76a-e717-4c98-81ea-47371d028eb6),
        pointer_default(unique)
    ]
    interface IUIAutomationTextRangeArray : IUnknown
    {
        [propget] HRESULT Length([out, retval] int *length);
        HRESULT GetElement(
                [in] int index,
                [out, retval] IUIAutomationTextRange **element);
    }

    [
        object,
        uuid(32eba289-3583-42c9-9c59-3b6d9a1e9b6a),
        pointer_default(unique)
    ]
    interface IUIAutomationTextPattern : IUnknown
    {
        HRESULT RangeFromPoint(
                [in] POINT pt,
                [out, retval] IUIAutomationTextRange **range);
        HRESULT RangeFromChild(
                [in] IUIAutomationElement *child,
                [out, retval] IUIAutomationTextRange **range);

        HRESULT GetSelection([out, retval] IUIAutomationTextRangeArray **ranges);
        HRESULT GetVisibleRanges([out, retval] IUIAutomationTextRangeArray **ranges);
        [propget] HRESULT DocumentRange([out, retval] IUIAutomationTextRange **range);
        [propget] HRESULT SupportedTextSelection([out, retval] enum SupportedTextSelection *supportedTextSelection);

    }

    [
        object,
        uuid(506a921a-fcc9-409f-b23b-37eb74106872),
        pointer_default(unique)
    ]
    interface IUIAutomationTextPattern2 : IUIAutomationTextPattern
    {
        HRESULT RangeFromAnnotation(
                [in] IUIAutomationElement *annotation,
                [out, retval] IUIAutomationTextRange **range);

        HRESULT GetCaretRange(
                [out] BOOL *isActive,
                [out, retval] IUIAutomationTextRange **range);
    }

    [
        object,
        uuid(17e21576-996c-4870-99d9-bff323380c06),
        pointer_default(unique)
    ]
    interface IUIAutomationTextEditPattern : IUIAutomationTextPattern
    {
        HRESULT GetActiveComposition([out, retval] IUIAutomationTextRange **range);
        HRESULT GetConversionTarget([out, retval] IUIAutomationTextRange **range);
    }

    [
        object,
        uuid(01ea217a-1766-47ed-a6cc-acf492854b1f),
        pointer_default(unique)
    ]
    interface IUIAutomationCustomNavigationPattern : IUnknown
    {
        HRESULT Navigate(
                [in] enum NavigateDirection direction,
                [out, retval] IUIAutomationElement **pRetVal);
    }

    [
        object,
        uuid(f97933b0-8dae-4496-8997-5ba015fe0d82),
        pointer_default(unique),
        oleautomation
    ]
    interface IUIAutomationActiveTextPositionChangedEventHandler : IUnknown
    {
        HRESULT HandleActiveTextPositionChangedEvent(
                [in] IUIAutomationElement *sender,
                [in] IUIAutomationTextRange *range);
    }

    [
        object,
        uuid(828055ad-355b-4435-86d5-3b51c14a9b1b),
        pointer_default(unique)
    ]
    interface IUIAutomationLegacyIAccessiblePattern: IUnknown
    {
        HRESULT Select(long flagsSelect);
        HRESULT DoDefaultAction();
        HRESULT SetValue(LPCWSTR szValue);

        [propget] HRESULT CurrentChildId([out, retval] int *pRetVal);
        [propget] HRESULT CurrentName([out, retval] BSTR *pszName);
        [propget] HRESULT CurrentValue([out, retval] BSTR *pszValue);
        [propget] HRESULT CurrentDescription([out, retval] BSTR *pszDescription);
        [propget] HRESULT CurrentRole([out, retval] DWORD *pdwRole);
        [propget] HRESULT CurrentState([out, retval] DWORD *pdwState);
        [propget] HRESULT CurrentHelp([out, retval] BSTR *pszHelp);
        [propget] HRESULT CurrentKeyboardShortcut([out, retval] BSTR *pszKeyboardShortcut);
        HRESULT GetCurrentSelection([out, retval] IUIAutomationElementArray **pvarSelectedChildren);
        [propget] HRESULT CurrentDefaultAction([out, retval] BSTR *pszDefaultAction);

        [propget] HRESULT CachedChildId([out, retval] int *pRetVal);
        [propget] HRESULT CachedName([out, retval] BSTR *pszName);
        [propget] HRESULT CachedValue([out, retval] BSTR *pszValue);
        [propget] HRESULT CachedDescription([out, retval] BSTR *pszDescription);
        [propget] HRESULT CachedRole([out, retval] DWORD *pdwRole);
        [propget] HRESULT CachedState([out, retval] DWORD *pdwState);
        [propget] HRESULT CachedHelp([out, retval] BSTR *pszHelp);
        [propget] HRESULT CachedKeyboardShortcut([out, retval] BSTR *pszKeyboardShortcut);
        HRESULT GetCachedSelection([out, retval] IUIAutomationElementArray **pvarSelectedChildren);
        [propget] HRESULT CachedDefaultAction([out, retval] BSTR *pszDefaultAction);

        HRESULT GetIAccessible([out, retval] IAccessible **ppAccessible);
    };

    [
        object,
        uuid(c690fdb2-27a8-423c-812d-429773c9084e),
        pointer_default(unique)
    ]
    interface IUIAutomationItemContainerPattern : IUnknown
    {
        HRESULT FindItemByProperty(
                [in] IUIAutomationElement *pStartAfter,
                [in] PROPERTYID propertyId,
                [in] VARIANT value,
                [out, retval] IUIAutomationElement **pFound);
    };

    [
        object,
        uuid(6ba3d7a6-04cf-4f11-8793-a8d1cde9969f),
        pointer_default(unique)
    ]
    interface IUIAutomationVirtualizedItemPattern : IUnknown
    {
        HRESULT Realize();
    };

    [
        object,
        uuid(9a175b21-339e-41b1-8e8b-623f6b681098),
        pointer_default(unique)
    ]
    interface IUIAutomationAnnotationPattern : IUnknown
    {
        [propget] HRESULT CurrentAnnotationTypeId([out, retval] int *retVal);
        [propget] HRESULT CurrentAnnotationTypeName([out, retval] BSTR *retVal);
        [propget] HRESULT CurrentAuthor( [out, retval] BSTR *retVal);
        [propget] HRESULT CurrentDateTime( [out, retval] BSTR *retVal);
        [propget] HRESULT CurrentTarget( [out, retval] IUIAutomationElement **retVal);

        [propget] HRESULT CachedAnnotationTypeId([out, retval] int *retVal);
        [propget] HRESULT CachedAnnotationTypeName([out, retval] BSTR *retVal);
        [propget] HRESULT CachedAuthor( [out, retval] BSTR *retVal);
        [propget] HRESULT CachedDateTime( [out, retval] BSTR *retVal);
        [propget] HRESULT CachedTarget( [out, retval] IUIAutomationElement **retVal);
    };

    [
        object,
        uuid(85b5f0a2-bd79-484a-ad2b-388c9838d5fb),
        pointer_default(unique)
    ]
    interface IUIAutomationStylesPattern : IUnknown
    {
        [propget] HRESULT CurrentStyleId([out, retval] int *retVal);
        [propget] HRESULT CurrentStyleName([out, retval] BSTR *retVal);
        [propget] HRESULT CurrentFillColor([out, retval] int *retVal);
        [propget] HRESULT CurrentFillPatternStyle([out, retval] BSTR *retVal);
        [propget] HRESULT CurrentShape([out, retval] BSTR *retVal);
        [propget] HRESULT CurrentFillPatternColor([out, retval] int *retVal);
        [propget] HRESULT CurrentExtendedProperties([out, retval] BSTR *retVal);
        HRESULT GetCurrentExtendedPropertiesAsArray(
                [out, size_is( ,*propertyCount)] struct ExtendedProperty **propertyArray,
                [out] int *propertyCount);

        [propget] HRESULT CachedStyleId([out, retval] int *retVal);
        [propget] HRESULT CachedStyleName([out, retval] BSTR *retVal);
        [propget] HRESULT CachedFillColor([out, retval] int *retVal);
        [propget] HRESULT CachedFillPatternStyle([out, retval] BSTR *retVal);
        [propget] HRESULT CachedShape([out, retval] BSTR *retVal);
        [propget] HRESULT CachedFillPatternColor([out, retval] int *retVal);
        [propget] HRESULT CachedExtendedProperties([out, retval] BSTR *retVal);
        HRESULT GetCachedExtendedPropertiesAsArray(
                [out, size_is( ,*propertyCount)] struct ExtendedProperty **propertyArray,
                [out] int *propertyCount);
    };

    [
        object,
        uuid(7517a7c8-faae-4de9-9f08-29b91e8595c1),
        pointer_default(unique)
    ]
    interface IUIAutomationSpreadsheetPattern : IUnknown
    {
        HRESULT GetItemByName(
                [in] BSTR name,
                [out, retval] IUIAutomationElement **element);
    };

    [
        object,
        uuid(7d4fb86c-8d34-40e1-8e83-62c15204e335),
        pointer_default(unique)
    ]
    interface IUIAutomationSpreadsheetItemPattern : IUnknown
    {
        [propget] HRESULT CurrentFormula([out, retval] BSTR *retVal);
        HRESULT GetCurrentAnnotationObjects([out, retval] IUIAutomationElementArray **retVal);
        HRESULT GetCurrentAnnotationTypes([out, retval] SAFEARRAY(int) *retVal);

        [propget] HRESULT CachedFormula([out, retval] BSTR *retVal);
        HRESULT GetCachedAnnotationObjects([out, retval] IUIAutomationElementArray **retVal);
        HRESULT GetCachedAnnotationTypes([out, retval] SAFEARRAY(int) *retVal);
    };

    [
        object,
        uuid(6d74d017-6ecb-4381-b38b-3c17a48ff1c2),
        pointer_default(unique)
    ]
    interface IUIAutomationTransformPattern2 : IUIAutomationTransformPattern
    {
        HRESULT Zoom([in] double zoomValue);
        HRESULT ZoomByUnit([in] enum ZoomUnit zoomUnit);

        [propget] HRESULT CurrentCanZoom([out, retval] BOOL *retVal);
        [propget] HRESULT CachedCanZoom([out, retval] BOOL *retVal);

        [propget] HRESULT CurrentZoomLevel([out, retval] double *retVal);
        [propget] HRESULT CachedZoomLevel([out, retval] double *retVal);

        [propget] HRESULT CurrentZoomMinimum([out, retval] double *retVal);
        [propget] HRESULT CachedZoomMinimum([out, retval] double *retVal);

        [propget] HRESULT CurrentZoomMaximum([out, retval] double *retVal);
        [propget] HRESULT CachedZoomMaximum([out, retval] double *retVal);
    }

    [
        object,
        uuid(6552b038-ae05-40c8-abfd-aa08352aab86),
        pointer_default(unique)
    ]
    interface IUIAutomationTextChildPattern : IUnknown
    {
        [propget] HRESULT TextContainer([out, retval] IUIAutomationElement **container);
        [propget] HRESULT TextRange([out, retval] IUIAutomationTextRange **range);
    }

    [
        object,
        uuid(1dc7b570-1f54-4bad-bcda-d36a722fb7bd),
        pointer_default(unique)
    ]
    interface IUIAutomationDragPattern : IUnknown
    {
        [propget] HRESULT CurrentIsGrabbed([out, retval] BOOL *retVal);
        [propget] HRESULT CachedIsGrabbed([out, retval] BOOL *retVal);

        [propget] HRESULT CurrentDropEffect([out, retval] BSTR *retVal);
        [propget] HRESULT CachedDropEffect([out, retval] BSTR *retVal);

        [propget] HRESULT CurrentDropEffects([out, retval] SAFEARRAY(BSTR) *retVal);
        [propget] HRESULT CachedDropEffects([out, retval] SAFEARRAY(BSTR) *retVal);

        HRESULT GetCurrentGrabbedItems([out, retval] IUIAutomationElementArray **retVal);
        HRESULT GetCachedGrabbedItems([out, retval] IUIAutomationElementArray **retVal);
    }

    [
        object,
        uuid(69a095f7-eee4-430e-a46b-fb73b1ae39a5),
        pointer_default(unique)
    ]
    interface IUIAutomationDropTargetPattern : IUnknown
    {
        [propget] HRESULT CurrentDropTargetEffect([out, retval] BSTR *retVal);
        [propget] HRESULT CachedDropTargetEffect([out, retval] BSTR *retVal);

        [propget] HRESULT CurrentDropTargetEffects([out, retval] SAFEARRAY(BSTR) *retVal);
        [propget] HRESULT CachedDropTargetEffects([out, retval] SAFEARRAY(BSTR) *retVal);
    }

    [
        object,
        uuid(d22108aa-8ac5-49a5-837b-37bbb3d7591e),
        pointer_default(unique)
    ]
    interface IUIAutomationElement : IUnknown
    {
        HRESULT SetFocus();
        HRESULT GetRuntimeId([out, retval] SAFEARRAY(int) *runtimeId);

        HRESULT FindFirst(
                [in] enum TreeScope scope,
                [in] IUIAutomationCondition *condition,
                [out, retval] IUIAutomationElement **found);
        HRESULT FindAll(
                [in] enum TreeScope scope,
                [in] IUIAutomationCondition *condition,
                [out, retval] IUIAutomationElementArray **found);

        HRESULT FindFirstBuildCache(
                [in] enum TreeScope scope,
                [in] IUIAutomationCondition *condition,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [out, retval] IUIAutomationElement **found);
        HRESULT FindAllBuildCache(
                [in] enum TreeScope scope,
                [in] IUIAutomationCondition *condition,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [out, retval] IUIAutomationElementArray **found);

        HRESULT BuildUpdatedCache(
                [in] IUIAutomationCacheRequest *cacheRequest,
                [out, retval] IUIAutomationElement **updatedElement);

        HRESULT GetCurrentPropertyValue(
                [in] PROPERTYID propertyId,
                [out, retval] VARIANT *retVal);
        HRESULT GetCurrentPropertyValueEx(
                [in] PROPERTYID propertyId,
                [in] BOOL ignoreDefaultValue,
                [out, retval] VARIANT *retVal);

        HRESULT GetCachedPropertyValue(
                [in] PROPERTYID propertyId,
                [out, retval] VARIANT *retVal);
        HRESULT GetCachedPropertyValueEx(
                [in] PROPERTYID propertyId,
                [in] BOOL ignoreDefaultValue,
                [out, retval] VARIANT *retVal);

        HRESULT GetCurrentPatternAs(
                [in] PATTERNID patternId,
                [in] REFIID riid,
                [out, iid_is(riid), retval] void **patternObject);
        HRESULT GetCachedPatternAs(
                [in] PATTERNID patternId,
                [in] REFIID riid,
                [out, iid_is(riid), retval] void **patternObject);

        HRESULT GetCurrentPattern(
                [in] PATTERNID patternId,
                [out, retval] IUnknown **patternObject);
        HRESULT GetCachedPattern(
                [in] PATTERNID patternId,
                [out, retval] IUnknown **patternObject);

        HRESULT GetCachedParent([out, retval] IUIAutomationElement **parent);
        HRESULT GetCachedChildren([out, retval] IUIAutomationElementArray **children);

        [propget] HRESULT CurrentProcessId([out, retval] int *retVal);
        [propget] HRESULT CurrentControlType([out, retval] CONTROLTYPEID *retVal);
        [propget] HRESULT CurrentLocalizedControlType([out, retval] BSTR *retVal);
        [propget] HRESULT CurrentName([out, retval] BSTR *retVal);
        [propget] HRESULT CurrentAcceleratorKey([out, retval] BSTR *retVal);
        [propget] HRESULT CurrentAccessKey([out, retval] BSTR *retVal);
        [propget] HRESULT CurrentHasKeyboardFocus([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentIsKeyboardFocusable([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentIsEnabled([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentAutomationId([out, retval] BSTR *retVal);
        [propget] HRESULT CurrentClassName([out, retval] BSTR *retVal);
        [propget] HRESULT CurrentHelpText([out, retval] BSTR *retVal);
        [propget] HRESULT CurrentCulture([out, retval] int *retVal);
        [propget] HRESULT CurrentIsControlElement([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentIsContentElement([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentIsPassword([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentNativeWindowHandle([out, retval] UIA_HWND *retVal);
        [propget] HRESULT CurrentItemType([out, retval] BSTR *retVal);
        [propget] HRESULT CurrentIsOffscreen([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentOrientation([out, retval] enum OrientationType *retVal);
        [propget] HRESULT CurrentFrameworkId([out, retval] BSTR *retVal);
        [propget] HRESULT CurrentIsRequiredForForm([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentItemStatus([out, retval] BSTR *retVal);
        [propget] HRESULT CurrentBoundingRectangle([out, retval] RECT *retVal);
        [propget] HRESULT CurrentLabeledBy([out, retval] IUIAutomationElement **retVal);
        [propget] HRESULT CurrentAriaRole([out, retval] BSTR *retVal);
        [propget] HRESULT CurrentAriaProperties([out, retval] BSTR *retVal);
        [propget] HRESULT CurrentIsDataValidForForm([out, retval] BOOL *retVal);
        [propget] HRESULT CurrentControllerFor([out, retval] IUIAutomationElementArray **retVal);
        [propget] HRESULT CurrentDescribedBy([out, retval] IUIAutomationElementArray **retVal);
        [propget] HRESULT CurrentFlowsTo([out, retval] IUIAutomationElementArray **retVal);
        [propget] HRESULT CurrentProviderDescription([out, retval] BSTR *retVal);

        [propget] HRESULT CachedProcessId([out, retval] int *retVal);
        [propget] HRESULT CachedControlType([out, retval] CONTROLTYPEID *retVal);
        [propget] HRESULT CachedLocalizedControlType([out, retval] BSTR *retVal);
        [propget] HRESULT CachedName([out, retval] BSTR *retVal);
        [propget] HRESULT CachedAcceleratorKey([out, retval] BSTR *retVal);
        [propget] HRESULT CachedAccessKey([out, retval] BSTR *retVal);
        [propget] HRESULT CachedHasKeyboardFocus([out, retval] BOOL *retVal);
        [propget] HRESULT CachedIsKeyboardFocusable([out, retval] BOOL *retVal);
        [propget] HRESULT CachedIsEnabled([out, retval] BOOL *retVal);
        [propget] HRESULT CachedAutomationId([out, retval] BSTR *retVal);
        [propget] HRESULT CachedClassName([out, retval] BSTR *retVal);
        [propget] HRESULT CachedHelpText([out, retval] BSTR *retVal);
        [propget] HRESULT CachedCulture([out, retval] int *retVal);
        [propget] HRESULT CachedIsControlElement([out, retval] BOOL *retVal);
        [propget] HRESULT CachedIsContentElement([out, retval] BOOL *retVal);
        [propget] HRESULT CachedIsPassword([out, retval] BOOL *retVal);
        [propget] HRESULT CachedNativeWindowHandle([out, retval] UIA_HWND *retVal);
        [propget] HRESULT CachedItemType([out, retval] BSTR *retVal);
        [propget] HRESULT CachedIsOffscreen([out, retval] BOOL *retVal);
        [propget] HRESULT CachedOrientation([out, retval] enum OrientationType *retVal);
        [propget] HRESULT CachedFrameworkId([out, retval] BSTR *retVal);
        [propget] HRESULT CachedIsRequiredForForm([out, retval] BOOL *retVal);
        [propget] HRESULT CachedItemStatus([out, retval] BSTR *retVal);
        [propget] HRESULT CachedBoundingRectangle([out, retval] RECT *retVal);
        [propget] HRESULT CachedLabeledBy([out, retval] IUIAutomationElement **retVal);
        [propget] HRESULT CachedAriaRole([out, retval] BSTR *retVal);
        [propget] HRESULT CachedAriaProperties([out, retval] BSTR *retVal);
        [propget] HRESULT CachedIsDataValidForForm([out, retval] BOOL *retVal);
        [propget] HRESULT CachedControllerFor([out, retval] IUIAutomationElementArray **retVal);
        [propget] HRESULT CachedDescribedBy([out, retval] IUIAutomationElementArray **retVal);
        [propget] HRESULT CachedFlowsTo([out, retval] IUIAutomationElementArray **retVal);
        [propget] HRESULT CachedProviderDescription([out, retval] BSTR *retVal);

        HRESULT GetClickablePoint(
                [out] POINT *clickable,
                [out, retval] BOOL *gotClickable);
    }

    [
        object,
        uuid(6749c683-f70d-4487-a698-5f79d55290d6),
        pointer_default(unique)
    ]
    interface IUIAutomationElement2 : IUIAutomationElement
    {
        [propget] HRESULT CurrentOptimizeForVisualContent([out, retval] BOOL *retVal);
        [propget] HRESULT CachedOptimizeForVisualContent([out, retval] BOOL *retVal);

        [propget] HRESULT CurrentLiveSetting([out, retval] enum LiveSetting *retVal);
        [propget] HRESULT CachedLiveSetting([out, retval] enum LiveSetting *retVal);

        [propget] HRESULT CurrentFlowsFrom([out, retval] IUIAutomationElementArray **retVal);
        [propget] HRESULT CachedFlowsFrom([out, retval] IUIAutomationElementArray **retVal);
    }

    [
        object,
        uuid(8471df34-aee0-4a01-a7de-7db9af12c296),
        pointer_default(unique)
    ]
    interface IUIAutomationElement3 : IUIAutomationElement2
    {
        HRESULT ShowContextMenu();

        [propget] HRESULT CurrentIsPeripheral([out, retval] BOOL *retVal);
        [propget] HRESULT CachedIsPeripheral([out, retval] BOOL *retVal);
    }

    [
        object,
        uuid(3b6e233c-52fb-4063-a4c9-77c075c2a06b),
        pointer_default(unique)
    ]
    interface IUIAutomationElement4 : IUIAutomationElement3
    {
        [propget] HRESULT CurrentPositionInSet([out, retval] int *retVal);
        [propget] HRESULT CurrentSizeOfSet([out, retval] int *retVal);
        [propget] HRESULT CurrentLevel([out, retval] int *retVal);
        [propget] HRESULT CurrentAnnotationTypes([out, retval] SAFEARRAY(int) *retVal);
        [propget] HRESULT CurrentAnnotationObjects([out, retval] IUIAutomationElementArray **retVal);

        [propget] HRESULT CachedPositionInSet([out, retval] int *retVal);
        [propget] HRESULT CachedSizeOfSet([out, retval] int *retVal);
        [propget] HRESULT CachedLevel([out, retval] int *retVal);
        [propget] HRESULT CachedAnnotationTypes([out, retval] SAFEARRAY(int) *retVal);
        [propget] HRESULT CachedAnnotationObjects([out, retval] IUIAutomationElementArray **retVal);
    }

    [
        object,
        uuid(98141c1d-0d0e-4175-bbe2-6bff455842a7),
        pointer_default(unique)
    ]
    interface IUIAutomationElement5 : IUIAutomationElement4
    {
        [propget] HRESULT CurrentLandmarkType([out, retval] LANDMARKTYPEID *retVal);
        [propget] HRESULT CurrentLocalizedLandmarkType([out, retval] BSTR *retVal);

        [propget] HRESULT CachedLandmarkType([out, retval] LANDMARKTYPEID *retVal);
        [propget] HRESULT CachedLocalizedLandmarkType([out, retval] BSTR *retVal);
    }

    [
        object,
        uuid(4780d450-8bca-4977-afa5-a4a517f555e3),
        pointer_default(unique)
    ]
    interface IUIAutomationElement6 : IUIAutomationElement5
    {
        [propget] HRESULT CurrentFullDescription([out, retval] BSTR *retVal);
        [propget] HRESULT CachedFullDescription([out, retval] BSTR *retVal);
    }

    [
        object,
        uuid(204e8572-cfc3-4c11-b0c8-7da7420750b7),
        pointer_default(unique)
    ]
    interface IUIAutomationElement7 : IUIAutomationElement6
    {
        HRESULT FindFirstWithOptions(
                [in] enum TreeScope scope,
                [in] IUIAutomationCondition *condition,
                [in] enum TreeTraversalOptions traversalOptions,
                [in] IUIAutomationElement *root,
                [out, retval] IUIAutomationElement **found);
        HRESULT FindAllWithOptions(
                [in] enum TreeScope scope,
                [in] IUIAutomationCondition *condition,
                [in] enum TreeTraversalOptions traversalOptions,
                [in] IUIAutomationElement *root,
                [out, retval] IUIAutomationElementArray **found);

        HRESULT FindFirstWithOptionsBuildCache(
                [in] enum TreeScope scope,
                [in] IUIAutomationCondition *condition,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [in] enum TreeTraversalOptions traversalOptions,
                [in] IUIAutomationElement *root,
                [out, retval] IUIAutomationElement **found);
        HRESULT FindAllWithOptionsBuildCache(
                [in] enum TreeScope scope,
                [in] IUIAutomationCondition *condition,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [in] enum TreeTraversalOptions traversalOptions,
                [in] IUIAutomationElement *root,
                [out, retval] IUIAutomationElementArray **found);

        HRESULT GetCurrentMetadataValue(
                [in] int targetId,
                [in] METADATAID metadataId,
                [out, retval] VARIANT *returnVal);
    }

    [
        object,
        uuid(8c60217d-5411-4cde-bcc0-1ceda223830c),
        pointer_default(unique)
    ]
    interface IUIAutomationElement8 : IUIAutomationElement7
    {
        [propget] HRESULT CurrentHeadingLevel([out, retval] HEADINGLEVELID *retVal);
        [propget] HRESULT CachedHeadingLevel([out, retval] HEADINGLEVELID *retVal);
    }

    [
        object,
        uuid(39325fac-039d-440e-a3a3-5eb81a5cecc3),
        pointer_default(unique)
    ]
    interface IUIAutomationElement9 : IUIAutomationElement8
    {
        [propget] HRESULT CurrentIsDialog([out, retval] BOOL *retVal);
        [propget] HRESULT CachedIsDialog([out, retval] BOOL *retVal);
    }

    [
        object,
        uuid(14314595-b4bc-4055-95f2-58f2e42c9855),
        pointer_default(unique)
    ]
    interface IUIAutomationElementArray : IUnknown
    {
        [propget] HRESULT Length([out, retval] int *length);

        HRESULT GetElement(
                [in] int index,
                [out, retval] IUIAutomationElement **element);
    }

    [
        object,
        uuid(85b94ecd-849d-42b6-b94d-d6db23fdf5a4),
        pointer_default(unique)
    ]
    interface IUIAutomationProxyFactory : IUnknown
    {
        HRESULT CreateProvider(
                [in] UIA_HWND hwnd,
                [in] LONG idObject,
                [in] LONG idChild,
                [out, retval] IRawElementProviderSimple **provider);

        [propget] HRESULT ProxyFactoryId([out, retval] BSTR *factoryId);
    }

    [
        object,
        uuid(d50e472e-b64b-490c-bca1-d30696f9f289),
        pointer_default(unique)
    ]
    interface IUIAutomationProxyFactoryEntry : IUnknown
    {
        [propget] HRESULT ProxyFactory([out, retval] IUIAutomationProxyFactory **factory);

        [propget] HRESULT ClassName([out, retval] BSTR *className);
        [propget] HRESULT ImageName([out, retval] BSTR *imageName);
        [propget] HRESULT AllowSubstringMatch([out, retval] BOOL *allowSubstringMatch);
        [propget] HRESULT CanCheckBaseClass([out, retval] BOOL *canCheckBaseClass);
        [propget] HRESULT NeedsAdviseEvents([out, retval] BOOL *adviseEvents);

        [propput] HRESULT ClassName([in] LPCWSTR className);
        [propput] HRESULT ImageName([in] LPCWSTR imageName);
        [propput] HRESULT AllowSubstringMatch([in] BOOL allowSubstringMatch);
        [propput] HRESULT CanCheckBaseClass([in] BOOL canCheckBaseClass);
        [propput] HRESULT NeedsAdviseEvents([in] BOOL adviseEvents);

        HRESULT SetWinEventsForAutomationEvent(
                [in] EVENTID eventId,
                [in] PROPERTYID propertyId,
                [in] SAFEARRAY(UINT) winEvents);
        HRESULT GetWinEventsForAutomationEvent(
                [in] EVENTID eventId,
                [in] PROPERTYID propertyId,
                [out, retval] SAFEARRAY(UINT) *winEvents);
    }

    [
        object,
        uuid(09e31e18-872d-4873-93d1-1e541ec133fd),
        pointer_default(unique)
    ]
    interface IUIAutomationProxyFactoryMapping : IUnknown
    {
        [propget] HRESULT Count([out, retval] UINT *count);

        HRESULT GetTable([out, retval] SAFEARRAY(IUIAutomationProxyFactoryEntry) *table);
        HRESULT GetEntry(
                [in] UINT index,
                [out, retval] IUIAutomationProxyFactoryEntry **entry);

        HRESULT SetTable([in] SAFEARRAY(IUIAutomationProxyFactoryEntry) factoryList);
        HRESULT InsertEntries(
                [in] UINT before,
                [in] SAFEARRAY(IUIAutomationProxyFactoryEntry) factoryList);
        HRESULT InsertEntry(
                [in] UINT before,
                [in] IUIAutomationProxyFactoryEntry *factory);

        HRESULT RemoveEntry([in] UINT index);
        HRESULT ClearTable();
        HRESULT RestoreDefaultTable();
    }

    [
        object,
        uuid(c9ee12f2-c13b-4408-997c-639914377f4e),
        pointer_default(unique)
    ]
    interface IUIAutomationEventHandlerGroup : IUnknown
    {
        HRESULT AddActiveTextPositionChangedEventHandler(
                [in] enum TreeScope scope,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [in] IUIAutomationActiveTextPositionChangedEventHandler *handler);
        HRESULT AddAutomationEventHandler(
                [in] EVENTID eventId,
                [in] enum TreeScope scope,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [in] IUIAutomationEventHandler *handler);
        HRESULT AddChangesEventHandler(
                [in] enum TreeScope scope,
                [in, size_is(changesCount)] int *changeTypes,
                [in] int changesCount,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [in] IUIAutomationChangesEventHandler *handler);
        HRESULT AddNotificationEventHandler(
                [in] enum TreeScope scope,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [in] IUIAutomationNotificationEventHandler *handler);
        HRESULT AddPropertyChangedEventHandler(
                [in] enum TreeScope scope,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [in] IUIAutomationPropertyChangedEventHandler *handler,
                [in, size_is(propertyCount)] PROPERTYID *propertyArray,
                [in] int propertyCount);
        HRESULT AddStructureChangedEventHandler(
                [in] enum TreeScope scope,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [in] IUIAutomationStructureChangedEventHandler *handler);
        HRESULT AddTextEditTextChangedEventHandler(
                [in] enum TreeScope scope,
                [in] enum TextEditChangeType textEditChangeType,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [in] IUIAutomationTextEditTextChangedEventHandler *handler);
    }

    [
        object,
        uuid(30cbe57d-d9d0-452a-ab13-7ac5ac4825ee),
        pointer_default(unique)
    ]
    interface IUIAutomation : IUnknown
    {
        HRESULT CompareElements(
                [in] IUIAutomationElement *el1,
                [in] IUIAutomationElement *el2,
                [out, retval] BOOL *areSame);
        HRESULT CompareRuntimeIds(
                [in] SAFEARRAY(int) runtimeId1,
                [in] SAFEARRAY(int) runtimeId2,
                [out, retval] BOOL *areSame);

        HRESULT GetRootElement([out, retval] IUIAutomationElement **root);
        HRESULT ElementFromHandle(
                [in] UIA_HWND hwnd,
                [out, retval] IUIAutomationElement **element);
        HRESULT ElementFromPoint(
                [in] POINT pt,
                [out, retval] IUIAutomationElement **element);
        HRESULT GetFocusedElement([out, retval] IUIAutomationElement **element);

        HRESULT GetRootElementBuildCache(
                [in] IUIAutomationCacheRequest *cacheRequest,
                [out, retval] IUIAutomationElement **root);
        HRESULT ElementFromHandleBuildCache(
                [in] UIA_HWND hwnd,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [out, retval] IUIAutomationElement **element);
        HRESULT ElementFromPointBuildCache(
                [in] POINT pt,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [out, retval] IUIAutomationElement **element);
        HRESULT GetFocusedElementBuildCache(
                [in] IUIAutomationCacheRequest *cacheRequest,
                [out, retval] IUIAutomationElement **element);

        HRESULT CreateTreeWalker(
                [in] IUIAutomationCondition *pCondition,
                [out, retval] IUIAutomationTreeWalker **walker);

        [propget] HRESULT ControlViewWalker([out, retval] IUIAutomationTreeWalker **walker);
        [propget] HRESULT ContentViewWalker([out, retval] IUIAutomationTreeWalker **walker);
        [propget] HRESULT RawViewWalker([out, retval] IUIAutomationTreeWalker **walker);

        [propget] HRESULT RawViewCondition([out, retval] IUIAutomationCondition **condition);
        [propget] HRESULT ControlViewCondition([out, retval] IUIAutomationCondition **condition);
        [propget] HRESULT ContentViewCondition([out, retval] IUIAutomationCondition **condition);

        HRESULT CreateCacheRequest([out, retval] IUIAutomationCacheRequest **cacheRequest);

        HRESULT CreateTrueCondition([out, retval] IUIAutomationCondition **newCondition);
        HRESULT CreateFalseCondition([out, retval] IUIAutomationCondition **newCondition);
        HRESULT CreatePropertyCondition(
                [in] PROPERTYID propertyId,
                [in] VARIANT value,
                [out, retval] IUIAutomationCondition **newCondition);
        HRESULT CreatePropertyConditionEx(
                [in] PROPERTYID propertyId,
                [in] VARIANT value,
                [in] enum PropertyConditionFlags flags,
                [out, retval] IUIAutomationCondition **newCondition);

        HRESULT CreateAndCondition(
                [in] IUIAutomationCondition *condition1,
                [in] IUIAutomationCondition *condition2,
                [out, retval] IUIAutomationCondition **newCondition);
        HRESULT CreateAndConditionFromArray(
                [in] SAFEARRAY(IUIAutomationCondition) conditions,
                [out, retval] IUIAutomationCondition **newCondition);
        HRESULT CreateAndConditionFromNativeArray(
                [in, size_is(conditionCount)] IUIAutomationCondition **conditions,
                [in] int conditionCount,
                [out, retval] IUIAutomationCondition **newCondition);

        HRESULT CreateOrCondition(
                [in] IUIAutomationCondition *condition1,
                [in] IUIAutomationCondition *condition2,
                [out, retval] IUIAutomationCondition **newCondition);
        HRESULT CreateOrConditionFromArray(
                [in] SAFEARRAY(IUIAutomationCondition) conditions,
                [out, retval] IUIAutomationCondition **newCondition);
        HRESULT CreateOrConditionFromNativeArray(
                [in, size_is(conditionCount)] IUIAutomationCondition **conditions,
                [in] int conditionCount,
                [out, retval] IUIAutomationCondition **newCondition);

        HRESULT CreateNotCondition(
                [in] IUIAutomationCondition *condition,
                [out, retval] IUIAutomationCondition **newCondition);

        HRESULT AddAutomationEventHandler(
                [in] EVENTID eventId,
                [in] IUIAutomationElement *element,
                [in] enum TreeScope scope,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [in] IUIAutomationEventHandler *handler);
        HRESULT RemoveAutomationEventHandler(
                [in] EVENTID eventId,
                [in] IUIAutomationElement *element,
                [in] IUIAutomationEventHandler *handler);

        HRESULT AddPropertyChangedEventHandlerNativeArray(
                [in] IUIAutomationElement *element,
                [in] enum TreeScope scope,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [in] IUIAutomationPropertyChangedEventHandler *handler,
                [in, size_is(propertyCount)] PROPERTYID *propertyArray,
                [in] int propertyCount);
        HRESULT AddPropertyChangedEventHandler(
                [in] IUIAutomationElement *element,
                [in] enum TreeScope scope,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [in] IUIAutomationPropertyChangedEventHandler *handler,
                [in] SAFEARRAY(PROPERTYID) propertyArray);
        HRESULT RemovePropertyChangedEventHandler(
                [in] IUIAutomationElement *element,
                [in] IUIAutomationPropertyChangedEventHandler *handler);

        HRESULT AddStructureChangedEventHandler(
                [in] IUIAutomationElement *element,
                [in] enum TreeScope scope,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [in] IUIAutomationStructureChangedEventHandler *handler);
        HRESULT RemoveStructureChangedEventHandler(
                [in] IUIAutomationElement *element,
                [in] IUIAutomationStructureChangedEventHandler *handler);

        HRESULT AddFocusChangedEventHandler(
                [in] IUIAutomationCacheRequest *cacheRequest,
                [in] IUIAutomationFocusChangedEventHandler *handler);
        HRESULT RemoveFocusChangedEventHandler([in] IUIAutomationFocusChangedEventHandler *handler);

        HRESULT RemoveAllEventHandlers();

        HRESULT IntNativeArrayToSafeArray(
                [in, size_is(arrayCount)] int *array,
                [in] int arrayCount,
                [out, retval] SAFEARRAY(int) *safeArray);
        HRESULT IntSafeArrayToNativeArray(
                [in] SAFEARRAY(int) intArray,
                [out, size_is( ,*arrayCount)] int **array,
                [out, retval] int *arrayCount);

        HRESULT RectToVariant(
                [in] RECT rc,
                [out, retval] VARIANT *var);
        HRESULT VariantToRect(
                [in] VARIANT var,
                [out, retval] RECT *rc);
        HRESULT SafeArrayToRectNativeArray(
                [in] SAFEARRAY(double) rects,
                [out, size_is( ,*rectArrayCount)] RECT **rectArray,
                [out, retval] int *rectArrayCount);

        HRESULT CreateProxyFactoryEntry(
                [in] IUIAutomationProxyFactory *factory,
                [out, retval] IUIAutomationProxyFactoryEntry **factoryEntry);
        [propget] HRESULT ProxyFactoryMapping([out, retval] IUIAutomationProxyFactoryMapping **factoryMapping);

        HRESULT GetPropertyProgrammaticName(
                [in] PROPERTYID property,
                [out, retval] BSTR *name);
        HRESULT GetPatternProgrammaticName(
                [in] PATTERNID pattern,
                [out, retval] BSTR *name);

        HRESULT PollForPotentialSupportedPatterns(
                [in] IUIAutomationElement *pElement,
                [out] SAFEARRAY(int) *patternIds,
                [out] SAFEARRAY(BSTR) *patternNames);
        HRESULT PollForPotentialSupportedProperties(
                [in] IUIAutomationElement *pElement,
                [out] SAFEARRAY(int) *propertyIds,
                [out] SAFEARRAY(BSTR) *propertyNames);

        HRESULT CheckNotSupported(
                [in] VARIANT value,
                [out, retval] BOOL *isNotSupported);

        [propget] HRESULT ReservedNotSupportedValue([out, retval] IUnknown **notSupportedValue);
        [propget] HRESULT ReservedMixedAttributeValue([out, retval] IUnknown **mixedAttributeValue);

        HRESULT ElementFromIAccessible(
                [in] IAccessible *accessible,
                [in] int childId,
                [out, retval] IUIAutomationElement **element);
        HRESULT ElementFromIAccessibleBuildCache(
                [in] IAccessible *accessible,
                [in] int childId,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [out, retval] IUIAutomationElement **element);
    }

    [
        object,
        uuid(34723aff-0c9d-49d0-9896-7ab52df8cd8a),
        pointer_default(unique)
    ]
    interface IUIAutomation2 : IUIAutomation
    {
        [propget] HRESULT AutoSetFocus([out, retval] BOOL *autoSetFocus);
        [propput] HRESULT AutoSetFocus([in] BOOL autoSetFocus);

        [propget] HRESULT ConnectionTimeout([out, retval] DWORD *timeout);
        [propput] HRESULT ConnectionTimeout([in] DWORD timeout);

        [propget] HRESULT TransactionTimeout([out, retval] DWORD *timeout);
        [propput] HRESULT TransactionTimeout([in] DWORD timeout);
    }

    [
        object,
        uuid(73d768da-9b51-4b89-936e-c209290973e7),
        pointer_default(unique)
    ]
    interface IUIAutomation3 : IUIAutomation2
    {
        HRESULT AddTextEditTextChangedEventHandler(
                [in] IUIAutomationElement *element,
                [in] enum TreeScope scope,
                [in] enum TextEditChangeType textEditChangeType,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [in] IUIAutomationTextEditTextChangedEventHandler *handler);
        HRESULT RemoveTextEditTextChangedEventHandler(
                [in] IUIAutomationElement *element,
                [in] IUIAutomationTextEditTextChangedEventHandler *handler);
    }

    [
        object,
        uuid(1189c02a-05f8-4319-8e21-e817e3db2860),
        pointer_default(unique)
    ]
    interface IUIAutomation4 : IUIAutomation3
    {
        HRESULT AddChangesEventHandler(
                [in] IUIAutomationElement *element,
                [in] enum TreeScope scope,
                [in, size_is(changesCount)] int *changeTypes,
                [in] int changesCount,
                [in] IUIAutomationCacheRequest *pCacheRequest,
                [in] IUIAutomationChangesEventHandler *handler);
        HRESULT RemoveChangesEventHandler(
                [in] IUIAutomationElement *element,
                [in] IUIAutomationChangesEventHandler *handler);
    }

    [
        object,
        uuid(25f700c8-d816-4057-a9dc-3cbdee77e256),
        pointer_default(unique)
    ]
    interface IUIAutomation5 : IUIAutomation4
    {
        HRESULT AddNotificationEventHandler(
                [in] IUIAutomationElement *element,
                [in] enum TreeScope scope,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [in] IUIAutomationNotificationEventHandler *handler);
        HRESULT RemoveNotificationEventHandler(
                [in] IUIAutomationElement *element,
                [in] IUIAutomationNotificationEventHandler *handler);
    }

    [
        object,
        uuid(aae072da-29e3-413d-87a7-192dbf81ed10),
        pointer_default(unique)
    ]
    interface IUIAutomation6 : IUIAutomation5
    {
        HRESULT CreateEventHandlerGroup([out] IUIAutomationEventHandlerGroup **handlerGroup);
        HRESULT AddEventHandlerGroup(
                [in] IUIAutomationElement *element,
                [in] IUIAutomationEventHandlerGroup *handlerGroup);
        HRESULT RemoveEventHandlerGroup(
                [in] IUIAutomationElement *element,
                [in] IUIAutomationEventHandlerGroup *handlerGroup);

        [propget] HRESULT ConnectionRecoveryBehavior(
                          [out, retval] enum ConnectionRecoveryBehaviorOptions *connectionRecoveryBehaviorOptions);
        [propput] HRESULT ConnectionRecoveryBehavior(
                          [in] enum ConnectionRecoveryBehaviorOptions connectionRecoveryBehaviorOptions);

        [propget] HRESULT CoalesceEvents([out, retval] enum CoalesceEventsOptions *coalesceEventsOptions);
        [propput] HRESULT CoalesceEvents([in] enum CoalesceEventsOptions coalesceEventsOptions);

        HRESULT AddActiveTextPositionChangedEventHandler(
                [in] IUIAutomationElement *element,
                [in] enum TreeScope scope,
                [in] IUIAutomationCacheRequest *cacheRequest,
                [in] IUIAutomationActiveTextPositionChangedEventHandler *handler);
        HRESULT RemoveActiveTextPositionChangedEventHandler(
                [in] IUIAutomationElement *element,
                [in] IUIAutomationActiveTextPositionChangedEventHandler *handler);
    }

    [
        uuid(ff48dba4-60ef-4201-aa87-54103eef594e),
        version(1.0),
        threading(both)
    ]
    coclass CUIAutomation
    {
        [default] interface IUIAutomation;
    }

    [
        uuid(e22ad333-b25f-460c-83d0-0581107395c9),
        version(1.0),
        threading(both)
    ]
    coclass CUIAutomation8
    {
        [default] interface IUIAutomation2;
        interface IUIAutomation3;
        interface IUIAutomation4;
        interface IUIAutomation5;
        interface IUIAutomation6;
    }
}
