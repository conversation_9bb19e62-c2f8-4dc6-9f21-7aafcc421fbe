/*** Autogenerated by WIDL 10.8 from include/windows.security.authentication.onlineid.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_security_authentication_onlineid_h__
#define __windows_security_authentication_onlineid_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest ABI::Windows::Security::Authentication::OnlineId::IOnlineIdServiceTicketRequest
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    interface IOnlineIdServiceTicketRequest;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory ABI::Windows::Security::Authentication::OnlineId::IOnlineIdServiceTicketRequestFactory
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    interface IOnlineIdServiceTicketRequestFactory;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser ABI::Windows::Security::Authentication::OnlineId::IOnlineIdSystemAuthenticatorForUser
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    interface IOnlineIdSystemAuthenticatorForUser;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics ABI::Windows::Security::Authentication::OnlineId::IOnlineIdSystemAuthenticatorStatics
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    interface IOnlineIdSystemAuthenticatorStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult ABI::Windows::Security::Authentication::OnlineId::IOnlineIdSystemTicketResult
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    interface IOnlineIdSystemTicketResult;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdServiceTicketRequest_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdServiceTicketRequest_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    class OnlineIdServiceTicketRequest;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdServiceTicketRequest __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdServiceTicketRequest;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdServiceTicketRequest_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemAuthenticator_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemAuthenticator_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    class OnlineIdSystemAuthenticator;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemAuthenticator __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemAuthenticator;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemAuthenticator_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemAuthenticatorForUser_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemAuthenticatorForUser_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    class OnlineIdSystemAuthenticatorForUser;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemAuthenticatorForUser __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemAuthenticatorForUser;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemAuthenticatorForUser_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemIdentity_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemIdentity_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    class OnlineIdSystemIdentity;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemIdentity __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemIdentity;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemIdentity_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemTicketResult_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemTicketResult_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    class OnlineIdSystemTicketResult;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemTicketResult __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemTicketResult;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemTicketResult_FWD_DEFINED__ */

#ifndef ____FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_FWD_DEFINED__
#define ____FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Security::Authentication::OnlineId::OnlineIdServiceTicketRequest* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_FWD_DEFINED__
#define ____FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Security::Authentication::OnlineId::OnlineIdServiceTicketRequest* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Security::Authentication::OnlineId::OnlineIdSystemTicketResult* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Security::Authentication::OnlineId::OnlineIdSystemTicketResult* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.system.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemTicketStatus __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemTicketStatus;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest ABI::Windows::Security::Authentication::OnlineId::IOnlineIdServiceTicketRequest
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    interface IOnlineIdServiceTicketRequest;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory ABI::Windows::Security::Authentication::OnlineId::IOnlineIdServiceTicketRequestFactory
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    interface IOnlineIdServiceTicketRequestFactory;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser ABI::Windows::Security::Authentication::OnlineId::IOnlineIdSystemAuthenticatorForUser
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    interface IOnlineIdSystemAuthenticatorForUser;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics ABI::Windows::Security::Authentication::OnlineId::IOnlineIdSystemAuthenticatorStatics
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    interface IOnlineIdSystemAuthenticatorStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemIdentity_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemIdentity_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemIdentity __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemIdentity;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemIdentity ABI::Windows::Security::Authentication::OnlineId::IOnlineIdSystemIdentity
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    interface IOnlineIdSystemIdentity;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult ABI::Windows::Security::Authentication::OnlineId::IOnlineIdSystemTicketResult
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    interface IOnlineIdSystemTicketResult;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_FWD_DEFINED__
#define ____FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Security::Authentication::OnlineId::OnlineIdServiceTicketRequest* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_FWD_DEFINED__
#define ____FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Security::Authentication::OnlineId::OnlineIdServiceTicketRequest* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Security::Authentication::OnlineId::OnlineIdSystemTicketResult* >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    enum OnlineIdSystemTicketStatus {
                        OnlineIdSystemTicketStatus_Success = 0,
                        OnlineIdSystemTicketStatus_Error = 1,
                        OnlineIdSystemTicketStatus_ServiceConnectionError = 2
                    };
                }
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemTicketStatus {
    OnlineIdSystemTicketStatus_Success = 0,
    OnlineIdSystemTicketStatus_Error = 1,
    OnlineIdSystemTicketStatus_ServiceConnectionError = 2
};
#ifdef WIDL_using_Windows_Security_Authentication_OnlineId
#define OnlineIdSystemTicketStatus __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemTicketStatus
#endif /* WIDL_using_Windows_Security_Authentication_OnlineId */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
/*****************************************************************************
 * IOnlineIdServiceTicketRequest interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest, 0x297445d3, 0xfb63, 0x4135, 0x89,0x09, 0x4e,0x35,0x4c,0x06,0x14,0x66);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    MIDL_INTERFACE("297445d3-fb63-4135-8909-4e354c061466")
                    IOnlineIdServiceTicketRequest : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_Service(
                            HSTRING *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_Policy(
                            HSTRING *value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest, 0x297445d3, 0xfb63, 0x4135, 0x89,0x09, 0x4e,0x35,0x4c,0x06,0x14,0x66)
#endif
#else
typedef struct __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest *This,
        TrustLevel *trustLevel);

    /*** IOnlineIdServiceTicketRequest methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Service)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Policy)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest *This,
        HSTRING *value);

    END_INTERFACE
} __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestVtbl;

interface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest {
    CONST_VTBL __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IOnlineIdServiceTicketRequest methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_get_Service(This,value) (This)->lpVtbl->get_Service(This,value)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_get_Policy(This,value) (This)->lpVtbl->get_Policy(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_QueryInterface(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_AddRef(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_Release(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_GetIids(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_GetRuntimeClassName(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_GetTrustLevel(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IOnlineIdServiceTicketRequest methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_get_Service(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest* This,HSTRING *value) {
    return This->lpVtbl->get_Service(This,value);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_get_Policy(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest* This,HSTRING *value) {
    return This->lpVtbl->get_Policy(This,value);
}
#endif
#ifdef WIDL_using_Windows_Security_Authentication_OnlineId
#define IID_IOnlineIdServiceTicketRequest IID___x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest
#define IOnlineIdServiceTicketRequestVtbl __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestVtbl
#define IOnlineIdServiceTicketRequest __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest
#define IOnlineIdServiceTicketRequest_QueryInterface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_QueryInterface
#define IOnlineIdServiceTicketRequest_AddRef __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_AddRef
#define IOnlineIdServiceTicketRequest_Release __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_Release
#define IOnlineIdServiceTicketRequest_GetIids __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_GetIids
#define IOnlineIdServiceTicketRequest_GetRuntimeClassName __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_GetRuntimeClassName
#define IOnlineIdServiceTicketRequest_GetTrustLevel __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_GetTrustLevel
#define IOnlineIdServiceTicketRequest_get_Service __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_get_Service
#define IOnlineIdServiceTicketRequest_get_Policy __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_get_Policy
#endif /* WIDL_using_Windows_Security_Authentication_OnlineId */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IOnlineIdServiceTicketRequestFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory, 0xbebb0a08, 0x9e73, 0x4077, 0x96,0x14, 0x08,0x61,0x4c,0x0b,0xc2,0x45);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    MIDL_INTERFACE("bebb0a08-9e73-4077-9614-08614c0bc245")
                    IOnlineIdServiceTicketRequestFactory : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE CreateOnlineIdServiceTicketRequest(
                            HSTRING service,
                            HSTRING policy,
                            ABI::Windows::Security::Authentication::OnlineId::IOnlineIdServiceTicketRequest **request) = 0;

                        virtual HRESULT STDMETHODCALLTYPE CreateOnlineIdServiceTicketRequestAdvanced(
                            HSTRING service,
                            ABI::Windows::Security::Authentication::OnlineId::IOnlineIdServiceTicketRequest **request) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory, 0xbebb0a08, 0x9e73, 0x4077, 0x96,0x14, 0x08,0x61,0x4c,0x0b,0xc2,0x45)
#endif
#else
typedef struct __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory *This,
        TrustLevel *trustLevel);

    /*** IOnlineIdServiceTicketRequestFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateOnlineIdServiceTicketRequest)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory *This,
        HSTRING service,
        HSTRING policy,
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest **request);

    HRESULT (STDMETHODCALLTYPE *CreateOnlineIdServiceTicketRequestAdvanced)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory *This,
        HSTRING service,
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest **request);

    END_INTERFACE
} __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactoryVtbl;

interface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory {
    CONST_VTBL __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IOnlineIdServiceTicketRequestFactory methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_CreateOnlineIdServiceTicketRequest(This,service,policy,request) (This)->lpVtbl->CreateOnlineIdServiceTicketRequest(This,service,policy,request)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_CreateOnlineIdServiceTicketRequestAdvanced(This,service,request) (This)->lpVtbl->CreateOnlineIdServiceTicketRequestAdvanced(This,service,request)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_QueryInterface(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_AddRef(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_Release(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_GetIids(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_GetRuntimeClassName(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_GetTrustLevel(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IOnlineIdServiceTicketRequestFactory methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_CreateOnlineIdServiceTicketRequest(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory* This,HSTRING service,HSTRING policy,__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest **request) {
    return This->lpVtbl->CreateOnlineIdServiceTicketRequest(This,service,policy,request);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_CreateOnlineIdServiceTicketRequestAdvanced(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory* This,HSTRING service,__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest **request) {
    return This->lpVtbl->CreateOnlineIdServiceTicketRequestAdvanced(This,service,request);
}
#endif
#ifdef WIDL_using_Windows_Security_Authentication_OnlineId
#define IID_IOnlineIdServiceTicketRequestFactory IID___x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory
#define IOnlineIdServiceTicketRequestFactoryVtbl __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactoryVtbl
#define IOnlineIdServiceTicketRequestFactory __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory
#define IOnlineIdServiceTicketRequestFactory_QueryInterface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_QueryInterface
#define IOnlineIdServiceTicketRequestFactory_AddRef __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_AddRef
#define IOnlineIdServiceTicketRequestFactory_Release __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_Release
#define IOnlineIdServiceTicketRequestFactory_GetIids __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_GetIids
#define IOnlineIdServiceTicketRequestFactory_GetRuntimeClassName __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_GetRuntimeClassName
#define IOnlineIdServiceTicketRequestFactory_GetTrustLevel __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_GetTrustLevel
#define IOnlineIdServiceTicketRequestFactory_CreateOnlineIdServiceTicketRequest __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_CreateOnlineIdServiceTicketRequest
#define IOnlineIdServiceTicketRequestFactory_CreateOnlineIdServiceTicketRequestAdvanced __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_CreateOnlineIdServiceTicketRequestAdvanced
#endif /* WIDL_using_Windows_Security_Authentication_OnlineId */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequestFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IOnlineIdSystemAuthenticatorForUser interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser, 0x5798befb, 0x1de4, 0x4186, 0xa2,0xe6, 0xb5,0x63,0xf8,0x6a,0xaf,0x44);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    MIDL_INTERFACE("5798befb-1de4-4186-a2e6-b563f86aaf44")
                    IOnlineIdSystemAuthenticatorForUser : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE GetTicketAsync(
                            ABI::Windows::Security::Authentication::OnlineId::IOnlineIdServiceTicketRequest *request,
                            ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Security::Authentication::OnlineId::OnlineIdSystemTicketResult* > **operation) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_ApplicationId(
                            GUID value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_ApplicationId(
                            GUID *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_User(
                            ABI::Windows::System::IUser **user) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser, 0x5798befb, 0x1de4, 0x4186, 0xa2,0xe6, 0xb5,0x63,0xf8,0x6a,0xaf,0x44)
#endif
#else
typedef struct __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUserVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser *This,
        TrustLevel *trustLevel);

    /*** IOnlineIdSystemAuthenticatorForUser methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTicketAsync)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser *This,
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest *request,
        __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult **operation);

    HRESULT (STDMETHODCALLTYPE *put_ApplicationId)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser *This,
        GUID value);

    HRESULT (STDMETHODCALLTYPE *get_ApplicationId)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser *This,
        GUID *value);

    HRESULT (STDMETHODCALLTYPE *get_User)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser *This,
        __x_ABI_CWindows_CSystem_CIUser **user);

    END_INTERFACE
} __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUserVtbl;

interface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser {
    CONST_VTBL __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUserVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IOnlineIdSystemAuthenticatorForUser methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_GetTicketAsync(This,request,operation) (This)->lpVtbl->GetTicketAsync(This,request,operation)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_put_ApplicationId(This,value) (This)->lpVtbl->put_ApplicationId(This,value)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_get_ApplicationId(This,value) (This)->lpVtbl->get_ApplicationId(This,value)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_get_User(This,user) (This)->lpVtbl->get_User(This,user)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_QueryInterface(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_AddRef(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_Release(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_GetIids(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_GetRuntimeClassName(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_GetTrustLevel(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IOnlineIdSystemAuthenticatorForUser methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_GetTicketAsync(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser* This,__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest *request,__FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult **operation) {
    return This->lpVtbl->GetTicketAsync(This,request,operation);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_put_ApplicationId(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser* This,GUID value) {
    return This->lpVtbl->put_ApplicationId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_get_ApplicationId(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser* This,GUID *value) {
    return This->lpVtbl->get_ApplicationId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_get_User(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser* This,__x_ABI_CWindows_CSystem_CIUser **user) {
    return This->lpVtbl->get_User(This,user);
}
#endif
#ifdef WIDL_using_Windows_Security_Authentication_OnlineId
#define IID_IOnlineIdSystemAuthenticatorForUser IID___x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser
#define IOnlineIdSystemAuthenticatorForUserVtbl __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUserVtbl
#define IOnlineIdSystemAuthenticatorForUser __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser
#define IOnlineIdSystemAuthenticatorForUser_QueryInterface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_QueryInterface
#define IOnlineIdSystemAuthenticatorForUser_AddRef __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_AddRef
#define IOnlineIdSystemAuthenticatorForUser_Release __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_Release
#define IOnlineIdSystemAuthenticatorForUser_GetIids __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_GetIids
#define IOnlineIdSystemAuthenticatorForUser_GetRuntimeClassName __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_GetRuntimeClassName
#define IOnlineIdSystemAuthenticatorForUser_GetTrustLevel __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_GetTrustLevel
#define IOnlineIdSystemAuthenticatorForUser_GetTicketAsync __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_GetTicketAsync
#define IOnlineIdSystemAuthenticatorForUser_put_ApplicationId __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_put_ApplicationId
#define IOnlineIdSystemAuthenticatorForUser_get_ApplicationId __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_get_ApplicationId
#define IOnlineIdSystemAuthenticatorForUser_get_User __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_get_User
#endif /* WIDL_using_Windows_Security_Authentication_OnlineId */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IOnlineIdSystemAuthenticatorStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics, 0x85047792, 0xf634, 0x41e3, 0x96,0xa4, 0x51,0x64,0xe9,0x02,0xc7,0x40);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    MIDL_INTERFACE("85047792-f634-41e3-96a4-5164e902c740")
                    IOnlineIdSystemAuthenticatorStatics : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_Default(
                            ABI::Windows::Security::Authentication::OnlineId::IOnlineIdSystemAuthenticatorForUser **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE GetForUser(
                            ABI::Windows::System::IUser *user,
                            ABI::Windows::Security::Authentication::OnlineId::IOnlineIdSystemAuthenticatorForUser **value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics, 0x85047792, 0xf634, 0x41e3, 0x96,0xa4, 0x51,0x64,0xe9,0x02,0xc7,0x40)
#endif
#else
typedef struct __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics *This,
        TrustLevel *trustLevel);

    /*** IOnlineIdSystemAuthenticatorStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Default)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics *This,
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser **value);

    HRESULT (STDMETHODCALLTYPE *GetForUser)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics *This,
        __x_ABI_CWindows_CSystem_CIUser *user,
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser **value);

    END_INTERFACE
} __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStaticsVtbl;

interface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics {
    CONST_VTBL __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IOnlineIdSystemAuthenticatorStatics methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_get_Default(This,value) (This)->lpVtbl->get_Default(This,value)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_GetForUser(This,user,value) (This)->lpVtbl->GetForUser(This,user,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_QueryInterface(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_AddRef(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_Release(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_GetIids(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_GetRuntimeClassName(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_GetTrustLevel(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IOnlineIdSystemAuthenticatorStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_get_Default(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics* This,__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser **value) {
    return This->lpVtbl->get_Default(This,value);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_GetForUser(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics* This,__x_ABI_CWindows_CSystem_CIUser *user,__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorForUser **value) {
    return This->lpVtbl->GetForUser(This,user,value);
}
#endif
#ifdef WIDL_using_Windows_Security_Authentication_OnlineId
#define IID_IOnlineIdSystemAuthenticatorStatics IID___x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics
#define IOnlineIdSystemAuthenticatorStaticsVtbl __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStaticsVtbl
#define IOnlineIdSystemAuthenticatorStatics __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics
#define IOnlineIdSystemAuthenticatorStatics_QueryInterface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_QueryInterface
#define IOnlineIdSystemAuthenticatorStatics_AddRef __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_AddRef
#define IOnlineIdSystemAuthenticatorStatics_Release __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_Release
#define IOnlineIdSystemAuthenticatorStatics_GetIids __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_GetIids
#define IOnlineIdSystemAuthenticatorStatics_GetRuntimeClassName __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_GetRuntimeClassName
#define IOnlineIdSystemAuthenticatorStatics_GetTrustLevel __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_GetTrustLevel
#define IOnlineIdSystemAuthenticatorStatics_get_Default __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_get_Default
#define IOnlineIdSystemAuthenticatorStatics_GetForUser __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_GetForUser
#endif /* WIDL_using_Windows_Security_Authentication_OnlineId */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemAuthenticatorStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IOnlineIdSystemTicketResult interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult, 0xdb0a5ff8, 0xb098, 0x4acd, 0x9d,0x13, 0x9e,0x64,0x06,0x52,0xb5,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authentication {
                namespace OnlineId {
                    MIDL_INTERFACE("db0a5ff8-b098-4acd-9d13-9e640652b5b6")
                    IOnlineIdSystemTicketResult : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_Identity(
                            ABI::Windows::Security::Authentication::OnlineId::IOnlineIdSystemIdentity **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_Status(
                            ABI::Windows::Security::Authentication::OnlineId::OnlineIdSystemTicketStatus *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_ExtendedError(
                            HRESULT *value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult, 0xdb0a5ff8, 0xb098, 0x4acd, 0x9d,0x13, 0x9e,0x64,0x06,0x52,0xb5,0xb6)
#endif
#else
typedef struct __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult *This,
        TrustLevel *trustLevel);

    /*** IOnlineIdSystemTicketResult methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Identity)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult *This,
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemIdentity **value);

    HRESULT (STDMETHODCALLTYPE *get_Status)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult *This,
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemTicketStatus *value);

    HRESULT (STDMETHODCALLTYPE *get_ExtendedError)(
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult *This,
        HRESULT *value);

    END_INTERFACE
} __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResultVtbl;

interface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult {
    CONST_VTBL __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IOnlineIdSystemTicketResult methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_get_Identity(This,value) (This)->lpVtbl->get_Identity(This,value)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_get_Status(This,value) (This)->lpVtbl->get_Status(This,value)
#define __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_get_ExtendedError(This,value) (This)->lpVtbl->get_ExtendedError(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_QueryInterface(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_AddRef(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_Release(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_GetIids(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_GetRuntimeClassName(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_GetTrustLevel(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IOnlineIdSystemTicketResult methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_get_Identity(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult* This,__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemIdentity **value) {
    return This->lpVtbl->get_Identity(This,value);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_get_Status(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult* This,__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_COnlineIdSystemTicketStatus *value) {
    return This->lpVtbl->get_Status(This,value);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_get_ExtendedError(__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult* This,HRESULT *value) {
    return This->lpVtbl->get_ExtendedError(This,value);
}
#endif
#ifdef WIDL_using_Windows_Security_Authentication_OnlineId
#define IID_IOnlineIdSystemTicketResult IID___x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult
#define IOnlineIdSystemTicketResultVtbl __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResultVtbl
#define IOnlineIdSystemTicketResult __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult
#define IOnlineIdSystemTicketResult_QueryInterface __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_QueryInterface
#define IOnlineIdSystemTicketResult_AddRef __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_AddRef
#define IOnlineIdSystemTicketResult_Release __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_Release
#define IOnlineIdSystemTicketResult_GetIids __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_GetIids
#define IOnlineIdSystemTicketResult_GetRuntimeClassName __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_GetRuntimeClassName
#define IOnlineIdSystemTicketResult_GetTrustLevel __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_GetTrustLevel
#define IOnlineIdSystemTicketResult_get_Identity __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_get_Identity
#define IOnlineIdSystemTicketResult_get_Status __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_get_Status
#define IOnlineIdSystemTicketResult_get_ExtendedError __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_get_ExtendedError
#endif /* WIDL_using_Windows_Security_Authentication_OnlineId */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*
 * Class Windows.Security.Authentication.OnlineId.OnlineIdServiceTicketRequest
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Security_Authentication_OnlineId_OnlineIdServiceTicketRequest_DEFINED
#define RUNTIMECLASS_Windows_Security_Authentication_OnlineId_OnlineIdServiceTicketRequest_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Security_Authentication_OnlineId_OnlineIdServiceTicketRequest[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','A','u','t','h','e','n','t','i','c','a','t','i','o','n','.','O','n','l','i','n','e','I','d','.','O','n','l','i','n','e','I','d','S','e','r','v','i','c','e','T','i','c','k','e','t','R','e','q','u','e','s','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Authentication_OnlineId_OnlineIdServiceTicketRequest[] = L"Windows.Security.Authentication.OnlineId.OnlineIdServiceTicketRequest";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Authentication_OnlineId_OnlineIdServiceTicketRequest[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','A','u','t','h','e','n','t','i','c','a','t','i','o','n','.','O','n','l','i','n','e','I','d','.','O','n','l','i','n','e','I','d','S','e','r','v','i','c','e','T','i','c','k','e','t','R','e','q','u','e','s','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Security_Authentication_OnlineId_OnlineIdServiceTicketRequest_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Security.Authentication.OnlineId.OnlineIdSystemAuthenticator
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef RUNTIMECLASS_Windows_Security_Authentication_OnlineId_OnlineIdSystemAuthenticator_DEFINED
#define RUNTIMECLASS_Windows_Security_Authentication_OnlineId_OnlineIdSystemAuthenticator_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Security_Authentication_OnlineId_OnlineIdSystemAuthenticator[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','A','u','t','h','e','n','t','i','c','a','t','i','o','n','.','O','n','l','i','n','e','I','d','.','O','n','l','i','n','e','I','d','S','y','s','t','e','m','A','u','t','h','e','n','t','i','c','a','t','o','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Authentication_OnlineId_OnlineIdSystemAuthenticator[] = L"Windows.Security.Authentication.OnlineId.OnlineIdSystemAuthenticator";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Authentication_OnlineId_OnlineIdSystemAuthenticator[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','A','u','t','h','e','n','t','i','c','a','t','i','o','n','.','O','n','l','i','n','e','I','d','.','O','n','l','i','n','e','I','d','S','y','s','t','e','m','A','u','t','h','e','n','t','i','c','a','t','o','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Security_Authentication_OnlineId_OnlineIdSystemAuthenticator_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*
 * Class Windows.Security.Authentication.OnlineId.OnlineIdSystemAuthenticatorForUser
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef RUNTIMECLASS_Windows_Security_Authentication_OnlineId_OnlineIdSystemAuthenticatorForUser_DEFINED
#define RUNTIMECLASS_Windows_Security_Authentication_OnlineId_OnlineIdSystemAuthenticatorForUser_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Security_Authentication_OnlineId_OnlineIdSystemAuthenticatorForUser[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','A','u','t','h','e','n','t','i','c','a','t','i','o','n','.','O','n','l','i','n','e','I','d','.','O','n','l','i','n','e','I','d','S','y','s','t','e','m','A','u','t','h','e','n','t','i','c','a','t','o','r','F','o','r','U','s','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Authentication_OnlineId_OnlineIdSystemAuthenticatorForUser[] = L"Windows.Security.Authentication.OnlineId.OnlineIdSystemAuthenticatorForUser";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Authentication_OnlineId_OnlineIdSystemAuthenticatorForUser[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','A','u','t','h','e','n','t','i','c','a','t','i','o','n','.','O','n','l','i','n','e','I','d','.','O','n','l','i','n','e','I','d','S','y','s','t','e','m','A','u','t','h','e','n','t','i','c','a','t','o','r','F','o','r','U','s','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Security_Authentication_OnlineId_OnlineIdSystemAuthenticatorForUser_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*
 * Class Windows.Security.Authentication.OnlineId.OnlineIdSystemIdentity
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef RUNTIMECLASS_Windows_Security_Authentication_OnlineId_OnlineIdSystemIdentity_DEFINED
#define RUNTIMECLASS_Windows_Security_Authentication_OnlineId_OnlineIdSystemIdentity_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Security_Authentication_OnlineId_OnlineIdSystemIdentity[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','A','u','t','h','e','n','t','i','c','a','t','i','o','n','.','O','n','l','i','n','e','I','d','.','O','n','l','i','n','e','I','d','S','y','s','t','e','m','I','d','e','n','t','i','t','y',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Authentication_OnlineId_OnlineIdSystemIdentity[] = L"Windows.Security.Authentication.OnlineId.OnlineIdSystemIdentity";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Authentication_OnlineId_OnlineIdSystemIdentity[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','A','u','t','h','e','n','t','i','c','a','t','i','o','n','.','O','n','l','i','n','e','I','d','.','O','n','l','i','n','e','I','d','S','y','s','t','e','m','I','d','e','n','t','i','t','y',0};
#endif
#endif /* RUNTIMECLASS_Windows_Security_Authentication_OnlineId_OnlineIdSystemIdentity_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*
 * Class Windows.Security.Authentication.OnlineId.OnlineIdSystemTicketResult
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef RUNTIMECLASS_Windows_Security_Authentication_OnlineId_OnlineIdSystemTicketResult_DEFINED
#define RUNTIMECLASS_Windows_Security_Authentication_OnlineId_OnlineIdSystemTicketResult_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Security_Authentication_OnlineId_OnlineIdSystemTicketResult[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','A','u','t','h','e','n','t','i','c','a','t','i','o','n','.','O','n','l','i','n','e','I','d','.','O','n','l','i','n','e','I','d','S','y','s','t','e','m','T','i','c','k','e','t','R','e','s','u','l','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Authentication_OnlineId_OnlineIdSystemTicketResult[] = L"Windows.Security.Authentication.OnlineId.OnlineIdSystemTicketResult";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Authentication_OnlineId_OnlineIdSystemTicketResult[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','A','u','t','h','e','n','t','i','c','a','t','i','o','n','.','O','n','l','i','n','e','I','d','.','O','n','l','i','n','e','I','d','S','y','s','t','e','m','T','i','c','k','e','t','R','e','s','u','l','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Security_Authentication_OnlineId_OnlineIdSystemTicketResult_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IIterable<ABI::Windows::Security::Authentication::OnlineId::OnlineIdServiceTicketRequest* > interface
 */
#ifndef ____FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest, 0xcb72d686, 0x9516, 0x520d, 0xa2,0x74, 0xfa,0x4c,0xd1,0x76,0x2c,0xb2);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("cb72d686-9516-520d-a274-fa4cd1762cb2")
                IIterable<ABI::Windows::Security::Authentication::OnlineId::OnlineIdServiceTicketRequest* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Security::Authentication::OnlineId::OnlineIdServiceTicketRequest*, ABI::Windows::Security::Authentication::OnlineId::IOnlineIdServiceTicketRequest* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest, 0xcb72d686, 0x9516, 0x520d, 0xa2,0x74, 0xfa,0x4c,0xd1,0x76,0x2c,0xb2)
#endif
#else
typedef struct __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequestVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Security::Authentication::OnlineId::OnlineIdServiceTicketRequest* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest *This,
        __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest **value);

    END_INTERFACE
} __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequestVtbl;

interface __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest {
    CONST_VTBL __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequestVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Security::Authentication::OnlineId::OnlineIdServiceTicketRequest* > methods ***/
#define __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_QueryInterface(__FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_AddRef(__FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_Release(__FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetIids(__FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetRuntimeClassName(__FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetTrustLevel(__FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Security::Authentication::OnlineId::OnlineIdServiceTicketRequest* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_First(__FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest* This,__FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_OnlineIdServiceTicketRequest IID___FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest
#define IIterable_OnlineIdServiceTicketRequestVtbl __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequestVtbl
#define IIterable_OnlineIdServiceTicketRequest __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest
#define IIterable_OnlineIdServiceTicketRequest_QueryInterface __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_QueryInterface
#define IIterable_OnlineIdServiceTicketRequest_AddRef __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_AddRef
#define IIterable_OnlineIdServiceTicketRequest_Release __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_Release
#define IIterable_OnlineIdServiceTicketRequest_GetIids __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetIids
#define IIterable_OnlineIdServiceTicketRequest_GetRuntimeClassName __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetRuntimeClassName
#define IIterable_OnlineIdServiceTicketRequest_GetTrustLevel __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetTrustLevel
#define IIterable_OnlineIdServiceTicketRequest_First __FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Security::Authentication::OnlineId::OnlineIdServiceTicketRequest* > interface
 */
#ifndef ____FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest, 0xb6a5c8e4, 0x6e3c, 0x5c37, 0x92,0xcf, 0xcf,0x9f,0x1c,0x38,0x33,0x35);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("b6a5c8e4-6e3c-5c37-92cf-cf9f1c383335")
                IIterator<ABI::Windows::Security::Authentication::OnlineId::OnlineIdServiceTicketRequest* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Security::Authentication::OnlineId::OnlineIdServiceTicketRequest*, ABI::Windows::Security::Authentication::OnlineId::IOnlineIdServiceTicketRequest* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest, 0xb6a5c8e4, 0x6e3c, 0x5c37, 0x92,0xcf, 0xcf,0x9f,0x1c,0x38,0x33,0x35)
#endif
#else
typedef struct __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequestVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Security::Authentication::OnlineId::OnlineIdServiceTicketRequest* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest *This,
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest *This,
        UINT32 items_size,
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequestVtbl;

interface __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest {
    CONST_VTBL __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequestVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Security::Authentication::OnlineId::OnlineIdServiceTicketRequest* > methods ***/
#define __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_QueryInterface(__FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_AddRef(__FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_Release(__FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetIids(__FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetRuntimeClassName(__FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetTrustLevel(__FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Security::Authentication::OnlineId::OnlineIdServiceTicketRequest* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_get_Current(__FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest* This,__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_get_HasCurrent(__FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_MoveNext(__FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetMany(__FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest* This,UINT32 items_size,__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdServiceTicketRequest **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_OnlineIdServiceTicketRequest IID___FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest
#define IIterator_OnlineIdServiceTicketRequestVtbl __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequestVtbl
#define IIterator_OnlineIdServiceTicketRequest __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest
#define IIterator_OnlineIdServiceTicketRequest_QueryInterface __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_QueryInterface
#define IIterator_OnlineIdServiceTicketRequest_AddRef __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_AddRef
#define IIterator_OnlineIdServiceTicketRequest_Release __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_Release
#define IIterator_OnlineIdServiceTicketRequest_GetIids __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetIids
#define IIterator_OnlineIdServiceTicketRequest_GetRuntimeClassName __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetRuntimeClassName
#define IIterator_OnlineIdServiceTicketRequest_GetTrustLevel __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetTrustLevel
#define IIterator_OnlineIdServiceTicketRequest_get_Current __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_get_Current
#define IIterator_OnlineIdServiceTicketRequest_get_HasCurrent __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_get_HasCurrent
#define IIterator_OnlineIdServiceTicketRequest_MoveNext __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_MoveNext
#define IIterator_OnlineIdServiceTicketRequest_GetMany __FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdServiceTicketRequest_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Security::Authentication::OnlineId::OnlineIdSystemTicketResult* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult, 0x05f9f2ec, 0x5950, 0x56f8, 0xb7,0xf8, 0x22,0xe2,0x0b,0x98,0x46,0x79);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("05f9f2ec-5950-56f8-b7f8-22e20b984679")
            IAsyncOperationCompletedHandler<ABI::Windows::Security::Authentication::OnlineId::OnlineIdSystemTicketResult* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Security::Authentication::OnlineId::OnlineIdSystemTicketResult*, ABI::Windows::Security::Authentication::OnlineId::IOnlineIdSystemTicketResult* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult, 0x05f9f2ec, 0x5950, 0x56f8, 0xb7,0xf8, 0x22,0xe2,0x0b,0x98,0x46,0x79)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Security::Authentication::OnlineId::OnlineIdSystemTicketResult* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult *This,
        __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResultVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Security::Authentication::OnlineId::OnlineIdSystemTicketResult* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_Release(__FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Security::Authentication::OnlineId::OnlineIdSystemTicketResult* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult* This,__FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_OnlineIdSystemTicketResult IID___FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult
#define IAsyncOperationCompletedHandler_OnlineIdSystemTicketResultVtbl __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResultVtbl
#define IAsyncOperationCompletedHandler_OnlineIdSystemTicketResult __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult
#define IAsyncOperationCompletedHandler_OnlineIdSystemTicketResult_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_QueryInterface
#define IAsyncOperationCompletedHandler_OnlineIdSystemTicketResult_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_AddRef
#define IAsyncOperationCompletedHandler_OnlineIdSystemTicketResult_Release __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_Release
#define IAsyncOperationCompletedHandler_OnlineIdSystemTicketResult_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Security::Authentication::OnlineId::OnlineIdSystemTicketResult* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult, 0x162f5870, 0x5a4a, 0x503c, 0x98,0x7f, 0xa0,0x5a,0x13,0x12,0xd8,0xe4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("162f5870-5a4a-503c-987f-a05a1312d8e4")
            IAsyncOperation<ABI::Windows::Security::Authentication::OnlineId::OnlineIdSystemTicketResult* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Security::Authentication::OnlineId::OnlineIdSystemTicketResult*, ABI::Windows::Security::Authentication::OnlineId::IOnlineIdSystemTicketResult* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult, 0x162f5870, 0x5a4a, 0x503c, 0x98,0x7f, 0xa0,0x5a,0x13,0x12,0xd8,0xe4)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Security::Authentication::OnlineId::OnlineIdSystemTicketResult* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult *This,
        __x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResultVtbl;

interface __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult {
    CONST_VTBL __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Security::Authentication::OnlineId::OnlineIdSystemTicketResult* > methods ***/
#define __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_QueryInterface(__FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_AddRef(__FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_Release(__FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_GetIids(__FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_GetTrustLevel(__FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Security::Authentication::OnlineId::OnlineIdSystemTicketResult* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_put_Completed(__FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult* This,__FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_get_Completed(__FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult* This,__FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_GetResults(__FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult* This,__x_ABI_CWindows_CSecurity_CAuthentication_COnlineId_CIOnlineIdSystemTicketResult **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_OnlineIdSystemTicketResult IID___FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult
#define IAsyncOperation_OnlineIdSystemTicketResultVtbl __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResultVtbl
#define IAsyncOperation_OnlineIdSystemTicketResult __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult
#define IAsyncOperation_OnlineIdSystemTicketResult_QueryInterface __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_QueryInterface
#define IAsyncOperation_OnlineIdSystemTicketResult_AddRef __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_AddRef
#define IAsyncOperation_OnlineIdSystemTicketResult_Release __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_Release
#define IAsyncOperation_OnlineIdSystemTicketResult_GetIids __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_GetIids
#define IAsyncOperation_OnlineIdSystemTicketResult_GetRuntimeClassName __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_GetRuntimeClassName
#define IAsyncOperation_OnlineIdSystemTicketResult_GetTrustLevel __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_GetTrustLevel
#define IAsyncOperation_OnlineIdSystemTicketResult_put_Completed __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_put_Completed
#define IAsyncOperation_OnlineIdSystemTicketResult_get_Completed __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_get_Completed
#define IAsyncOperation_OnlineIdSystemTicketResult_GetResults __FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CSecurity__CAuthentication__COnlineId__COnlineIdSystemTicketResult_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_security_authentication_onlineid_h__ */
