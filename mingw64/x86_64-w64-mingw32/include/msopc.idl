/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

import "oaidl.idl";
import "ocidl.idl";
import "urlmon.idl";
import "wincrypt.idl";

cpp_quote("#include <winapifamily.h>")

cpp_quote("#if NTDDI_VERSION >= 0x06010000")
interface IOpcUri;
interface IOpcPartUri;

[object, uuid (bc9c1b9b-d62c-49eb-aef0-3b4e0b28ebed), nonextensible, pointer_default (ref)]
interface IOpcUri : IUri {
  HRESULT GetRelationshipsPartUri ([out, retval] IOpcPartUri **relationshipPartUri);
  HRESULT GetRelativeUri ([in] IOpcPartUri *targetPartUri,[out, retval] IUri **relativeUri);
  HRESULT CombinePartUri ([in] IUri *relativeUri,[out, retval] IOpcPartUri **combinedUri);
};

[object, uuid (7d3babe7-88b2-46ba-85cb-4203cb016c87), nonextensible, pointer_default (ref)]
interface IOpcPartUri : IOpcUri {
  HRESULT ComparePartUri ([in] IOpcPartUri *partUri,[out, retval] INT32 *comparisonResult);
  HRESULT GetSourceUri ([out, retval] IOpcUri **sourceUri);
  HRESULT IsRelationshipsPartUri ([out, retval] BOOL *isRelationshipUri);
};

cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
cpp_quote("#define IS_OPC_CONFORMANCE_ERROR(x) (((x) & 0x1ffff000) == (0x0000 + (FACILITY_OPC << 16)))")
cpp_quote("#define IS_ZIP_CONFORMANCE_ERROR(x) (((x) & 0x1ffff000) == (0x1000 + (FACILITY_OPC << 16)))")

cpp_quote("#define OPC_E_NONCONFORMING_URI MAKE_HRESULT(1, FACILITY_OPC, 0x1)")
cpp_quote("#define OPC_E_RELATIVE_URI_REQUIRED MAKE_HRESULT(1, FACILITY_OPC, 0x2)")
cpp_quote("#define OPC_E_RELATIONSHIP_URI_REQUIRED MAKE_HRESULT(1, FACILITY_OPC, 0x3)")
cpp_quote("#define OPC_E_PART_CANNOT_BE_DIRECTORY MAKE_HRESULT(1, FACILITY_OPC, 0x4)")
cpp_quote("#define OPC_E_UNEXPECTED_CONTENT_TYPE MAKE_HRESULT(1, FACILITY_OPC, 0x5)")
cpp_quote("#define OPC_E_INVALID_CONTENT_TYPE_XML MAKE_HRESULT(1, FACILITY_OPC, 0x6)")
cpp_quote("#define OPC_E_MISSING_CONTENT_TYPES MAKE_HRESULT(1, FACILITY_OPC, 0x7)")
cpp_quote("#define OPC_E_NONCONFORMING_CONTENT_TYPES_XML MAKE_HRESULT(1, FACILITY_OPC, 0x8)")
cpp_quote("#define OPC_E_NONCONFORMING_RELS_XML MAKE_HRESULT(1, FACILITY_OPC, 0x9)")
cpp_quote("#define OPC_E_INVALID_RELS_XML MAKE_HRESULT(1, FACILITY_OPC, 0xa)")
cpp_quote("#define OPC_E_DUPLICATE_PART MAKE_HRESULT(1, FACILITY_OPC, 0xb)")
cpp_quote("#define OPC_E_INVALID_OVERRIDE_PART_NAME MAKE_HRESULT(1, FACILITY_OPC, 0xc)")
cpp_quote("#define OPC_E_DUPLICATE_OVERRIDE_PART MAKE_HRESULT(1, FACILITY_OPC, 0xd)")
cpp_quote("#define OPC_E_INVALID_DEFAULT_EXTENSION MAKE_HRESULT(1, FACILITY_OPC, 0xe)")
cpp_quote("#define OPC_E_DUPLICATE_DEFAULT_EXTENSION MAKE_HRESULT(1, FACILITY_OPC, 0xf)")
cpp_quote("#define OPC_E_INVALID_RELATIONSHIP_ID MAKE_HRESULT(1, FACILITY_OPC, 0x10)")
cpp_quote("#define OPC_E_INVALID_RELATIONSHIP_TYPE MAKE_HRESULT(1, FACILITY_OPC, 0x11)")
cpp_quote("#define OPC_E_INVALID_RELATIONSHIP_TARGET MAKE_HRESULT(1, FACILITY_OPC, 0x12)")
cpp_quote("#define OPC_E_DUPLICATE_RELATIONSHIP MAKE_HRESULT(1, FACILITY_OPC, 0x13)")
cpp_quote("#define OPC_E_CONFLICTING_SETTINGS MAKE_HRESULT(1, FACILITY_OPC, 0x14)")
cpp_quote("#define OPC_E_DUPLICATE_PIECE MAKE_HRESULT(1, FACILITY_OPC, 0x15)")
cpp_quote("#define OPC_E_INVALID_PIECE MAKE_HRESULT(1, FACILITY_OPC, 0x16)")
cpp_quote("#define OPC_E_MISSING_PIECE MAKE_HRESULT(1, FACILITY_OPC, 0x17)")
cpp_quote("#define OPC_E_NO_SUCH_PART MAKE_HRESULT(1, FACILITY_OPC, 0x18)")
cpp_quote("#define OPC_E_DS_SIGNATURE_CORRUPT MAKE_HRESULT(1, FACILITY_OPC, 0x19)")
cpp_quote("#define OPC_E_DS_DIGEST_VALUE_ERROR MAKE_HRESULT(1, FACILITY_OPC, 0x1a)")
cpp_quote("#define OPC_E_DS_DUPLICATE_SIGNATURE_ORIGIN_RELATIONSHIP MAKE_HRESULT(1, FACILITY_OPC, 0x1b)")
cpp_quote("#define OPC_E_DS_INVALID_SIGNATURE_ORIGIN_RELATIONSHIP MAKE_HRESULT(1, FACILITY_OPC, 0x1c)")
cpp_quote("#define OPC_E_DS_INVALID_CERTIFICATE_RELATIONSHIP MAKE_HRESULT(1, FACILITY_OPC, 0x1d)")
cpp_quote("#define OPC_E_DS_EXTERNAL_SIGNATURE MAKE_HRESULT(1, FACILITY_OPC, 0x1e)")
cpp_quote("#define OPC_E_DS_MISSING_SIGNATURE_ORIGIN_PART MAKE_HRESULT(1, FACILITY_OPC, 0x1f)")
cpp_quote("#define OPC_E_DS_MISSING_SIGNATURE_PART MAKE_HRESULT(1, FACILITY_OPC, 0x20)")
cpp_quote("#define OPC_E_DS_INVALID_RELATIONSHIP_TRANSFORM_XML MAKE_HRESULT(1, FACILITY_OPC, 0x21)")
cpp_quote("#define OPC_E_DS_INVALID_CANONICALIZATION_METHOD MAKE_HRESULT(1, FACILITY_OPC, 0x22)")
cpp_quote("#define OPC_E_DS_INVALID_RELATIONSHIPS_SIGNING_OPTION MAKE_HRESULT(1, FACILITY_OPC, 0x23)")
cpp_quote("#define OPC_E_DS_INVALID_OPC_SIGNATURE_TIME_FORMAT MAKE_HRESULT(1, FACILITY_OPC, 0x24)")
cpp_quote("#define OPC_E_DS_PACKAGE_REFERENCE_URI_RESERVED MAKE_HRESULT(1, FACILITY_OPC, 0x25)")
cpp_quote("#define OPC_E_DS_MISSING_SIGNATURE_PROPERTIES_ELEMENT MAKE_HRESULT(1, FACILITY_OPC, 0x26)")
cpp_quote("#define OPC_E_DS_MISSING_SIGNATURE_PROPERTY_ELEMENT MAKE_HRESULT(1, FACILITY_OPC, 0x27)")
cpp_quote("#define OPC_E_DS_DUPLICATE_SIGNATURE_PROPERTY_ELEMENT MAKE_HRESULT(1, FACILITY_OPC, 0x28)")
cpp_quote("#define OPC_E_DS_MISSING_SIGNATURE_TIME_PROPERTY MAKE_HRESULT(1, FACILITY_OPC, 0x29)")
cpp_quote("#define OPC_E_DS_INVALID_SIGNATURE_XML MAKE_HRESULT(1, FACILITY_OPC, 0x2a)")
cpp_quote("#define OPC_E_DS_INVALID_SIGNATURE_COUNT MAKE_HRESULT(1, FACILITY_OPC, 0x2b)")
cpp_quote("#define OPC_E_DS_MISSING_SIGNATURE_ALGORITHM MAKE_HRESULT(1, FACILITY_OPC, 0x2c)")
cpp_quote("#define OPC_E_DS_DUPLICATE_PACKAGE_OBJECT_REFERENCES MAKE_HRESULT(1, FACILITY_OPC, 0x2d)")
cpp_quote("#define OPC_E_DS_MISSING_PACKAGE_OBJECT_REFERENCE MAKE_HRESULT(1, FACILITY_OPC, 0x2e)")
cpp_quote("#define OPC_E_DS_EXTERNAL_SIGNATURE_REFERENCE MAKE_HRESULT(1, FACILITY_OPC, 0x2f)")
cpp_quote("#define OPC_E_DS_REFERENCE_MISSING_CONTENT_TYPE MAKE_HRESULT(1, FACILITY_OPC, 0x30)")
cpp_quote("#define OPC_E_DS_MULTIPLE_RELATIONSHIP_TRANSFORMS MAKE_HRESULT(1, FACILITY_OPC, 0x31)")
cpp_quote("#define OPC_E_DS_MISSING_CANONICALIZATION_TRANSFORM MAKE_HRESULT(1, FACILITY_OPC, 0x32)")
cpp_quote("#define OPC_E_MC_UNEXPECTED_ELEMENT MAKE_HRESULT(1, FACILITY_OPC, 0x33)")
cpp_quote("#define OPC_E_MC_UNEXPECTED_REQUIRES_ATTR MAKE_HRESULT(1, FACILITY_OPC, 0x34)")
cpp_quote("#define OPC_E_MC_MISSING_REQUIRES_ATTR MAKE_HRESULT(1, FACILITY_OPC, 0x35)")
cpp_quote("#define OPC_E_MC_UNEXPECTED_ATTR MAKE_HRESULT(1, FACILITY_OPC, 0x36)")
cpp_quote("#define OPC_E_MC_INVALID_PREFIX_LIST MAKE_HRESULT(1, FACILITY_OPC, 0x37)")
cpp_quote("#define OPC_E_MC_INVALID_QNAME_LIST MAKE_HRESULT(1, FACILITY_OPC, 0x38)")
cpp_quote("#define OPC_E_MC_NESTED_ALTERNATE_CONTENT MAKE_HRESULT(1, FACILITY_OPC, 0x39)")
cpp_quote("#define OPC_E_MC_UNEXPECTED_CHOICE MAKE_HRESULT(1, FACILITY_OPC, 0x3a)")
cpp_quote("#define OPC_E_MC_MISSING_CHOICE MAKE_HRESULT(1, FACILITY_OPC, 0x3b)")
cpp_quote("#define OPC_E_MC_INVALID_ENUM_TYPE MAKE_HRESULT(1, FACILITY_OPC, 0x3c)")
cpp_quote("#define OPC_E_MC_UNKNOWN_NAMESPACE MAKE_HRESULT(1, FACILITY_OPC, 0x3e)")
cpp_quote("#define OPC_E_MC_UNKNOWN_PREFIX MAKE_HRESULT(1, FACILITY_OPC, 0x3f)")
cpp_quote("#define OPC_E_MC_INVALID_ATTRIBUTES_ON_IGNORABLE_ELEMENT MAKE_HRESULT(1, FACILITY_OPC, 0x40)")
cpp_quote("#define OPC_E_MC_INVALID_XMLNS_ATTRIBUTE MAKE_HRESULT(1, FACILITY_OPC, 0x41)")
cpp_quote("#define OPC_E_INVALID_XML_ENCODING MAKE_HRESULT(1, FACILITY_OPC, 0x42)")
cpp_quote("#define OPC_E_DS_SIGNATURE_REFERENCE_MISSING_URI MAKE_HRESULT(1, FACILITY_OPC, 0x43)")
cpp_quote("#define OPC_E_INVALID_CONTENT_TYPE MAKE_HRESULT(1, FACILITY_OPC, 0x44)")
cpp_quote("#define OPC_E_DS_SIGNATURE_PROPERTY_MISSING_TARGET MAKE_HRESULT(1, FACILITY_OPC, 0x45)")
cpp_quote("#define OPC_E_DS_SIGNATURE_METHOD_NOT_SET MAKE_HRESULT(1, FACILITY_OPC, 0x46)")
cpp_quote("#define OPC_E_DS_DEFAULT_DIGEST_METHOD_NOT_SET MAKE_HRESULT(1, FACILITY_OPC, 0x47)")
cpp_quote("#define OPC_E_NO_SUCH_RELATIONSHIP MAKE_HRESULT(1, FACILITY_OPC, 0x48)")
cpp_quote("#define OPC_E_MC_MULTIPLE_FALLBACK_ELEMENTS MAKE_HRESULT(1, FACILITY_OPC, 0x49)")
cpp_quote("#define OPC_E_MC_INCONSISTENT_PROCESS_CONTENT MAKE_HRESULT(1, FACILITY_OPC, 0x4a)")
cpp_quote("#define OPC_E_MC_INCONSISTENT_PRESERVE_ATTRIBUTES MAKE_HRESULT(1, FACILITY_OPC, 0x4b)")
cpp_quote("#define OPC_E_MC_INCONSISTENT_PRESERVE_ELEMENTS MAKE_HRESULT(1, FACILITY_OPC, 0x4c)")
cpp_quote("#define OPC_E_INVALID_RELATIONSHIP_TARGET_MODE MAKE_HRESULT(1, FACILITY_OPC, 0x4d)")
cpp_quote("#define OPC_E_COULD_NOT_RECOVER MAKE_HRESULT(1, FACILITY_OPC, 0x4e)")
cpp_quote("#define OPC_E_UNSUPPORTED_PACKAGE MAKE_HRESULT(1, FACILITY_OPC, 0x4f)")
cpp_quote("#define OPC_E_ENUM_COLLECTION_CHANGED MAKE_HRESULT(1, FACILITY_OPC, 0x50)")
cpp_quote("#define OPC_E_ENUM_CANNOT_MOVE_NEXT MAKE_HRESULT(1, FACILITY_OPC, 0x51)")
cpp_quote("#define OPC_E_ENUM_CANNOT_MOVE_PREVIOUS MAKE_HRESULT(1, FACILITY_OPC, 0x52)")
cpp_quote("#define OPC_E_ENUM_INVALID_POSITION MAKE_HRESULT(1, FACILITY_OPC, 0x53)")
cpp_quote("#define OPC_E_DS_SIGNATURE_ORIGIN_EXISTS MAKE_HRESULT(1, FACILITY_OPC, 0x54)")
cpp_quote("#define OPC_E_DS_UNSIGNED_PACKAGE MAKE_HRESULT(1, FACILITY_OPC, 0x55)")
cpp_quote("#define OPC_E_DS_MISSING_CERTIFICATE_PART MAKE_HRESULT(1, FACILITY_OPC, 0x56)")
cpp_quote("#define OPC_E_NO_SUCH_SETTINGS MAKE_HRESULT(1, FACILITY_OPC, 0x57)")
cpp_quote("#define OPC_E_ZIP_INCORRECT_DATA_SIZE MAKE_HRESULT(1, FACILITY_OPC, 0x1001)")
cpp_quote("#define OPC_E_ZIP_CORRUPTED_ARCHIVE MAKE_HRESULT(1, FACILITY_OPC, 0x1002)")
cpp_quote("#define OPC_E_ZIP_COMPRESSION_FAILED MAKE_HRESULT(1, FACILITY_OPC, 0x1003)")
cpp_quote("#define OPC_E_ZIP_DECOMPRESSION_FAILED MAKE_HRESULT(1, FACILITY_OPC, 0x1004)")
cpp_quote("#define OPC_E_ZIP_INCONSISTENT_FILEITEM MAKE_HRESULT(1, FACILITY_OPC, 0x1005)")
cpp_quote("#define OPC_E_ZIP_INCONSISTENT_DIRECTORY MAKE_HRESULT(1, FACILITY_OPC, 0x1006)")
cpp_quote("#define OPC_E_ZIP_MISSING_DATA_DESCRIPTOR MAKE_HRESULT(1, FACILITY_OPC, 0x1007)")
cpp_quote("#define OPC_E_ZIP_UNSUPPORTEDARCHIVE MAKE_HRESULT(1, FACILITY_OPC, 0x1008)")
cpp_quote("#define OPC_E_ZIP_CENTRAL_DIRECTORY_TOO_LARGE MAKE_HRESULT(1, FACILITY_OPC, 0x1009)")
cpp_quote("#define OPC_E_ZIP_NAME_TOO_LARGE MAKE_HRESULT(1, FACILITY_OPC, 0x100a)")
cpp_quote("#define OPC_E_ZIP_DUPLICATE_NAME MAKE_HRESULT(1, FACILITY_OPC, 0x100b)")
cpp_quote("#define OPC_E_ZIP_COMMENT_TOO_LARGE MAKE_HRESULT(1, FACILITY_OPC, 0x100c)")
cpp_quote("#define OPC_E_ZIP_EXTRA_FIELDS_TOO_LARGE MAKE_HRESULT(1, FACILITY_OPC, 0x100d)")
cpp_quote("#define OPC_E_ZIP_FILE_HEADER_TOO_LARGE MAKE_HRESULT(1, FACILITY_OPC, 0x100e)")
cpp_quote("#define OPC_E_ZIP_MISSING_END_OF_CENTRAL_DIRECTORY MAKE_HRESULT(1, FACILITY_OPC, 0x100f)")
cpp_quote("#define OPC_E_ZIP_REQUIRES_64_BIT MAKE_HRESULT(1, FACILITY_OPC, 0x1010)")

[uuid (3d8d6062-2749-442b-9e32-E40EF801A766), version (1.0), lcid (0x0)]
library MSOPC {
  interface IOpcCertificateEnumerator;
  interface IOpcCertificateSet;
  interface IOpcDigitalSignature;
  interface IOpcDigitalSignatureEnumerator;
  interface IOpcDigitalSignatureManager;
  interface IOpcPackage;
  interface IOpcPart;
  interface IOpcPartEnumerator;
  interface IOpcPartSet;
  interface IOpcPartUri;
  interface IOpcRelationship;
  interface IOpcRelationshipEnumerator;
  interface IOpcRelationshipSelector;
  interface IOpcRelationshipSelectorSet;
  interface IOpcRelationshipSet;
  interface IOpcSigningOptions;
  interface IOpcSignatureCustomObject;
  interface IOpcSignatureCustomObjectSet;
  interface IOpcSignaturePartReference;
  interface IOpcSignaturePartReferenceEnumerator;
  interface IOpcSignaturePartReferenceSet;
  interface IOpcSignatureReference;
  interface IOpcSignatureReferenceSet;
  interface IOpcSignatureRelationshipReference;
  interface IOpcSignatureRelationshipReferenceEnumerator;
  interface IOpcSignatureRelationshipReferenceSet;
  interface IOpcRelationshipSelectorEnumerator;
  interface IOpcSignatureReferenceEnumerator;
  interface IOpcSignatureCustomObjectEnumerator;
  interface IOpcUri;

  typedef enum {
    OPC_CANONICALIZATION_NONE = 0,
    OPC_CANONICALIZATION_C14N = 1,
    OPC_CANONICALIZATION_C14N_WITH_COMMENTS = 2
  } OPC_CANONICALIZATION_METHOD;

  typedef enum {
    OPC_CERTIFICATE_IN_CERTIFICATE_PART = 0,
    OPC_CERTIFICATE_IN_SIGNATURE_PART = 1,
    OPC_CERTIFICATE_NOT_EMBEDDED = 2
  } OPC_CERTIFICATE_EMBEDDING_OPTION;


  typedef [v1_enum] enum {
    OPC_COMPRESSION_NONE = -1,
    OPC_COMPRESSION_NORMAL = 0,
    OPC_COMPRESSION_MAXIMUM = 1,
    OPC_COMPRESSION_FAST = 2,
    OPC_COMPRESSION_SUPERFAST = 3
  } OPC_COMPRESSION_OPTIONS;

  typedef [v1_enum] enum {
    OPC_READ_DEFAULT = 0x0,
    OPC_VALIDATE_ON_LOAD = 0x1,
    OPC_CACHE_ON_ACCESS = 0x2
  } OPC_READ_FLAGS;

  typedef enum {
    OPC_RELATIONSHIP_SELECT_BY_ID = 0,
    OPC_RELATIONSHIP_SELECT_BY_TYPE = 1,
  } OPC_RELATIONSHIP_SELECTOR;

  typedef enum {
    OPC_RELATIONSHIP_SIGN_USING_SELECTORS = 0,
    OPC_RELATIONSHIP_SIGN_PART = 1
  } OPC_RELATIONSHIPS_SIGNING_OPTION;

  typedef enum OPC_SIGNATURE_VALIDATION_RESULT {
    OPC_SIGNATURE_VALID = 0,
    OPC_SIGNATURE_INVALID = -1,
  } OPC_SIGNATURE_VALIDATION_RESULT;

  typedef enum {
    OPC_SIGNATURE_TIME_FORMAT_MILLISECONDS = 0,
    OPC_SIGNATURE_TIME_FORMAT_SECONDS = 1,
    OPC_SIGNATURE_TIME_FORMAT_MINUTES = 2,
    OPC_SIGNATURE_TIME_FORMAT_DAYS = 3,
    OPC_SIGNATURE_TIME_FORMAT_MONTHS = 4,
    OPC_SIGNATURE_TIME_FORMAT_YEARS = 5
  } OPC_SIGNATURE_TIME_FORMAT;

  typedef [v1_enum] enum {
    OPC_STREAM_IO_READ = 1,
    OPC_STREAM_IO_WRITE = 2
  } OPC_STREAM_IO_MODE;

  typedef [v1_enum] enum {
    OPC_URI_TARGET_MODE_INTERNAL = 0,
    OPC_URI_TARGET_MODE_EXTERNAL = 1
  } OPC_URI_TARGET_MODE;

  typedef [v1_enum] enum {
    OPC_WRITE_DEFAULT = 0x0,
    OPC_WRITE_FORCE_ZIP32 = 0x1
  } OPC_WRITE_FLAGS;

cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(OPC_READ_FLAGS);")
cpp_quote("DEFINE_ENUM_FLAG_OPERATORS(OPC_WRITE_FLAGS);")

  [object, uuid (42195949-3b79-4fc8-89c6-FC7FB979EE71), pointer_default (ref)]
  interface IOpcPart : IUnknown {
    HRESULT GetRelationshipSet ([out, retval] IOpcRelationshipSet **relationshipSet);
    HRESULT GetContentStream ([out, retval] IStream **stream);
    HRESULT GetName ([out, retval] IOpcPartUri **name);
    HRESULT GetContentType ([out, string, retval] LPWSTR *contentType);
    HRESULT GetCompressionOptions ([out, retval] OPC_COMPRESSION_OPTIONS *compressionOptions);
  }

  [object, uuid (42195949-3b79-4fc8-89c6-FC7FB979EE75), pointer_default (ref)]
  interface IOpcPartEnumerator : IUnknown {
    HRESULT MoveNext ([out, retval] BOOL *hasNext);
    HRESULT MovePrevious ([out, retval] BOOL *hasPrevious);
    HRESULT GetCurrent ([out, retval] IOpcPart **part);
    HRESULT Clone ([out, retval] IOpcPartEnumerator **copy);
  }

  [object, uuid (42195949-3b79-4fc8-89c6-FC7FB979EE73), pointer_default (ref)]
  interface IOpcPartSet : IUnknown {
    HRESULT GetPart ([in] IOpcPartUri *name,[out, retval] IOpcPart **part);
    HRESULT CreatePart ([in] IOpcPartUri *name,[in, string] LPCWSTR contentType,[in] OPC_COMPRESSION_OPTIONS compressionOptions,[out, retval] IOpcPart **part);
    HRESULT DeletePart ([in] IOpcPartUri *name);
    HRESULT PartExists ([in] IOpcPartUri *name,[out, retval] BOOL *partExists);
    HRESULT GetEnumerator ([out, retval] IOpcPartEnumerator **partEnumerator);
  }

  [object, uuid (42195949-3b79-4fc8-89c6-FC7FB979EE70), pointer_default (ref)]
  interface IOpcPackage : IUnknown {
    HRESULT GetPartSet ([out, retval] IOpcPartSet **partSet);
    HRESULT GetRelationshipSet ([out, retval] IOpcRelationshipSet **relationshipSet);
  }

  [object, uuid (42195949-3b79-4fc8-89c6-FC7FB979EE72), pointer_default (ref)]
  interface IOpcRelationship : IUnknown {
    HRESULT GetId ([out, string, retval] LPWSTR *relationshipIdentifier);
    HRESULT GetRelationshipType ([out, string, retval] LPWSTR *relationshipType);
    HRESULT GetSourceUri ([out, retval] IOpcUri **sourceUri);
    HRESULT GetTargetUri ([out, retval] IUri **targetUri);
    HRESULT GetTargetMode ([out, retval] OPC_URI_TARGET_MODE *targetMode);
  }

  [object, uuid (42195949-3b79-4fc8-89c6-FC7FB979EE76), pointer_default (ref)]
  interface IOpcRelationshipEnumerator : IUnknown {
    HRESULT MoveNext ([out, retval] BOOL *hasNext);
    HRESULT MovePrevious ([out, retval] BOOL *hasPrevious);
    HRESULT GetCurrent ([out, retval] IOpcRelationship **relationship);
    HRESULT Clone ([out, retval] IOpcRelationshipEnumerator **copy);
  }

  [object, uuid (f8f26c7f-b28f-4899-84c8-5d5639ede75f), nonextensible, pointer_default (ref)]
  interface IOpcRelationshipSelector : IUnknown {
    HRESULT GetSelectorType ([out, retval] OPC_RELATIONSHIP_SELECTOR *selector);
    HRESULT GetSelectionCriterion ([out, string, retval] LPWSTR *selectionCriterion);
  }

  [object, uuid (42195949-3b79-4fc8-89c6-FC7FB979EE74), pointer_default (ref)]
  interface IOpcRelationshipSet : IUnknown {
    HRESULT GetRelationship ([in, string] LPCWSTR relationshipIdentifier,[out, retval] IOpcRelationship **relationship);
    HRESULT CreateRelationship ([in, string, unique] LPCWSTR relationshipIdentifier,[in, string] LPCWSTR relationshipType,[in] IUri *targetUri,[in] OPC_URI_TARGET_MODE targetMode,[out, retval] IOpcRelationship **relationship);
    HRESULT DeleteRelationship ([in, string] LPCWSTR relationshipIdentifier);
    HRESULT RelationshipExists ([in, string] LPCWSTR relationshipIdentifier,[out, retval] BOOL *relationshipExists);
    HRESULT GetEnumerator ([out, retval] IOpcRelationshipEnumerator **relationshipEnumerator);
    HRESULT GetEnumeratorForType ([in, string] LPCWSTR relationshipType,[out, retval] IOpcRelationshipEnumerator **relationshipEnumerator);
    HRESULT GetRelationshipsContentStream ([out, retval] IStream **contents);
  }

  [object, uuid (5d77a19e-62c1-44e7-becd-45da5ae51a56), nonextensible, pointer_default (unique)]
  interface IOpcSignatureCustomObject : IUnknown {
    HRESULT GetXml ([out, size_is (,*count)] UINT8 **xmlMarkup,[out] UINT32 *count);
  }

  [object, uuid (5ee4fe1d-e1b0-4683-8079-7ea0fcf80b4c), nonextensible, pointer_default (ref)]
  interface IOpcSignatureCustomObjectEnumerator : IUnknown {
    HRESULT MoveNext ([out, retval] BOOL *hasNext);
    HRESULT MovePrevious ([out, retval] BOOL *hasPrevious);
    HRESULT GetCurrent ([out, retval] IOpcSignatureCustomObject **customObject);
    HRESULT Clone ([out, retval] IOpcSignatureCustomObjectEnumerator **copy);
  };

  [object, uuid (e24231ca-59f4-484e-b64b-36eeda36072c), nonextensible, pointer_default (unique)]
  interface IOpcSignaturePartReference : IUnknown {
    HRESULT GetPartName ([out, retval] IOpcPartUri **partName);
    HRESULT GetContentType ([out, string, retval] LPWSTR *contentType);
    HRESULT GetDigestMethod ([out, string, retval] LPWSTR *digestMethod);
    HRESULT GetDigestValue ([out, size_is (,*count)] UINT8 **digestValue,[out] UINT32 *count);
    HRESULT GetTransformMethod ([out, retval] OPC_CANONICALIZATION_METHOD *transformMethod);
  }

  [object, uuid (80eb1561-8c77-49cf-8266-459b356ee99a), nonextensible, pointer_default (ref)]
  interface IOpcSignaturePartReferenceEnumerator : IUnknown {
    HRESULT MoveNext ([out, retval] BOOL *hasNext);
    HRESULT MovePrevious ([out, retval] BOOL *hasPrevious);
    HRESULT GetCurrent ([out, retval] IOpcSignaturePartReference **partReference);
    HRESULT Clone ([out, retval] IOpcSignaturePartReferenceEnumerator **copy);
  };

  [object, uuid (1b47005e-3011-4edc-be6f-0f65e5ab0342), nonextensible, pointer_default (unique)]
  interface IOpcSignatureReference : IUnknown {
    HRESULT GetId ([out, string, retval] LPWSTR *referenceId);
    HRESULT GetUri ([out, retval] IUri **referenceUri);
    HRESULT GetType ([out, string, retval] LPWSTR *type);
    HRESULT GetTransformMethod ([out, retval] OPC_CANONICALIZATION_METHOD *transformMethod);
    HRESULT GetDigestMethod ([out, string, retval] LPWSTR *digestMethod);
    HRESULT GetDigestValue ([out, size_is (,*count)] UINT8 **digestValue,[out] UINT32 *count);
  }

  [object, uuid (cfa59a45-28b1-4868-969e-fa8097fdc12a), nonextensible, pointer_default (ref)]
  interface IOpcSignatureReferenceEnumerator : IUnknown {
    HRESULT MoveNext ([out, retval] BOOL *hasNext);
    HRESULT MovePrevious ([out, retval] BOOL *hasPrevious);
    HRESULT GetCurrent ([out, retval] IOpcSignatureReference **reference);
    HRESULT Clone ([out, retval] IOpcSignatureReferenceEnumerator **copy);
  };

  [object, uuid (773ba3e4-f021-48e4-aa04-9816db5d3495), nonextensible, pointer_default (ref)]
  interface IOpcSignatureRelationshipReferenceEnumerator : IUnknown {
    HRESULT MoveNext ([out, retval] BOOL *hasNext);
    HRESULT MovePrevious ([out, retval] BOOL *hasPrevious);
    HRESULT GetCurrent ([out, retval] IOpcSignatureRelationshipReference **relationshipReference);
    HRESULT Clone ([out, retval] IOpcSignatureRelationshipReferenceEnumerator **copy);
  };

  [object, uuid (57babac6-9d4a-4e50-8b86-e5d4051eae7c), nonextensible, pointer_default (unique)]
  interface IOpcSignatureRelationshipReference : IUnknown {
    HRESULT GetSourceUri ([out, retval] IOpcUri **sourceUri);
    HRESULT GetDigestMethod ([out, string, retval] LPWSTR *digestMethod);
    HRESULT GetDigestValue ([out, size_is (,*count)] UINT8 **digestValue,[out] UINT32 *count);
    HRESULT GetTransformMethod ([out, retval] OPC_CANONICALIZATION_METHOD *transformMethod);
    HRESULT GetRelationshipSigningOption ([out, retval] OPC_RELATIONSHIPS_SIGNING_OPTION *relationshipSigningOption);
    HRESULT GetRelationshipSelectorEnumerator ([out, retval] IOpcRelationshipSelectorEnumerator **selectorEnumerator);
  }

  [object, uuid (5e50a181-a91b-48ac-88d2-bca3d8f8c0b1), nonextensible, pointer_default (ref)]
  interface IOpcRelationshipSelectorEnumerator : IUnknown {
    HRESULT MoveNext ([out, retval] BOOL *hasNext);
    HRESULT MovePrevious ([out, retval] BOOL *hasPrevious);
    HRESULT GetCurrent ([out, retval] IOpcRelationshipSelector **relationshipSelector);
    HRESULT Clone ([out, retval] IOpcRelationshipSelectorEnumerator **copy);
  };

  [object, local, uuid (85131937-8f24-421f-b439-59ab24d140b8), nonextensible, pointer_default (ref)]
  interface IOpcCertificateEnumerator : IUnknown {
    HRESULT MoveNext ([out, retval] BOOL *hasNext);
    HRESULT MovePrevious ([out, retval] BOOL *hasPrevious);
    HRESULT GetCurrent ([out, retval] const CERT_CONTEXT **certificate);
    HRESULT Clone ([out, retval] IOpcCertificateEnumerator **copy);
  };

  [object, local, uuid (56ea4325-8e2d-4167-b1a4-e486d24c8fa7), nonextensible, pointer_default (ref)]
  interface IOpcCertificateSet : IUnknown {
    HRESULT Add ([in] const CERT_CONTEXT *certificate);
    HRESULT Remove ([in] const CERT_CONTEXT *certificate);
    HRESULT GetEnumerator ([out, retval] IOpcCertificateEnumerator **certificateEnumerator);
  }

  [object, uuid (967b6882-0ba3-4358-b9e7-b64c75063c5e), nonextensible, pointer_default (ref)]
  interface IOpcDigitalSignatureEnumerator : IUnknown {
    HRESULT MoveNext ([out, retval] BOOL *hasNext);
    HRESULT MovePrevious ([out, retval] BOOL *hasPrevious);
    HRESULT GetCurrent ([out, retval] IOpcDigitalSignature **digitalSignature);
    HRESULT Clone ([out, retval] IOpcDigitalSignatureEnumerator **copy);
  };

  [object, local, uuid (d5e62a0b-696d-462f-94df-72e33cef2659), nonextensible, pointer_default (ref)]
  interface IOpcDigitalSignatureManager : IUnknown {
    HRESULT GetSignatureOriginPartName ([out, retval] IOpcPartUri **signatureOriginPartName);
    HRESULT SetSignatureOriginPartName ([in, unique] IOpcPartUri *signatureOriginPartName);
    HRESULT GetSignatureEnumerator ([out, retval] IOpcDigitalSignatureEnumerator **signatureEnumerator);
    HRESULT RemoveSignature ([in] IOpcPartUri *signaturePartName);
    HRESULT CreateSigningOptions ([out, retval] IOpcSigningOptions **signingOptions);
    HRESULT Validate ([in] IOpcDigitalSignature *signature,[in] const CERT_CONTEXT *certificate,[out, retval] OPC_SIGNATURE_VALIDATION_RESULT *validationResult);
    HRESULT Sign ([in] const CERT_CONTEXT *certificate,[in] IOpcSigningOptions *signingOptions,[out, retval] IOpcDigitalSignature **digitalSignature);
    HRESULT ReplaceSignatureXml ([in] IOpcPartUri *signaturePartName,[in, size_is (count)] const UINT8 *newSignatureXml,[in] UINT32 count,[out, retval] IOpcDigitalSignature **digitalSignature);
  };

  [object, uuid (6c9fe28c-ecd9-4b22-9d36-7fdde670fec0), nonextensible, pointer_default (ref)]
  interface IOpcSignaturePartReferenceSet : IUnknown {
    HRESULT Create ([in] IOpcPartUri *partUri,[in, unique] LPCWSTR digestMethod,[in] OPC_CANONICALIZATION_METHOD transformMethod,[out, retval] IOpcSignaturePartReference **partReference);
    HRESULT Delete ([in] IOpcSignaturePartReference *partReference);
    HRESULT GetEnumerator ([out, retval] IOpcSignaturePartReferenceEnumerator **partReferenceEnumerator);
  }

  [object, uuid (6e34c269-a4d3-47c0-b5c4-87ff2b3b6136), nonextensible, pointer_default (ref)]
  interface IOpcRelationshipSelectorSet : IUnknown {
    HRESULT Create ([in] OPC_RELATIONSHIP_SELECTOR selector,[in] LPCWSTR selectionCriterion,[out, retval] IOpcRelationshipSelector **relationshipSelector);
    HRESULT Delete ([in] IOpcRelationshipSelector *relationshipSelector);
    HRESULT GetEnumerator ([out, retval] IOpcRelationshipSelectorEnumerator **relationshipSelectorEnumerator);
  }

  [object, uuid (9f863ca5-3631-404c-828d-807e0715069b), nonextensible, pointer_default (ref)]
  interface IOpcSignatureRelationshipReferenceSet : IUnknown {
    HRESULT Create ([in] IOpcUri *sourceUri,[in, unique] LPCWSTR digestMethod,[in] OPC_RELATIONSHIPS_SIGNING_OPTION relationshipSigningOption,[in, unique] IOpcRelationshipSelectorSet *selectorSet,[in] OPC_CANONICALIZATION_METHOD transformMethod,[out, retval] IOpcSignatureRelationshipReference **relationshipReference);
    HRESULT CreateRelationshipSelectorSet ([out] IOpcRelationshipSelectorSet **selectorSet);
    HRESULT Delete ([in] IOpcSignatureRelationshipReference *relationshipReference);
    HRESULT GetEnumerator ([out, retval] IOpcSignatureRelationshipReferenceEnumerator **relationshipReferenceEnumerator);
  }

  [object, uuid (f3b02d31-ab12-42dd-9e2f-2b16761c3c1e), nonextensible, pointer_default (ref)]
  interface IOpcSignatureReferenceSet : IUnknown {
    HRESULT Create ([in] IUri *referenceUri,[in, unique] LPCWSTR referenceId,[in, unique] LPCWSTR type,[in, unique] LPCWSTR digestMethod,[in] OPC_CANONICALIZATION_METHOD transformMethod,[out, retval] IOpcSignatureReference **reference);
    HRESULT Delete ([in] IOpcSignatureReference *reference);
    HRESULT GetEnumerator ([out, retval] IOpcSignatureReferenceEnumerator **referenceEnumerator);
  }

  [object, uuid (8f792ac5-7947-4e11-bc3d-2659ff046ae1), nonextensible, pointer_default (ref)]
  interface IOpcSignatureCustomObjectSet : IUnknown {
    HRESULT Create ([in, size_is (count)] const UINT8 *xmlMarkup,[in] UINT32 count,[out, retval] IOpcSignatureCustomObject **customObject);
    HRESULT Delete ([in] IOpcSignatureCustomObject *customObject);
    HRESULT GetEnumerator ([out, retval] IOpcSignatureCustomObjectEnumerator **customObjectEnumerator);
  }

  [object, local, uuid (52ab21dd-1cd0-4949-bc80-0c1232d00cb4), nonextensible, pointer_default (unique)]
  interface IOpcDigitalSignature : IUnknown {
    HRESULT GetNamespaces ([out, size_is (,*count)] LPWSTR **prefixes,[out, size_is (,*count)] LPWSTR **namespaces,[out] UINT32 *count);
    HRESULT GetSignatureId ([out, string, retval] LPWSTR *signatureId);
    HRESULT GetSignaturePartName ([out, retval] IOpcPartUri **signaturePartName);
    HRESULT GetSignatureMethod ([out, string, retval] LPWSTR *signatureMethod);
    HRESULT GetCanonicalizationMethod ([out, retval] OPC_CANONICALIZATION_METHOD *canonicalizationMethod);
    HRESULT GetSignatureValue ([out, size_is (,*count)] UINT8 **signatureValue,[out] UINT32 *count);
    HRESULT GetSignaturePartReferenceEnumerator ([out, retval] IOpcSignaturePartReferenceEnumerator **partReferenceEnumerator);
    HRESULT GetSignatureRelationshipReferenceEnumerator ([out, retval] IOpcSignatureRelationshipReferenceEnumerator **relationshipReferenceEnumerator);
    HRESULT GetSigningTime ([out, string, retval] LPWSTR *signingTime);
    HRESULT GetTimeFormat ([out, retval] OPC_SIGNATURE_TIME_FORMAT *timeFormat);
    HRESULT GetPackageObjectReference ([out, retval] IOpcSignatureReference **packageObjectReference);
    HRESULT GetCertificateEnumerator ([out, retval] IOpcCertificateEnumerator **certificateEnumerator);
    HRESULT GetCustomReferenceEnumerator ([out, retval] IOpcSignatureReferenceEnumerator **customReferenceEnumerator);
    HRESULT GetCustomObjectEnumerator ([out, retval] IOpcSignatureCustomObjectEnumerator **customObjectEnumerator);
    HRESULT GetSignatureXml ([out, size_is (,*count)] UINT8 **signatureXml,[out] UINT32 *count);
  };

  [object, uuid (50d2d6a5-7aeb-46c0-b241-43ab0e9b407e), nonextensible, pointer_default (ref)]
  interface IOpcSigningOptions : IUnknown {
    HRESULT GetSignatureId ([out, string, retval] LPWSTR *signatureId);
    HRESULT SetSignatureId ([in] LPCWSTR signatureId);
    HRESULT GetSignatureMethod ([out, string, retval] LPWSTR *signatureMethod);
    HRESULT SetSignatureMethod ([in] LPCWSTR signatureMethod);
    HRESULT GetDefaultDigestMethod ([out, string, retval] LPWSTR *digestMethod);
    HRESULT SetDefaultDigestMethod ([in] LPCWSTR digestMethod);
    HRESULT GetCertificateEmbeddingOption ([out, retval] OPC_CERTIFICATE_EMBEDDING_OPTION *embeddingOption);
    HRESULT SetCertificateEmbeddingOption ([in] OPC_CERTIFICATE_EMBEDDING_OPTION embeddingOption);
    HRESULT GetTimeFormat ([out, retval] OPC_SIGNATURE_TIME_FORMAT *timeFormat);
    HRESULT SetTimeFormat ([in] OPC_SIGNATURE_TIME_FORMAT timeFormat);
    HRESULT GetSignaturePartReferenceSet ([out, retval] IOpcSignaturePartReferenceSet **partReferenceSet);
    HRESULT GetSignatureRelationshipReferenceSet ([out, retval] IOpcSignatureRelationshipReferenceSet **relationshipReferenceSet);
    HRESULT GetCustomObjectSet ([out, retval] IOpcSignatureCustomObjectSet **customObjectSet);
    HRESULT GetCustomReferenceSet ([out, retval] IOpcSignatureReferenceSet **customReferenceSet);
    HRESULT GetCertificateSet ([out, retval] IOpcCertificateSet **certificateSet);
    HRESULT GetSignaturePartName ([out, retval] IOpcPartUri **signaturePartName);
    HRESULT SetSignaturePartName ([in, unique] IOpcPartUri *signaturePartName);
  };

  [object, uuid (6d0b4446-cd73-4ab3-94f4-8ccdf6116154), pointer_default (ref)]
  interface IOpcFactory : IUnknown {
    HRESULT CreatePackageRootUri ([out, retval] IOpcUri **rootUri);
    HRESULT CreatePartUri ([in, string] LPCWSTR pwzUri,[out, retval] IOpcPartUri **partUri);
    [local] HRESULT CreateStreamOnFile ([in, string] LPCWSTR filename,[in] OPC_STREAM_IO_MODE ioMode,[in, unique] LPSECURITY_ATTRIBUTES securityAttributes,[in] DWORD dwFlagsAndAttributes,[out, retval] IStream **stream);
    HRESULT CreatePackage ([out, retval] IOpcPackage **package);
    HRESULT ReadPackageFromStream ([in] IStream *stream,[in] OPC_READ_FLAGS flags,[out, retval] IOpcPackage **package);
    HRESULT WritePackageToStream ([in] IOpcPackage *package,[in] OPC_WRITE_FLAGS flags,[in] IStream *stream);
    HRESULT CreateDigitalSignatureManager ([in] IOpcPackage *package,[out, retval] IOpcDigitalSignatureManager **signatureManager);
  }

  [uuid (6b2d6ba0-9f3e-4f27-920b-313cc426a39e)]
  coclass OpcFactory {
    interface IOpcFactory;
  };
};
cpp_quote("#endif")

cpp_quote("#endif")
