************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number:
            cb:2d:80:99:5a:69:52:60
        Signature Algorithm: sha256WithRSAEncryption
        Issuer: C=XY, O=Python Software Foundation CA, CN=our-ca-server
        Validity
            Not Before: Aug 29 14:23:16 2018 GMT
            Not After : Oct 28 14:23:16 2037 GMT
        Subject: C=XY, L=Castle Anthrax, O=Python Software Foundation, CN=idnsans
        Subject Public Key Info:
            Public Key Algorithm: rsaEncryption
                RSA Public-Key: (3072 bit)
                Modulus:
                    00:bc:b2:aa:65:4e:e1:ee:2e:36:d6:4c:be:52:2f:
                    60:f5:7f:0f:54:e9:64:33:2d:50:05:e6:0b:32:85:
                    de:65:af:61:69:7d:8c:23:ae:86:f9:31:ab:fd:d5:
                    a3:8b:6c:94:53:be:83:72:5e:c2:76:e8:53:d2:d1:
                    1c:9c:44:65:db:a2:67:08:10:9c:57:1b:2f:5a:23:
                    ed:0c:2d:80:6d:d4:6e:66:82:a0:87:f8:77:15:0b:
                    03:f3:08:d9:61:56:74:f0:be:98:00:ef:2f:33:b3:
                    7f:ba:7b:36:40:3d:69:05:d3:25:8e:31:82:ed:4d:
                    ca:bd:03:91:96:79:ab:ed:e5:53:20:9b:52:99:17:
                    78:0e:e2:4e:7c:a0:fc:a4:dc:07:bd:0f:42:c0:69:
                    8b:17:e8:31:62:05:8c:78:d5:e5:11:e5:46:d2:6f:
                    92:18:5d:a0:dd:f1:de:a3:a9:6f:e3:9d:88:60:73:
                    a0:b1:92:fd:60:4c:91:67:f3:b5:79:96:b1:b4:bd:
                    83:12:45:4b:56:0d:0f:58:26:1f:c4:28:0c:63:1b:
                    0e:c0:79:8e:36:f6:9e:93:13:85:28:26:10:e6:a0:
                    56:11:d8:d2:ef:6b:08:4d:22:99:71:a2:5f:ef:d1:
                    fb:34:bd:e1:50:8c:8f:d4:b1:30:fc:da:d4:5f:9d:
                    82:f8:21:7f:2c:ce:12:ec:13:9f:f9:22:af:1a:88:
                    b1:e3:55:b2:0c:c2:60:d8:01:ad:0f:eb:70:29:da:
                    47:f5:6e:24:a7:f6:6a:43:2f:c9:50:6b:34:a8:ca:
                    bf:31:cc:8a:b6:41:2e:47:32:f1:9b:78:c0:26:4b:
                    48:a1:d7:46:71:f3:8b:95:9a:45:a5:6a:f8:2f:b5:
                    27:e5:c3:c2:bf:65:74:fd:73:bd:2b:66:9f:d3:74:
                    11:98:f7:97:0e:16:c6:e0:e5:4f:f6:d0:cf:cb:96:
                    98:ac:f6:d7:01:09:aa:15:69:84:85:ba:96:ad:ac:
                    ff:a9:f3:2d:7d:a8:fd:a7:79:bb
                Exponent: 65537 (0x10001)
        X509v3 extensions:
            X509v3 Subject Alternative Name: 
                DNS:idnsans, DNS:xn--knig-5qa.idn.pythontest.net, DNS:xn--knigsgsschen-lcb0w.idna2003.pythontest.net, DNS:xn--knigsgchen-b4a3dun.idna2008.pythontest.net, DNS:xn--nxasmq6b.idna2003.pythontest.net, DNS:xn--nxasmm1c.idna2008.pythontest.net
            X509v3 Key Usage: critical
                Digital Signature, Key Encipherment
            X509v3 Extended Key Usage: 
                TLS Web Server Authentication, TLS Web Client Authentication
            X509v3 Basic Constraints: critical
                CA:FALSE
            X509v3 Subject Key Identifier: 
                5C:BE:18:7F:7B:3F:CE:99:66:80:79:53:4B:DD:33:1B:42:A5:7E:00
            X509v3 Authority Key Identifier: 
                keyid:B3:8A:A0:A2:BA:71:F1:A8:24:79:D4:A4:5B:25:36:15:1E:49:C8:CD
                DirName:/C=XY/O=Python Software Foundation CA/CN=our-ca-server
                serial:CB:2D:80:99:5A:69:52:5B

            Authority Information Access: 
                CA Issuers - URI:http://testca.pythontest.net/testca/pycacert.cer
                OCSP - URI:http://testca.pythontest.net/testca/ocsp/

            X509v3 CRL Distribution Points: 

                Full Name:
                  URI:http://testca.pythontest.net/testca/revocation.crl

    Signature Algorithm: sha256WithRSAEncryption
         5d:7a:f8:81:e0:a7:c1:3f:39:eb:d3:52:2c:e1:cb:4d:29:b3:
         77:18:17:18:9e:12:fc:11:cc:3c:49:cb:6b:f4:4d:6c:b8:d2:
         f4:e9:37:f8:6b:ed:f5:d7:f1:eb:5a:41:04:c7:f3:8c:da:e1:
         05:8e:ae:58:71:d9:01:8a:32:46:b2:dd:95:46:e1:ce:82:04:
         fa:0b:1c:29:75:07:85:ce:cd:59:d4:cc:f3:56:b3:72:4d:cb:
         90:0f:ce:02:21:ce:5d:17:84:96:7f:6a:00:57:42:b7:24:5b:
         07:25:1e:77:a8:9d:da:41:09:8e:29:79:b4:b0:a1:45:c8:70:
         ae:2c:86:24:ae:3d:9a:74:a7:04:78:d6:1f:1b:17:c5:c1:6d:
         b1:1a:fd:f4:50:2e:61:16:84:89:d0:42:3f:b6:bf:bd:52:bd:
         c8:3e:8e:87:b4:f0:bd:ad:c7:51:65:2f:77:e8:69:79:0e:03:
         63:89:e7:70:ad:c8:d1:2f:1a:a5:06:d2:90:db:7c:07:35:9a:
         0b:0e:85:87:d1:70:17:a7:88:0f:c6:b5:9c:88:00:fa:f9:b2:
         0a:19:5a:4b:8d:91:12:51:5e:0e:c1:d8:9e:02:78:d0:2d:24:
         09:fe:d4:97:3c:cb:a0:1f:9a:ab:f7:0f:e2:fa:64:23:4e:53:
         0a:15:3e:f5:04:01:86:29:8b:8e:24:40:2f:b1:90:87:5c:3b:
         7b:a7:4c:06:af:c3:90:7f:e9:c6:56:42:61:15:2c:83:f1:7c:
         4f:89:17:f3:a0:11:34:3f:8d:af:75:34:60:1e:e0:f2:f3:02:
         e7:aa:b3:f7:9f:1c:f8:69:f4:fe:da:57:6e:1b:95:53:70:cd:
         ed:b6:bb:2a:84:eb:ab:c3:a9:b4:d5:15:a0:b2:cc:81:2d:f1:
         56:c1:54:9b:5f:14:4c:5f:ad:5f:f5:06:ee:22:60:45:e4:50:
         35:64:ac:ac:ca:4a:bf:86:78:f8:53:2d:17:d8:e8:84:c8:07:
         a4:c2:29:76:c7:1f
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
