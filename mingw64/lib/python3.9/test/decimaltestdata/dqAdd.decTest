------------------------------------------------------------------------
-- dqAdd.decTest -- decQuad addition                                  --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

-- This set of tests are for decQuads only; all arguments are
-- representable in a decQuad
extended:    1
clamp:       1
precision:   34
maxExponent: 6144
minExponent: -6143
rounding:    half_even

-- [first group are 'quick confidence check']
dqadd001 add 1       1       ->  2
dqadd002 add 2       3       ->  5
dqadd003 add '5.75'  '3.3'   ->  9.05
dqadd004 add '5'     '-3'    ->  2
dqadd005 add '-5'    '-3'    ->  -8
dqadd006 add '-7'    '2.5'   ->  -4.5
dqadd007 add '0.7'   '0.3'   ->  1.0
dqadd008 add '1.25'  '1.25'  ->  2.50
dqadd009 add '1.23456789'  '1.00000000' -> '2.23456789'
dqadd010 add '1.23456789'  '1.00000011' -> '2.23456800'

--             1234567890123456      1234567890123456
dqadd011 add '0.4444444444444444444444444444444446'  '0.5555555555555555555555555555555555' -> '1.000000000000000000000000000000000' Inexact Rounded
dqadd012 add '0.4444444444444444444444444444444445'  '0.5555555555555555555555555555555555' -> '1.000000000000000000000000000000000' Rounded
dqadd013 add '0.4444444444444444444444444444444444'  '0.5555555555555555555555555555555555' -> '0.9999999999999999999999999999999999'
dqadd014 add   '4444444444444444444444444444444444' '0.49'   -> '4444444444444444444444444444444444' Inexact Rounded
dqadd015 add   '4444444444444444444444444444444444' '0.499'  -> '4444444444444444444444444444444444' Inexact Rounded
dqadd016 add   '4444444444444444444444444444444444' '0.4999' -> '4444444444444444444444444444444444' Inexact Rounded
dqadd017 add   '4444444444444444444444444444444444' '0.5000' -> '4444444444444444444444444444444444' Inexact Rounded
dqadd018 add   '4444444444444444444444444444444444' '0.5001' -> '4444444444444444444444444444444445' Inexact Rounded
dqadd019 add   '4444444444444444444444444444444444' '0.501'  -> '4444444444444444444444444444444445' Inexact Rounded
dqadd020 add   '4444444444444444444444444444444444' '0.51'   -> '4444444444444444444444444444444445' Inexact Rounded

dqadd021 add 0 1 -> 1
dqadd022 add 1 1 -> 2
dqadd023 add 2 1 -> 3
dqadd024 add 3 1 -> 4
dqadd025 add 4 1 -> 5
dqadd026 add 5 1 -> 6
dqadd027 add 6 1 -> 7
dqadd028 add 7 1 -> 8
dqadd029 add 8 1 -> 9
dqadd030 add 9 1 -> 10

-- some carrying effects
dqadd031 add '0.9998'  '0.0000' -> '0.9998'
dqadd032 add '0.9998'  '0.0001' -> '0.9999'
dqadd033 add '0.9998'  '0.0002' -> '1.0000'
dqadd034 add '0.9998'  '0.0003' -> '1.0001'

dqadd035 add '70'  '10000e+34' -> '1.000000000000000000000000000000000E+38' Inexact Rounded
dqadd036 add '700'  '10000e+34' -> '1.000000000000000000000000000000000E+38' Inexact Rounded
dqadd037 add '7000'  '10000e+34' -> '1.000000000000000000000000000000000E+38' Inexact Rounded
dqadd038 add '70000'  '10000e+34' -> '1.000000000000000000000000000000001E+38' Inexact Rounded
dqadd039 add '700000'  '10000e+34' -> '1.000000000000000000000000000000007E+38' Rounded

-- symmetry:
dqadd040 add '10000e+34'  '70' -> '1.000000000000000000000000000000000E+38' Inexact Rounded
dqadd041 add '10000e+34'  '700' -> '1.000000000000000000000000000000000E+38' Inexact Rounded
dqadd042 add '10000e+34'  '7000' -> '1.000000000000000000000000000000000E+38' Inexact Rounded
dqadd044 add '10000e+34'  '70000' -> '1.000000000000000000000000000000001E+38' Inexact Rounded
dqadd045 add '10000e+34'  '700000' -> '1.000000000000000000000000000000007E+38' Rounded

-- same, without rounding
dqadd046 add '10000e+9'  '7' -> '10000000000007'
dqadd047 add '10000e+9'  '70' -> '10000000000070'
dqadd048 add '10000e+9'  '700' -> '10000000000700'
dqadd049 add '10000e+9'  '7000' -> '10000000007000'
dqadd050 add '10000e+9'  '70000' -> '10000000070000'
dqadd051 add '10000e+9'  '700000' -> '10000000700000'
dqadd052 add '10000e+9'  '7000000' -> '10000007000000'

-- examples from decarith
dqadd053 add '12' '7.00' -> '19.00'
dqadd054 add '1.3' '-1.07' -> '0.23'
dqadd055 add '1.3' '-1.30' -> '0.00'
dqadd056 add '1.3' '-2.07' -> '-0.77'
dqadd057 add '1E+2' '1E+4' -> '1.01E+4'

-- leading zero preservation
dqadd061 add 1 '0.0001' -> '1.0001'
dqadd062 add 1 '0.00001' -> '1.00001'
dqadd063 add 1 '0.000001' -> '1.000001'
dqadd064 add 1 '0.0000001' -> '1.0000001'
dqadd065 add 1 '0.00000001' -> '1.00000001'

-- some funny zeros [in case of bad signum]
dqadd070 add 1  0    -> 1
dqadd071 add 1 0.    -> 1
dqadd072 add 1  .0   -> 1.0
dqadd073 add 1 0.0   -> 1.0
dqadd074 add 1 0.00  -> 1.00
dqadd075 add  0  1   -> 1
dqadd076 add 0.  1   -> 1
dqadd077 add  .0 1   -> 1.0
dqadd078 add 0.0 1   -> 1.0
dqadd079 add 0.00 1  -> 1.00

-- some carries
dqadd080 add 999999998 1  -> 999999999
dqadd081 add 999999999 1  -> 1000000000
dqadd082 add  99999999 1  -> 100000000
dqadd083 add   9999999 1  -> 10000000
dqadd084 add    999999 1  -> 1000000
dqadd085 add     99999 1  -> 100000
dqadd086 add      9999 1  -> 10000
dqadd087 add       999 1  -> 1000
dqadd088 add        99 1  -> 100
dqadd089 add         9 1  -> 10


-- more LHS swaps
dqadd090 add '-56267E-10'   0 ->  '-0.0000056267'
dqadd091 add '-56267E-6'    0 ->  '-0.056267'
dqadd092 add '-56267E-5'    0 ->  '-0.56267'
dqadd093 add '-56267E-4'    0 ->  '-5.6267'
dqadd094 add '-56267E-3'    0 ->  '-56.267'
dqadd095 add '-56267E-2'    0 ->  '-562.67'
dqadd096 add '-56267E-1'    0 ->  '-5626.7'
dqadd097 add '-56267E-0'    0 ->  '-56267'
dqadd098 add '-5E-10'       0 ->  '-5E-10'
dqadd099 add '-5E-7'        0 ->  '-5E-7'
dqadd100 add '-5E-6'        0 ->  '-0.000005'
dqadd101 add '-5E-5'        0 ->  '-0.00005'
dqadd102 add '-5E-4'        0 ->  '-0.0005'
dqadd103 add '-5E-1'        0 ->  '-0.5'
dqadd104 add '-5E0'         0 ->  '-5'
dqadd105 add '-5E1'         0 ->  '-50'
dqadd106 add '-5E5'         0 ->  '-500000'
dqadd107 add '-5E33'        0 ->  '-5000000000000000000000000000000000'
dqadd108 add '-5E34'        0 ->  '-5.000000000000000000000000000000000E+34'  Rounded
dqadd109 add '-5E35'        0 ->  '-5.000000000000000000000000000000000E+35'  Rounded
dqadd110 add '-5E36'        0 ->  '-5.000000000000000000000000000000000E+36'  Rounded
dqadd111 add '-5E100'       0 ->  '-5.000000000000000000000000000000000E+100' Rounded

-- more RHS swaps
dqadd113 add 0  '-56267E-10' ->  '-0.0000056267'
dqadd114 add 0  '-56267E-6'  ->  '-0.056267'
dqadd116 add 0  '-56267E-5'  ->  '-0.56267'
dqadd117 add 0  '-56267E-4'  ->  '-5.6267'
dqadd119 add 0  '-56267E-3'  ->  '-56.267'
dqadd120 add 0  '-56267E-2'  ->  '-562.67'
dqadd121 add 0  '-56267E-1'  ->  '-5626.7'
dqadd122 add 0  '-56267E-0'  ->  '-56267'
dqadd123 add 0  '-5E-10'     ->  '-5E-10'
dqadd124 add 0  '-5E-7'      ->  '-5E-7'
dqadd125 add 0  '-5E-6'      ->  '-0.000005'
dqadd126 add 0  '-5E-5'      ->  '-0.00005'
dqadd127 add 0  '-5E-4'      ->  '-0.0005'
dqadd128 add 0  '-5E-1'      ->  '-0.5'
dqadd129 add 0  '-5E0'       ->  '-5'
dqadd130 add 0  '-5E1'       ->  '-50'
dqadd131 add 0  '-5E5'       ->  '-500000'
dqadd132 add 0  '-5E33'      ->  '-5000000000000000000000000000000000'
dqadd133 add 0  '-5E34'      ->  '-5.000000000000000000000000000000000E+34'   Rounded
dqadd134 add 0  '-5E35'      ->  '-5.000000000000000000000000000000000E+35'   Rounded
dqadd135 add 0  '-5E36'      ->  '-5.000000000000000000000000000000000E+36'   Rounded
dqadd136 add 0  '-5E100'     ->  '-5.000000000000000000000000000000000E+100'  Rounded

-- related
dqadd137 add  1  '0E-39'      ->  '1.000000000000000000000000000000000'  Rounded
dqadd138 add -1  '0E-39'      ->  '-1.000000000000000000000000000000000' Rounded
dqadd139 add '0E-39' 1        ->  '1.000000000000000000000000000000000'  Rounded
dqadd140 add '0E-39' -1       ->  '-1.000000000000000000000000000000000' Rounded
dqadd141 add 1E+29   0.0000   ->  '100000000000000000000000000000.0000'
dqadd142 add 1E+29   0.00000  ->  '100000000000000000000000000000.0000'  Rounded
dqadd143 add 0.000   1E+30    ->  '1000000000000000000000000000000.000'
dqadd144 add 0.0000  1E+30    ->  '1000000000000000000000000000000.000'  Rounded

-- [some of the next group are really constructor tests]
dqadd146 add '00.0'  0       ->  '0.0'
dqadd147 add '0.00'  0       ->  '0.00'
dqadd148 add  0      '0.00'  ->  '0.00'
dqadd149 add  0      '00.0'  ->  '0.0'
dqadd150 add '00.0'  '0.00'  ->  '0.00'
dqadd151 add '0.00'  '00.0'  ->  '0.00'
dqadd152 add '3'     '.3'    ->  '3.3'
dqadd153 add '3.'    '.3'    ->  '3.3'
dqadd154 add '3.0'   '.3'    ->  '3.3'
dqadd155 add '3.00'  '.3'    ->  '3.30'
dqadd156 add '3'     '3'     ->  '6'
dqadd157 add '3'     '+3'    ->  '6'
dqadd158 add '3'     '-3'    ->  '0'
dqadd159 add '0.3'   '-0.3'  ->  '0.0'
dqadd160 add '0.03'  '-0.03' ->  '0.00'

-- try borderline precision, with carries, etc.
dqadd161 add '1E+12' '-1'    -> '999999999999'
dqadd162 add '1E+12'  '1.11' -> '1000000000001.11'
dqadd163 add '1.11'  '1E+12' -> '1000000000001.11'
dqadd164 add '-1'    '1E+12' -> '999999999999'
dqadd165 add '7E+12' '-1'    -> '6999999999999'
dqadd166 add '7E+12'  '1.11' -> '7000000000001.11'
dqadd167 add '1.11'  '7E+12' -> '7000000000001.11'
dqadd168 add '-1'    '7E+12' -> '6999999999999'

rounding: half_up
dqadd170 add '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555567' -> '5.000000000000000000000000000000001' Inexact Rounded
dqadd171 add '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555566' -> '5.000000000000000000000000000000001' Inexact Rounded
dqadd172 add '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555565' -> '5.000000000000000000000000000000001' Inexact Rounded
dqadd173 add '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555564' -> '5.000000000000000000000000000000000' Inexact Rounded
dqadd174 add '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555553' -> '4.999999999999999999999999999999999' Inexact Rounded
dqadd175 add '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555552' -> '4.999999999999999999999999999999999' Inexact Rounded
dqadd176 add '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555551' -> '4.999999999999999999999999999999999' Inexact Rounded
dqadd177 add '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555550' -> '4.999999999999999999999999999999999' Rounded
dqadd178 add '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555545' -> '4.999999999999999999999999999999999' Inexact Rounded
dqadd179 add '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555544' -> '4.999999999999999999999999999999998' Inexact Rounded
dqadd180 add '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555543' -> '4.999999999999999999999999999999998' Inexact Rounded
dqadd181 add '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555542' -> '4.999999999999999999999999999999998' Inexact Rounded
dqadd182 add '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555541' -> '4.999999999999999999999999999999998' Inexact Rounded
dqadd183 add '4.444444444444444444444444444444444'  '0.5555555555555555555555555555555540' -> '4.999999999999999999999999999999998' Rounded

-- and some more, including residue effects and different roundings
rounding: half_up
dqadd200 add '1231234567890123456784560123456789' 0             -> '1231234567890123456784560123456789'
dqadd201 add '1231234567890123456784560123456789' 0.000000001   -> '1231234567890123456784560123456789' Inexact Rounded
dqadd202 add '1231234567890123456784560123456789' 0.000001      -> '1231234567890123456784560123456789' Inexact Rounded
dqadd203 add '1231234567890123456784560123456789' 0.1           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd204 add '1231234567890123456784560123456789' 0.4           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd205 add '1231234567890123456784560123456789' 0.49          -> '1231234567890123456784560123456789' Inexact Rounded
dqadd206 add '1231234567890123456784560123456789' 0.499999      -> '1231234567890123456784560123456789' Inexact Rounded
dqadd207 add '1231234567890123456784560123456789' 0.499999999   -> '1231234567890123456784560123456789' Inexact Rounded
dqadd208 add '1231234567890123456784560123456789' 0.5           -> '1231234567890123456784560123456790' Inexact Rounded
dqadd209 add '1231234567890123456784560123456789' 0.500000001   -> '1231234567890123456784560123456790' Inexact Rounded
dqadd210 add '1231234567890123456784560123456789' 0.500001      -> '1231234567890123456784560123456790' Inexact Rounded
dqadd211 add '1231234567890123456784560123456789' 0.51          -> '1231234567890123456784560123456790' Inexact Rounded
dqadd212 add '1231234567890123456784560123456789' 0.6           -> '1231234567890123456784560123456790' Inexact Rounded
dqadd213 add '1231234567890123456784560123456789' 0.9           -> '1231234567890123456784560123456790' Inexact Rounded
dqadd214 add '1231234567890123456784560123456789' 0.99999       -> '1231234567890123456784560123456790' Inexact Rounded
dqadd215 add '1231234567890123456784560123456789' 0.999999999   -> '1231234567890123456784560123456790' Inexact Rounded
dqadd216 add '1231234567890123456784560123456789' 1             -> '1231234567890123456784560123456790'
dqadd217 add '1231234567890123456784560123456789' 1.000000001   -> '1231234567890123456784560123456790' Inexact Rounded
dqadd218 add '1231234567890123456784560123456789' 1.00001       -> '1231234567890123456784560123456790' Inexact Rounded
dqadd219 add '1231234567890123456784560123456789' 1.1           -> '1231234567890123456784560123456790' Inexact Rounded

rounding: half_even
dqadd220 add '1231234567890123456784560123456789' 0             -> '1231234567890123456784560123456789'
dqadd221 add '1231234567890123456784560123456789' 0.000000001   -> '1231234567890123456784560123456789' Inexact Rounded
dqadd222 add '1231234567890123456784560123456789' 0.000001      -> '1231234567890123456784560123456789' Inexact Rounded
dqadd223 add '1231234567890123456784560123456789' 0.1           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd224 add '1231234567890123456784560123456789' 0.4           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd225 add '1231234567890123456784560123456789' 0.49          -> '1231234567890123456784560123456789' Inexact Rounded
dqadd226 add '1231234567890123456784560123456789' 0.499999      -> '1231234567890123456784560123456789' Inexact Rounded
dqadd227 add '1231234567890123456784560123456789' 0.499999999   -> '1231234567890123456784560123456789' Inexact Rounded
dqadd228 add '1231234567890123456784560123456789' 0.5           -> '1231234567890123456784560123456790' Inexact Rounded
dqadd229 add '1231234567890123456784560123456789' 0.500000001   -> '1231234567890123456784560123456790' Inexact Rounded
dqadd230 add '1231234567890123456784560123456789' 0.500001      -> '1231234567890123456784560123456790' Inexact Rounded
dqadd231 add '1231234567890123456784560123456789' 0.51          -> '1231234567890123456784560123456790' Inexact Rounded
dqadd232 add '1231234567890123456784560123456789' 0.6           -> '1231234567890123456784560123456790' Inexact Rounded
dqadd233 add '1231234567890123456784560123456789' 0.9           -> '1231234567890123456784560123456790' Inexact Rounded
dqadd234 add '1231234567890123456784560123456789' 0.99999       -> '1231234567890123456784560123456790' Inexact Rounded
dqadd235 add '1231234567890123456784560123456789' 0.999999999   -> '1231234567890123456784560123456790' Inexact Rounded
dqadd236 add '1231234567890123456784560123456789' 1             -> '1231234567890123456784560123456790'
dqadd237 add '1231234567890123456784560123456789' 1.00000001    -> '1231234567890123456784560123456790' Inexact Rounded
dqadd238 add '1231234567890123456784560123456789' 1.00001       -> '1231234567890123456784560123456790' Inexact Rounded
dqadd239 add '1231234567890123456784560123456789' 1.1           -> '1231234567890123456784560123456790' Inexact Rounded
-- critical few with even bottom digit...
dqadd240 add '1231234567890123456784560123456788' 0.499999999   -> '1231234567890123456784560123456788' Inexact Rounded
dqadd241 add '1231234567890123456784560123456788' 0.5           -> '1231234567890123456784560123456788' Inexact Rounded
dqadd242 add '1231234567890123456784560123456788' 0.500000001   -> '1231234567890123456784560123456789' Inexact Rounded

rounding: down
dqadd250 add '1231234567890123456784560123456789' 0             -> '1231234567890123456784560123456789'
dqadd251 add '1231234567890123456784560123456789' 0.000000001   -> '1231234567890123456784560123456789' Inexact Rounded
dqadd252 add '1231234567890123456784560123456789' 0.000001      -> '1231234567890123456784560123456789' Inexact Rounded
dqadd253 add '1231234567890123456784560123456789' 0.1           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd254 add '1231234567890123456784560123456789' 0.4           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd255 add '1231234567890123456784560123456789' 0.49          -> '1231234567890123456784560123456789' Inexact Rounded
dqadd256 add '1231234567890123456784560123456789' 0.499999      -> '1231234567890123456784560123456789' Inexact Rounded
dqadd257 add '1231234567890123456784560123456789' 0.499999999   -> '1231234567890123456784560123456789' Inexact Rounded
dqadd258 add '1231234567890123456784560123456789' 0.5           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd259 add '1231234567890123456784560123456789' 0.500000001   -> '1231234567890123456784560123456789' Inexact Rounded
dqadd260 add '1231234567890123456784560123456789' 0.500001      -> '1231234567890123456784560123456789' Inexact Rounded
dqadd261 add '1231234567890123456784560123456789' 0.51          -> '1231234567890123456784560123456789' Inexact Rounded
dqadd262 add '1231234567890123456784560123456789' 0.6           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd263 add '1231234567890123456784560123456789' 0.9           -> '1231234567890123456784560123456789' Inexact Rounded
dqadd264 add '1231234567890123456784560123456789' 0.99999       -> '1231234567890123456784560123456789' Inexact Rounded
dqadd265 add '1231234567890123456784560123456789' 0.999999999   -> '1231234567890123456784560123456789' Inexact Rounded
dqadd266 add '1231234567890123456784560123456789' 1             -> '1231234567890123456784560123456790'
dqadd267 add '1231234567890123456784560123456789' 1.00000001    -> '1231234567890123456784560123456790' Inexact Rounded
dqadd268 add '1231234567890123456784560123456789' 1.00001       -> '1231234567890123456784560123456790' Inexact Rounded
dqadd269 add '1231234567890123456784560123456789' 1.1           -> '1231234567890123456784560123456790' Inexact Rounded

-- 1 in last place tests
rounding: half_up
dqadd301 add  -1   1      ->   0
dqadd302 add   0   1      ->   1
dqadd303 add   1   1      ->   2
dqadd304 add  12   1      ->  13
dqadd305 add  98   1      ->  99
dqadd306 add  99   1      -> 100
dqadd307 add 100   1      -> 101
dqadd308 add 101   1      -> 102
dqadd309 add  -1  -1      ->  -2
dqadd310 add   0  -1      ->  -1
dqadd311 add   1  -1      ->   0
dqadd312 add  12  -1      ->  11
dqadd313 add  98  -1      ->  97
dqadd314 add  99  -1      ->  98
dqadd315 add 100  -1      ->  99
dqadd316 add 101  -1      -> 100

dqadd321 add -0.01  0.01    ->  0.00
dqadd322 add  0.00  0.01    ->  0.01
dqadd323 add  0.01  0.01    ->  0.02
dqadd324 add  0.12  0.01    ->  0.13
dqadd325 add  0.98  0.01    ->  0.99
dqadd326 add  0.99  0.01    ->  1.00
dqadd327 add  1.00  0.01    ->  1.01
dqadd328 add  1.01  0.01    ->  1.02
dqadd329 add -0.01 -0.01    -> -0.02
dqadd330 add  0.00 -0.01    -> -0.01
dqadd331 add  0.01 -0.01    ->  0.00
dqadd332 add  0.12 -0.01    ->  0.11
dqadd333 add  0.98 -0.01    ->  0.97
dqadd334 add  0.99 -0.01    ->  0.98
dqadd335 add  1.00 -0.01    ->  0.99
dqadd336 add  1.01 -0.01    ->  1.00

-- some more cases where adding 0 affects the coefficient
dqadd340 add 1E+3    0    ->         1000
dqadd341 add 1E+33   0    ->    1000000000000000000000000000000000
dqadd342 add 1E+34   0    ->   1.000000000000000000000000000000000E+34  Rounded
dqadd343 add 1E+35   0    ->   1.000000000000000000000000000000000E+35  Rounded
-- which simply follow from these cases ...
dqadd344 add 1E+3    1    ->         1001
dqadd345 add 1E+33   1    ->    1000000000000000000000000000000001
dqadd346 add 1E+34   1    ->   1.000000000000000000000000000000000E+34  Inexact Rounded
dqadd347 add 1E+35   1    ->   1.000000000000000000000000000000000E+35  Inexact Rounded
dqadd348 add 1E+3    7    ->         1007
dqadd349 add 1E+33   7    ->    1000000000000000000000000000000007
dqadd350 add 1E+34   7    ->   1.000000000000000000000000000000001E+34  Inexact Rounded
dqadd351 add 1E+35   7    ->   1.000000000000000000000000000000000E+35  Inexact Rounded

-- tryzeros cases
rounding:    half_up
dqadd360  add 0E+50 10000E+1  -> 1.0000E+5
dqadd361  add 0E-50 10000E+1  -> 100000.0000000000000000000000000000 Rounded
dqadd362  add 10000E+1 0E-50  -> 100000.0000000000000000000000000000 Rounded
dqadd363  add 10000E+1 10000E-50  -> 100000.0000000000000000000000000000 Rounded Inexact
dqadd364  add 9.999999999999999999999999999999999E+6144 -9.999999999999999999999999999999999E+6144 -> 0E+6111
--            1 234567890123456789012345678901234

-- a curiosity from JSR 13 testing
rounding:    half_down
dqadd370 add  999999999999999999999999999999999 815 -> 1000000000000000000000000000000814
dqadd371 add 9999999999999999999999999999999999 815 -> 1.000000000000000000000000000000081E+34 Rounded Inexact
rounding:    half_up
dqadd372 add  999999999999999999999999999999999 815 -> 1000000000000000000000000000000814
dqadd373 add 9999999999999999999999999999999999 815 -> 1.000000000000000000000000000000081E+34 Rounded Inexact
rounding:    half_even
dqadd374 add  999999999999999999999999999999999 815 -> 1000000000000000000000000000000814
dqadd375 add 9999999999999999999999999999999999 815 -> 1.000000000000000000000000000000081E+34 Rounded Inexact

-- ulp replacement tests
dqadd400 add   1   77e-32      ->  1.00000000000000000000000000000077
dqadd401 add   1   77e-33      ->  1.000000000000000000000000000000077
dqadd402 add   1   77e-34      ->  1.000000000000000000000000000000008 Inexact Rounded
dqadd403 add   1   77e-35      ->  1.000000000000000000000000000000001 Inexact Rounded
dqadd404 add   1   77e-36      ->  1.000000000000000000000000000000000 Inexact Rounded
dqadd405 add   1   77e-37      ->  1.000000000000000000000000000000000 Inexact Rounded
dqadd406 add   1   77e-299     ->  1.000000000000000000000000000000000 Inexact Rounded

dqadd410 add  10   77e-32      ->  10.00000000000000000000000000000077
dqadd411 add  10   77e-33      ->  10.00000000000000000000000000000008 Inexact Rounded
dqadd412 add  10   77e-34      ->  10.00000000000000000000000000000001 Inexact Rounded
dqadd413 add  10   77e-35      ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd414 add  10   77e-36      ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd415 add  10   77e-37      ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd416 add  10   77e-299     ->  10.00000000000000000000000000000000 Inexact Rounded

dqadd420 add  77e-32       1   ->  1.00000000000000000000000000000077
dqadd421 add  77e-33       1   ->  1.000000000000000000000000000000077
dqadd422 add  77e-34       1   ->  1.000000000000000000000000000000008 Inexact Rounded
dqadd423 add  77e-35       1   ->  1.000000000000000000000000000000001 Inexact Rounded
dqadd424 add  77e-36       1   ->  1.000000000000000000000000000000000 Inexact Rounded
dqadd425 add  77e-37       1   ->  1.000000000000000000000000000000000 Inexact Rounded
dqadd426 add  77e-299      1   ->  1.000000000000000000000000000000000 Inexact Rounded

dqadd430 add  77e-32      10   ->  10.00000000000000000000000000000077
dqadd431 add  77e-33      10   ->  10.00000000000000000000000000000008 Inexact Rounded
dqadd432 add  77e-34      10   ->  10.00000000000000000000000000000001 Inexact Rounded
dqadd433 add  77e-35      10   ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd434 add  77e-36      10   ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd435 add  77e-37      10   ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd436 add  77e-299     10   ->  10.00000000000000000000000000000000 Inexact Rounded

-- fastpath boundaries
--            1234567890123456789012345678901234
dqadd501 add '4444444444444444444444444444444444'  '5555555555555555555555555555555555' -> '9999999999999999999999999999999999'
dqadd502 add '4444444444444444444444444444444444'  '4555555555555555555555555555555555' -> '8999999999999999999999999999999999'
dqadd503 add '4444444444444444444444444444444444'  '3555555555555555555055555555555555' -> '7999999999999999999499999999999999'
dqadd504 add '4444444444444444444444444444444444'  '3955555555555555555555555555555555' -> '8399999999999999999999999999999999'
dqadd505 add '4444444444444444444444444444444444'  '4955555555555555555555555555555555' -> '9399999999999999999999999999999999'
dqadd506 add '4444444444444444444444444444444444'  '5955555555555555555555555555555555' -> 1.040000000000000000000000000000000E+34 Inexact Rounded
dqadd511 add '344444444444444444444444444444444'  '555555555555555555555555555555555' -> '899999999999999999999999999999999'
dqadd512 add '34444444444444444444444444444444'  '55555555555555555555555555555555' -> '89999999999999999999999999999999'
dqadd513 add '3444444444444444444444444444444'  '5555555555555555555555555555555' -> '8999999999999999999999999999999'
dqadd514 add '344444444444444444444444444444'  '555555555555555555555555555555' -> '899999999999999999999999999999'
dqadd515 add '34444444444444444444444444444'  '55555555555555555555555555555' -> '89999999999999999999999999999'
dqadd516 add '3444444444444444444444444444'  '5555555555555555555555555555' -> '8999999999999999999999999999'
dqadd517 add '344444444444444444444444444'  '555555555555555555555555555' -> '899999999999999999999999999'
dqadd518 add '34444444444444444444444444'  '55555555555555555555555555' -> '89999999999999999999999999'
dqadd519 add '3444444444444444444444444'  '5555555555555555555555555' -> '8999999999999999999999999'
dqadd520 add '344444444444444444444444'  '555555555555555555555555' -> '899999999999999999999999'
dqadd521 add '34444444444444444444444'  '55555555555555555555555' -> '89999999999999999999999'
dqadd522 add '3444444444444444444444'  '5555555555555555555555' -> '8999999999999999999999'
dqadd523 add '4444444444444444444444'  '3333333333333333333333' -> '7777777777777777777777'
dqadd524 add '344444444444444444444'  '555555555555555555555' -> '899999999999999999999'
dqadd525 add '34444444444444444444'  '55555555555555555555' -> '89999999999999999999'
dqadd526 add '3444444444444444444'  '5555555555555555555' -> '8999999999999999999'
dqadd527 add '344444444444444444'  '555555555555555555' -> '899999999999999999'
dqadd528 add '34444444444444444'  '55555555555555555' -> '89999999999999999'
dqadd529 add '3444444444444444'  '5555555555555555' -> '8999999999999999'
dqadd530 add '344444444444444'  '555555555555555' -> '899999999999999'
dqadd531 add '34444444444444'  '55555555555555' -> '89999999999999'
dqadd532 add '3444444444444'  '5555555555555' -> '8999999999999'
dqadd533 add '344444444444'  '555555555555' -> '899999999999'
dqadd534 add '34444444444'  '55555555555' -> '89999999999'
dqadd535 add '3444444444'  '5555555555' -> '8999999999'
dqadd536 add '344444444'  '555555555' -> '899999999'
dqadd537 add '34444444'  '55555555' -> '89999999'
dqadd538 add '3444444'  '5555555' -> '8999999'
dqadd539 add '344444'  '555555' -> '899999'
dqadd540 add '34444'  '55555' -> '89999'
dqadd541 add '3444'  '5555' -> '8999'
dqadd542 add '344'  '555' -> '899'
dqadd543 add '34'  '55' -> '89'
dqadd544 add '3'  '5' -> '8'

dqadd545 add '3000004000000000000000000000000000'  '3000000000000040000000000000000000' -> '6000004000000040000000000000000000'
dqadd546 add '3000000400000000000000000000000000'  '4000000000000400000000000000000000' -> '7000000400000400000000000000000000'
dqadd547 add '3000000040000000000000000000000000'  '5000000000004000000000000000000000' -> '8000000040004000000000000000000000'
dqadd548 add '4000000004000000000000000000000000'  '3000000000040000000000000000000000' -> '7000000004040000000000000000000000'
dqadd549 add '4000000000400000000000000000000000'  '4000000000400000000000000000000000' -> '8000000000800000000000000000000000'
dqadd550 add '4000000000040000000000000000000000'  '5000000004000000000000000000000000' -> '9000000004040000000000000000000000'
dqadd551 add '5000000000004000000000000000000000'  '3000000040000000000000000000000000' -> '8000000040004000000000000000000000'
dqadd552 add '5000000000000400000000000000000000'  '4000000400000000000000000000000000' -> '9000000400000400000000000000000000'
dqadd553 add '5000000000000040000000000000000000'  '5000004000000000000000000000000000' -> 1.000000400000004000000000000000000E+34 Rounded
-- check propagation
dqadd554 add '8999999999999999999999999999999999'  '0000000000000000000000000000000001' ->  9000000000000000000000000000000000
dqadd555 add '0000000000000000000000000000000001'  '8999999999999999999999999999999999' ->  9000000000000000000000000000000000
dqadd556 add '4444444444444444444444444444444444'  '4555555555555555555555555555555556' ->  9000000000000000000000000000000000
dqadd557 add '4555555555555555555555555555555556'  '4444444444444444444444444444444444' ->  9000000000000000000000000000000000

-- negative ulps
dqadd6440 add   1   -77e-32      ->  0.99999999999999999999999999999923
dqadd6441 add   1   -77e-33      ->  0.999999999999999999999999999999923
dqadd6442 add   1   -77e-34      ->  0.9999999999999999999999999999999923
dqadd6443 add   1   -77e-35      ->  0.9999999999999999999999999999999992 Inexact Rounded
dqadd6444 add   1   -77e-36      ->  0.9999999999999999999999999999999999 Inexact Rounded
dqadd6445 add   1   -77e-37      ->  1.000000000000000000000000000000000 Inexact Rounded
dqadd6446 add   1   -77e-99      ->  1.000000000000000000000000000000000 Inexact Rounded

dqadd6450 add  10   -77e-32      ->   9.99999999999999999999999999999923
dqadd6451 add  10   -77e-33      ->   9.999999999999999999999999999999923
dqadd6452 add  10   -77e-34      ->   9.999999999999999999999999999999992 Inexact Rounded
dqadd6453 add  10   -77e-35      ->   9.999999999999999999999999999999999 Inexact Rounded
dqadd6454 add  10   -77e-36      ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd6455 add  10   -77e-37      ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd6456 add  10   -77e-99      ->  10.00000000000000000000000000000000 Inexact Rounded

dqadd6460 add  -77e-32       1   ->  0.99999999999999999999999999999923
dqadd6461 add  -77e-33       1   ->  0.999999999999999999999999999999923
dqadd6462 add  -77e-34       1   ->  0.9999999999999999999999999999999923
dqadd6463 add  -77e-35       1   ->  0.9999999999999999999999999999999992 Inexact Rounded
dqadd6464 add  -77e-36       1   ->  0.9999999999999999999999999999999999 Inexact Rounded
dqadd6465 add  -77e-37       1   ->  1.000000000000000000000000000000000 Inexact Rounded
dqadd6466 add  -77e-99       1   ->  1.000000000000000000000000000000000 Inexact Rounded

dqadd6470 add  -77e-32      10   ->   9.99999999999999999999999999999923
dqadd6471 add  -77e-33      10   ->   9.999999999999999999999999999999923
dqadd6472 add  -77e-34      10   ->   9.999999999999999999999999999999992 Inexact Rounded
dqadd6473 add  -77e-35      10   ->   9.999999999999999999999999999999999 Inexact Rounded
dqadd6474 add  -77e-36      10   ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd6475 add  -77e-37      10   ->  10.00000000000000000000000000000000 Inexact Rounded
dqadd6476 add  -77e-99      10   ->  10.00000000000000000000000000000000 Inexact Rounded

-- negative ulps
dqadd6480 add  -1    77e-32      ->  -0.99999999999999999999999999999923
dqadd6481 add  -1    77e-33      ->  -0.999999999999999999999999999999923
dqadd6482 add  -1    77e-34      ->  -0.9999999999999999999999999999999923
dqadd6483 add  -1    77e-35      ->  -0.9999999999999999999999999999999992 Inexact Rounded
dqadd6484 add  -1    77e-36      ->  -0.9999999999999999999999999999999999 Inexact Rounded
dqadd6485 add  -1    77e-37      ->  -1.000000000000000000000000000000000 Inexact Rounded
dqadd6486 add  -1    77e-99      ->  -1.000000000000000000000000000000000 Inexact Rounded

dqadd6490 add -10    77e-32      ->   -9.99999999999999999999999999999923
dqadd6491 add -10    77e-33      ->   -9.999999999999999999999999999999923
dqadd6492 add -10    77e-34      ->   -9.999999999999999999999999999999992 Inexact Rounded
dqadd6493 add -10    77e-35      ->   -9.999999999999999999999999999999999 Inexact Rounded
dqadd6494 add -10    77e-36      ->  -10.00000000000000000000000000000000 Inexact Rounded
dqadd6495 add -10    77e-37      ->  -10.00000000000000000000000000000000 Inexact Rounded
dqadd6496 add -10    77e-99      ->  -10.00000000000000000000000000000000 Inexact Rounded

dqadd6500 add   77e-32      -1   ->  -0.99999999999999999999999999999923
dqadd6501 add   77e-33      -1   ->  -0.999999999999999999999999999999923
dqadd6502 add   77e-34      -1   ->  -0.9999999999999999999999999999999923
dqadd6503 add   77e-35      -1   ->  -0.9999999999999999999999999999999992 Inexact Rounded
dqadd6504 add   77e-36      -1   ->  -0.9999999999999999999999999999999999 Inexact Rounded
dqadd6505 add   77e-37      -1   ->  -1.000000000000000000000000000000000 Inexact Rounded
dqadd6506 add   77e-99      -1   ->  -1.000000000000000000000000000000000 Inexact Rounded

dqadd6510 add   77e-32      -10  ->   -9.99999999999999999999999999999923
dqadd6511 add   77e-33      -10  ->   -9.999999999999999999999999999999923
dqadd6512 add   77e-34      -10  ->   -9.999999999999999999999999999999992 Inexact Rounded
dqadd6513 add   77e-35      -10  ->   -9.999999999999999999999999999999999 Inexact Rounded
dqadd6514 add   77e-36      -10  ->  -10.00000000000000000000000000000000 Inexact Rounded
dqadd6515 add   77e-37      -10  ->  -10.00000000000000000000000000000000 Inexact Rounded
dqadd6516 add   77e-99      -10  ->  -10.00000000000000000000000000000000 Inexact Rounded

-- and some more residue effects and different roundings
rounding: half_up
dqadd6540 add '9876543219876543216543210123456789' 0             -> '9876543219876543216543210123456789'
dqadd6541 add '9876543219876543216543210123456789' 0.000000001   -> '9876543219876543216543210123456789' Inexact Rounded
dqadd6542 add '9876543219876543216543210123456789' 0.000001      -> '9876543219876543216543210123456789' Inexact Rounded
dqadd6543 add '9876543219876543216543210123456789' 0.1           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd6544 add '9876543219876543216543210123456789' 0.4           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd6545 add '9876543219876543216543210123456789' 0.49          -> '9876543219876543216543210123456789' Inexact Rounded
dqadd6546 add '9876543219876543216543210123456789' 0.499999      -> '9876543219876543216543210123456789' Inexact Rounded
dqadd6547 add '9876543219876543216543210123456789' 0.499999999   -> '9876543219876543216543210123456789' Inexact Rounded
dqadd6548 add '9876543219876543216543210123456789' 0.5           -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6549 add '9876543219876543216543210123456789' 0.500000001   -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6550 add '9876543219876543216543210123456789' 0.500001      -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6551 add '9876543219876543216543210123456789' 0.51          -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6552 add '9876543219876543216543210123456789' 0.6           -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6553 add '9876543219876543216543210123456789' 0.9           -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6554 add '9876543219876543216543210123456789' 0.99999       -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6555 add '9876543219876543216543210123456789' 0.999999999   -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6556 add '9876543219876543216543210123456789' 1             -> '9876543219876543216543210123456790'
dqadd6557 add '9876543219876543216543210123456789' 1.000000001   -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6558 add '9876543219876543216543210123456789' 1.00001       -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6559 add '9876543219876543216543210123456789' 1.1           -> '9876543219876543216543210123456790' Inexact Rounded

rounding: half_even
dqadd6560 add '9876543219876543216543210123456789' 0             -> '9876543219876543216543210123456789'
dqadd6561 add '9876543219876543216543210123456789' 0.000000001   -> '9876543219876543216543210123456789' Inexact Rounded
dqadd6562 add '9876543219876543216543210123456789' 0.000001      -> '9876543219876543216543210123456789' Inexact Rounded
dqadd6563 add '9876543219876543216543210123456789' 0.1           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd6564 add '9876543219876543216543210123456789' 0.4           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd6565 add '9876543219876543216543210123456789' 0.49          -> '9876543219876543216543210123456789' Inexact Rounded
dqadd6566 add '9876543219876543216543210123456789' 0.499999      -> '9876543219876543216543210123456789' Inexact Rounded
dqadd6567 add '9876543219876543216543210123456789' 0.499999999   -> '9876543219876543216543210123456789' Inexact Rounded
dqadd6568 add '9876543219876543216543210123456789' 0.5           -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6569 add '9876543219876543216543210123456789' 0.500000001   -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6570 add '9876543219876543216543210123456789' 0.500001      -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6571 add '9876543219876543216543210123456789' 0.51          -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6572 add '9876543219876543216543210123456789' 0.6           -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6573 add '9876543219876543216543210123456789' 0.9           -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6574 add '9876543219876543216543210123456789' 0.99999       -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6575 add '9876543219876543216543210123456789' 0.999999999   -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6576 add '9876543219876543216543210123456789' 1             -> '9876543219876543216543210123456790'
dqadd6577 add '9876543219876543216543210123456789' 1.00000001    -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6578 add '9876543219876543216543210123456789' 1.00001       -> '9876543219876543216543210123456790' Inexact Rounded
dqadd6579 add '9876543219876543216543210123456789' 1.1           -> '9876543219876543216543210123456790' Inexact Rounded

-- critical few with even bottom digit...
dqadd7540 add '9876543219876543216543210123456788' 0.499999999   -> '9876543219876543216543210123456788' Inexact Rounded
dqadd7541 add '9876543219876543216543210123456788' 0.5           -> '9876543219876543216543210123456788' Inexact Rounded
dqadd7542 add '9876543219876543216543210123456788' 0.500000001   -> '9876543219876543216543210123456789' Inexact Rounded

rounding: down
dqadd7550 add '9876543219876543216543210123456789' 0             -> '9876543219876543216543210123456789'
dqadd7551 add '9876543219876543216543210123456789' 0.000000001   -> '9876543219876543216543210123456789' Inexact Rounded
dqadd7552 add '9876543219876543216543210123456789' 0.000001      -> '9876543219876543216543210123456789' Inexact Rounded
dqadd7553 add '9876543219876543216543210123456789' 0.1           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd7554 add '9876543219876543216543210123456789' 0.4           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd7555 add '9876543219876543216543210123456789' 0.49          -> '9876543219876543216543210123456789' Inexact Rounded
dqadd7556 add '9876543219876543216543210123456789' 0.499999      -> '9876543219876543216543210123456789' Inexact Rounded
dqadd7557 add '9876543219876543216543210123456789' 0.499999999   -> '9876543219876543216543210123456789' Inexact Rounded
dqadd7558 add '9876543219876543216543210123456789' 0.5           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd7559 add '9876543219876543216543210123456789' 0.500000001   -> '9876543219876543216543210123456789' Inexact Rounded
dqadd7560 add '9876543219876543216543210123456789' 0.500001      -> '9876543219876543216543210123456789' Inexact Rounded
dqadd7561 add '9876543219876543216543210123456789' 0.51          -> '9876543219876543216543210123456789' Inexact Rounded
dqadd7562 add '9876543219876543216543210123456789' 0.6           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd7563 add '9876543219876543216543210123456789' 0.9           -> '9876543219876543216543210123456789' Inexact Rounded
dqadd7564 add '9876543219876543216543210123456789' 0.99999       -> '9876543219876543216543210123456789' Inexact Rounded
dqadd7565 add '9876543219876543216543210123456789' 0.999999999   -> '9876543219876543216543210123456789' Inexact Rounded
dqadd7566 add '9876543219876543216543210123456789' 1             -> '9876543219876543216543210123456790'
dqadd7567 add '9876543219876543216543210123456789' 1.00000001    -> '9876543219876543216543210123456790' Inexact Rounded
dqadd7568 add '9876543219876543216543210123456789' 1.00001       -> '9876543219876543216543210123456790' Inexact Rounded
dqadd7569 add '9876543219876543216543210123456789' 1.1           -> '9876543219876543216543210123456790' Inexact Rounded

-- more zeros, etc.
rounding: half_even

dqadd7701 add 5.00 1.00E-3 -> 5.00100
dqadd7702 add 00.00 0.000  -> 0.000
dqadd7703 add 00.00 0E-3   -> 0.000
dqadd7704 add 0E-3  00.00  -> 0.000

dqadd7710 add 0E+3  00.00  -> 0.00
dqadd7711 add 0E+3  00.0   -> 0.0
dqadd7712 add 0E+3  00.    -> 0
dqadd7713 add 0E+3  00.E+1 -> 0E+1
dqadd7714 add 0E+3  00.E+2 -> 0E+2
dqadd7715 add 0E+3  00.E+3 -> 0E+3
dqadd7716 add 0E+3  00.E+4 -> 0E+3
dqadd7717 add 0E+3  00.E+5 -> 0E+3
dqadd7718 add 0E+3  -00.0   -> 0.0
dqadd7719 add 0E+3  -00.    -> 0
dqadd7731 add 0E+3  -00.E+1 -> 0E+1

dqadd7720 add 00.00  0E+3  -> 0.00
dqadd7721 add 00.0   0E+3  -> 0.0
dqadd7722 add 00.    0E+3  -> 0
dqadd7723 add 00.E+1 0E+3  -> 0E+1
dqadd7724 add 00.E+2 0E+3  -> 0E+2
dqadd7725 add 00.E+3 0E+3  -> 0E+3
dqadd7726 add 00.E+4 0E+3  -> 0E+3
dqadd7727 add 00.E+5 0E+3  -> 0E+3
dqadd7728 add -00.00 0E+3  -> 0.00
dqadd7729 add -00.0  0E+3  -> 0.0
dqadd7730 add -00.   0E+3  -> 0

dqadd7732 add  0     0     ->  0
dqadd7733 add  0    -0     ->  0
dqadd7734 add -0     0     ->  0
dqadd7735 add -0    -0     -> -0     -- IEEE 754 special case

dqadd7736 add  1    -1     ->  0
dqadd7737 add -1    -1     -> -2
dqadd7738 add  1     1     ->  2
dqadd7739 add -1     1     ->  0

dqadd7741 add  0    -1     -> -1
dqadd7742 add -0    -1     -> -1
dqadd7743 add  0     1     ->  1
dqadd7744 add -0     1     ->  1
dqadd7745 add -1     0     -> -1
dqadd7746 add -1    -0     -> -1
dqadd7747 add  1     0     ->  1
dqadd7748 add  1    -0     ->  1

dqadd7751 add  0.0  -1     -> -1.0
dqadd7752 add -0.0  -1     -> -1.0
dqadd7753 add  0.0   1     ->  1.0
dqadd7754 add -0.0   1     ->  1.0
dqadd7755 add -1.0   0     -> -1.0
dqadd7756 add -1.0  -0     -> -1.0
dqadd7757 add  1.0   0     ->  1.0
dqadd7758 add  1.0  -0     ->  1.0

dqadd7761 add  0    -1.0   -> -1.0
dqadd7762 add -0    -1.0   -> -1.0
dqadd7763 add  0     1.0   ->  1.0
dqadd7764 add -0     1.0   ->  1.0
dqadd7765 add -1     0.0   -> -1.0
dqadd7766 add -1    -0.0   -> -1.0
dqadd7767 add  1     0.0   ->  1.0
dqadd7768 add  1    -0.0   ->  1.0

dqadd7771 add  0.0  -1.0   -> -1.0
dqadd7772 add -0.0  -1.0   -> -1.0
dqadd7773 add  0.0   1.0   ->  1.0
dqadd7774 add -0.0   1.0   ->  1.0
dqadd7775 add -1.0   0.0   -> -1.0
dqadd7776 add -1.0  -0.0   -> -1.0
dqadd7777 add  1.0   0.0   ->  1.0
dqadd7778 add  1.0  -0.0   ->  1.0

-- Specials
dqadd7780 add -Inf  -Inf   -> -Infinity
dqadd7781 add -Inf  -1000  -> -Infinity
dqadd7782 add -Inf  -1     -> -Infinity
dqadd7783 add -Inf  -0     -> -Infinity
dqadd7784 add -Inf   0     -> -Infinity
dqadd7785 add -Inf   1     -> -Infinity
dqadd7786 add -Inf   1000  -> -Infinity
dqadd7787 add -1000 -Inf   -> -Infinity
dqadd7788 add -Inf  -Inf   -> -Infinity
dqadd7789 add -1    -Inf   -> -Infinity
dqadd7790 add -0    -Inf   -> -Infinity
dqadd7791 add  0    -Inf   -> -Infinity
dqadd7792 add  1    -Inf   -> -Infinity
dqadd7793 add  1000 -Inf   -> -Infinity
dqadd7794 add  Inf  -Inf   ->  NaN  Invalid_operation

dqadd7800 add  Inf  -Inf   ->  NaN  Invalid_operation
dqadd7801 add  Inf  -1000  ->  Infinity
dqadd7802 add  Inf  -1     ->  Infinity
dqadd7803 add  Inf  -0     ->  Infinity
dqadd7804 add  Inf   0     ->  Infinity
dqadd7805 add  Inf   1     ->  Infinity
dqadd7806 add  Inf   1000  ->  Infinity
dqadd7807 add  Inf   Inf   ->  Infinity
dqadd7808 add -1000  Inf   ->  Infinity
dqadd7809 add -Inf   Inf   ->  NaN  Invalid_operation
dqadd7810 add -1     Inf   ->  Infinity
dqadd7811 add -0     Inf   ->  Infinity
dqadd7812 add  0     Inf   ->  Infinity
dqadd7813 add  1     Inf   ->  Infinity
dqadd7814 add  1000  Inf   ->  Infinity
dqadd7815 add  Inf   Inf   ->  Infinity

dqadd7821 add  NaN -Inf    ->  NaN
dqadd7822 add  NaN -1000   ->  NaN
dqadd7823 add  NaN -1      ->  NaN
dqadd7824 add  NaN -0      ->  NaN
dqadd7825 add  NaN  0      ->  NaN
dqadd7826 add  NaN  1      ->  NaN
dqadd7827 add  NaN  1000   ->  NaN
dqadd7828 add  NaN  Inf    ->  NaN
dqadd7829 add  NaN  NaN    ->  NaN
dqadd7830 add -Inf  NaN    ->  NaN
dqadd7831 add -1000 NaN    ->  NaN
dqadd7832 add -1    NaN    ->  NaN
dqadd7833 add -0    NaN    ->  NaN
dqadd7834 add  0    NaN    ->  NaN
dqadd7835 add  1    NaN    ->  NaN
dqadd7836 add  1000 NaN    ->  NaN
dqadd7837 add  Inf  NaN    ->  NaN

dqadd7841 add  sNaN -Inf   ->  NaN  Invalid_operation
dqadd7842 add  sNaN -1000  ->  NaN  Invalid_operation
dqadd7843 add  sNaN -1     ->  NaN  Invalid_operation
dqadd7844 add  sNaN -0     ->  NaN  Invalid_operation
dqadd7845 add  sNaN  0     ->  NaN  Invalid_operation
dqadd7846 add  sNaN  1     ->  NaN  Invalid_operation
dqadd7847 add  sNaN  1000  ->  NaN  Invalid_operation
dqadd7848 add  sNaN  NaN   ->  NaN  Invalid_operation
dqadd7849 add  sNaN sNaN   ->  NaN  Invalid_operation
dqadd7850 add  NaN  sNaN   ->  NaN  Invalid_operation
dqadd7851 add -Inf  sNaN   ->  NaN  Invalid_operation
dqadd7852 add -1000 sNaN   ->  NaN  Invalid_operation
dqadd7853 add -1    sNaN   ->  NaN  Invalid_operation
dqadd7854 add -0    sNaN   ->  NaN  Invalid_operation
dqadd7855 add  0    sNaN   ->  NaN  Invalid_operation
dqadd7856 add  1    sNaN   ->  NaN  Invalid_operation
dqadd7857 add  1000 sNaN   ->  NaN  Invalid_operation
dqadd7858 add  Inf  sNaN   ->  NaN  Invalid_operation
dqadd7859 add  NaN  sNaN   ->  NaN  Invalid_operation

-- propagating NaNs
dqadd7861 add  NaN1   -Inf    ->  NaN1
dqadd7862 add +NaN2   -1000   ->  NaN2
dqadd7863 add  NaN3    1000   ->  NaN3
dqadd7864 add  NaN4    Inf    ->  NaN4
dqadd7865 add  NaN5   +NaN6   ->  NaN5
dqadd7866 add -Inf     NaN7   ->  NaN7
dqadd7867 add -1000    NaN8   ->  NaN8
dqadd7868 add  1000    NaN9   ->  NaN9
dqadd7869 add  Inf    +NaN10  ->  NaN10
dqadd7871 add  sNaN11  -Inf   ->  NaN11  Invalid_operation
dqadd7872 add  sNaN12  -1000  ->  NaN12  Invalid_operation
dqadd7873 add  sNaN13   1000  ->  NaN13  Invalid_operation
dqadd7874 add  sNaN14   NaN17 ->  NaN14  Invalid_operation
dqadd7875 add  sNaN15  sNaN18 ->  NaN15  Invalid_operation
dqadd7876 add  NaN16   sNaN19 ->  NaN19  Invalid_operation
dqadd7877 add -Inf    +sNaN20 ->  NaN20  Invalid_operation
dqadd7878 add -1000    sNaN21 ->  NaN21  Invalid_operation
dqadd7879 add  1000    sNaN22 ->  NaN22  Invalid_operation
dqadd7880 add  Inf     sNaN23 ->  NaN23  Invalid_operation
dqadd7881 add +NaN25  +sNaN24 ->  NaN24  Invalid_operation
dqadd7882 add -NaN26    NaN28 -> -NaN26
dqadd7883 add -sNaN27  sNaN29 -> -NaN27  Invalid_operation
dqadd7884 add  1000    -NaN30 -> -NaN30
dqadd7885 add  1000   -sNaN31 -> -NaN31  Invalid_operation

-- Here we explore near the boundary of rounding a subnormal to Nmin
dqadd7575 add  1E-6143 -1E-6176 ->  9.99999999999999999999999999999999E-6144 Subnormal
dqadd7576 add -1E-6143 +1E-6176 -> -9.99999999999999999999999999999999E-6144 Subnormal

-- check overflow edge case
--               1234567890123456
dqadd7972 apply   9.999999999999999999999999999999999E+6144         -> 9.999999999999999999999999999999999E+6144
dqadd7973 add     9.999999999999999999999999999999999E+6144  1      -> 9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd7974 add      9999999999999999999999999999999999E+6111  1      -> 9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd7975 add      9999999999999999999999999999999999E+6111  1E+6111  -> Infinity Overflow Inexact Rounded
dqadd7976 add      9999999999999999999999999999999999E+6111  9E+6110  -> Infinity Overflow Inexact Rounded
dqadd7977 add      9999999999999999999999999999999999E+6111  8E+6110  -> Infinity Overflow Inexact Rounded
dqadd7978 add      9999999999999999999999999999999999E+6111  7E+6110  -> Infinity Overflow Inexact Rounded
dqadd7979 add      9999999999999999999999999999999999E+6111  6E+6110  -> Infinity Overflow Inexact Rounded
dqadd7980 add      9999999999999999999999999999999999E+6111  5E+6110  -> Infinity Overflow Inexact Rounded
dqadd7981 add      9999999999999999999999999999999999E+6111  4E+6110  -> 9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd7982 add      9999999999999999999999999999999999E+6111  3E+6110  -> 9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd7983 add      9999999999999999999999999999999999E+6111  2E+6110  -> 9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd7984 add      9999999999999999999999999999999999E+6111  1E+6110  -> 9.999999999999999999999999999999999E+6144 Inexact Rounded

dqadd7985 apply  -9.999999999999999999999999999999999E+6144         -> -9.999999999999999999999999999999999E+6144
dqadd7986 add    -9.999999999999999999999999999999999E+6144 -1      -> -9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd7987 add     -9999999999999999999999999999999999E+6111 -1      -> -9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd7988 add     -9999999999999999999999999999999999E+6111 -1E+6111  -> -Infinity Overflow Inexact Rounded
dqadd7989 add     -9999999999999999999999999999999999E+6111 -9E+6110  -> -Infinity Overflow Inexact Rounded
dqadd7990 add     -9999999999999999999999999999999999E+6111 -8E+6110  -> -Infinity Overflow Inexact Rounded
dqadd7991 add     -9999999999999999999999999999999999E+6111 -7E+6110  -> -Infinity Overflow Inexact Rounded
dqadd7992 add     -9999999999999999999999999999999999E+6111 -6E+6110  -> -Infinity Overflow Inexact Rounded
dqadd7993 add     -9999999999999999999999999999999999E+6111 -5E+6110  -> -Infinity Overflow Inexact Rounded
dqadd7994 add     -9999999999999999999999999999999999E+6111 -4E+6110  -> -9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd7995 add     -9999999999999999999999999999999999E+6111 -3E+6110  -> -9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd7996 add     -9999999999999999999999999999999999E+6111 -2E+6110  -> -9.999999999999999999999999999999999E+6144 Inexact Rounded
dqadd7997 add     -9999999999999999999999999999999999E+6111 -1E+6110  -> -9.999999999999999999999999999999999E+6144 Inexact Rounded

-- And for round down full and subnormal results
rounding:     down
dqadd71100 add 1e+2 -1e-6143    -> 99.99999999999999999999999999999999 Rounded Inexact
dqadd71101 add 1e+1 -1e-6143    -> 9.999999999999999999999999999999999  Rounded Inexact
dqadd71103 add   +1 -1e-6143    -> 0.9999999999999999999999999999999999  Rounded Inexact
dqadd71104 add 1e-1 -1e-6143    -> 0.09999999999999999999999999999999999  Rounded Inexact
dqadd71105 add 1e-2 -1e-6143    -> 0.009999999999999999999999999999999999  Rounded Inexact
dqadd71106 add 1e-3 -1e-6143    -> 0.0009999999999999999999999999999999999  Rounded Inexact
dqadd71107 add 1e-4 -1e-6143    -> 0.00009999999999999999999999999999999999  Rounded Inexact
dqadd71108 add 1e-5 -1e-6143    -> 0.000009999999999999999999999999999999999  Rounded Inexact
dqadd71109 add 1e-6 -1e-6143    -> 9.999999999999999999999999999999999E-7  Rounded Inexact

rounding:     ceiling
dqadd71110 add -1e+2 +1e-6143   -> -99.99999999999999999999999999999999 Rounded Inexact
dqadd71111 add -1e+1 +1e-6143   -> -9.999999999999999999999999999999999  Rounded Inexact
dqadd71113 add    -1 +1e-6143   -> -0.9999999999999999999999999999999999  Rounded Inexact
dqadd71114 add -1e-1 +1e-6143   -> -0.09999999999999999999999999999999999  Rounded Inexact
dqadd71115 add -1e-2 +1e-6143   -> -0.009999999999999999999999999999999999  Rounded Inexact
dqadd71116 add -1e-3 +1e-6143   -> -0.0009999999999999999999999999999999999  Rounded Inexact
dqadd71117 add -1e-4 +1e-6143   -> -0.00009999999999999999999999999999999999  Rounded Inexact
dqadd71118 add -1e-5 +1e-6143   -> -0.000009999999999999999999999999999999999  Rounded Inexact
dqadd71119 add -1e-6 +1e-6143   -> -9.999999999999999999999999999999999E-7  Rounded Inexact

-- tests based on Gunnar Degnbol's edge case
rounding:     half_even

dqadd71300 add 1E34  -0.5                 ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71310 add 1E34  -0.51                ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71311 add 1E34  -0.501               ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71312 add 1E34  -0.5001              ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71313 add 1E34  -0.50001             ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71314 add 1E34  -0.500001            ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71315 add 1E34  -0.5000001           ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71316 add 1E34  -0.50000001          ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71317 add 1E34  -0.500000001         ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71318 add 1E34  -0.5000000001        ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71319 add 1E34  -0.50000000001       ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71320 add 1E34  -0.500000000001      ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71321 add 1E34  -0.5000000000001     ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71322 add 1E34  -0.50000000000001    ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71323 add 1E34  -0.500000000000001   ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71324 add 1E34  -0.5000000000000001  ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71325 add 1E34  -0.5000000000000000  ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71326 add 1E34  -0.500000000000000   ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71327 add 1E34  -0.50000000000000    ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71328 add 1E34  -0.5000000000000     ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71329 add 1E34  -0.500000000000      ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71330 add 1E34  -0.50000000000       ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71331 add 1E34  -0.5000000000        ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71332 add 1E34  -0.500000000         ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71333 add 1E34  -0.50000000          ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71334 add 1E34  -0.5000000           ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71335 add 1E34  -0.500000            ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71336 add 1E34  -0.50000             ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71337 add 1E34  -0.5000              ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71338 add 1E34  -0.500               ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71339 add 1E34  -0.50                ->  1.000000000000000000000000000000000E+34 Inexact Rounded

dqadd71340 add 1E34  -5000000.000010001   ->  9999999999999999999999999995000000      Inexact Rounded
dqadd71341 add 1E34  -5000000.000000001   ->  9999999999999999999999999995000000      Inexact Rounded

dqadd71349 add 9999999999999999999999999999999999 0.4                 ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71350 add 9999999999999999999999999999999999 0.49                ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71351 add 9999999999999999999999999999999999 0.499               ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71352 add 9999999999999999999999999999999999 0.4999              ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71353 add 9999999999999999999999999999999999 0.49999             ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71354 add 9999999999999999999999999999999999 0.499999            ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71355 add 9999999999999999999999999999999999 0.4999999           ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71356 add 9999999999999999999999999999999999 0.49999999          ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71357 add 9999999999999999999999999999999999 0.499999999         ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71358 add 9999999999999999999999999999999999 0.4999999999        ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71359 add 9999999999999999999999999999999999 0.49999999999       ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71360 add 9999999999999999999999999999999999 0.499999999999      ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71361 add 9999999999999999999999999999999999 0.4999999999999     ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71362 add 9999999999999999999999999999999999 0.49999999999999    ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71363 add 9999999999999999999999999999999999 0.499999999999999   ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71364 add 9999999999999999999999999999999999 0.4999999999999999  ->  9999999999999999999999999999999999      Inexact Rounded
dqadd71365 add 9999999999999999999999999999999999 0.5000000000000000  ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71367 add 9999999999999999999999999999999999 0.500000000000000   ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71368 add 9999999999999999999999999999999999 0.50000000000000    ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71369 add 9999999999999999999999999999999999 0.5000000000000     ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71370 add 9999999999999999999999999999999999 0.500000000000      ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71371 add 9999999999999999999999999999999999 0.50000000000       ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71372 add 9999999999999999999999999999999999 0.5000000000        ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71373 add 9999999999999999999999999999999999 0.500000000         ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71374 add 9999999999999999999999999999999999 0.50000000          ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71375 add 9999999999999999999999999999999999 0.5000000           ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71376 add 9999999999999999999999999999999999 0.500000            ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71377 add 9999999999999999999999999999999999 0.50000             ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71378 add 9999999999999999999999999999999999 0.5000              ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71379 add 9999999999999999999999999999999999 0.500               ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71380 add 9999999999999999999999999999999999 0.50                ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71381 add 9999999999999999999999999999999999 0.5                 ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71382 add 9999999999999999999999999999999999 0.5000000000000001  ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71383 add 9999999999999999999999999999999999 0.500000000000001   ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71384 add 9999999999999999999999999999999999 0.50000000000001    ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71385 add 9999999999999999999999999999999999 0.5000000000001     ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71386 add 9999999999999999999999999999999999 0.500000000001      ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71387 add 9999999999999999999999999999999999 0.50000000001       ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71388 add 9999999999999999999999999999999999 0.5000000001        ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71389 add 9999999999999999999999999999999999 0.500000001         ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71390 add 9999999999999999999999999999999999 0.50000001          ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71391 add 9999999999999999999999999999999999 0.5000001           ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71392 add 9999999999999999999999999999999999 0.500001            ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71393 add 9999999999999999999999999999999999 0.50001             ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71394 add 9999999999999999999999999999999999 0.5001              ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71395 add 9999999999999999999999999999999999 0.501               ->  1.000000000000000000000000000000000E+34 Inexact Rounded
dqadd71396 add 9999999999999999999999999999999999 0.51                ->  1.000000000000000000000000000000000E+34 Inexact Rounded

-- More GD edge cases, where difference between the unadjusted
-- exponents is larger than the maximum precision and one side is 0
dqadd71420 add  0 1.123456789987654321123456789012345     -> 1.123456789987654321123456789012345
dqadd71421 add  0 1.123456789987654321123456789012345E-1  -> 0.1123456789987654321123456789012345
dqadd71422 add  0 1.123456789987654321123456789012345E-2  -> 0.01123456789987654321123456789012345
dqadd71423 add  0 1.123456789987654321123456789012345E-3  -> 0.001123456789987654321123456789012345
dqadd71424 add  0 1.123456789987654321123456789012345E-4  -> 0.0001123456789987654321123456789012345
dqadd71425 add  0 1.123456789987654321123456789012345E-5  -> 0.00001123456789987654321123456789012345
dqadd71426 add  0 1.123456789987654321123456789012345E-6  -> 0.000001123456789987654321123456789012345
dqadd71427 add  0 1.123456789987654321123456789012345E-7  -> 1.123456789987654321123456789012345E-7
dqadd71428 add  0 1.123456789987654321123456789012345E-8  -> 1.123456789987654321123456789012345E-8
dqadd71429 add  0 1.123456789987654321123456789012345E-9  -> 1.123456789987654321123456789012345E-9
dqadd71430 add  0 1.123456789987654321123456789012345E-10 -> 1.123456789987654321123456789012345E-10
dqadd71431 add  0 1.123456789987654321123456789012345E-11 -> 1.123456789987654321123456789012345E-11
dqadd71432 add  0 1.123456789987654321123456789012345E-12 -> 1.123456789987654321123456789012345E-12
dqadd71433 add  0 1.123456789987654321123456789012345E-13 -> 1.123456789987654321123456789012345E-13
dqadd71434 add  0 1.123456789987654321123456789012345E-14 -> 1.123456789987654321123456789012345E-14
dqadd71435 add  0 1.123456789987654321123456789012345E-15 -> 1.123456789987654321123456789012345E-15
dqadd71436 add  0 1.123456789987654321123456789012345E-16 -> 1.123456789987654321123456789012345E-16
dqadd71437 add  0 1.123456789987654321123456789012345E-17 -> 1.123456789987654321123456789012345E-17
dqadd71438 add  0 1.123456789987654321123456789012345E-18 -> 1.123456789987654321123456789012345E-18
dqadd71439 add  0 1.123456789987654321123456789012345E-19 -> 1.123456789987654321123456789012345E-19
dqadd71440 add  0 1.123456789987654321123456789012345E-20 -> 1.123456789987654321123456789012345E-20
dqadd71441 add  0 1.123456789987654321123456789012345E-21 -> 1.123456789987654321123456789012345E-21
dqadd71442 add  0 1.123456789987654321123456789012345E-22 -> 1.123456789987654321123456789012345E-22
dqadd71443 add  0 1.123456789987654321123456789012345E-23 -> 1.123456789987654321123456789012345E-23
dqadd71444 add  0 1.123456789987654321123456789012345E-24 -> 1.123456789987654321123456789012345E-24
dqadd71445 add  0 1.123456789987654321123456789012345E-25 -> 1.123456789987654321123456789012345E-25
dqadd71446 add  0 1.123456789987654321123456789012345E-26 -> 1.123456789987654321123456789012345E-26
dqadd71447 add  0 1.123456789987654321123456789012345E-27 -> 1.123456789987654321123456789012345E-27
dqadd71448 add  0 1.123456789987654321123456789012345E-28 -> 1.123456789987654321123456789012345E-28
dqadd71449 add  0 1.123456789987654321123456789012345E-29 -> 1.123456789987654321123456789012345E-29
dqadd71450 add  0 1.123456789987654321123456789012345E-30 -> 1.123456789987654321123456789012345E-30
dqadd71451 add  0 1.123456789987654321123456789012345E-31 -> 1.123456789987654321123456789012345E-31
dqadd71452 add  0 1.123456789987654321123456789012345E-32 -> 1.123456789987654321123456789012345E-32
dqadd71453 add  0 1.123456789987654321123456789012345E-33 -> 1.123456789987654321123456789012345E-33
dqadd71454 add  0 1.123456789987654321123456789012345E-34 -> 1.123456789987654321123456789012345E-34
dqadd71455 add  0 1.123456789987654321123456789012345E-35 -> 1.123456789987654321123456789012345E-35
dqadd71456 add  0 1.123456789987654321123456789012345E-36 -> 1.123456789987654321123456789012345E-36

-- same, reversed 0
dqadd71460 add 1.123456789987654321123456789012345     0 -> 1.123456789987654321123456789012345
dqadd71461 add 1.123456789987654321123456789012345E-1  0 -> 0.1123456789987654321123456789012345
dqadd71462 add 1.123456789987654321123456789012345E-2  0 -> 0.01123456789987654321123456789012345
dqadd71463 add 1.123456789987654321123456789012345E-3  0 -> 0.001123456789987654321123456789012345
dqadd71464 add 1.123456789987654321123456789012345E-4  0 -> 0.0001123456789987654321123456789012345
dqadd71465 add 1.123456789987654321123456789012345E-5  0 -> 0.00001123456789987654321123456789012345
dqadd71466 add 1.123456789987654321123456789012345E-6  0 -> 0.000001123456789987654321123456789012345
dqadd71467 add 1.123456789987654321123456789012345E-7  0 -> 1.123456789987654321123456789012345E-7
dqadd71468 add 1.123456789987654321123456789012345E-8  0 -> 1.123456789987654321123456789012345E-8
dqadd71469 add 1.123456789987654321123456789012345E-9  0 -> 1.123456789987654321123456789012345E-9
dqadd71470 add 1.123456789987654321123456789012345E-10 0 -> 1.123456789987654321123456789012345E-10
dqadd71471 add 1.123456789987654321123456789012345E-11 0 -> 1.123456789987654321123456789012345E-11
dqadd71472 add 1.123456789987654321123456789012345E-12 0 -> 1.123456789987654321123456789012345E-12
dqadd71473 add 1.123456789987654321123456789012345E-13 0 -> 1.123456789987654321123456789012345E-13
dqadd71474 add 1.123456789987654321123456789012345E-14 0 -> 1.123456789987654321123456789012345E-14
dqadd71475 add 1.123456789987654321123456789012345E-15 0 -> 1.123456789987654321123456789012345E-15
dqadd71476 add 1.123456789987654321123456789012345E-16 0 -> 1.123456789987654321123456789012345E-16
dqadd71477 add 1.123456789987654321123456789012345E-17 0 -> 1.123456789987654321123456789012345E-17
dqadd71478 add 1.123456789987654321123456789012345E-18 0 -> 1.123456789987654321123456789012345E-18
dqadd71479 add 1.123456789987654321123456789012345E-19 0 -> 1.123456789987654321123456789012345E-19
dqadd71480 add 1.123456789987654321123456789012345E-20 0 -> 1.123456789987654321123456789012345E-20
dqadd71481 add 1.123456789987654321123456789012345E-21 0 -> 1.123456789987654321123456789012345E-21
dqadd71482 add 1.123456789987654321123456789012345E-22 0 -> 1.123456789987654321123456789012345E-22
dqadd71483 add 1.123456789987654321123456789012345E-23 0 -> 1.123456789987654321123456789012345E-23
dqadd71484 add 1.123456789987654321123456789012345E-24 0 -> 1.123456789987654321123456789012345E-24
dqadd71485 add 1.123456789987654321123456789012345E-25 0 -> 1.123456789987654321123456789012345E-25
dqadd71486 add 1.123456789987654321123456789012345E-26 0 -> 1.123456789987654321123456789012345E-26
dqadd71487 add 1.123456789987654321123456789012345E-27 0 -> 1.123456789987654321123456789012345E-27
dqadd71488 add 1.123456789987654321123456789012345E-28 0 -> 1.123456789987654321123456789012345E-28
dqadd71489 add 1.123456789987654321123456789012345E-29 0 -> 1.123456789987654321123456789012345E-29
dqadd71490 add 1.123456789987654321123456789012345E-30 0 -> 1.123456789987654321123456789012345E-30
dqadd71491 add 1.123456789987654321123456789012345E-31 0 -> 1.123456789987654321123456789012345E-31
dqadd71492 add 1.123456789987654321123456789012345E-32 0 -> 1.123456789987654321123456789012345E-32
dqadd71493 add 1.123456789987654321123456789012345E-33 0 -> 1.123456789987654321123456789012345E-33
dqadd71494 add 1.123456789987654321123456789012345E-34 0 -> 1.123456789987654321123456789012345E-34
dqadd71495 add 1.123456789987654321123456789012345E-35 0 -> 1.123456789987654321123456789012345E-35
dqadd71496 add 1.123456789987654321123456789012345E-36 0 -> 1.123456789987654321123456789012345E-36

-- same, Es on the 0
dqadd71500 add 1.123456789987654321123456789012345  0E-0   -> 1.123456789987654321123456789012345
dqadd71501 add 1.123456789987654321123456789012345  0E-1   -> 1.123456789987654321123456789012345
dqadd71502 add 1.123456789987654321123456789012345  0E-2   -> 1.123456789987654321123456789012345
dqadd71503 add 1.123456789987654321123456789012345  0E-3   -> 1.123456789987654321123456789012345
dqadd71504 add 1.123456789987654321123456789012345  0E-4   -> 1.123456789987654321123456789012345
dqadd71505 add 1.123456789987654321123456789012345  0E-5   -> 1.123456789987654321123456789012345
dqadd71506 add 1.123456789987654321123456789012345  0E-6   -> 1.123456789987654321123456789012345
dqadd71507 add 1.123456789987654321123456789012345  0E-7   -> 1.123456789987654321123456789012345
dqadd71508 add 1.123456789987654321123456789012345  0E-8   -> 1.123456789987654321123456789012345
dqadd71509 add 1.123456789987654321123456789012345  0E-9   -> 1.123456789987654321123456789012345
dqadd71510 add 1.123456789987654321123456789012345  0E-10  -> 1.123456789987654321123456789012345
dqadd71511 add 1.123456789987654321123456789012345  0E-11  -> 1.123456789987654321123456789012345
dqadd71512 add 1.123456789987654321123456789012345  0E-12  -> 1.123456789987654321123456789012345
dqadd71513 add 1.123456789987654321123456789012345  0E-13  -> 1.123456789987654321123456789012345
dqadd71514 add 1.123456789987654321123456789012345  0E-14  -> 1.123456789987654321123456789012345
dqadd71515 add 1.123456789987654321123456789012345  0E-15  -> 1.123456789987654321123456789012345
dqadd71516 add 1.123456789987654321123456789012345  0E-16  -> 1.123456789987654321123456789012345
dqadd71517 add 1.123456789987654321123456789012345  0E-17  -> 1.123456789987654321123456789012345
dqadd71518 add 1.123456789987654321123456789012345  0E-18  -> 1.123456789987654321123456789012345
dqadd71519 add 1.123456789987654321123456789012345  0E-19  -> 1.123456789987654321123456789012345
dqadd71520 add 1.123456789987654321123456789012345  0E-20  -> 1.123456789987654321123456789012345
dqadd71521 add 1.123456789987654321123456789012345  0E-21  -> 1.123456789987654321123456789012345
dqadd71522 add 1.123456789987654321123456789012345  0E-22  -> 1.123456789987654321123456789012345
dqadd71523 add 1.123456789987654321123456789012345  0E-23  -> 1.123456789987654321123456789012345
dqadd71524 add 1.123456789987654321123456789012345  0E-24  -> 1.123456789987654321123456789012345
dqadd71525 add 1.123456789987654321123456789012345  0E-25  -> 1.123456789987654321123456789012345
dqadd71526 add 1.123456789987654321123456789012345  0E-26  -> 1.123456789987654321123456789012345
dqadd71527 add 1.123456789987654321123456789012345  0E-27  -> 1.123456789987654321123456789012345
dqadd71528 add 1.123456789987654321123456789012345  0E-28  -> 1.123456789987654321123456789012345
dqadd71529 add 1.123456789987654321123456789012345  0E-29  -> 1.123456789987654321123456789012345
dqadd71530 add 1.123456789987654321123456789012345  0E-30  -> 1.123456789987654321123456789012345
dqadd71531 add 1.123456789987654321123456789012345  0E-31  -> 1.123456789987654321123456789012345
dqadd71532 add 1.123456789987654321123456789012345  0E-32  -> 1.123456789987654321123456789012345
dqadd71533 add 1.123456789987654321123456789012345  0E-33  -> 1.123456789987654321123456789012345
-- next four flag Rounded because the 0 extends the result
dqadd71534 add 1.123456789987654321123456789012345  0E-34  -> 1.123456789987654321123456789012345 Rounded
dqadd71535 add 1.123456789987654321123456789012345  0E-35  -> 1.123456789987654321123456789012345 Rounded
dqadd71536 add 1.123456789987654321123456789012345  0E-36  -> 1.123456789987654321123456789012345 Rounded
dqadd71537 add 1.123456789987654321123456789012345  0E-37  -> 1.123456789987654321123456789012345 Rounded

-- sum of two opposite-sign operands is exactly 0 and floor => -0
rounding:    half_up
-- exact zeros from zeros
dqadd71600 add  0        0E-19  ->  0E-19
dqadd71601 add -0        0E-19  ->  0E-19
dqadd71602 add  0       -0E-19  ->  0E-19
dqadd71603 add -0       -0E-19  -> -0E-19
-- exact zeros from non-zeros
dqadd71611 add -11      11    ->  0
dqadd71612 add  11     -11    ->  0

rounding:    half_down
-- exact zeros from zeros
dqadd71620 add  0        0E-19  ->  0E-19
dqadd71621 add -0        0E-19  ->  0E-19
dqadd71622 add  0       -0E-19  ->  0E-19
dqadd71623 add -0       -0E-19  -> -0E-19
-- exact zeros from non-zeros
dqadd71631 add -11      11    ->  0
dqadd71632 add  11     -11    ->  0

rounding:    half_even
-- exact zeros from zeros
dqadd71640 add  0        0E-19  ->  0E-19
dqadd71641 add -0        0E-19  ->  0E-19
dqadd71642 add  0       -0E-19  ->  0E-19
dqadd71643 add -0       -0E-19  -> -0E-19
-- exact zeros from non-zeros
dqadd71651 add -11      11    ->  0
dqadd71652 add  11     -11    ->  0

rounding:    up
-- exact zeros from zeros
dqadd71660 add  0        0E-19  ->  0E-19
dqadd71661 add -0        0E-19  ->  0E-19
dqadd71662 add  0       -0E-19  ->  0E-19
dqadd71663 add -0       -0E-19  -> -0E-19
-- exact zeros from non-zeros
dqadd71671 add -11      11    ->  0
dqadd71672 add  11     -11    ->  0

rounding:    down
-- exact zeros from zeros
dqadd71680 add  0        0E-19  ->  0E-19
dqadd71681 add -0        0E-19  ->  0E-19
dqadd71682 add  0       -0E-19  ->  0E-19
dqadd71683 add -0       -0E-19  -> -0E-19
-- exact zeros from non-zeros
dqadd71691 add -11      11    ->  0
dqadd71692 add  11     -11    ->  0

rounding:    ceiling
-- exact zeros from zeros
dqadd71700 add  0        0E-19  ->  0E-19
dqadd71701 add -0        0E-19  ->  0E-19
dqadd71702 add  0       -0E-19  ->  0E-19
dqadd71703 add -0       -0E-19  -> -0E-19
-- exact zeros from non-zeros
dqadd71711 add -11      11    ->  0
dqadd71712 add  11     -11    ->  0

-- and the extra-special ugly case; unusual minuses marked by -- *
rounding:    floor
-- exact zeros from zeros
dqadd71720 add  0        0E-19  ->  0E-19
dqadd71721 add -0        0E-19  -> -0E-19           -- *
dqadd71722 add  0       -0E-19  -> -0E-19           -- *
dqadd71723 add -0       -0E-19  -> -0E-19
-- exact zeros from non-zeros
dqadd71731 add -11      11    ->  -0                -- *
dqadd71732 add  11     -11    ->  -0                -- *

-- Examples from SQL proposal (Krishna Kulkarni)
dqadd71741 add 130E-2    120E-2    -> 2.50
dqadd71742 add 130E-2    12E-1     -> 2.50
dqadd71743 add 130E-2    1E0       -> 2.30
dqadd71744 add 1E2       1E4       -> 1.01E+4
dqadd71745 add 130E-2   -120E-2 -> 0.10
dqadd71746 add 130E-2   -12E-1  -> 0.10
dqadd71747 add 130E-2   -1E0    -> 0.30
dqadd71748 add 1E2      -1E4    -> -9.9E+3

-- Gappy coefficients; check residue handling even with full coefficient gap
rounding: half_even

dqadd75001 add 1239876543211234567894567890123456 1      -> 1239876543211234567894567890123457
dqadd75002 add 1239876543211234567894567890123456 0.6    -> 1239876543211234567894567890123457  Inexact Rounded
dqadd75003 add 1239876543211234567894567890123456 0.06   -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75004 add 1239876543211234567894567890123456 6E-3   -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75005 add 1239876543211234567894567890123456 6E-4   -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75006 add 1239876543211234567894567890123456 6E-5   -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75007 add 1239876543211234567894567890123456 6E-6   -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75008 add 1239876543211234567894567890123456 6E-7   -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75009 add 1239876543211234567894567890123456 6E-8   -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75010 add 1239876543211234567894567890123456 6E-9   -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75011 add 1239876543211234567894567890123456 6E-10  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75012 add 1239876543211234567894567890123456 6E-11  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75013 add 1239876543211234567894567890123456 6E-12  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75014 add 1239876543211234567894567890123456 6E-13  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75015 add 1239876543211234567894567890123456 6E-14  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75016 add 1239876543211234567894567890123456 6E-15  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75017 add 1239876543211234567894567890123456 6E-16  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75018 add 1239876543211234567894567890123456 6E-17  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75019 add 1239876543211234567894567890123456 6E-18  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75020 add 1239876543211234567894567890123456 6E-19  -> 1239876543211234567894567890123456  Inexact Rounded
dqadd75021 add 1239876543211234567894567890123456 6E-20  -> 1239876543211234567894567890123456  Inexact Rounded

-- widening second argument at gap
dqadd75030 add 12398765432112345678945678 1                       -> 12398765432112345678945679
dqadd75031 add 12398765432112345678945678 0.1                     -> 12398765432112345678945678.1
dqadd75032 add 12398765432112345678945678 0.12                    -> 12398765432112345678945678.12
dqadd75033 add 12398765432112345678945678 0.123                   -> 12398765432112345678945678.123
dqadd75034 add 12398765432112345678945678 0.1234                  -> 12398765432112345678945678.1234
dqadd75035 add 12398765432112345678945678 0.12345                 -> 12398765432112345678945678.12345
dqadd75036 add 12398765432112345678945678 0.123456                -> 12398765432112345678945678.123456
dqadd75037 add 12398765432112345678945678 0.1234567               -> 12398765432112345678945678.1234567
dqadd75038 add 12398765432112345678945678 0.12345678              -> 12398765432112345678945678.12345678
dqadd75039 add 12398765432112345678945678 0.123456789             -> 12398765432112345678945678.12345679 Inexact Rounded
dqadd75040 add 12398765432112345678945678 0.123456785             -> 12398765432112345678945678.12345678 Inexact Rounded
dqadd75041 add 12398765432112345678945678 0.1234567850            -> 12398765432112345678945678.12345678 Inexact Rounded
dqadd75042 add 12398765432112345678945678 0.1234567851            -> 12398765432112345678945678.12345679 Inexact Rounded
dqadd75043 add 12398765432112345678945678 0.12345678501           -> 12398765432112345678945678.12345679 Inexact Rounded
dqadd75044 add 12398765432112345678945678 0.123456785001          -> 12398765432112345678945678.12345679 Inexact Rounded
dqadd75045 add 12398765432112345678945678 0.1234567850001         -> 12398765432112345678945678.12345679 Inexact Rounded
dqadd75046 add 12398765432112345678945678 0.12345678500001        -> 12398765432112345678945678.12345679 Inexact Rounded
dqadd75047 add 12398765432112345678945678 0.123456785000001       -> 12398765432112345678945678.12345679 Inexact Rounded
dqadd75048 add 12398765432112345678945678 0.1234567850000001      -> 12398765432112345678945678.12345679 Inexact Rounded
dqadd75049 add 12398765432112345678945678 0.1234567850000000      -> 12398765432112345678945678.12345678 Inexact Rounded
--                               90123456
rounding: half_even
dqadd75050 add 12398765432112345678945678 0.0234567750000000      -> 12398765432112345678945678.02345678 Inexact Rounded
dqadd75051 add 12398765432112345678945678 0.0034567750000000      -> 12398765432112345678945678.00345678 Inexact Rounded
dqadd75052 add 12398765432112345678945678 0.0004567750000000      -> 12398765432112345678945678.00045678 Inexact Rounded
dqadd75053 add 12398765432112345678945678 0.0000567750000000      -> 12398765432112345678945678.00005678 Inexact Rounded
dqadd75054 add 12398765432112345678945678 0.0000067750000000      -> 12398765432112345678945678.00000678 Inexact Rounded
dqadd75055 add 12398765432112345678945678 0.0000007750000000      -> 12398765432112345678945678.00000078 Inexact Rounded
dqadd75056 add 12398765432112345678945678 0.0000000750000000      -> 12398765432112345678945678.00000008 Inexact Rounded
dqadd75057 add 12398765432112345678945678 0.0000000050000000      -> 12398765432112345678945678.00000000 Inexact Rounded
dqadd75060 add 12398765432112345678945678 0.0234567750000001      -> 12398765432112345678945678.02345678 Inexact Rounded
dqadd75061 add 12398765432112345678945678 0.0034567750000001      -> 12398765432112345678945678.00345678 Inexact Rounded
dqadd75062 add 12398765432112345678945678 0.0004567750000001      -> 12398765432112345678945678.00045678 Inexact Rounded
dqadd75063 add 12398765432112345678945678 0.0000567750000001      -> 12398765432112345678945678.00005678 Inexact Rounded
dqadd75064 add 12398765432112345678945678 0.0000067750000001      -> 12398765432112345678945678.00000678 Inexact Rounded
dqadd75065 add 12398765432112345678945678 0.0000007750000001      -> 12398765432112345678945678.00000078 Inexact Rounded
dqadd75066 add 12398765432112345678945678 0.0000000750000001      -> 12398765432112345678945678.00000008 Inexact Rounded
dqadd75067 add 12398765432112345678945678 0.0000000050000001      -> 12398765432112345678945678.00000001 Inexact Rounded
-- far-out residues (full coefficient gap is 16+15 digits)
rounding: up
dqadd75070 add 12398765432112345678945678 1E-8                    -> 12398765432112345678945678.00000001
dqadd75071 add 12398765432112345678945678 1E-9                    -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75072 add 12398765432112345678945678 1E-10                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75073 add 12398765432112345678945678 1E-11                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75074 add 12398765432112345678945678 1E-12                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75075 add 12398765432112345678945678 1E-13                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75076 add 12398765432112345678945678 1E-14                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75077 add 12398765432112345678945678 1E-15                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75078 add 12398765432112345678945678 1E-16                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75079 add 12398765432112345678945678 1E-17                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75080 add 12398765432112345678945678 1E-18                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75081 add 12398765432112345678945678 1E-19                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75082 add 12398765432112345678945678 1E-20                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75083 add 12398765432112345678945678 1E-25                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75084 add 12398765432112345678945678 1E-30                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75085 add 12398765432112345678945678 1E-31                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75086 add 12398765432112345678945678 1E-32                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75087 add 12398765432112345678945678 1E-33                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75088 add 12398765432112345678945678 1E-34                   -> 12398765432112345678945678.00000001 Inexact Rounded
dqadd75089 add 12398765432112345678945678 1E-35                   -> 12398765432112345678945678.00000001 Inexact Rounded

-- Null tests
dqadd9990 add 10  # -> NaN Invalid_operation
dqadd9991 add  # 10 -> NaN Invalid_operation
