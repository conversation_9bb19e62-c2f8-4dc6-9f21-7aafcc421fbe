------------------------------------------------------------------------
-- ddScalebB.decTest -- scale a decDouble by powers of 10             --
-- Copyright (c) IBM Corporation, 1981, 2008.  All rights reserved.   --
------------------------------------------------------------------------
-- Please see the document "General Decimal Arithmetic Testcases"     --
-- at http://www2.hursley.ibm.com/decimal for the description of      --
-- these testcases.                                                   --
--                                                                    --
-- These testcases are experimental ('beta' versions), and they       --
-- may contain errors.  They are offered on an as-is basis.  In       --
-- particular, achieving the same results as the tests here is not    --
-- a guarantee that an implementation complies with any Standard      --
-- or specification.  The tests are not exhaustive.                   --
--                                                                    --
-- Please send comments, suggestions, and corrections to the author:  --
--   <PERSON>, IBM Fellow                                       --
--   IBM UK, PO Box 31, Birmingham Road, Warwick CV34 5JL, UK         --
--   <EMAIL>                                                   --
------------------------------------------------------------------------
version: 2.59

precision:   16
maxExponent: 384
minExponent: -383
extended:    1
clamp:       1
rounding:    half_even

-- Max |rhs| is 2*(384+16) = 800

-- Sanity checks
ddscb001 scaleb       7.50   10 -> 7.50E+10
ddscb002 scaleb       7.50    3 -> 7.50E+3
ddscb003 scaleb       7.50    2 -> 750
ddscb004 scaleb       7.50    1 -> 75.0
ddscb005 scaleb       7.50    0 -> 7.50
ddscb006 scaleb       7.50   -1 -> 0.750
ddscb007 scaleb       7.50   -2 -> 0.0750
ddscb008 scaleb       7.50  -10 -> 7.50E-10
ddscb009 scaleb      -7.50    3 -> -7.50E+3
ddscb010 scaleb      -7.50    2 -> -750
ddscb011 scaleb      -7.50    1 -> -75.0
ddscb012 scaleb      -7.50    0 -> -7.50
ddscb013 scaleb      -7.50   -1 -> -0.750

-- Infinities
ddscb014 scaleb  Infinity   1 -> Infinity
ddscb015 scaleb  -Infinity  2 -> -Infinity
ddscb016 scaleb  Infinity  -1 -> Infinity
ddscb017 scaleb  -Infinity -2 -> -Infinity

-- Next two are somewhat undefined in 754r; treat as non-integer
ddscb018 scaleb  10  Infinity -> NaN Invalid_operation
ddscb019 scaleb  10 -Infinity -> NaN Invalid_operation

-- NaNs are undefined in 754r; assume usual processing
-- NaNs, 0 payload
ddscb021 scaleb         NaN  1 -> NaN
ddscb022 scaleb        -NaN -1 -> -NaN
ddscb023 scaleb        sNaN  1 -> NaN Invalid_operation
ddscb024 scaleb       -sNaN  1 -> -NaN Invalid_operation
ddscb025 scaleb    4    NaN    -> NaN
ddscb026 scaleb -Inf   -NaN    -> -NaN
ddscb027 scaleb    4   sNaN    -> NaN Invalid_operation
ddscb028 scaleb  Inf  -sNaN    -> -NaN Invalid_operation

-- non-integer RHS
ddscb030 scaleb  1.23    1    ->  12.3
ddscb031 scaleb  1.23    1.00 ->  NaN Invalid_operation
ddscb032 scaleb  1.23    1.1  ->  NaN Invalid_operation
ddscb033 scaleb  1.23    1.01 ->  NaN Invalid_operation
ddscb034 scaleb  1.23    0.01 ->  NaN Invalid_operation
ddscb035 scaleb  1.23    0.11 ->  NaN Invalid_operation
ddscb036 scaleb  1.23    0.999999999 ->  NaN Invalid_operation
ddscb037 scaleb  1.23   -1    ->  0.123
ddscb038 scaleb  1.23   -1.00 ->  NaN Invalid_operation
ddscb039 scaleb  1.23   -1.1  ->  NaN Invalid_operation
ddscb040 scaleb  1.23   -1.01 ->  NaN Invalid_operation
ddscb041 scaleb  1.23   -0.01 ->  NaN Invalid_operation
ddscb042 scaleb  1.23   -0.11 ->  NaN Invalid_operation
ddscb043 scaleb  1.23   -0.999999999 ->  NaN Invalid_operation
ddscb044 scaleb  1.23    0.1         ->  NaN Invalid_operation
ddscb045 scaleb  1.23    1E+1        ->  NaN Invalid_operation
ddscb046 scaleb  1.23    1.1234E+6   ->  NaN Invalid_operation
ddscb047 scaleb  1.23    1.123E+4    ->  NaN Invalid_operation

-- out-of range RHS
ddscb120 scaleb  1.23    799         ->  Infinity Overflow Inexact Rounded
ddscb121 scaleb  1.23    800         ->  Infinity Overflow Inexact Rounded
ddscb122 scaleb  1.23    801         ->  NaN Invalid_operation
ddscb123 scaleb  1.23    802         ->  NaN Invalid_operation
ddscb124 scaleb  1.23   -799         ->  0E-398 Underflow Subnormal Inexact Rounded Clamped
ddscb125 scaleb  1.23   -800         ->  0E-398 Underflow Subnormal Inexact Rounded Clamped
ddscb126 scaleb  1.23   -801         ->  NaN Invalid_operation
ddscb127 scaleb  1.23   -802         ->  NaN Invalid_operation

-- NaNs, non-0 payload
-- propagating NaNs
ddscb861 scaleb  NaN01   -Inf     ->  NaN1
ddscb862 scaleb -NaN02   -1000    -> -NaN2
ddscb863 scaleb  NaN03    1000    ->  NaN3
ddscb864 scaleb  NaN04    Inf     ->  NaN4
ddscb865 scaleb  NaN05    NaN61   ->  NaN5
ddscb866 scaleb -Inf     -NaN71   -> -NaN71
ddscb867 scaleb -1000     NaN81   ->  NaN81
ddscb868 scaleb  1000     NaN91   ->  NaN91
ddscb869 scaleb  Inf      NaN101  ->  NaN101
ddscb871 scaleb  sNaN011  -Inf    ->  NaN11  Invalid_operation
ddscb872 scaleb  sNaN012  -1000   ->  NaN12  Invalid_operation
ddscb873 scaleb -sNaN013   1000   -> -NaN13  Invalid_operation
ddscb874 scaleb  sNaN014   NaN171 ->  NaN14  Invalid_operation
ddscb875 scaleb  sNaN015  sNaN181 ->  NaN15  Invalid_operation
ddscb876 scaleb  NaN016   sNaN191 ->  NaN191 Invalid_operation
ddscb877 scaleb -Inf      sNaN201 ->  NaN201 Invalid_operation
ddscb878 scaleb -1000     sNaN211 ->  NaN211 Invalid_operation
ddscb879 scaleb  1000    -sNaN221 -> -NaN221 Invalid_operation
ddscb880 scaleb  Inf      sNaN231 ->  NaN231 Invalid_operation
ddscb881 scaleb  NaN025   sNaN241 ->  NaN241 Invalid_operation

-- finites
ddscb051 scaleb          7   -2  -> 0.07
ddscb052 scaleb         -7   -2  -> -0.07
ddscb053 scaleb         75   -2  -> 0.75
ddscb054 scaleb        -75   -2  -> -0.75
ddscb055 scaleb       7.50   -2  -> 0.0750
ddscb056 scaleb      -7.50   -2  -> -0.0750
ddscb057 scaleb       7.500  -2  -> 0.07500
ddscb058 scaleb      -7.500  -2  -> -0.07500
ddscb061 scaleb          7   -1  -> 0.7
ddscb062 scaleb         -7   -1  -> -0.7
ddscb063 scaleb         75   -1  -> 7.5
ddscb064 scaleb        -75   -1  -> -7.5
ddscb065 scaleb       7.50   -1  -> 0.750
ddscb066 scaleb      -7.50   -1  -> -0.750
ddscb067 scaleb       7.500  -1  -> 0.7500
ddscb068 scaleb      -7.500  -1  -> -0.7500
ddscb071 scaleb          7    0  -> 7
ddscb072 scaleb         -7    0  -> -7
ddscb073 scaleb         75    0  -> 75
ddscb074 scaleb        -75    0  -> -75
ddscb075 scaleb       7.50    0  -> 7.50
ddscb076 scaleb      -7.50    0  -> -7.50
ddscb077 scaleb       7.500   0  -> 7.500
ddscb078 scaleb      -7.500   0  -> -7.500
ddscb081 scaleb          7    1  -> 7E+1
ddscb082 scaleb         -7    1  -> -7E+1
ddscb083 scaleb         75    1  -> 7.5E+2
ddscb084 scaleb        -75    1  -> -7.5E+2
ddscb085 scaleb       7.50    1  -> 75.0
ddscb086 scaleb      -7.50    1  -> -75.0
ddscb087 scaleb       7.500   1  -> 75.00
ddscb088 scaleb      -7.500   1  -> -75.00
ddscb091 scaleb          7    2  -> 7E+2
ddscb092 scaleb         -7    2  -> -7E+2
ddscb093 scaleb         75    2  -> 7.5E+3
ddscb094 scaleb        -75    2  -> -7.5E+3
ddscb095 scaleb       7.50    2  -> 750
ddscb096 scaleb      -7.50    2  -> -750
ddscb097 scaleb       7.500   2  -> 750.0
ddscb098 scaleb      -7.500   2  -> -750.0

-- zeros
ddscb111 scaleb          0  1 -> 0E+1
ddscb112 scaleb         -0  2 -> -0E+2
ddscb113 scaleb       0E+4  3 -> 0E+7
ddscb114 scaleb      -0E+4  4 -> -0E+8
ddscb115 scaleb     0.0000  5 -> 0E+1
ddscb116 scaleb    -0.0000  6 -> -0E+2
ddscb117 scaleb      0E-141 7 -> 0E-134
ddscb118 scaleb     -0E-141 8 -> -0E-133

-- Nmax, Nmin, Ntiny
ddscb132 scaleb  9.999999999999999E+384  +384 -> Infinity    Overflow Inexact Rounded
ddscb133 scaleb  9.999999999999999E+384  +10 -> Infinity     Overflow Inexact Rounded
ddscb134 scaleb  9.999999999999999E+384  +1  -> Infinity     Overflow Inexact Rounded
ddscb135 scaleb  9.999999999999999E+384   0  -> 9.999999999999999E+384
ddscb136 scaleb  9.999999999999999E+384  -1  -> 9.999999999999999E+383
ddscb137 scaleb  1E-383           +1  -> 1E-382
ddscb138 scaleb  1E-383           -0  -> 1E-383
ddscb139 scaleb  1E-383           -1  -> 1E-384          Subnormal
ddscb140 scaleb  1.000000000000000E-383  +1  -> 1.000000000000000E-382
ddscb141 scaleb  1.000000000000000E-383   0  -> 1.000000000000000E-383
ddscb142 scaleb  1.000000000000000E-383  -1  -> 1.00000000000000E-384 Subnormal Rounded
ddscb143 scaleb  1E-398          +1  -> 1E-397          Subnormal
ddscb144 scaleb  1E-398          -0  -> 1E-398         Subnormal
ddscb145 scaleb  1E-398          -1  -> 0E-398         Underflow Subnormal Inexact Rounded Clamped

ddscb150 scaleb  -1E-398         +1  -> -1E-397         Subnormal
ddscb151 scaleb  -1E-398         -0  -> -1E-398        Subnormal
ddscb152 scaleb  -1E-398         -1  -> -0E-398        Underflow Subnormal Inexact Rounded Clamped
ddscb153 scaleb  -1.000000000000000E-383 +1  -> -1.000000000000000E-382
ddscb154 scaleb  -1.000000000000000E-383 +0  -> -1.000000000000000E-383
ddscb155 scaleb  -1.000000000000000E-383 -1  -> -1.00000000000000E-384 Subnormal Rounded
ddscb156 scaleb  -1E-383          +1  -> -1E-382
ddscb157 scaleb  -1E-383          -0  -> -1E-383
ddscb158 scaleb  -1E-383          -1  -> -1E-384          Subnormal
ddscb159 scaleb  -9.999999999999999E+384 +1  -> -Infinity        Overflow Inexact Rounded
ddscb160 scaleb  -9.999999999999999E+384 +0  -> -9.999999999999999E+384
ddscb161 scaleb  -9.999999999999999E+384 -1  -> -9.999999999999999E+383
ddscb162 scaleb  -9E+384          +1  -> -Infinity        Overflow Inexact Rounded
ddscb163 scaleb  -1E+384          +1  -> -Infinity        Overflow Inexact Rounded

-- some Origami
-- (these check that overflow is being done correctly)
ddscb171 scaleb   1000E+365  +1 -> 1.000E+369
ddscb172 scaleb   1000E+366  +1 -> 1.000E+370
ddscb173 scaleb   1000E+367  +1 -> 1.000E+371
ddscb174 scaleb   1000E+368  +1 -> 1.000E+372
ddscb175 scaleb   1000E+369  +1 -> 1.0000E+373                  Clamped
ddscb176 scaleb   1000E+370  +1 -> 1.00000E+374                 Clamped
ddscb177 scaleb   1000E+371  +1 -> 1.000000E+375                Clamped
ddscb178 scaleb   1000E+372  +1 -> 1.0000000E+376               Clamped
ddscb179 scaleb   1000E+373  +1 -> 1.00000000E+377              Clamped
ddscb180 scaleb   1000E+374  +1 -> 1.000000000E+378             Clamped
ddscb181 scaleb   1000E+375  +1 -> 1.0000000000E+379            Clamped
ddscb182 scaleb   1000E+376  +1 -> 1.00000000000E+380           Clamped
ddscb183 scaleb   1000E+377  +1 -> 1.000000000000E+381          Clamped
ddscb184 scaleb   1000E+378  +1 -> 1.0000000000000E+382         Clamped
ddscb185 scaleb   1000E+379  +1 -> 1.00000000000000E+383        Clamped
ddscb186 scaleb   1000E+380  +1 -> 1.000000000000000E+384       Clamped
ddscb187 scaleb   1000E+381  +1 -> Infinity    Overflow Inexact Rounded

-- and a few more subnormal truncations
-- (these check that underflow is being done correctly)
ddscb201 scaleb  1.000000000000000E-383   0  -> 1.000000000000000E-383
ddscb202 scaleb  1.000000000000000E-383  -1  -> 1.00000000000000E-384 Subnormal Rounded
ddscb203 scaleb  1.000000000000000E-383  -2  -> 1.0000000000000E-385 Subnormal Rounded
ddscb204 scaleb  1.000000000000000E-383  -3  -> 1.000000000000E-386 Subnormal Rounded
ddscb205 scaleb  1.000000000000000E-383  -4  -> 1.00000000000E-387 Subnormal Rounded
ddscb206 scaleb  1.000000000000000E-383  -5  -> 1.0000000000E-388 Subnormal Rounded
ddscb207 scaleb  1.000000000000000E-383  -6  -> 1.000000000E-389 Subnormal Rounded
ddscb208 scaleb  1.000000000000000E-383  -7  -> 1.00000000E-390 Subnormal Rounded
ddscb209 scaleb  1.000000000000000E-383  -8  -> 1.0000000E-391 Subnormal Rounded
ddscb210 scaleb  1.000000000000000E-383  -9  -> 1.000000E-392 Subnormal Rounded
ddscb211 scaleb  1.000000000000000E-383  -10 -> 1.00000E-393 Subnormal Rounded
ddscb212 scaleb  1.000000000000000E-383  -11 -> 1.0000E-394 Subnormal Rounded
ddscb213 scaleb  1.000000000000000E-383  -12 -> 1.000E-395 Subnormal Rounded
ddscb214 scaleb  1.000000000000000E-383  -13 -> 1.00E-396 Subnormal Rounded
ddscb215 scaleb  1.000000000000000E-383  -14 -> 1.0E-397 Subnormal Rounded
ddscb216 scaleb  1.000000000000000E-383  -15 -> 1E-398 Subnormal Rounded
ddscb217 scaleb  1.000000000000000E-383  -16 -> 0E-398 Underflow Subnormal Inexact Rounded Clamped
ddscb218 scaleb  1.000000000000000E-383  -17 -> 0E-398 Underflow Subnormal Inexact Rounded Clamped

