package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os/exec"
	"runtime"
	"time"
)

// API结构
type GenerateRequest struct {
	Prompt             string  `json:"prompt"`
	ImageSize          string  `json:"image_size,omitempty"`
	BatchSize          int     `json:"batch_size,omitempty"`
	NumInferenceSteps  int     `json:"num_inference_steps,omitempty"`
	GuidanceScale      float64 `json:"guidance_scale,omitempty"`
	NegativePrompt     string  `json:"negative_prompt,omitempty"`
	Model              string  `json:"model,omitempty"`
	Quality            string  `json:"quality,omitempty"`
}

type GenerateResponse struct {
	Success bool `json:"success"`
	Data    struct {
		ImageURL       string `json:"image_url,omitempty"`
		LocalURL       string `json:"local_url,omitempty"`
		Seed           int64  `json:"seed"`
		GenerationTime float64 `json:"generation_time"`
		Images         []struct {
			ImageURL string `json:"image_url"`
			LocalURL string `json:"local_url"`
			Index    int    `json:"index"`
		} `json:"images,omitempty"`
	} `json:"data"`
	Message string `json:"message,omitempty"`
}

const BaseURL = "https://www.diwangzhidao.com/api/wenshengtu/api.php"

var htmlTemplate = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 AI图片生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            display: flex;
            min-height: 600px;
        }
        
        .left-panel {
            flex: 1;
            padding: 30px;
            border-right: 1px solid #eee;
        }
        
        .right-panel {
            flex: 1;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .generate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .generate-btn:hover {
            transform: translateY(-2px);
        }
        
        .generate-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }
        
        .status.loading {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .status.error {
            background: #ffebee;
            color: #c62828;
        }
        
        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .image-item {
            background: white;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .image-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        
        .download-btn {
            padding: 8px 16px;
            background: #4caf50;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .download-btn:hover {
            background: #45a049;
        }
        
        .progress {
            width: 100%;
            height: 6px;
            background: #eee;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s;
            animation: progress-animation 2s infinite;
        }
        
        @keyframes progress-animation {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 AI图片生成器</h1>
            <p>基于Kolors模型的高质量AI图片生成工具</p>
        </div>
        
        <div class="main-content">
            <div class="left-panel">
                <h2>🖼️ 生成参数</h2>
                <form id="generateForm">
                    <div class="form-group">
                        <label for="prompt">✨ 正面提示词</label>
                        <textarea id="prompt" name="prompt" placeholder="请输入图片描述，例如：杰作，最佳画质，一位美丽的女性..." required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="negativePrompt">🚫 负面提示词</label>
                        <textarea id="negativePrompt" name="negativePrompt" placeholder="请输入不希望出现的内容，例如：模糊，低质量，变形..."></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="imageSize">📐 图片尺寸</label>
                            <select id="imageSize" name="imageSize">
                                <option value="512x512">512x512</option>
                                <option value="768x768">768x768</option>
                                <option value="1024x1024" selected>1024x1024 (推荐)</option>
                                <option value="1024x768">1024x768</option>
                                <option value="768x1024">768x1024</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="batchSize">🔢 生成数量</label>
                            <select id="batchSize" name="batchSize">
                                <option value="1" selected>1张</option>
                                <option value="2">2张</option>
                                <option value="3">3张</option>
                                <option value="4">4张</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="steps">⚡ 推理步数</label>
                            <input type="number" id="steps" name="steps" value="25" min="10" max="50">
                        </div>
                        
                        <div class="form-group">
                            <label for="guidance">🎯 引导强度</label>
                            <input type="number" id="guidance" name="guidance" value="8.0" min="1.0" max="20.0" step="0.1">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="quality">💎 图片质量</label>
                        <select id="quality" name="quality">
                            <option value="standard" selected>标准</option>
                            <option value="hd">高清</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="generate-btn" id="generateBtn">
                        🚀 开始生成
                    </button>
                </form>
            </div>
            
            <div class="right-panel">
                <h2>🖼️ 生成结果</h2>
                
                <div id="status" class="status hidden"></div>
                
                <div id="progress" class="progress hidden">
                    <div class="progress-bar"></div>
                </div>
                
                <div id="imageGallery" class="image-gallery"></div>
            </div>
        </div>
    </div>

    <script>
        const form = document.getElementById('generateForm');
        const generateBtn = document.getElementById('generateBtn');
        const status = document.getElementById('status');
        const progress = document.getElementById('progress');
        const imageGallery = document.getElementById('imageGallery');

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(form);
            const data = {
                prompt: formData.get('prompt'),
                negative_prompt: formData.get('negativePrompt'),
                image_size: formData.get('imageSize'),
                batch_size: parseInt(formData.get('batchSize')),
                num_inference_steps: parseInt(formData.get('steps')),
                guidance_scale: parseFloat(formData.get('guidance')),
                model: 'Kwai-Kolors/Kolors',
                quality: formData.get('quality')
            };

            // 显示加载状态
            generateBtn.disabled = true;
            generateBtn.textContent = '🔄 生成中...';
            status.className = 'status loading';
            status.textContent = '正在生成图片，请稍候...';
            status.classList.remove('hidden');
            progress.classList.remove('hidden');
            imageGallery.innerHTML = '';

            try {
                const response = await fetch('/api/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    status.className = 'status success';
                    status.textContent = '✅ 生成成功！耗时: ' + result.data.generation_time.toFixed(2) + '秒，种子: ' + result.data.seed;

                    // 显示图片
                    if (result.data.local_url || result.data.image_url) {
                        const imageUrl = result.data.local_url || result.data.image_url;
                        addImageToGallery(imageUrl, 1);
                    }

                    if (result.data.images && result.data.images.length > 0) {
                        result.data.images.forEach((img, index) => {
                            const imageUrl = img.local_url || img.image_url;
                            addImageToGallery(imageUrl, index + 1);
                        });
                    }
                } else {
                    status.className = 'status error';
                    status.textContent = '❌ 生成失败: ' + result.message;
                }
            } catch (error) {
                status.className = 'status error';
                status.textContent = '❌ 请求失败: ' + error.message;
            } finally {
                generateBtn.disabled = false;
                generateBtn.textContent = '🚀 开始生成';
                progress.classList.add('hidden');
            }
        });

        function addImageToGallery(imageUrl, index) {
            const imageItem = document.createElement('div');
            imageItem.className = 'image-item';
            imageItem.innerHTML = '<img src="' + imageUrl + '" alt="生成的图片 ' + index + '" loading="lazy">' +
                '<div><a href="' + imageUrl + '" download="ai_image_' + Date.now() + '_' + index + '.png" class="download-btn">' +
                '💾 下载图片</a></div>';
            imageGallery.appendChild(imageItem);
        }

        // 预设提示词
        const presetPrompts = [
            "杰作，最佳画质，超高分辨率，一位美丽的古典东方女性，精致五官，明亮有神的眼睛，穿着红色汉服，古典园林背景，柔和光照，完美构图",
            "masterpiece, best quality, ultra detailed, beautiful anime girl, large expressive eyes, colorful hair, school uniform, cherry blossoms background, soft lighting",
            "杰作，最佳画质，8K壁纸，自然风光摄影，壮丽山景，层次分明，色彩丰富，黄金时刻光照，完美构图"
        ];

        // 随机填充提示词
        document.getElementById('prompt').value = presetPrompts[Math.floor(Math.random() * presetPrompts.length)];
        document.getElementById('negativePrompt').value = "模糊，低质量，变形，噪点，失焦，多余手指，错误解剖，构图混乱";
    </script>
</body>
</html>
`

func main() {
	// 设置HTTP路由
	http.HandleFunc("/", serveHTML)
	http.HandleFunc("/api/generate", handleGenerate)

	port := "8080"
	fmt.Printf("🚀 AI图片生成器启动成功！\n")
	fmt.Printf("🌐 访问地址: http://localhost:%s\n", port)
	fmt.Printf("💡 程序将自动打开浏览器窗口\n")
	fmt.Println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

	// 自动打开浏览器
	go func() {
		time.Sleep(1 * time.Second)
		openBrowser("http://localhost:" + port)
	}()

	// 启动HTTP服务器
	log.Fatal(http.ListenAndServe(":"+port, nil))
}

func serveHTML(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	fmt.Fprint(w, htmlTemplate)
}

func handleGenerate(w http.ResponseWriter, r *http.Request) {
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req GenerateRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// 调用API
	resp, err := callAPI(req)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(resp)
}

func callAPI(req GenerateRequest) (*GenerateResponse, error) {
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	url := fmt.Sprintf("%s?action=generate", BaseURL)
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 120 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var result GenerateResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &result, nil
}

func openBrowser(url string) {
	var err error
	switch runtime.GOOS {
	case "linux":
		err = exec.Command("xdg-open", url).Start()
	case "windows":
		err = exec.Command("rundll32", "url.dll,FileProtocolHandler", url).Start()
	case "darwin":
		err = exec.Command("open", url).Start()
	default:
		err = fmt.Errorf("unsupported platform")
	}
	if err != nil {
		log.Printf("无法自动打开浏览器: %v", err)
		fmt.Printf("请手动访问: %s\n", url)
	}
}
