package ui

import (
	"fmt"
	"log"
	"path/filepath"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"

	"ai-image-generator/api"
	"ai-image-generator/config"
	"ai-image-generator/utils"
)

// App 主应用程序
type App struct {
	fyneApp        fyne.App
	window         fyne.Window
	config         *config.Config
	apiClient      *api.Client
	
	parameterPanel *ParameterPanel
	imagePanel     *ImageDisplayPanel
	generateButton *widget.Button
	
	isGenerating   bool
}

// NewApp 创建新应用程序
func NewApp() *App {
	fyneApp := app.NewWithID("ai.image.generator")
	fyneApp.SetMetadata(&fyne.AppMetadata{
		Name:        "AI图片生成器",
		Description: "基于Kolors模型的AI图片生成工具",
		Version:     "1.0.0",
	})
	
	window := fyneApp.NewWindow("AI图片生成器")
	window.SetIcon(nil) // 可以设置应用图标
	
	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Printf("加载配置失败: %v", err)
		cfg = config.DefaultConfig()
	}
	
	// 设置窗口大小
	window.Resize(fyne.NewSize(float32(cfg.WindowWidth), float32(cfg.WindowHeight)))
	window.CenterOnScreen()
	
	app := &App{
		fyneApp:   fyneApp,
		window:    window,
		config:    cfg,
		apiClient: api.NewClient(),
	}
	
	app.setupUI()
	app.setupCallbacks()
	
	return app
}

// setupUI 设置用户界面
func (a *App) setupUI() {
	// 创建参数面板
	a.parameterPanel = NewParameterPanel()
	a.parameterPanel.LoadFromConfig(a.config)
	
	// 创建图片显示面板
	a.imagePanel = NewImageDisplayPanel()
	
	// 创建生成按钮
	a.generateButton = widget.NewButton("生成图片", a.onGenerateClick)
	a.generateButton.Importance = widget.HighImportance
	
	// 创建菜单
	a.setupMenu()
	
	// 创建主布局
	leftPanel := container.NewVBox(
		a.parameterPanel.GetContainer(),
		a.generateButton,
	)
	
	leftScroll := container.NewScroll(leftPanel)
	leftScroll.SetMinSize(fyne.NewSize(450, 600))
	
	mainContent := container.NewHSplit(
		leftScroll,
		a.imagePanel.GetContainer(),
	)
	mainContent.SetOffset(0.35) // 左侧占35%
	
	a.window.SetContent(mainContent)
}

// setupMenu 设置菜单
func (a *App) setupMenu() {
	// 文件菜单
	fileMenu := fyne.NewMenu("文件",
		fyne.NewMenuItem("设置保存目录", a.onSetSaveDirectory),
		fyne.NewMenuItemSeparator(),
		fyne.NewMenuItem("退出", func() {
			a.fyneApp.Quit()
		}),
	)
	
	// 帮助菜单
	helpMenu := fyne.NewMenu("帮助",
		fyne.NewMenuItem("关于", a.onAbout),
		fyne.NewMenuItem("API信息", a.onAPIInfo),
	)
	
	mainMenu := fyne.NewMainMenu(fileMenu, helpMenu)
	a.window.SetMainMenu(mainMenu)
}

// setupCallbacks 设置回调函数
func (a *App) setupCallbacks() {
	// 设置保存图片回调
	a.imagePanel.SetOnSaveImage(a.onSaveImage)
	
	// 设置窗口关闭回调
	a.window.SetCloseIntercept(func() {
		a.saveConfig()
		a.fyneApp.Quit()
	})
}

// onGenerateClick 生成按钮点击事件
func (a *App) onGenerateClick() {
	if a.isGenerating {
		return
	}
	
	// 获取请求参数
	req, err := a.parameterPanel.GetRequest()
	if err != nil {
		dialog.ShowError(err, a.window)
		return
	}
	
	// 添加到历史记录
	a.config.AddPromptHistory(req.Prompt)
	a.config.AddNegativePromptHistory(req.NegativePrompt)
	a.parameterPanel.UpdateHistoryOptions(a.config.PromptHistory, a.config.NegativePromptHistory)
	
	// 开始生成
	a.startGeneration(req)
}

// startGeneration 开始生成图片
func (a *App) startGeneration(req *api.GenerateRequest) {
	a.isGenerating = true
	a.generateButton.SetText("生成中...")
	a.generateButton.Disable()
	a.imagePanel.ShowProgress()
	a.imagePanel.SetStatus("正在生成图片...")
	
	// 在goroutine中执行API调用
	go func() {
		defer func() {
			a.isGenerating = false
			a.generateButton.SetText("生成图片")
			a.generateButton.Enable()
			a.imagePanel.HideProgress()
		}()
		
		// 调用API
		resp, err := a.apiClient.GenerateImage(req)
		if err != nil {
			a.imagePanel.SetStatus(fmt.Sprintf("生成失败: %v", err))
			dialog.ShowError(err, a.window)
			return
		}
		
		if !resp.Success {
			errMsg := fmt.Sprintf("API错误: %s", resp.Message)
			a.imagePanel.SetStatus(errMsg)
			dialog.ShowError(fmt.Errorf(errMsg), a.window)
			return
		}
		
		// 处理生成结果
		a.handleGenerationResult(resp)
	}()
}

// handleGenerationResult 处理生成结果
func (a *App) handleGenerationResult(resp *api.GenerateResponse) {
	data := resp.Data
	
	// 更新状态
	statusMsg := fmt.Sprintf("生成完成！耗时: %.2f秒, 种子: %d", 
		data.GenerationTime, data.Seed)
	a.imagePanel.SetStatus(statusMsg)
	
	// 下载并显示图片
	if data.Count == 1 {
		// 单张图片
		imageURL := data.LocalURL
		if imageURL == "" {
			imageURL = data.ImageURL
		}
		a.downloadAndDisplayImage(imageURL, "generated_image.png")
	} else {
		// 多张图片
		for i, img := range data.Images {
			imageURL := img.LocalURL
			if imageURL == "" {
				imageURL = img.ImageURL
			}
			filename := fmt.Sprintf("generated_image_%d.png", i+1)
			a.downloadAndDisplayImage(imageURL, filename)
		}
	}
}

// downloadAndDisplayImage 下载并显示图片
func (a *App) downloadAndDisplayImage(url, filename string) {
	imageData, err := a.apiClient.DownloadImage(url)
	if err != nil {
		log.Printf("下载图片失败: %v", err)
		return
	}
	
	// 添加到显示面板
	if err := a.imagePanel.AddImage(imageData, url, filename); err != nil {
		log.Printf("添加图片失败: %v", err)
	}
}

// onSaveImage 保存图片事件
func (a *App) onSaveImage(item ImageItem) {
	// 生成文件名
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("ai_image_%s.png", timestamp)
	
	// 保存图片
	savedPath, err := utils.SaveImageToFile(item.Data, a.config.SaveDirectory, filename)
	if err != nil {
		dialog.ShowError(fmt.Errorf("保存图片失败: %w", err), a.window)
		return
	}
	
	// 显示成功消息
	dialog.ShowInformation("保存成功", 
		fmt.Sprintf("图片已保存到:\n%s", savedPath), a.window)
}

// onSetSaveDirectory 设置保存目录
func (a *App) onSetSaveDirectory() {
	dialog.ShowFolderOpen(func(uri fyne.ListableURI, err error) {
		if err != nil || uri == nil {
			return
		}
		
		a.config.SaveDirectory = uri.Path()
		a.saveConfig()
		
		dialog.ShowInformation("设置成功", 
			fmt.Sprintf("保存目录已设置为:\n%s", a.config.SaveDirectory), a.window)
	}, a.window)
}

// onAbout 关于对话框
func (a *App) onAbout() {
	content := widget.NewRichTextFromMarkdown(`
# AI图片生成器

基于Kolors模型的AI图片生成工具

**版本:** 1.0.0  
**作者:** AI Assistant  
**技术栈:** Go + Fyne

## 功能特性

- 支持中文提示词
- 多种图片尺寸
- 批量生成
- 历史记录
- 参数调节

## 使用说明

1. 输入提示词描述想要生成的图片
2. 调整生成参数
3. 点击生成按钮
4. 等待生成完成并保存图片
`)
	
	dialog.ShowCustom("关于", "确定", content, a.window)
}

// onAPIInfo 显示API信息
func (a *App) onAPIInfo() {
	go func() {
		info, err := a.apiClient.GetAPIInfo()
		if err != nil {
			dialog.ShowError(fmt.Errorf("获取API信息失败: %w", err), a.window)
			return
		}
		
		if !info.Success {
			dialog.ShowError(fmt.Errorf("API错误"), a.window)
			return
		}
		
		data := info.Data
		content := widget.NewRichTextFromMarkdown(fmt.Sprintf(`
# API信息

**API名称:** %s  
**版本:** %s  
**模型:** %s  
**价格:** %s  
**服务器时间:** %s

## 支持的图片尺寸

%s
`, data.APIName, data.Version, data.Model, data.PricePerImage, data.ServerTime,
			fmt.Sprintf("- %s", data.SupportedSizes)))
		
		dialog.ShowCustom("API信息", "确定", content, a.window)
	}()
}

// saveConfig 保存配置
func (a *App) saveConfig() {
	// 更新窗口大小
	size := a.window.Content().Size()
	a.config.WindowWidth = int(size.Width)
	a.config.WindowHeight = int(size.Height)
	
	// 保存配置
	if err := a.config.Save(); err != nil {
		log.Printf("保存配置失败: %v", err)
	}
}

// Run 运行应用程序
func (a *App) Run() {
	a.window.ShowAndRun()
}
