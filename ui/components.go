package ui

import (
	"fmt"
	"strconv"
	"strings"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"ai-image-generator/api"
	"ai-image-generator/config"
	"ai-image-generator/utils"
)

// ParameterPanel 参数设置面板
type ParameterPanel struct {
	container *container.VBox
	
	// 输入控件
	PromptEntry         *widget.Entry
	NegativePromptEntry *widget.Entry
	ModelSelect         *widget.Select
	SizeSelect          *widget.Select
	BatchSizeEntry      *widget.Entry
	StepsEntry          *widget.Entry
	GuidanceEntry       *widget.Entry
	SeedEntry           *widget.Entry
	StyleSelect         *widget.Select
	QualitySelect       *widget.Select
	
	// 历史记录
	PromptHistory         *widget.Select
	NegativePromptHistory *widget.Select
}

// NewParameterPanel 创建参数设置面板
func NewParameterPanel() *ParameterPanel {
	panel := &ParameterPanel{}
	
	// 创建输入控件
	panel.PromptEntry = widget.NewMultiLineEntry()
	panel.PromptEntry.SetPlaceHolder("请输入图片描述提示词...")
	panel.PromptEntry.Resize(fyne.NewSize(400, 100))
	
	panel.NegativePromptEntry = widget.NewMultiLineEntry()
	panel.NegativePromptEntry.SetPlaceHolder("请输入负面提示词（可选）...")
	panel.NegativePromptEntry.Resize(fyne.NewSize(400, 60))
	
	panel.ModelSelect = widget.NewSelect(api.SupportedModels, nil)
	panel.ModelSelect.SetSelected("Kwai-Kolors/Kolors")
	
	panel.SizeSelect = widget.NewSelect(api.SupportedSizes, nil)
	panel.SizeSelect.SetSelected("1024x1024")
	
	panel.BatchSizeEntry = widget.NewEntry()
	panel.BatchSizeEntry.SetText("1")
	
	panel.StepsEntry = widget.NewEntry()
	panel.StepsEntry.SetText("25")
	
	panel.GuidanceEntry = widget.NewEntry()
	panel.GuidanceEntry.SetText("8.0")
	
	panel.SeedEntry = widget.NewEntry()
	panel.SeedEntry.SetPlaceHolder("随机（留空）")
	
	panel.StyleSelect = widget.NewSelect(api.SupportedStyles, nil)
	panel.StyleSelect.SetSelected("")
	
	panel.QualitySelect = widget.NewSelect(api.SupportedQualities, nil)
	panel.QualitySelect.SetSelected("standard")
	
	// 历史记录选择器
	panel.PromptHistory = widget.NewSelect([]string{}, func(selected string) {
		if selected != "" {
			panel.PromptEntry.SetText(selected)
		}
	})
	panel.PromptHistory.PlaceHolder = "选择历史提示词..."
	
	panel.NegativePromptHistory = widget.NewSelect([]string{}, func(selected string) {
		if selected != "" {
			panel.NegativePromptEntry.SetText(selected)
		}
	})
	panel.NegativePromptHistory.PlaceHolder = "选择历史负面提示词..."
	
	// 创建布局
	panel.container = container.NewVBox(
		widget.NewCard("提示词", "", container.NewVBox(
			widget.NewLabel("正面提示词:"),
			panel.PromptEntry,
			widget.NewLabel("历史记录:"),
			panel.PromptHistory,
			widget.NewSeparator(),
			widget.NewLabel("负面提示词:"),
			panel.NegativePromptEntry,
			widget.NewLabel("历史记录:"),
			panel.NegativePromptHistory,
		)),
		
		widget.NewCard("生成参数", "", container.NewVBox(
			container.NewGridWithColumns(2,
				widget.NewLabel("模型:"),
				panel.ModelSelect,
				widget.NewLabel("图片尺寸:"),
				panel.SizeSelect,
				widget.NewLabel("生成数量:"),
				panel.BatchSizeEntry,
				widget.NewLabel("推理步数:"),
				panel.StepsEntry,
				widget.NewLabel("引导强度:"),
				panel.GuidanceEntry,
				widget.NewLabel("随机种子:"),
				panel.SeedEntry,
				widget.NewLabel("艺术风格:"),
				panel.StyleSelect,
				widget.NewLabel("图片质量:"),
				panel.QualitySelect,
			),
		)),
	)
	
	return panel
}

// GetContainer 获取容器
func (p *ParameterPanel) GetContainer() *container.VBox {
	return p.container
}

// GetRequest 获取生成请求
func (p *ParameterPanel) GetRequest() (*api.GenerateRequest, error) {
	req := &api.GenerateRequest{
		Prompt:        strings.TrimSpace(p.PromptEntry.Text),
		Model:         p.ModelSelect.Selected,
		ImageSize:     p.SizeSelect.Selected,
		NegativePrompt: strings.TrimSpace(p.NegativePromptEntry.Text),
		Style:         p.StyleSelect.Selected,
		Quality:       p.QualitySelect.Selected,
	}
	
	// 验证必填字段
	if req.Prompt == "" {
		return nil, fmt.Errorf("请输入提示词")
	}
	
	// 解析数值字段
	if batchSize, err := strconv.Atoi(p.BatchSizeEntry.Text); err != nil {
		return nil, fmt.Errorf("生成数量必须是数字")
	} else if batchSize < 1 || batchSize > 4 {
		return nil, fmt.Errorf("生成数量必须在1-4之间")
	} else {
		req.BatchSize = batchSize
	}
	
	if steps, err := strconv.Atoi(p.StepsEntry.Text); err != nil {
		return nil, fmt.Errorf("推理步数必须是数字")
	} else if steps < 10 || steps > 50 {
		return nil, fmt.Errorf("推理步数必须在10-50之间")
	} else {
		req.NumInferenceSteps = steps
	}
	
	if guidance, err := strconv.ParseFloat(p.GuidanceEntry.Text, 64); err != nil {
		return nil, fmt.Errorf("引导强度必须是数字")
	} else if guidance < 1.0 || guidance > 20.0 {
		return nil, fmt.Errorf("引导强度必须在1.0-20.0之间")
	} else {
		req.GuidanceScale = guidance
	}
	
	// 解析种子（可选）
	if seedText := strings.TrimSpace(p.SeedEntry.Text); seedText != "" {
		if seed, err := strconv.ParseInt(seedText, 10, 64); err != nil {
			return nil, fmt.Errorf("随机种子必须是数字")
		} else {
			req.Seed = seed
		}
	}
	
	return req, nil
}

// UpdateHistoryOptions 更新历史记录选项
func (p *ParameterPanel) UpdateHistoryOptions(promptHistory, negativePromptHistory []string) {
	p.PromptHistory.Options = promptHistory
	p.PromptHistory.Refresh()

	p.NegativePromptHistory.Options = negativePromptHistory
	p.NegativePromptHistory.Refresh()
}

// LoadFromConfig 从配置加载默认值
func (p *ParameterPanel) LoadFromConfig(cfg *config.Config) {
	p.ModelSelect.SetSelected(cfg.DefaultModel)
	p.SizeSelect.SetSelected(cfg.DefaultImageSize)
	p.BatchSizeEntry.SetText(strconv.Itoa(cfg.DefaultBatchSize))
	p.StepsEntry.SetText(strconv.Itoa(cfg.DefaultNumInferenceSteps))
	p.GuidanceEntry.SetText(fmt.Sprintf("%.1f", cfg.DefaultGuidanceScale))
	p.StyleSelect.SetSelected(cfg.DefaultStyle)
	p.QualitySelect.SetSelected(cfg.DefaultQuality)

	p.UpdateHistoryOptions(cfg.PromptHistory, cfg.NegativePromptHistory)
}

// ImageDisplayPanel 图片显示面板
type ImageDisplayPanel struct {
	container    *container.VBox
	scrollArea   *container.Scroll
	imageGrid    *container.GridWithColumns
	statusLabel  *widget.Label
	progressBar  *widget.ProgressBar
	saveButton   *widget.Button
	clearButton  *widget.Button

	images       []ImageItem
	onSaveImage  func(ImageItem)
}

// ImageItem 图片项
type ImageItem struct {
	Data     []byte
	URL      string
	Filename string
	Widget   *widget.Card
}

// NewImageDisplayPanel 创建图片显示面板
func NewImageDisplayPanel() *ImageDisplayPanel {
	panel := &ImageDisplayPanel{
		images: make([]ImageItem, 0),
	}

	// 创建控件
	panel.statusLabel = widget.NewLabel("准备就绪")
	panel.progressBar = widget.NewProgressBar()
	panel.progressBar.Hide()

	panel.saveButton = widget.NewButton("保存所有图片", func() {
		panel.saveAllImages()
	})
	panel.saveButton.Disable()

	panel.clearButton = widget.NewButton("清空图片", func() {
		panel.clearImages()
	})
	panel.clearButton.Disable()

	// 创建图片网格
	panel.imageGrid = container.NewGridWithColumns(2)
	panel.scrollArea = container.NewScroll(panel.imageGrid)
	panel.scrollArea.SetMinSize(fyne.NewSize(600, 400))

	// 创建布局
	buttonContainer := container.NewHBox(
		panel.saveButton,
		panel.clearButton,
	)

	panel.container = container.NewVBox(
		widget.NewCard("生成状态", "", container.NewVBox(
			panel.statusLabel,
			panel.progressBar,
		)),
		widget.NewCard("生成结果", "", container.NewVBox(
			buttonContainer,
			panel.scrollArea,
		)),
	)

	return panel
}

// GetContainer 获取容器
func (p *ImageDisplayPanel) GetContainer() *container.VBox {
	return p.container
}

// SetStatus 设置状态
func (p *ImageDisplayPanel) SetStatus(status string) {
	p.statusLabel.SetText(status)
}

// ShowProgress 显示进度
func (p *ImageDisplayPanel) ShowProgress() {
	p.progressBar.Show()
	p.progressBar.Start()
}

// HideProgress 隐藏进度
func (p *ImageDisplayPanel) HideProgress() {
	p.progressBar.Stop()
	p.progressBar.Hide()
}

// AddImage 添加图片
func (p *ImageDisplayPanel) AddImage(imageData []byte, url, filename string) error {
	// 调整图片大小用于显示
	resizedData, err := utils.ResizeImageData(imageData, 300, 300)
	if err != nil {
		return fmt.Errorf("调整图片大小失败: %w", err)
	}

	// 创建Fyne资源
	resource, err := utils.ConvertToFyneResource(resizedData)
	if err != nil {
		return fmt.Errorf("转换图片资源失败: %w", err)
	}

	// 创建图片显示控件
	imageWidget := widget.NewIcon(resource)

	// 创建保存按钮
	saveBtn := widget.NewButton("保存", func() {
		if p.onSaveImage != nil {
			item := ImageItem{
				Data:     imageData,
				URL:      url,
				Filename: filename,
			}
			p.onSaveImage(item)
		}
	})

	// 创建卡片
	card := widget.NewCard("", "", container.NewVBox(
		imageWidget,
		saveBtn,
	))

	// 添加到图片列表
	item := ImageItem{
		Data:     imageData,
		URL:      url,
		Filename: filename,
		Widget:   card,
	}
	p.images = append(p.images, item)

	// 添加到网格
	p.imageGrid.Add(card)

	// 启用按钮
	p.saveButton.Enable()
	p.clearButton.Enable()

	return nil
}

// SetOnSaveImage 设置保存图片回调
func (p *ImageDisplayPanel) SetOnSaveImage(callback func(ImageItem)) {
	p.onSaveImage = callback
}

// saveAllImages 保存所有图片
func (p *ImageDisplayPanel) saveAllImages() {
	if p.onSaveImage != nil {
		for _, item := range p.images {
			p.onSaveImage(item)
		}
	}
}

// clearImages 清空图片
func (p *ImageDisplayPanel) clearImages() {
	p.images = make([]ImageItem, 0)
	p.imageGrid.RemoveAll()
	p.saveButton.Disable()
	p.clearButton.Disable()
}
