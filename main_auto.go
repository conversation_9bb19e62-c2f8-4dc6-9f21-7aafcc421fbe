package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"
)

// 简化的API结构
type SimpleRequest struct {
	Prompt             string  `json:"prompt"`
	ImageSize          string  `json:"image_size,omitempty"`
	BatchSize          int     `json:"batch_size,omitempty"`
	NumInferenceSteps  int     `json:"num_inference_steps,omitempty"`
	GuidanceScale      float64 `json:"guidance_scale,omitempty"`
	NegativePrompt     string  `json:"negative_prompt,omitempty"`
	Model              string  `json:"model,omitempty"`
	Quality            string  `json:"quality,omitempty"`
}

type SimpleResponse struct {
	Success bool `json:"success"`
	Data    struct {
		ImageURL       string `json:"image_url,omitempty"`
		LocalURL       string `json:"local_url,omitempty"`
		Seed           int64  `json:"seed"`
		GenerationTime float64 `json:"generation_time"`
		Images         []struct {
			ImageURL string `json:"image_url"`
			LocalURL string `json:"local_url"`
			Index    int    `json:"index"`
		} `json:"images,omitempty"`
	} `json:"data"`
	Message string `json:"message,omitempty"`
}

const BaseURL = "https://www.diwangzhidao.com/api/wenshengtu/api.php"

func main() {
	fmt.Println("🎨 === AI图片生成器 (自动演示版本) ===")
	fmt.Println()

	// 预设的测试提示词
	testPrompts := []string{
		"杰作，最佳画质，超高分辨率，一位美丽的古典东方女性，精致五官，明亮有神的眼睛，穿着红色汉服，古典园林背景，柔和光照，完美构图",
		"masterpiece, best quality, ultra detailed, beautiful anime girl, large expressive eyes, colorful hair, school uniform, cherry blossoms background, soft lighting",
		"杰作，最佳画质，8K壁纸，自然风光摄影，壮丽山景，层次分明，色彩丰富，黄金时刻光照，完美构图",
	}

	negativePrompt := "模糊，低质量，变形，噪点，失焦，多余手指，错误解剖，构图混乱"

	for i, prompt := range testPrompts {
		fmt.Printf("🔄 测试 %d/3: 正在生成图片...\n", i+1)
		fmt.Printf("📝 提示词: %s\n", prompt)
		fmt.Printf("🚫 负面提示词: %s\n", negativePrompt)
		fmt.Println()

		// 创建请求
		req := SimpleRequest{
			Prompt:            prompt,
			NegativePrompt:    negativePrompt,
			ImageSize:         "1024x1024",
			BatchSize:         1,
			NumInferenceSteps: 25,
			GuidanceScale:     8.0,
			Model:             "Kwai-Kolors/Kolors",
			Quality:           "standard",
		}

		// 调用API
		resp, err := callAPI(req)
		if err != nil {
			log.Printf("❌ API调用失败: %v", err)
			continue
		}

		if !resp.Success {
			log.Printf("❌ 生成失败: %s", resp.Message)
			continue
		}

		// 显示结果
		fmt.Printf("✅ 生成成功！\n")
		fmt.Printf("⏱️  生成时间: %.2f秒\n", resp.Data.GenerationTime)
		fmt.Printf("🎲 随机种子: %d\n", resp.Data.Seed)

		// 下载图片
		var imageURL string
		if resp.Data.LocalURL != "" {
			imageURL = resp.Data.LocalURL
		} else {
			imageURL = resp.Data.ImageURL
		}

		if imageURL != "" {
			fmt.Printf("🔗 图片URL: %s\n", imageURL)
			
			// 下载并保存图片
			if err := downloadAndSaveImageWithIndex(imageURL, i+1); err != nil {
				log.Printf("❌ 保存图片失败: %v", err)
			} else {
				fmt.Printf("💾 图片已保存到当前目录\n")
			}
		}

		// 处理多张图片
		if len(resp.Data.Images) > 0 {
			fmt.Printf("📸 共生成 %d 张图片:\n", len(resp.Data.Images))
			for j, img := range resp.Data.Images {
				url := img.LocalURL
				if url == "" {
					url = img.ImageURL
				}
				fmt.Printf("   图片 %d: %s\n", j+1, url)
				
				if err := downloadAndSaveImageWithIndex(url, (i*10)+j+1); err != nil {
					log.Printf("❌ 保存图片 %d 失败: %v", j+1, err)
				}
			}
		}

		fmt.Println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
		fmt.Println()

		// 等待一下再进行下一个测试
		if i < len(testPrompts)-1 {
			fmt.Println("⏳ 等待3秒后进行下一个测试...")
			time.Sleep(3 * time.Second)
			fmt.Println()
		}
	}

	fmt.Println("🎉 所有测试完成！")
	fmt.Println("📁 生成的图片已保存在当前目录中")
	fmt.Println()
	fmt.Println("💡 提示：您可以修改代码中的 testPrompts 来测试不同的提示词")
}

func callAPI(req SimpleRequest) (*SimpleResponse, error) {
	// 序列化请求
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	// 创建HTTP请求
	url := fmt.Sprintf("%s?action=generate", BaseURL)
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{Timeout: 120 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 解析响应
	var result SimpleResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &result, nil
}

func downloadAndSaveImageWithIndex(url string, index int) error {
	// 下载图片
	resp, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("下载图片失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载失败，状态码: %d", resp.StatusCode)
	}

	// 读取图片数据
	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取图片数据失败: %w", err)
	}

	// 生成文件名
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("ai_image_%s_test%d.png", timestamp, index)

	// 保存图片
	if err := os.WriteFile(filename, imageData, 0644); err != nil {
		return fmt.Errorf("保存图片失败: %w", err)
	}

	fmt.Printf("✅ 图片已保存: %s\n", filename)
	return nil
}
