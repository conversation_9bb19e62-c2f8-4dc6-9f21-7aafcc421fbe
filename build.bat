@echo off
echo 正在构建AI图片生成器...

REM 设置Go环境变量
set CGO_ENABLED=1
set GO111MODULE=on

REM 清理之前的构建
if exist ai-image-generator.exe del ai-image-generator.exe
if exist build mkdir build
if not exist build mkdir build

echo.
echo 正在安装依赖...
go mod tidy

echo.
echo 正在编译Windows版本...
go build -ldflags "-s -w" -o build/ai-image-generator-windows.exe

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Windows版本编译成功！
    echo 输出文件: build/ai-image-generator-windows.exe
) else (
    echo.
    echo ❌ Windows版本编译失败！
    pause
    exit /b 1
)

echo.
echo 正在编译Linux版本...
set GOOS=linux
set GOARCH=amd64
go build -ldflags "-s -w" -o build/ai-image-generator-linux

if %ERRORLEVEL% EQU 0 (
    echo ✅ Linux版本编译成功！
    echo 输出文件: build/ai-image-generator-linux
) else (
    echo ❌ Linux版本编译失败！
)

echo.
echo 正在编译macOS版本...
set GOOS=darwin
set GOARCH=amd64
go build -ldflags "-s -w" -o build/ai-image-generator-macos

if %ERRORLEVEL% EQU 0 (
    echo ✅ macOS版本编译成功！
    echo 输出文件: build/ai-image-generator-macos
) else (
    echo ❌ macOS版本编译失败！
)

REM 重置环境变量
set GOOS=
set GOARCH=

echo.
echo 🎉 构建完成！
echo.
echo 生成的文件：
dir build /b

echo.
echo 使用说明：
echo 1. Windows用户运行: build/ai-image-generator-windows.exe
echo 2. Linux用户运行: ./build/ai-image-generator-linux
echo 3. macOS用户运行: ./build/ai-image-generator-macos
echo.
pause
