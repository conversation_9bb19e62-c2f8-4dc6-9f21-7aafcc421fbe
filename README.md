# AI图片生成器

基于Kolors模型的桌面端AI图片生成工具，使用Go语言和Fyne框架开发。

## 功能特性

### 🎨 核心功能
- **中文友好**: 完美支持中文提示词，基于快手Kolors模型
- **图形界面**: 现代化的桌面GUI，操作简单直观
- **批量生成**: 支持一次生成1-4张图片
- **多种尺寸**: 支持11种不同的图片尺寸比例
- **参数调节**: 完整支持所有API参数设置
- **历史记录**: 自动保存提示词历史，方便重复使用

### 🛠️ 技术特性
- **跨平台**: 支持Windows、macOS、Linux
- **独立运行**: 单个可执行文件，无需额外依赖
- **配置保存**: 自动保存用户设置和历史记录
- **错误处理**: 完善的错误提示和异常处理
- **进度显示**: 实时显示生成进度和状态

## 安装和使用

### 环境要求
- Go 1.21 或更高版本
- 网络连接（调用API）

### 编译安装

1. **克隆项目**
```bash
git clone <repository-url>
cd ai-image-generator
```

2. **安装依赖**
```bash
go mod tidy
```

3. **编译程序**
```bash
# Windows
go build -o ai-image-generator.exe

# macOS/Linux
go build -o ai-image-generator

# 交叉编译（可选）
# Windows (在其他平台编译)
GOOS=windows GOARCH=amd64 go build -o ai-image-generator.exe

# macOS (在其他平台编译)
GOOS=darwin GOARCH=amd64 go build -o ai-image-generator

# Linux (在其他平台编译)
GOOS=linux GOARCH=amd64 go build -o ai-image-generator
```

4. **运行程序**
```bash
# Windows
./ai-image-generator.exe

# macOS/Linux
./ai-image-generator
```

### 使用说明

1. **启动程序**: 双击可执行文件或在终端运行
2. **输入提示词**: 在左侧面板输入图片描述
3. **调整参数**: 根据需要调整生成参数
4. **生成图片**: 点击"生成图片"按钮
5. **保存图片**: 生成完成后点击"保存"按钮

## 界面说明

### 左侧参数面板

#### 提示词设置
- **正面提示词**: 描述想要生成的图片内容
- **负面提示词**: 描述不希望出现的内容
- **历史记录**: 快速选择之前使用过的提示词

#### 生成参数
- **模型**: 选择AI模型（推荐Kwai-Kolors/Kolors）
- **图片尺寸**: 选择输出图片的尺寸
- **生成数量**: 一次生成的图片数量（1-4张）
- **推理步数**: 影响生成质量，建议20-30步
- **引导强度**: 控制对提示词的遵循程度，建议7-9
- **随机种子**: 控制随机性，留空为随机
- **艺术风格**: 选择预设的艺术风格
- **图片质量**: 选择标准或高清质量

### 右侧显示面板

#### 生成状态
- 显示当前生成状态和进度
- 显示生成时间和种子值

#### 生成结果
- 网格显示生成的图片
- 每张图片都有独立的保存按钮
- 支持批量保存所有图片

## 提示词技巧

### 高质量提示词模板

#### 人物肖像
```
杰作，最佳画质，超高分辨率，8K画质，专业摄影，一位美丽的女性，精致五官，明亮眼神，自然表情，柔和光照，完美构图
```

#### 风景画面
```
杰作，最佳画质，8K壁纸，自然风光摄影，壮丽山景，层次分明，色彩丰富，黄金时刻光照，完美构图，高动态范围
```

#### 动漫风格
```
杰作，最佳画质，动漫风格，美丽少女，大眼睛，彩色头发，校服，樱花背景，柔和光照，新海诚风格，完美细节
```

### 常用负面提示词
```
模糊，低质量，变形，噪点，失焦，多余手指，错误解剖，构图混乱，色彩失真
```

## 配置文件

程序会在用户目录下创建配置文件：
- **Windows**: `%USERPROFILE%\.ai-image-generator\config.json`
- **macOS**: `~/.ai-image-generator/config.json`
- **Linux**: `~/.ai-image-generator/config.json`

配置文件包含：
- 默认参数设置
- 窗口大小和位置
- 保存目录路径
- 提示词历史记录

## 故障排除

### 常见问题

1. **网络连接错误**
   - 检查网络连接
   - 确认API服务可用
   - 检查防火墙设置

2. **生成失败**
   - 检查提示词是否合规
   - 尝试调整参数设置
   - 查看错误信息详情

3. **保存失败**
   - 检查保存目录权限
   - 确认磁盘空间充足
   - 检查文件名是否合法

4. **界面显示异常**
   - 尝试调整窗口大小
   - 重启程序
   - 删除配置文件重置设置

### 性能优化

1. **减少内存使用**
   - 及时清空生成的图片
   - 避免同时生成过多图片
   - 定期重启程序

2. **提高生成速度**
   - 选择较小的图片尺寸
   - 减少推理步数
   - 使用较快的模型

## 技术架构

### 项目结构
```
ai-image-generator/
├── main.go              # 程序入口
├── api/                 # API客户端
│   ├── client.go       # HTTP客户端
│   └── types.go        # 数据结构
├── ui/                  # 用户界面
│   ├── app.go          # 主应用程序
│   └── components.go   # UI组件
├── config/              # 配置管理
│   └── config.go       # 配置文件处理
├── utils/               # 工具函数
│   └── image.go        # 图片处理
└── README.md           # 说明文档
```

### 依赖库
- **fyne.io/fyne/v2**: GUI框架
- **encoding/json**: JSON处理
- **net/http**: HTTP客户端
- **image**: 图片处理

## 开发说明

### 添加新功能
1. 在对应模块添加功能代码
2. 更新UI界面（如需要）
3. 更新配置结构（如需要）
4. 测试功能正常工作
5. 更新文档

### 调试模式
```bash
# 启用详细日志
export FYNE_THEME=light
go run main.go
```

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至开发者

---

**注意**: 本工具仅供学习和研究使用，请遵守相关法律法规和API服务条款。
