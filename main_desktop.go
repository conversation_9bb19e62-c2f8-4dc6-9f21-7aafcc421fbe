package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"syscall"
	"time"
	"unsafe"
)

// Windows API 声明
var (
	user32               = syscall.NewLazyDLL("user32.dll")
	kernel32             = syscall.NewLazyDLL("kernel32.dll")
	procMessageBox       = user32.NewProc("MessageBoxW")
	procGetConsoleWindow = kernel32.NewProc("GetConsoleWindow")
	procShowWindow       = user32.NewProc("ShowWindow")
)

// API结构
type GenerateRequest struct {
	Prompt             string  `json:"prompt"`
	ImageSize          string  `json:"image_size,omitempty"`
	BatchSize          int     `json:"batch_size,omitempty"`
	NumInferenceSteps  int     `json:"num_inference_steps,omitempty"`
	GuidanceScale      float64 `json:"guidance_scale,omitempty"`
	NegativePrompt     string  `json:"negative_prompt,omitempty"`
	Model              string  `json:"model,omitempty"`
	Quality            string  `json:"quality,omitempty"`
}

type GenerateResponse struct {
	Success bool `json:"success"`
	Data    struct {
		ImageURL       string `json:"image_url,omitempty"`
		LocalURL       string `json:"local_url,omitempty"`
		Seed           int64  `json:"seed"`
		GenerationTime float64 `json:"generation_time"`
		Images         []struct {
			ImageURL string `json:"image_url"`
			LocalURL string `json:"local_url"`
			Index    int    `json:"index"`
		} `json:"images,omitempty"`
	} `json:"data"`
	Message string `json:"message,omitempty"`
}

const BaseURL = "https://www.diwangzhidao.com/api/wenshengtu/api.php"

func main() {
	// 隐藏控制台窗口
	hideConsole()
	
	// 显示欢迎对话框
	showMessage("🎨 AI图片生成器", "欢迎使用AI图片生成器！\n\n基于Kolors模型的高质量图片生成工具\n\n点击确定开始使用", 0)
	
	// 获取用户输入
	prompt := getInput("请输入图片描述提示词：", "杰作，最佳画质，超高分辨率，一位美丽的古典东方女性，精致五官，明亮有神的眼睛，穿着红色汉服，古典园林背景，柔和光照，完美构图")
	if prompt == "" {
		showMessage("提示", "未输入提示词，程序退出", 0)
		return
	}
	
	negativePrompt := getInput("请输入负面提示词（可选）：", "模糊，低质量，变形，噪点，失焦，多余手指，错误解剖，构图混乱")
	
	// 显示生成中对话框
	showMessage("生成中", "正在生成图片，请稍候...\n\n提示词: "+prompt, 0)
	
	// 创建请求
	req := GenerateRequest{
		Prompt:            prompt,
		NegativePrompt:    negativePrompt,
		ImageSize:         "1024x1024",
		BatchSize:         1,
		NumInferenceSteps: 25,
		GuidanceScale:     8.0,
		Model:             "Kwai-Kolors/Kolors",
		Quality:           "standard",
	}
	
	// 调用API
	resp, err := callAPI(req)
	if err != nil {
		showMessage("错误", "生成失败: "+err.Error(), 16)
		return
	}
	
	if !resp.Success {
		showMessage("错误", "API错误: "+resp.Message, 16)
		return
	}
	
	// 处理结果
	data := resp.Data
	resultMsg := fmt.Sprintf("✅ 生成成功！\n\n生成时间: %.2f秒\n随机种子: %d\n\n正在下载图片...", 
		data.GenerationTime, data.Seed)
	showMessage("成功", resultMsg, 0)
	
	// 下载并保存图片
	var imageURL string
	if data.LocalURL != "" {
		imageURL = data.LocalURL
	} else {
		imageURL = data.ImageURL
	}
	
	if imageURL != "" {
		savedPath, err := downloadAndSaveImage(imageURL)
		if err != nil {
			showMessage("错误", "保存图片失败: "+err.Error(), 16)
			return
		}
		
		// 显示成功消息并询问是否打开图片
		finalMsg := fmt.Sprintf("🎉 图片生成完成！\n\n保存位置: %s\n\n是否打开图片查看？", savedPath)
		result := showMessage("完成", finalMsg, 4) // MB_YESNO
		
		if result == 6 { // IDYES
			openFile(savedPath)
		}
		
		// 询问是否继续生成
		continueMsg := "是否继续生成其他图片？"
		result = showMessage("继续", continueMsg, 4) // MB_YESNO
		
		if result == 6 { // IDYES
			main() // 递归调用重新开始
		}
	}
}

func hideConsole() {
	if runtime.GOOS == "windows" {
		console, _, _ := procGetConsoleWindow.Call()
		if console != 0 {
			procShowWindow.Call(console, 0) // SW_HIDE
		}
	}
}

func showMessage(title, message string, msgType uintptr) uintptr {
	if runtime.GOOS != "windows" {
		fmt.Printf("%s: %s\n", title, message)
		return 1
	}
	
	titlePtr, _ := syscall.UTF16PtrFromString(title)
	messagePtr, _ := syscall.UTF16PtrFromString(message)
	
	ret, _, _ := procMessageBox.Call(
		0,
		uintptr(unsafe.Pointer(messagePtr)),
		uintptr(unsafe.Pointer(titlePtr)),
		msgType,
	)
	
	return ret
}

func getInput(prompt, defaultValue string) string {
	if runtime.GOOS == "windows" {
		// 在Windows上使用PowerShell获取输入
		script := fmt.Sprintf(`
Add-Type -AssemblyName Microsoft.VisualBasic
$result = [Microsoft.VisualBasic.Interaction]::InputBox('%s', 'AI图片生成器', '%s')
Write-Output $result
`, strings.ReplaceAll(prompt, "'", "''"), strings.ReplaceAll(defaultValue, "'", "''"))
		
		cmd := exec.Command("powershell", "-Command", script)
		output, err := cmd.Output()
		if err != nil {
			return defaultValue
		}
		
		result := strings.TrimSpace(string(output))
		if result == "" {
			return defaultValue
		}
		return result
	}
	
	// 非Windows系统的简单实现
	fmt.Print(prompt)
	var input string
	fmt.Scanln(&input)
	if input == "" {
		return defaultValue
	}
	return input
}

func callAPI(req GenerateRequest) (*GenerateResponse, error) {
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	url := fmt.Sprintf("%s?action=generate", BaseURL)
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 120 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var result GenerateResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &result, nil
}

func downloadAndSaveImage(url string) (string, error) {
	// 下载图片
	resp, err := http.Get(url)
	if err != nil {
		return "", fmt.Errorf("下载图片失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("下载失败，状态码: %d", resp.StatusCode)
	}

	// 读取图片数据
	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取图片数据失败: %w", err)
	}

	// 创建保存目录
	homeDir, _ := os.UserHomeDir()
	saveDir := filepath.Join(homeDir, "AI_Generated_Images")
	os.MkdirAll(saveDir, 0755)

	// 生成文件名
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("ai_image_%s.png", timestamp)
	filePath := filepath.Join(saveDir, filename)

	// 保存图片
	if err := os.WriteFile(filePath, imageData, 0644); err != nil {
		return "", fmt.Errorf("保存图片失败: %w", err)
	}

	return filePath, nil
}

func openFile(path string) {
	var cmd *exec.Cmd
	switch runtime.GOOS {
	case "windows":
		cmd = exec.Command("rundll32", "url.dll,FileProtocolHandler", path)
	case "darwin":
		cmd = exec.Command("open", path)
	default:
		cmd = exec.Command("xdg-open", path)
	}
	cmd.Start()
}
